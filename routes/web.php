<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['middleware' => ['subdomain', 'clientMail', 'ApiLog', 'debugIPVerify'], 'domain' => '{subdomain}.' . env('APP_URL')], function () {

    Auth::routes();

    Route::post('/debug/post', 'DebugController@storePost');
    Route::post('/captcha/verify', 'HomeController@captcha');
    Route::post('/snelstart/webhook', 'SnelstartController@webhook');

    //  Non auth api
    Route::group(['prefix' => 'api'], function () {
        Route::post('/getsubdomain', 'Api\ApiController@getSubdomain');
        Route::post('/settings', 'Api\ApiController@settings');
        Route::get('/file/{file}', 'Api\FileController@get')->where('file', '(.*)');
        Route::get('/filename/{name}/{file}', 'Api\FileController@getWithName')->where('file', '(.*)');
        Route::get('/developertokens/get', 'Api\UserController@getDeveloperTokens');
        Route::post('/rotate/file', 'Api\FileController@rotate');
        Route::post('/session/validate', 'Api\ApiController@validateSession');
        Route::post('/login', 'Api\UserController@login');
        Route::group(['prefix' => 'projecten/'], function () {
            Route::post('woocommerce/insert', 'Api\ProjectenController@woocommerceInsert')->middleware('woocommerceVerify');
            Route::post('woocommerce/update', 'Api\ProjectenController@woocommerceUpdate')->middleware('woocommerceVerify');
            Route::post('woocommerce/delete', 'Api\ProjectenController@woocommerceDelete')->middleware('woocommerceVerify');
        });
        Route::group(['prefix' => 'debug/'], function () {
            Route::post('token', 'DebugController@verifyApitoken');
        });
        Route::group(['prefix' => 'planning/'], function () {
            Route::post('/webhook/briq', 'PlanningController@webhookBriq');
        });
        Route::group(['prefix' => 'whatsapp/'], function () {
            Route::get('/webhook', 'Api\WhatsAppController@webhook');
            Route::post('/webhook', 'Api\WhatsAppController@webhook');
        });
    });
    //  Auth api
    Route::group(['prefix' => 'api', 'middleware' => 'apipost'], function () {
        Route::post('/location', 'Api\ApiController@location');
        Route::post('/correction', 'Api\UrenController@correction');
        Route::post('/delete/file', 'Api\FileController@delete');
        Route::post('/sms/store', 'Api\ApiController@storeSms');
        Route::post('/call/store', 'Api\ApiController@storeCall');
        Route::post('/call/opmerking', 'Api\ApiController@storeCallOpmerking');
        Route::post('/settings/store', 'Api\ApiController@storeSetting');
        Route::group(['prefix' => 'git'], function () {
            Route::post('/hide-commit', 'Api\GitController@hideCommit');
        });
        Route::group(['prefix' => 'news'], function () {
            Route::get('/', 'Api\NewsController@all');
            Route::get('/recent', 'Api\NewsController@paginate');
            Route::get('/{id}', 'Api\NewsController@show');
        });
        Route::group(['prefix' => 'memo'], function () {
          Route::get('/', 'Api\MemoController@get');
          Route::post('/push', 'Api\MemoController@push')->middleware("HasPermission:memo beheren");
          Route::post('/get', 'Api\MemoController@get');
          Route::post('/search', 'Api\MemoController@search');
          Route::post('/delete', 'Api\MemoController@delete')->middleware("HasPermission:memo beheren");
          Route::post('/changeorder', 'Api\MemoController@changeOrder')->middleware("HasPermission:memo beheren");
        });
        Route::group(['prefix' => 'abonnementen'], function () {
            Route::post('/autoverlengen', 'Api\AbonnementenController@autoverlengen');
            Route::post('/subscription/toggle', 'Api\AbonnementenController@toggleSubscription');
            Route::post('/send-credentials', 'Api\AbonnementenController@sendCredentials');
            Route::post('/notify-subscribers', 'Api\AbonnementenController@notifySubscribers');
            Route::post('get/imported-to-send', 'Api\AbonnementenController@getImportedToSend');
            Route::post('check/item/uitgaves/{daysFromNow}', 'Api\AbonnementenController@checkItemUitgave');
            Route::post('uitgaves/reminder/cronjob', 'Api\AbonnementenController@uitgaveReminderCronjob');
            Route::post('factuur/refresh/pdf', 'Api\AbonnementenController@refreshFactuurPdf');
            Route::group(['prefix' => 'formulier'], function () {
                Route::post('/update', 'Api\AbonnementenController@update');
            });
            Route::group(['prefix' => 'verenigingen'], function () {
                Route::post('/get', 'Api\AbonnementenController@verenigingenGet');
            });
        });
        Route::group(['prefix' => 'klanten'], function () {
            Route::post('/get', 'Api\KlantenController@get');
            Route::post('/search', 'Api\KlantenController@search');
            Route::post('/documenten', 'Api\KlantenController@documenten');
            Route::post('/store', 'Api\KlantenController@store');
            Route::post('/active', 'Api\KlantenController@active');
            Route::post('/delete', 'Api\KlantenController@delete');
            Route::post('/werkbonnen', 'Api\KlantenController@werkbonnen');
            Route::post('/projecten', 'Api\KlantenController@projecten');
            Route::post('/offertes', 'Api\KlantenController@offertes');
            Route::post('/checklists', 'Api\KlantenController@checklists');
            Route::post('/aanvragen', 'Api\KlantenController@aanvragen');
            Route::post('/klant', 'Api\KlantenController@klant');
            Route::post('/opmerking/store', 'Api\KlantenController@opmerking');
            Route::post('/opmerking/delete', 'Api\KlantenController@opmerkingDelete');
            Route::post('/opmerking/edit', 'Api\KlantenController@opmerkingEdit');
            Route::post('/email/store', 'Api\KlantenController@emailStore');
            Route::post('/viescheck', 'Api\KlantenController@viesCheck');
            Route::post('/getklantsettingvalue', 'Api\KlantenController@getKlantSettingValue');

            //todo weghalen zodra ramudden klaar is
            Route::post('/storeklantcp', 'Api\KlantenController@storeKlantCPField');
            Route::post('/storecp', 'Api\KlantenController@storeCPField');
        });
        Route::group(['prefix' => 'pictures'], function () {
            Route::post('/', 'Api\PicturesController@get');
            Route::post('/upload', 'Api\PicturesController@upload');
            Route::post('/delete', 'Api\PicturesController@delete');
            Route::post('/store', 'Api\PicturesController@store');
            Route::post('/klanten', 'Api\PicturesController@klanten');
        });
        Route::group(['prefix' => 'urenregistratie'], function () {
            Route::post('/get', 'Api\UrenController@get');
            Route::get('/feestdagen', 'Api\UrenController@feestdagen');
            Route::post('/datums', 'Api\UrenController@datums');
            Route::post('/date', 'Api\UrenController@date');
            Route::post('/week', 'Api\UrenController@week');
            Route::post('/store', 'Api\UrenController@store');
            Route::post('/storeweek', 'Api\UrenController@storeWeek');
            Route::post('/overzicht', 'Api\UrenController@overzicht');
            Route::post('/standaardureninvullen', 'Api\UrenController@standaardurenInvullen');
        });
        Route::group(['prefix' => 'verlof'], function () {
            Route::post('/store', 'Api\VerlofController@store');
            Route::post('/', 'Api\VerlofController@get');
        });
        Route::group(['prefix' => 'werkbonnen'], function () {
            Route::post('/get', 'Api\WerkbonnenController@get');
            Route::post('/getwerkbon', 'Api\WerkbonnenController@getWerkbon');
            Route::post('/new', 'Api\WerkbonnenController@newWerkbon');
            Route::post('/create', 'Api\WerkbonnenController@createWerkbon');
            Route::post('/edit', 'Api\WerkbonnenController@edit');
            Route::post('/store', 'Api\WerkbonnenController@store');
            Route::post('/offerteContent', 'Api\WerkbonnenController@offerteContent');
            Route::post('/index', 'Api\WerkbonnenController@index');
            Route::post('/inzien', 'Api\WerkbonnenController@inzien');
            Route::post('/upload', 'Api\WerkbonnenController@upload');
            Route::post('/send/signature', 'Api\WerkbonnenController@sendSignature');
            Route::post('/afronden', 'Api\WerkbonnenController@afronden');
            Route::post('/status', 'Api\WerkbonnenController@status');
            Route::post('/opvolgen', 'Api\WerkbonnenController@opvolgen');
            Route::post('/search', 'Api\WerkbonnenController@search');
            Route::post('/urenregistratie', 'Api\WerkbonnenController@urenregistratie');
            Route::post('/reminders', 'Api\WerkbonnenController@reminders');

          Route::group(['prefix' => 'list-input'], function () {
            Route::post('/store', 'Api\WerkbonnenController@storeListInputTemplate');
            Route::post('/delete', 'Api\WerkbonnenController@deleteListInputTemplate');
            Route::post('/get', 'Api\WerkbonnenController@getListInputTemplate');
          });

        });
        Route::group(['prefix' => 'planning'], function () {
            Route::post('/get', 'Api\PlanningController@get');
            Route::post('/index', 'Api\PlanningController@index');
            Route::post('/dagplanning', 'Api\PlanningController@dagPlanning');
            Route::post('/portal', 'Api\PlanningController@portal');
            Route::post('/urenregistratie', 'Api\PlanningController@urenregistratie');
            Route::post('/release', 'Api\PlanningController@release');
            Route::post('/releasedag', 'Api\PlanningController@releaseDag');
            Route::post('/complete', 'Api\PlanningController@complete');
            Route::post('/update', 'Api\PlanningController@update');
            Route::post('/resource', 'Api\PlanningController@storeResource');
            Route::post('/resource/delete', 'Api\PlanningController@deleteResource');
            Route::post('/resource/beschikbaar', 'Api\PlanningController@resourceBeschikbaarheid');
            Route::post('/vrijgevendag', 'Api\PlanningController@vrijgevenDag');
            Route::post('/afrondenDag', 'Api\PlanningController@afrondenDag');
            Route::post('/usersperplanning', 'Api\PlanningController@usersPerPlanning');
            Route::post('/cronjob/afronden', 'Api\PlanningController@automatischAfronden');
        });
        Route::group(['prefix' => 'rapporten'], function () {
            Route::post('/get', 'Api\VerslagenController@get');
            Route::post('/get/rapport', 'Api\VerslagenController@getRapport');
            Route::post('/upload', 'Api\VerslagenController@upload');
            Route::post('/upload-base', 'Api\VerslagenController@uploadBase');
            Route::post('/new', 'Api\VerslagenController@new');
            Route::post('/store', 'Api\VerslagenController@store');
            Route::post('/templates', 'Api\VerslagenController@templates');
            Route::post('/klant', 'Api\VerslagenController@klant');
            Route::get('/pdf/{id}', 'Api\VerslagenController@pdf');
        });
        Route::group(['prefix' => 'checklists'], function () {
            Route::post('/templates', 'Api\ChecklistsController@templates');
            Route::post('/show', 'Api\ChecklistsController@show');
            Route::post('/get', 'Api\ChecklistsController@get');
            Route::post('/search', 'Api\ChecklistsController@search');
            Route::post('/new', 'Api\ChecklistsController@new');
            Route::post('/store', 'Api\ChecklistsController@store');
            Route::post('/edit', 'Api\ChecklistsController@edit');
            Route::post('/upload', 'Api\ChecklistsController@upload');
            Route::post('/complete', 'Api\ChecklistsController@complete');
            Route::post('/activate', 'Api\ChecklistsController@activate');
            Route::post('/previouschecklistlocatie', 'Api\ChecklistsController@previousChecklistLocatie');
        });
        Route::group(['prefix' => 'actielijst'], function () {
            Route::post('/get', 'Api\ActielijstController@get');
            Route::post('/new', 'Api\ActielijstController@new');
            Route::post('/store', 'Api\ActielijstController@store');
            Route::post('/index', 'Api\ActielijstController@index');
            Route::post('/hide', 'Api\ActielijstController@hide');
            Route::post('/opmerking', 'Api\ActielijstController@opmerking');
            Route::post('/hide', 'Api\ActielijstController@hide');
            Route::post('/count', 'Api\ActielijstController@count');
            Route::post('/copy', 'Api\ActielijstController@copy');
        });
        Route::group(['prefix' => 'aanvragen'], function () {
            Route::post('/new', 'Api\AanvragenController@create');
            Route::post('/store', 'Api\AanvragenController@store');
            Route::post('/get', 'Api\AanvragenController@get');
            Route::post('/show', 'Api\AanvragenController@show');
            Route::post('/status/select', 'AanvragenController@statusSelect');
            Route::post('/leads/solvari', 'Api\AanvragenController@solvari');
            Route::post('/leads/offertenl', 'Api\AanvragenController@offertenl');
            Route::post('/leads/bobex', 'Api\AanvragenController@bobex');
        });
        Route::group(['prefix' => 'beschikbaarheid'], function () {
            Route::post('/store', 'Api\BeschikbaarheidController@store');
            Route::post('/delete', 'Api\BeschikbaarheidController@delete');
            Route::post('/get', 'Api\BeschikbaarheidController@get');
        });
        Route::group(['prefix' => 'users'], function () {
            Route::post('/get', 'Api\UserController@get');
            Route::post('/search', 'Api\UserController@search');
            Route::post('/delete', 'UserController@delete');
            Route::post('/refresh-data', 'Api\UserController@refreshData');
            Route::post('/signatures/get', 'Api\UserController@signaturesGet');
            Route::post('/signatures/store', 'Api\UserController@signaturesStore');
            Route::post('/syncstandaardurenrol', 'Api\UserController@syncStandaardurenRol');
            Route::post('/updateverlofsaldo', 'Api\UserController@updateVerlofsaldo');
            Route::post('/getyeartotal', 'Api\UserController@getYearTotal');
            Route::post('/getverlofsaldo', 'Api\UserController@getVerlofsaldo');
            Route::post('/getAtvUren', 'Api\UserController@getAtvUren');
            Route::post('/has-module', 'Api\UserController@hasModule');
            Route::post('/reset-password', 'Api\UserController@resetPassword');

            Route::group(['prefix' => 'intake'], function () {
              Route::post('/status', 'Api\UserController@intakeStatus');
            });

        });
        Route::group(['prefix' => 'roles'], function () {
          Route::post('/get', 'Api\RoleController@get');
          Route::post('/reset-passwords', 'Api\RoleController@resetPasswords');
          Route::post('/delete/{id}', 'Api\RoleController@destroy');
        });
        Route::group(['prefix' => 'offertes'], function () {
            Route::post('/get', 'Api\OffertesController@get');
            Route::post('/search', 'Api\OffertesController@search');
            Route::post('/total', 'Api\OffertesController@total');
            Route::post('/detail/get', 'Api\OffertesController@detailGet');
            Route::post('detail/detail-to-factuur', 'Api\OffertesController@detailToFactuur');
            Route::post('/description/store', 'Api\OffertesController@descriptionStore');
            Route::post('/description/delete', 'Api\OffertesController@descriptionDelete');
            Route::post('/set-version-as-current', 'Api\OffertesController@setVersionAsCurrent');
            Route::post('/copy', 'Api\OffertesController@copy');
            Route::post('/edited', 'Api\OffertesController@edited');
            Route::post('/send', 'Api\OffertesController@sendOfferte');
            Route::post('/status', 'Api\OffertesController@status')->middleware("HasPermission:Offertes aanmaken");
            Route::post('/datasetitem/get', 'Api\OffertesController@getDatasetItem');
            Route::post('/datasetitem/store', 'Api\OffertesController@storeDatasetItem');
            Route::post('/gettemplates', 'Api\OffertesController@getTemplates');
            Route::post('/generatepdf', 'Api\OffertesController@generatePdf');

            Route::group(['prefix' => 'datasets'], function () {
                Route::post('/get', 'Api\OffertesController@getDatasets');
                Route::post('/store', 'Api\OffertesController@storeDataset');

                Route::post('/items/store', 'Api\OffertesController@storeDatasetItems');
                Route::post('/items/file/connect', 'Api\OffertesController@connectDatasetItemFile');
                Route::post('/items/file/disconnect', 'Api\OffertesController@disconnectDatasetItemFile');

                Route::post('/details/get', 'Api\OffertesController@getDatasetDetails');
            });

        });
        Route::group(['prefix' => 'projecten'], function () {
            Route::post('/get', 'Api\ProjectenController@get');
            Route::post('/getrelation', 'Api\ProjectenController@getRelation');
            Route::post('/search', 'Api\ProjectenController@search');
            Route::post('/offertebedragen', 'Api\ProjectenController@offerteBedragen');
            Route::post('/first', 'Api\ProjectenController@first');
            Route::post('/verify', 'Api\ProjectenController@verify');
            Route::post('/store', 'Api\ProjectenController@store');
            Route::post('/status', 'Api\ProjectenController@status');
            Route::post('/planning', 'Api\ProjectenController@switchPlanning');
            Route::post('/color', 'Api\ProjectenController@color');
            Route::group(['prefix' => 'taken'], function () {
                Route::post('/get', 'Api\ProjectenController@getTaken');
                Route::post('/search', 'Api\ProjectenController@searchTaken');
                Route::post('/store', 'Api\ProjectenController@storeTaken');
                Route::post('/complete', 'Api\ProjectenController@completeTaken');
                Route::post('/delete', 'Api\ProjectenController@deleteTaken');
                Route::post('/redo', 'Api\ProjectenController@redoTaken');
                Route::post('/edit', 'Api\ProjectenController@editTaken');
                Route::post('/copy', 'Api\ProjectenController@copyTaken');
                Route::post('/checklist/toggle', 'Api\ProjectenController@checklistToggleTaken');
                Route::post('/color/update', 'Api\ProjectenController@updateTakenColor');
                Route::post('/templates/store', 'Api\ProjectenController@storeTakenTemplate');
                Route::post('/templates/active', 'Api\ProjectenController@activeTakenTemplate');
                Route::post('/templates/edit', 'Api\ProjectenController@editTakenTemplate');
                Route::post('/templates/taken/store', 'Api\ProjectenController@storeTemplateTaken');
                Route::post('/templates/taken/active', 'Api\ProjectenController@activeTemplateTaken');
            });
            Route::group(['prefix' => 'todo'], function () {
              Route::get('/get', 'Api\ProjectenController@getTodoLijsten');
            });
            Route::group(['prefix' => 'kastvakken'], function () {
                Route::post('/get', 'Api\ProjectenController@getKastvakken');
                Route::post('/opmerking', 'Api\ProjectenController@kastvakOpmerking');
            });
        });
        Route::group(['prefix' => 'machines'], function () {
            Route::post('/status', 'Api\MachinesController@status');
            Route::post('/groepen/store', 'Api\MachinesController@storeGroup');
            Route::post('/groepen/delete', 'Api\MachinesController@deleteGroup');
        });
        Route::group(['prefix' => 'uren'], function () {
            Route::post('/uursoorten/delete', 'Api\UrenController@uursoortDelete');
            Route::post('/mutatielijst/get', 'Uren\MutatieController@getMutatieLijsten');
        });
        Route::group(['prefix' => 'facturatie'], function () {
            Route::post('/get', 'Api\FacturatieController@get');
            Route::post('/search', 'Api\FacturatieController@search');
            Route::post('/reminders', 'Api\FacturatieController@reminders');
            Route::post('/credit/create', 'Api\FacturatieController@creditCreate');
            Route::post('/proforma/status', 'Api\FacturatieController@proformaStatus');
            Route::post('/factuur/status', 'Api\FacturatieController@factuurStatus');
            Route::post('/invoice/reminder', 'Api\FacturatieController@invoiceReminder');
            Route::post('/facturen/send', 'Api\FacturatieController@sendFactuur');
            Route::post('/facturen/delete', 'Api\FacturatieController@deleteFactuur');
            Route::post('/delete/files', 'Api\FacturatieController@deleteFactuurFiles');
            Route::post('/set/factuurnummer', 'Api\FacturatieController@setFactuurnummer');
            Route::post('/generatepdf', 'Api\FacturatieController@generatePdf');
            Route::post('/facturen/slice', 'Api\FacturatieController@slice')->middleware("HasPermission:Facturatie dashboard");
        });
        Route::group(['prefix' => 'accorderen'], function () {
            Route::post('/get', 'Api\AccorderenController@get');
            Route::post('/user-tasks', 'Api\AccorderenController@userTasks');
            Route::post('/history', 'Api\AccorderenController@history');
            Route::post('/status', 'Api\AccorderenController@status');
        });
        Route::group(['prefix' => 'leveranciers'], function () {
            Route::post('/get', 'Api\LeveranciersController@get');
            Route::post('/search', 'Api\LeveranciersController@search');
            Route::post('/active', 'Api\LeveranciersController@active');
            Route::post('/tarieven/get', 'Api\LeveranciersController@tarievenGet');
            Route::post('/tarieven/store', 'Api\LeveranciersController@tarievenStore');
        });
        Route::group(['prefix' => 'inkoopfacturen'], function () {
            Route::post('/get', 'Api\InkoopfacturenController@get');
            Route::post('/search', 'Api\InkoopfacturenController@search');
            Route::post('/status', 'Api\InkoopfacturenController@status');
        });
        Route::group(['prefix' => 'inkoopbonnen'], function () {
            Route::post('/get', 'Api\InkoopbonnenController@get');
            Route::post('/search', 'Api\InkoopbonnenController@search');
            Route::post('/status', 'Api\InkoopbonnenController@status');
            Route::post('/send', 'Api\InkoopbonnenController@send');

            Route::group(['prefix' => 'regels'], function () {
              Route::post('/get', 'Api\InkoopbonnenController@regelsGet');
            });

        });
        Route::group(['prefix' => 'statistieken'], function () {
            Route::post('/facturatie/groepen/get', 'Api\StatistiekenController@getFacturatieGroepen');
            Route::post('/facturatie/groepen/insert', 'Api\StatistiekenController@insertFacturatieGroepen');
            Route::post('/facturatie/groepen/delete', 'Api\StatistiekenController@deleteFacturatieGroepen');
            Route::post('/facturatie/groepen/addItems', 'Api\StatistiekenController@addItemsFacturatieGroepen');
        });
        Route::group(['prefix' => 'exact'], function () {
            Route::group(['prefix' => 'globe'], function () {
                Route::group(['prefix' => 'cronjob'], function () {
                    Route::post('/klanten', 'Api\ExactGlobeController@cronjobKlanten');
                    Route::post('/leveranciers', 'Api\ExactGlobeController@cronjobLeveranciers');
                    Route::post('/projecten', 'Api\ExactGlobeController@cronjobProjecten');
                    Route::post('/facturen', 'Api\ExactGlobeController@cronjobFacturen');
                    Route::post('/inkoopfacturen', 'Api\ExactGlobeController@cronjobInkoopfacturen');
                    Route::post('/facturen-status', 'Api\ExactGlobeController@cronjobFacturenStatus');
                    Route::post('/inkoopfacturen-status', 'Api\ExactGlobeController@cronjobInkoopfacturenStatus');
                });
                Route::group(['prefix' => 'sync'], function () {
                    Route::post('/resource', 'Api\ExactGlobeController@syncResource');
                    Route::post('/journals', 'Api\ExactGlobeController@syncJournals');
                    Route::post('/vat', 'Api\ExactGlobeController@syncVAT');
                    Route::post('/gl', 'Api\ExactGlobeController@syncGeneralLedger');
                    Route::post('/costcenter', 'Api\ExactGlobeController@syncCostCenter');
                    Route::post('/warehouse', 'Api\ExactGlobeController@syncWarehouse');
                });
                Route::group(['prefix' => 'klanten'], function () {
                    Route::post('/get', 'Api\ExactGlobeController@getexactklant');
                    Route::post('/upload', 'Api\ExactGlobeController@klantenUpload');
                });
                Route::group(['prefix' => 'account'], function () {
                    Route::post('/get', 'Api\ExactGlobeController@getExactAccount');
                });
                Route::group(['prefix' => 'facturen'], function () {
                    Route::post('/get', 'Api\ExactGlobeController@getexactFactuur');
                    Route::post('/upload', 'Api\ExactGlobeController@facturenUpload');
                    Route::post('/setrows', 'Api\ExactGlobeController@setRows');
                });
                Route::group(['prefix' => 'inkoopfacturen'], function () {
                    Route::post('/upload', 'Api\ExactGlobeController@inkoopfacturenUpload');
                    Route::post('/setrows', 'Api\ExactGlobeController@setInkoopRows');
                });
                Route::group(['prefix' => 'projecten'], function () {
                    Route::post('/upload', 'Api\ExactGlobeController@projectenUpload');
                });
                Route::group(['prefix' => 'leveranciers'], function () {
                    Route::post('/upload', 'Api\ExactGlobeController@leveranciersUpload');
                });
                Route::post('/connect', 'Api\ExactGlobeController@connect');
            });
            Route::group(['prefix' => 'online'], function () {
                Route::post('/search', 'Api\ExactOnlineController@search');
                Route::group(['prefix' => 'get'], function () {
                    Route::post('/logs', 'Api\ExactOnlineController@getLogs');
                    Route::post('/me', 'Api\ExactOnlineController@getMe');
                    Route::post('/division', 'Api\ExactOnlineController@getDivisions');
                });
                Route::group(['prefix' => 'upload'], function () {
                    Route::post('/klant', 'Api\ExactOnlineController@klantenUpload');
                    Route::post('/project', 'Api\ExactOnlineController@projectenUpload');
                    Route::post('/factuur', 'Api\ExactOnlineController@facturenUpload');
                });
                Route::group(['prefix' => 'sync'], function () {
                    Route::post('/VAT', 'Api\ExactOnlineController@syncVAT');
                    Route::post('/GL', 'Api\ExactOnlineController@syncGL');
                    Route::post('/items', 'Api\ExactOnlineController@syncItems');
                });
            });
        });
        Route::group(['prefix' => 'business-central'], function () {
            Route::post('/connect', 'Api\BusinessCentralController@connect');
            Route::post('/search', 'Api\BusinessCentralController@search');
            Route::post('/cronjob', 'Api\BusinessCentralController@cronjob');
            Route::group(['prefix' => 'upload'], function () {
                Route::post('/klant', 'Api\BusinessCentralController@klantenUpload');
                Route::post('/leverancier', 'Api\BusinessCentralController@leveranciersUpload');
                Route::post('/factuur', 'Api\BusinessCentralController@facturenUpload');
                Route::post('/inkoopbon', 'Api\BusinessCentralController@inkoopbonnenUpload');
                Route::post('/inkoopfactuur', 'Api\BusinessCentralController@inkoopfacturenUpload');
                Route::post('/project-dimension', 'Api\BusinessCentralController@projectenDimensionsUpload');
            });
            Route::group(['prefix' => 'sync'], function () {
                Route::post('/dimensions', 'Api\BusinessCentralController@syncDimensions');
                Route::post('/taxareas', 'Api\BusinessCentralController@syncTaxAreas');
                Route::post('/taxgroups', 'Api\BusinessCentralController@syncTaxGroups');
                Route::post('/accounts', 'Api\BusinessCentralController@syncAccounts');
                Route::post('/facturen/betaald', 'Api\BusinessCentralController@syncBetaaldeFacturen');
            });
            Route::group(['prefix' => 'get'], function () {
                Route::post('/logs', 'Api\BusinessCentralController@getLogs');
                Route::post('/que', 'Api\BusinessCentralController@getQue');
                Route::post('/cronjobs', 'Api\BusinessCentralController@getCronjobs');
            });
        });
        Route::group(['prefix' => 'tweeba'], function () {
          Route::post('/connect', 'Api\TweebaController@connect');
          Route::group(['prefix' => 'products'], function(){
              Route::post('/search', 'Api\TweebaController@productsSearch');
          });
          Route::group(['prefix' => 'suppliers'], function(){
              Route::post('/search', 'Api\TweebaController@suppliersSearch');
              Route::post('/import', 'Api\TweebaController@suppliersImport');
          });
          Route::group(['prefix' => 'tradeitems'], function(){
              Route::post('/forproduct', 'Api\TweebaController@tradeItemsForProduct');
              Route::post('/netprice', 'Api\TweebaController@netPriceByTradeItemId');
          });

        });
        Route::group(['prefix' => 'zenvoices'], function () {
          Route::post('/administration', 'Api\ZenvoicesController@getAdministration');
          Route::group(['prefix' => 'sync'], function(){
            Route::post('/leveranciers', 'Api\ZenvoicesController@syncAccounts');
            Route::post('/kostendragers', 'Api\ZenvoicesController@syncCostUnits');
            Route::post('/btwcodes', 'Api\ZenvoicesController@syncBtwCodes');
            Route::post('/producten', 'Api\ZenvoicesController@syncProducten');
          });
          Route::group(['prefix' => 'upload'], function(){
            Route::post('/commitment', 'Api\ZenvoicesController@uploadCommitment');
          });
          Route::group(['prefix' => 'cronjob'], function(){
            Route::post('/commitment/sync', 'Api\ZenvoicesController@cronjobCommitmentSync');
            Route::post('/sync/leveranciers', 'Api\ZenvoicesController@cronSyncAccounts');
          });
        });
        Route::group(['prefix' => 'eboekhouden'], function () {
            Route::post('/post/klant', 'Api\EboekhoudenController@postKlant');
            Route::post('/post/factuur', 'Api\EboekhoudenController@postFactuur');
            Route::post('/facturen/setrows', 'Api\EboekhoudenController@setFactuurRows');
            Route::get('/sync/grootboeken', 'Api\EboekhoudenController@syncGrootboeken');
            Route::post('/sync/facturen', 'Api\EboekhoudenController@syncFacturen');
            Route::get('/sync/klanten', 'Api\EboekhoudenController@syncKlanten');
        });
        Route::group(['prefix' => 'king'], function () {
          Route::post('/post/klant', 'Api\KingController@postKlant');
          Route::post('/post/leverancier', 'Api\KingController@postLeverancier');
          Route::post('/post/factuur', 'Api\KingController@postFactuur');
          Route::post('/post/inkoopfactuur', 'Api\KingController@postInkoopfactuur');
          Route::post('/sync/facturen', 'Api\KingController@syncFacturen');
          Route::get('/sync/grootboeken', 'Api\KingController@syncGrootboeken');
          Route::get('/sync/dagboeken', 'Api\KingController@syncDagboeken');
          Route::get('/sync/btw', 'Api\KingController@syncBtwcodes');
          Route::get('/sync/klanten', 'Api\KingController@syncKlanten');
          Route::post('/sync/leveranciers', 'Api\KingController@syncLeveranciers');
      });
        Route::group(['prefix' => 'support'], function () {
            Route::group(['prefix' => 'set'], function () {
                Route::post('/css', 'Api\SupportController@setCss');
                Route::post('/completed', 'Api\SupportController@setCompleted');
            });
            Route::post('/notify', 'Api\SupportController@notify');
        });
        Route::group(['prefix' => 'cronjob'], function () {
            Route::post('/acties', 'Api\ActielijstController@cronjob');
            Route::post('/wka', 'Api\LeveranciersController@cronjobWka');
        });
        Route::group(['prefix' => 'explorer'], function () {
            Route::post('/upload', 'Api\ExplorerController@upload')->middleware("HasPermission:Bestanden uploaden");
            Route::post('/get', 'Api\ExplorerController@get')->middleware("HasPermission:Alle bestanden bekijken,Eigen bestanden bekijken,Eigen medewerker map bekijken");
            Route::post('/update', 'Api\ExplorerController@update')->middleware("HasPermission:Bestanden uploaden");
            Route::post('/search', 'Api\ExplorerController@search')->middleware("HasPermission:Alle bestanden bekijken,Eigen bestanden bekijken,Eigen medewerker map bekijken");
            Route::post('/storage', 'Api\ExplorerController@storage');
            Route::post('/pathstorage', 'Api\ExplorerController@pathStorage');
            Route::post('/pathexists', 'Api\ExplorerController@pathExists');
            Route::group(['prefix' => 'store'], function () {
                Route::post('/folder', 'Api\ExplorerController@storeFolder')->middleware("HasPermission:Bestanden uploaden");
            });
        });
        Route::group(['prefix' => 'beheer', 'middleware' => 'beheer'], function () {
            Route::group(['prefix' => 'dashboard'], function () {
                Route::post('/errors', 'Api\DashboardController@getErrorLoggs');
                Route::post('/activity', 'Api\DashboardController@getClientActivity');
            });
            Route::group(['prefix' => 'dbsync'], function () {
                Route::group(['prefix' => 'sequences'], function () {
                    Route::post('/get', "Api\SyncController@get");
                });
                Route::group(['prefix' => 'columns'], function () {
                    Route::post('/start', "Api\SyncController@columnsStart");
                });
            });
            Route::group(['prefix' => 'cronjob'], function() {
                Route::post('/acties', 'Api\ActielijstController@cronjob');
            });


        });
        Route::group(['prefix' => 'setting'], function () {
            Route::post('/', 'Api\SettingsController@getSettingValue');
        });
        Route::group(['prefix' => 'wachtwoordkluis'], function() {
            Route::post('/get', 'Api\WachtwoordkluisController@get');
            Route::post('/decrypt', 'Api\WachtwoordkluisController@decrypt');
            Route::post('/aanvragen', 'Api\WachtwoordkluisController@aanvragen');
            Route::post('/judgerequest', 'Api\WachtwoordkluisController@judgeRequest');
            Route::post('/usersbypassword', 'Api\WachtwoordkluisController@usersByPassword');
            Route::post('/deletepassword', 'Api\WachtwoordkluisController@deletePassword');
            Route::post('/pushrequest', 'Api\WachtwoordkluisController@pushRequest');
            Route::post('/search', 'Api\WachtwoordkluisController@search');
            Route::post('/aanvragenOverzicht', 'Api\WachtwoordkluisController@aanvragenOverzicht');
            Route::post('/ontneemwachtwoordrechten', 'Api\WachtwoordkluisController@ontneemWachtwoordRechten');
        });
        Route::group(['prefix' => 'toolbox'], function() {
            Route::post('/get', 'Api\ToolboxController@get')->middleware("HasPermission:Toolbox bekijken");
            Route::post('/storeAnswers', 'Api\ToolboxController@storeAnswers');
            Route::post('/search', 'Api\ToolboxController@search');
            Route::post('/getsingletoolbox', 'Api\ToolboxController@getSingleToolbox');
            Route::post('/pushnietingevuld', 'Api\ToolboxController@pushNietIngevuld')->middleware("HasPermission:Toolbox beheren");
            Route::post('/mailnietingevuld', 'Api\ToolboxController@mailNietIngevuld')->middleware("HasPermission:Toolbox beheren");
            Route::post('/modaloverzicht', 'Api\ToolboxController@toolboxOverzicht')->middleware("HasPermission:Toolbox beheren");
            Route::post('/delete', 'Api\ToolboxController@delete')->middleware("HasPermission:Toolbox beheren");
        });
        Route::group(['prefix' => 'declaraties'], function() {
            Route::post('/get', 'Api\DeclaratiesController@get')->middleware("HasPermission:Declaratie indienen");
            Route::post('/create', 'Api\DeclaratiesController@createPost')->middleware("HasPermission:Declaratie indienen");
            Route::post('/search', 'Api\DeclaratiesController@search');
            Route::post('/upload', 'Api\DeclaratiesController@upload')->middleware("HasPermission:Declaratie indienen");
            Route::post('/delete', 'Api\DeclaratiesController@delete')->middleware("HasPermission:Declaratie beheren");
            Route::post('/status', 'Api\DeclaratiesController@changeStatus')->middleware("HasPermission:Declaratie indienen");
        });
        Route::group(['prefix' => 'whatsapp/'], function () {
            Route::group(['prefix' => 'app/', 'middleware' => 'HasPermission:Chat Applicatie'], function () {
                Route::post('/chat-messages', 'Api\WhatsAppController@chatMessages');
                Route::post('/chats', 'Api\WhatsAppController@chats');
            });
            Route::group(['prefix' => 'messages/'], function () {
                Route::post('/get', 'Api\WhatsAppController@messagesGet');
                Route::post('/search', 'Api\WhatsAppController@messagesSearch');
                Route::post('/send', 'Api\WhatsAppController@messagesSend');
                Route::post('/send-text', 'Api\WhatsAppController@messagesSendText');
                Route::post('/schedule', 'Api\WhatsAppController@messagesSchedule');
                Route::post('/cronjob', 'Api\WhatsAppController@messagesCronjob');
            });
            Route::group(['prefix' => 'sent-messages/'], function () {
                Route::post('/get', 'Api\WhatsAppController@sentMessagesGet');
                Route::post('/search', 'Api\WhatsAppController@sentMessagesSearch');
            });
            Route::group(['prefix' => 'scheduled-messages/'], function () {
                Route::post('/get', 'Api\WhatsAppController@scheduledMessagesGet');
                Route::post('/search', 'Api\WhatsAppController@scheduledMessagesSearch');
                Route::post('/status', 'Api\WhatsAppController@scheduledMessagesStatus');
            });
            Route::group(['prefix' => 'numbers/'], function () {
                Route::post('/store', 'Api\WhatsAppController@storeNumber');
            });
            Route::group(['prefix' => 'groups/'], function () {
                Route::post('/get', 'Api\WhatsAppController@groupsGet');
                Route::post('/store', 'Api\WhatsAppController@groupsStore');
                Route::post('/search', 'Api\WhatsAppController@groupsSearch');
                Route::post('/active', 'Api\WhatsAppController@groupsSetActive');
            });
            Route::group(['prefix' => 'contacts/'], function () {
                Route::post('/get', 'Api\WhatsAppController@contactsGet');
                Route::post('/search', 'Api\WhatsAppController@contactsSearch');
                Route::post('/active', 'Api\WhatsAppController@contactsSetActive');
                Route::post('/import', 'Api\WhatsAppController@import');
            });

            Route::post('/templates', 'Api\WhatsAppController@templates');
        });
        Route::group(['prefix' => 'imports/'], function () {
            Route::post('/get', 'Api\ImportController@get');
            Route::post('/trigger', 'Api\ImportController@trigger');
        });
    });
    //  Requests
    Route::group(['prefix' => 'request'], function () {
        Route::get('/docs', 'RequestsController@docs');

        Route::group(['prefix' => 'klanten'], function () {
            Route::post('/get', 'RequestsController@klantenGet')->middleware('requests:klanten_uitlezen');
            Route::post('/insert', 'RequestsController@klantenInsert')->middleware('requests:klanten_toevoegen');
        });
        Route::group(['prefix' => 'projecten'], function () {
            Route::post('/insert', 'RequestsController@projectenInsert')->middleware('requests:projecten_toevoegen');
        });
    });
    //  Loggedin
    Route::group(['middleware' => ['loggedin','inloggenPortal', 'passwordReset']], function () {
        Route::redirect('/', '/home');
        Route::get('/home', 'HomeController@index')->name('home');
        Route::get('/logout', 'HomeController@logout');
        Route::get('/whitelist_ip/{key}', 'DebugController@whitelistIP');
        Route::post('/api/data', 'Api\ApiController@data');
        
        Route::group(['prefix' => 'store'], function () {
            Route::get('/', 'StoreController@index')->middleware("HasPermission:Store bekijken");
            Route::post('/notify', 'Api\StoreController@requestNotification')->middleware("HasPermission:Store bekijken");
        });

        Route::group(['prefix' => 'beheer', 'middleware' => 'beheer'], function () {
            Route::group(['prefix' => 'dbsync'], function () {
                Route::get('/', "SyncController@index");
                Route::get('/fetch/{sub?}', "SyncController@fetch");
                Route::get('/fetchindexes/{sub?}', "SyncController@fetchIndexes");
                Route::get('/startindexes/{token}', "SyncController@startIndexes");
            });
            Route::group(['prefix' => 'store'], function () {
                Route::post('/update', 'Api\StoreController@update');
                Route::post('/image/add', 'Api\StoreController@addImage');
                Route::post('/image/remove', 'Api\StoreController@removeImage');
            });
            Route::get('/', 'DashboardController@index');
            Route::get('/dashboard', 'DashboardController@dashboard');
            Route::get('/module/create', 'DashboardController@createModule');
            Route::post('/module/create', 'DashboardController@storeModule');
            Route::get('/module/{id}', 'StoreController@edit');
            Route::post('/module/{id}', 'DashboardController@updateMoPer');
            Route::get('/module/update/{module}/{client}/{status}', 'DashboardController@updateModule');
            Route::get('/module/delete/{id}', 'DashboardController@deleteModule');
            Route::get('/client/create', 'DashboardController@createClient');
            Route::post('/client/create', 'DashboardController@storeClient');
            Route::get('/client/{id}', 'DashboardController@client');
            Route::post('/client/{id}', 'DashboardController@updateSettings');
            Route::get('/firsttimesetup', 'DashboardController@firstTimeSetup');

            Route::get('/livepreview', 'DashboardController@livePreview');
            Route::get('/template', 'DashboardController@template');
            Route::post('/template', 'DashboardController@storeTemplate');

            Route::post('/settings', 'DashboardController@settings');

            Route::get('/sql', 'DashboardController@sql');
            Route::post('/sql', 'DashboardController@sqlRun');

            Route::get('/queries/{client?}/{date?}', 'DashboardController@queryLog');

            Route::get('/encryption', 'DashboardController@encryption');
            Route::post('/encryption', 'DashboardController@encrypt');

            Route::get('/updates', 'DashboardController@createUpdates');
            Route::post('/updates', 'DashboardController@storeUpdates');
            Route::get('/updates/delete/{id}', 'DashboardController@deleteUpdates');

            Route::get('/facturatie', 'DashboardController@facturatie');
            Route::post('/facturatie/setprice', 'DashboardController@setPrice');

            Route::get('/facturatie/pdf/{id}', 'DashboardController@facturatiePdf');

            Route::get('phpinfo', function () { phpinfo(); })->name('phpinfo');
        });
        Route::group(['prefix' => 'iframe'], function () {
            Route::group(['prefix' => 'offertes'], function () {
                Route::get('/', 'OffertesController@index')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");
                Route::get('/edit/{offerteId}/{preview?}', 'OffertesController@edit')->middleware("HasPermission:Offertes aanmaken");
                Route::get('/select', 'OffertesController@select')->middleware("HasPermission:Offertes aanmaken");
                Route::get('/create/{id}/{klantId}', 'OffertesController@create')->middleware("HasPermission:Offertes aanmaken");

                Route::post('/edit/{offerteId}', 'OffertesController@update');
                Route::post('/create/{id}/{klantId}', 'OffertesController@store');
            });
            Route::group(['prefix' => 'leveranciers'], function () {
                Route::get('/show/{id}', 'LeveranciersController@windowShow');
            });
            Route::group(['prefix' => 'uren'], function () {
                Route::get('/projectnummers/{id}/preview', 'Uren\ProjectenController@preview')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::get('/projecturen/{id}/{startDate}/{endDate}', 'Uren\ProjectenController@projectUren')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
            });
            Route::group(['prefix' => 'checklists'], function () {
                Route::get('/{template_id}/{id}/preview', 'ChecklistsController@preview')->middleware("HasPermission:Alle checklist bekijken");
            });
            Route::group(['prefix' => 'editor'], function () {
                Route::get('/', 'IframeController@editor');
                Route::post('/', 'IframeController@editorStore');
            });
            Route::group(['prefix' => 'planning'], function () {
                Route::get('/preview/{id}', 'PlanningController@preview');
                Route::get('/overzicht/{type}/{year}/{period}', 'PlanningController@overzicht')->middleware("HasPermission:Planning bekijken,Volledige planning bekijken");
                Route::get('/projecten/{type}/{year?}/{week?}', 'PlanningController@projecten')->middleware("HasPermission:Planning bewerken");
            });
            Route::group(['prefix' => 'abonnementen'], function () {
                Route::get('/verenigingen', 'AbonnementenController@verenigingen')->middleware("HasPermission:Abonnementen beheren");
                Route::post('/verenigingen', 'AbonnementenController@verenigingenStore')->middleware("HasPermission:Abonnementen beheren");
            });
            Route::group(['prefix' => 'explorer'], function () {
                Route::get('/', 'ExplorerController@window')->middleware("HasPermission:Alle bestanden bekijken,Eigen bestanden bekijken,Eigen medewerker map bekijken");
            });
            Route::group(['prefix' => 'werkbonnen'], function () {
                Route::get('/show', 'WerkbonnenController@index');
            });
        });
        Route::group(['prefix' => 'support'], function () {
            Route::get('/', 'SupportController@index');
            Route::get('/create/{id?}', 'SupportController@create');
            Route::post('/create/{id?}', 'SupportController@store');

        });
        Route::group(['prefix' => 'settings'], function () {
            Route::get('/', 'SettingsController@settings')->middleware("HasPermission:Settings bewerken");
            Route::post('/', 'SettingsController@storeSettings');

            Route::post('/projecten/export/store', 'SettingsController@projectenExport');
            Route::post('/projecten/export/fields/store', 'SettingsController@projectenExportFields');

            Route::post('/requests/store', 'SettingsController@requestsStore');
            Route::post('/requests/delete', 'SettingsController@requestsDelete');
        });
        Route::group(['prefix' => 'news'], function () {
            Route::get('/', 'NewsController@index')->middleware("HasPermission:nieuws beheren");
            Route::get('/nieuws', 'NewsController@nieuws')->middleware("HasPermission:nieuws inzien");
            Route::get('/bekijken/{id}', 'NewsController@bekijken')->middleware("HasPermission:nieuws inzien");
            Route::get('/create', 'NewsController@new')->middleware("HasPermission:nieuws beheren");
            Route::get('/{slug}', 'NewsController@show')->middleware("HasPermission:nieuws beheren");
            Route::get('/edit/{id}', 'NewsController@edit')->middleware("HasPermission:nieuws beheren");
            Route::get('/push/{id}', 'NewsController@push')->middleware("HasPermission:nieuws beheren");
            Route::post('/create', 'NewsController@store');
            Route::post('/edit/{id}', 'NewsController@update');
            Route::post('/delete/{id}', 'NewsController@delete');
        });
        Route::group(['prefix' => 'memo'], function () {
            Route::get('/', 'MemoController@index')->middleware("HasPermission:memo inzien");
            Route::get('/bekijken/{id}', 'MemoController@bekijken')->middleware("HasPermission:memo inzien");
            Route::get('/create', 'MemoController@new')->middleware("HasPermission:memo beheren");
            Route::get('/{slug}', 'MemoController@show')->middleware("HasPermission:memo beheren");
            Route::get('/edit/{id}', 'MemoController@edit')->middleware("HasPermission:memo beheren");
            Route::post('/create', 'MemoController@store');
            Route::post('/edit/{id}', 'MemoController@store');
        });
        Route::group(['prefix' => 'roles'], function () {
            Route::get('/', 'RoleController@index')->middleware("HasPermission:rollen beheren");
            Route::get('/create', 'RoleController@create')->middleware("HasPermission:rollen beheren");
            Route::get('/edit/{id}', 'RoleController@edit')->middleware("HasPermission:rollen beheren");
            Route::post('/create', 'RoleController@store');
            Route::post('/edit/{id}', 'RoleController@update');
        });
        Route::group(['prefix' => 'users'], function () {
            Route::get('/', 'UserController@index')->middleware("HasPermission:medewerkers beheren");
            Route::get('/overview/{id}', 'UserController@overview')->middleware("HasPermission:medewerkers beheren");
            Route::get('/create/{id?}', 'UserController@create')->middleware("HasPermission:medewerkers beheren");
            Route::get('/default/{id}', 'UserController@default')->middleware("HasPermission:medewerkers beheren");
            Route::get('/export/excel', 'UserController@downloadExcel')->middleware("HasPermission:medewerkers beheren");
            Route::post('/create', 'UserController@store');
            Route::post('/create/{id}', 'UserController@update');
            Route::post('/default/{id}', 'UserController@storedefault');
            Route::get('/reset-password', 'UserController@resetPassword');

            Route::group(['prefix' => 'intake'], function () {

              Route::get('/', 'UserController@intakes')->middleware("HasPermission:medewerkers beheren");
              Route::get('/create', 'UserController@createIntake')->middleware("HasPermission:medewerkers beheren");
              Route::post('/create', 'UserController@storeIntake')->middleware("HasPermission:medewerkers beheren");

              Route::get('/edit/{src}', 'UserController@editIntake')->middleware("HasPermission:medewerkers beheren");
              Route::post('/edit/{src}', 'UserController@storeFilledIntake')->middleware("HasPermission:medewerkers beheren");

            });

        });
        Route::group(['prefix' => 'kentekens'], function () {
            Route::get('/', 'Uren\KentekenController@index')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/create', 'Uren\KentekenController@create')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/edit/{id}', 'Uren\KentekenController@edit')->middleware("HasPermission:urenregistratie beheren");
            Route::post('/create', 'Uren\KentekenController@store');
            Route::post('/edit/{id}', 'Uren\KentekenController@update');
            Route::post('/delete/{id}', 'Uren\KentekenController@destroy');
        });
        Route::group(['prefix' => 'naca'], function () {
            Route::get('/', 'Uren\NacaController@index')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/create', 'Uren\NacaController@create')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/edit/{id}', 'Uren\NacaController@edit')->middleware("HasPermission:urenregistratie beheren");
            Route::post('/create', 'Uren\NacaController@store');
            Route::post('/edit/{id}', 'Uren\NacaController@update');
            Route::post('/delete/{id}', 'Uren\NacaController@destroy'); // deactiveren, niet verwijderen!
        });
        Route::group(['prefix' => 'uren'], function () {

            Route::group(['prefix' => 'projectnummers'], function () {
                Route::get('/index', 'Uren\ProjectenController@index')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen,Projecten inzien");
                Route::get('/kastvakken', 'Uren\ProjectenController@kastvakken')->middleware("HasPermission:Kastvakken bekijken,Projecten toevoegen");
                Route::get('/upload', 'Uren\ProjectenController@excel')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::get('/download/{id}', 'Uren\ProjectenController@download')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::get('/new', 'Uren\ProjectenController@new')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/opdrachtbon/send', 'Uren\ProjectenController@sendOpdrachtbon')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/opdrachtbon/{id}', 'Uren\ProjectenController@opdrachtbon')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/status', 'Uren\ProjectenController@status')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/active', 'Uren\ProjectenController@active')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/upload', 'Uren\ProjectenController@upload');
                Route::post('/new', 'Uren\ProjectenController@store');
                Route::get('/{id}', 'Uren\ProjectenController@edit')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
                Route::post('/{id}', 'Uren\ProjectenController@update');
                Route::get('/{id}/preview', 'Uren\ProjectenController@preview')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen,Projecten inzien");
            });

            Route::group(['prefix' => 'verlof'], function () {
                Route::get('/', 'Uren\VerlofController@index')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/goedgekeurd', 'Uren\VerlofController@verlofGoedgekeurd')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/afgekeurd', 'Uren\VerlofController@verlofAfgekeurd')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/edit/{id}', 'Uren\VerlofController@edit')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/excel/{year}/{month}/{status}', 'Uren\VerlofController@export')->middleware("HasPermission:Verlof afhandelen");
                Route::post('/edit/{id}', 'Uren\VerlofController@store');
                
                Route::post('/aanvragen', 'Uren\VerlofController@aanvraag');
                Route::get('/aanvragen/{id}', 'Uren\VerlofController@aanvragen')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/aanvragen/inzien/{id}/{year}', 'Uren\VerlofController@aanvragenInzien')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/inzien/{year}', 'Uren\VerlofController@inzien')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/afhandelen', 'Uren\VerlofController@afhandelen')->middleware("HasPermission:Verlof afhandelen");
                Route::get('/invoeren', 'Uren\VerlofController@invoeren')->middleware("HasPermission:uren invoeren");
                Route::post('/invoeren', 'Uren\VerlofController@verlofAanvragen')->middleware("HasPermission:uren invoeren");
                Route::get('/redenen', 'Uren\VerlofController@verlofRedenen')->middleware("HasPermission:Verlofredenen wijzigen");
                Route::post('/updateRedenen', 'Uren\VerlofController@updateRedenen')->middleware("HasPermission:Verlofredenen wijzigen");
            });

            Route::group(['prefix' => 'invoeren'], function () {
                Route::get('/', 'Uren\InvoerenController@index')->middleware("HasPermission:uren invoeren,urenregistratie beheren");
                Route::get('/weekoverzicht/{year?}', 'Uren\InvoerenController@weekoverzicht')->middleware("HasPermission:uren invoeren,urenregistratie beheren");
                Route::get('/inzien/{date}', 'Uren\InvoerenController@inzien')->middleware("HasPermission:uren invoeren,urenregistratie beheren");
                Route::get('/{date}', 'Uren\InvoerenController@invoeren')->middleware("HasPermission:uren invoeren,urenregistratie beheren");
                Route::get('/correctie/{date}', 'Uren\InvoerenController@correctie');
                Route::post('/{date}', 'Uren\InvoerenController@store');
                Route::post('/correctie/{date}', 'Uren\InvoerenController@storecorrectie');
            });

            Route::get('/gefactureerd/{id}', 'Uren\ProjectenController@gefactureerd')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
            Route::post('/calculate', 'Uren\ProjectenController@calculate');
            Route::post('/calculatemachine', 'Uren\ProjectenController@calculateMachine');
            Route::get('/projecturen/{projectId?}', 'Uren\ProjectenController@projectUren')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");
            Route::get('/projectmachineuren/{projectId?}', 'Uren\ProjectenController@projectMachineUren')->middleware("HasPermission:urenregistratie beheren,Projecten toevoegen");

            Route::get('/feestdagen', 'Uren\FeestdagenController@index')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/feestdagen/create', 'Uren\FeestdagenController@create')->middleware("HasPermission:urenregistratie beheren");
            Route::post('/feestdagen/delete/{id}', 'Uren\FeestdagenController@destroy');
            Route::get('/overzicht/{week}/{year}/{id}', 'Uren\WeekController@weekoverzicht')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/correction', 'Uren\UrenController@index')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/correction/{date}/user/{id}', 'Uren\UrenController@edit')->middleware("HasPermission:urenregistratie beheren");

            Route::get('/weekoverzicht', 'Uren\WeekController@index')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/weekoverzicht/{id}/{year}', 'Uren\WeekController@overzicht')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/weekoverzicht/vierweken/{id}/{year}', 'Uren\WeekController@overzichtVierWeken')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/mutatielijst', 'Uren\MutatieController@index')->middleware("HasPermission:urenregistratie beheren");


            Route::post('/mutatie/generate', 'Uren\MutatieController@generate')->middleware("HasPermission:urenregistratie beheren");

            Route::get('/mutatie/{bv}/{period}/{year}', 'Uren\MutatieController@show')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/mutatie/{bv}/{period}/{year}/export', 'Uren\MutatieController@download')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/controlelijst', 'Uren\WeekController@wekenoverzicht')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/controlelijst/{jaar}/{weeknummer}', 'Uren\WeekController@controlelijst')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/controlelijst/{jaar}/{weeknummer}/export/{jobtype?}', 'Uren\WeekController@exportcontrolelijst')->middleware("HasPermission:urenregistratie beheren");
            Route::get('/controlelijst/{jaar}/{weeknummer}/exportxml/{jobtype?}', 'Uren\WeekController@xmlexportcontrolelijst')->middleware("HasPermission:urenregistratie beheren");


            Route::post('/correction/{date}/user/{id}', 'Uren\UrenController@store');
            Route::post('/mutatie/{month}/{year}/{id}', 'Uren\UrenController@mutatie');
            Route::post('/feestdagen/create', 'Uren\FeestdagenController@store');
            Route::post('/message/delete/{id}', 'Uren\UrenController@messagereaded');

            Route::get('/uursoorten', 'Uren\UrenController@uursoortIndex')->middleware("HasPermission:Uursoorten beheren");
            Route::get('/uursoorten/create/{id?}', 'Uren\UrenController@uursoortCreate')->middleware("HasPermission:Uursoorten beheren");
            Route::post('/uursoorten/create/{id?}', 'Uren\UrenController@uursoortStore')->middleware("HasPermission:Uursoorten beheren");
        });
        Route::group(['prefix' => 'offertes'], function () {
            Route::get('/', 'OffertesController@index')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");
            Route::get('/klant/{id}', 'OffertesController@indexByKlant')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");
            Route::get('/inactive', 'OffertesController@afgeronde')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");
            Route::get('/verwijderde', 'OffertesController@verwijderde')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");
            Route::get('/all', 'OffertesController@indexAll')->middleware("HasPermission:Alle offertes bekijken,Eigen offertes bekijken");

            Route::get('/templates', 'OffertesController@templates')->middleware("HasPermission:Offerte templates beheren");
            Route::get('/templates/edit/{id}', 'OffertesController@templatesEdit')->middleware("HasPermission:Offerte templates beheren");
            Route::post('/templates/edit/{id}', 'OffertesController@templatesUpdate')->middleware("HasPermission:Offerte templates beheren");


            Route::get('/reset/{token}', 'OffertesController@reset')->middleware("HasPermission:Offertes aanmaken");
            Route::get('/select', 'OffertesController@select')->middleware("HasPermission:Offertes aanmaken");
            Route::get('/create/{id}/{klantId}', 'OffertesController@create')->middleware("HasPermission:Offertes aanmaken");
            Route::get('/delete/{id}', 'OffertesController@delete')->middleware("HasPermission:Offertes aanmaken");
            Route::get('/enable/{id}', 'OffertesController@enable')->middleware("HasPermission:Offertes aanmaken");
            Route::get('/edit/{offerteId}/{preview?}', 'OffertesController@edit')->middleware("HasPermission:Offertes aanmaken");
            Route::post('/edit/{offerteId}', 'OffertesController@update');
            Route::post('/create/{id}/{klantId}', 'OffertesController@store');
            Route::get('/items', 'OffertesController@items')->middleware("HasPermission:Offerte templates beheren");
            Route::post('/items', 'OffertesController@itemsStore');

            Route::get('/datasets', 'OffertesController@datasets')->middleware("HasPermission:Offerte templates beheren");
            Route::post('/datasets', 'OffertesController@datasetsStore');
            Route::get('/datasets/export/excel/{id}', 'OffertesController@datasetsExportExcel');
            Route::get('/datasets/export/xml/{id}', 'OffertesController@datasetsExportXml');
            Route::post('/datasets/visibility', 'OffertesController@datasetsVisibility');
            Route::post('/datasets/delete', 'OffertesController@datasetsDelete');
            Route::post('/datasets/items/store', 'OffertesController@datasetsItemsStore');
            Route::post('/datasets/item/store', 'OffertesController@datasetsItemStore');
            Route::post('/datasets/items/files', 'OffertesController@datasetsItemsFiles');
            Route::post('/datasets/items/files/delete', 'OffertesController@datasetsItemsFilesDelete');
            Route::post('/subdatasets/items/files', 'OffertesController@subdatasetsItemsFiles');
            Route::post('/subdatasets/items/files/delete', 'OffertesController@subdatasetsItemsFilesDelete');
            Route::post('/subdatasets/store', 'OffertesController@subdatasetsStore');
            Route::post('/subdatasets/items/connect', 'OffertesController@datasetItemsConnect');

            Route::group(['prefix' => 'details'], function () {
                Route::post('/template/delete', 'OffertesController@deleteDetailTemplate');
                Route::get('/export/excel/{detail_template_id}/{token}', 'OffertesController@detailsExportExcel');
            });

        });
        Route::group(['prefix' => 'klanten'], function () {
            Route::get('/', 'KlantenController@index')->middleware("HasPermission:Alle klanten bekijken,Eigen klanten bekijken");
            Route::get('/disable/{id}', 'KlantenController@disable')->middleware("HasPermission:Klanten beheren");
            Route::get('/enable/{id}', 'KlantenController@enable')->middleware("HasPermission:Klanten beheren");
            Route::get('/create', 'KlantenController@create')->middleware("HasPermission:Klanten beheren");
            Route::post('/create', 'KlantenController@store');
            Route::get('/edit/{id}', 'KlantenController@edit')->middleware("HasPermission:Klanten beheren");
            Route::post('/edit/{id}', 'KlantenController@update');
            Route::get('/upload', 'KlantenController@excel')->middleware("HasPermission:Klanten beheren");
            Route::post('/users/relation', 'KlantenController@setUserRelation');
            Route::get('/export/excel', 'KlantenController@downloadExcel')->middleware("HasPermission:Klanten beheren");
            Route::get('/export/xml', 'KlantenController@downloadXml')->middleware("HasPermission:Klanten beheren");
            Route::post('/upload', 'KlantenController@upload');;
            Route::post('/file/store', 'KlantenController@fileStore');;

            Route::get('/emails/opstellen', 'KlantenController@opstellen')->middleware("HasPermission:Emails beheren");
            Route::get('/emails/opstellen/{id}', 'KlantenController@opstellenByTemplate')->middleware("HasPermission:Emails beheren");
            Route::post('/emails/opstellen', 'KlantenController@storeEmail');
            Route::get('/emails/verzonden', 'KlantenController@verzonden')->middleware("HasPermission:Emails beheren");
            Route::get('/emails/templates', 'KlantenController@templates')->middleware("HasPermission:Emails beheren");
            Route::get('/emails/templates/delete/{id}', 'KlantenController@templatesDelete')->middleware("HasPermission:Emails beheren");
            Route::get('/emails/templates/set/{id}', 'KlantenController@templatesSet')->middleware("HasPermission:Emails beheren");

        });
        Route::group(['prefix' => 'leveranciers'], function () {
            Route::get('/', 'LeveranciersController@index')->middleware("HasPermission:Leveranciers beheren");
            Route::get('/create', 'LeveranciersController@create')->middleware("HasPermission:Leveranciers beheren");
            Route::post('/create', 'LeveranciersController@store')->middleware("HasPermission:Leveranciers beheren");
            Route::get('/edit/{id}', 'LeveranciersController@edit')->middleware("HasPermission:Leveranciers beheren");
            Route::post('/edit/{id}', 'LeveranciersController@update')->middleware("HasPermission:Leveranciers beheren");
            Route::get('/tarieven', 'LeveranciersController@tarieven')->middleware("HasPermission:Tarieven beheren");
        });
        Route::group(['prefix' => 'facturatie'], function () {
            Route::get('/create', 'FacturatieController@create')->middleware("HasPermission:Facturatie dashboard");
            Route::post('/create', 'FacturatieController@storeFactuur');
            Route::get('/facturen', 'FacturatieController@facturen')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/export/xml', 'FacturatieController@downloadXml')->middleware("HasPermission:Facturatie dashboard");
            Route::post('/facturen/exact/xml', 'FacturatieController@exactXmlExport')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/exact/xml/download/{van}/{tot}', 'FacturatieController@downloadExactXmlExport')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/klanten/exact/xml/download/{van}/{tot}', 'FacturatieController@downloadKlantenExactXmlExport')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/datasets/exact/xml/download/{van}/{tot}/{datasetid}', 'FacturatieController@datasetsExportXml')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/confirm/{id}', 'FacturatieController@confirmFactuur')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/delete/{id}', 'FacturatieController@deleteFactuur')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/enable/{id}', 'FacturatieController@enableFactuur')->middleware("HasPermission:Facturatie dashboard");
            Route::get('/facturen/edit/{id}', 'FacturatieController@editFactuur')->middleware("HasPermission:Facturatie dashboard");
            Route::post('/facturen/edit/{id}', 'FacturatieController@updateFactuur');
            Route::get('/facturen/pdf/{id}', 'FacturatieController@factuurPdf')->middleware("HasPermission:Facturatie dashboard");

            Route::get('/proforma', 'FacturatieController@indexProforma')->middleware("HasPermission:Proforma facturen beheren");
            Route::post('/proforma/new', 'FacturatieController@storeProforma')->middleware("HasPermission:Proforma facturen beheren");
        });
        Route::group(['prefix' => 'planning'], function () {
            Route::get('/project', 'PlanningController@project')->middleware("HasPermission:Planning bekijken,Volledige planning bekijken");
            Route::get('/project/export/{id}', 'PlanningController@projectExport')->middleware("HasPermission:Planning bekijken,Volledige planning bekijken");

            //Proxy for the old format, can be removed after app update.
            Route::get('/{year}/{month}', function ($subdomain, $year, $month) { return redirect("/planning/overzicht/maand/$year/$month"); });

            Route::get('/inplannen/{type}/{year}/{period}', 'PlanningController@inplannen')->middleware("HasPermission:Planning bewerken");
            Route::get('/overzicht/{type}/{year}/{period}', 'PlanningController@overzicht')->middleware("HasPermission:Planning bekijken,Volledige planning bekijken");

            Route::get('/resource/{id}/{type}/{year}/{period}', 'PlanningController@resources')->middleware("HasPermission:Planning bekijken,Volledige planning bekijken");


            Route::get('/inplannen', 'PlanningController@selectMonth')->middleware("HasPermission:Planning bewerken");
            Route::get('/dagplanning/index/{date}', 'PlanningController@dagOverzicht')->middleware("HasPermission:Planning bewerken");
            Route::post('/delete/{id}', 'PlanningController@delete')->middleware("HasPermission:Planning bewerken");

            Route::get('/werkuren/inplannen/{type}/{date}', 'PlanningController@werkuren')->middleware("HasPermission:Planning bewerken");
            Route::post('/werkuren/inplannen/{type}/{date}', 'PlanningController@storeWerkuren')->middleware("HasPermission:Planning bewerken");
            Route::post('/werkuren/delete', 'PlanningController@deleteWerkuren')->middleware("HasPermission:Planning bewerken");

            Route::get('/google/calendar/{planning_id}', 'PlanningController@createCalendar')->middleware("HasPermission:Planning bewerken");
            Route::post('/google/calendar/{planning_id}', 'PlanningController@storeCalendar');

            Route::get('/legenda/delete/{id}/{type}/{year}/{target}', 'PlanningController@deleteLegenda')->middleware("HasPermission:Planning bewerken");
            Route::post('/legenda', 'PlanningController@storeLegenda');
            Route::post('/legenda/update', 'PlanningController@updateLegenda');

            Route::post('/rowInplannen', 'PlanningController@storeRowPlanning');
            Route::post('/inplannen', 'PlanningController@storePlanning');
            Route::post('/aanvraagInplannen', 'PlanningController@storeAanvraagPlanning');
            Route::post('/projectInplannen', 'PlanningController@storeProjectPlanning');
            Route::post('/update', 'PlanningController@update');

            Route::post('/calcroute', 'PlanningController@calcRoute');
            Route::post('/reserveerplein', 'PlanningController@reserveerplein');

            Route::get('/projecten/{type}/{year}/{week}', 'PlanningController@projecten')->middleware("HasPermission:Planning bewerken");
            Route::post('/projecten/{type}/{year}/{week}', 'PlanningController@projectenStore')->middleware("HasPermission:Planning bewerken");
            Route::post('/projecten/{type}/{year}/{week}/edit', 'PlanningController@projectenUpdate')->middleware("HasPermission:Planning bewerken");
            Route::get('/projecten/{type}/{year}/{week}/delete/{id}', 'PlanningController@projectenDelete')->middleware("HasPermission:Planning bewerken");

            Route::get('/dagplanning/pdf/{datum}', 'PlanningController@dagplanningPdf');
            Route::get('/dagplanning/periode/pdf/{periode}', 'PlanningController@dagplanningPeriodePdf');
        });
        Route::group(['prefix' => 'activity'], function () {
            Route::get('/weergaven/{pointer}/{type}', 'LogController@weergaven');
            Route::get('/activiteiten', 'LogController@activiteiten');
            Route::get('/log', 'LogController@clientLog')->middleware("HasPermission:Activiteitenlog bekijken");
            Route::get('/error', 'LogController@clientErrorLog')->middleware("beheer");
        });
        Route::group(['prefix' => 'statistieken'], function () {
            Route::get('/urenregistratie', 'StatistiekenController@urenregistratie')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/offertes', 'StatistiekenController@offertes')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/facturatie', 'StatistiekenController@facturatie')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/proformas', 'StatistiekenController@proformas')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/werkbonnen', 'StatistiekenController@werkbonnen')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/inkoopbonnen', 'StatistiekenController@inkoopbonnen')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/inkoopfacturen', 'StatistiekenController@inkoopfacturen')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/projecten', 'StatistiekenController@projecten')->middleware("HasPermission:Statistieken bekijken");
            Route::get('/checklists', 'StatistiekenController@checklists')->middleware("HasPermission:Statistieken bekijken");
        });
        Route::group(['prefix' => 'werkbonnen'], function () {
            Route::get('/', 'WerkbonnenController@index')->middleware("HasPermission:Werkbonnen beheren,Eigen werkbonnen beheren");
            Route::get('/new/{id}', 'WerkbonnenController@new')->middleware("HasPermission:Werkbonnen aanmaken");
            Route::post('/new/{id}', 'WerkbonnenController@store');
            Route::get('/pdf/{id}', 'WerkbonnenController@viewPdf')->middleware("HasPermission:Werkbonnen beheren,Eigen werkbonnen beheren");
            Route::get('/update/{id}', 'WerkbonnenController@edit')->middleware("HasPermission:Werkbonnen beheren,Eigen werkbonnen beheren");
            Route::post('/update/{id}', 'WerkbonnenController@update');
            Route::get('/disable/{id}', 'WerkbonnenController@disable')->middleware("HasPermission:Werkbonnen beheren,Eigen werkbonnen beheren");
            Route::get('/enable/{id}', 'WerkbonnenController@enable')->middleware("HasPermission:Werkbonnen beheren,Eigen werkbonnen beheren");
        });
        Route::group(['prefix' => 'legplannen'], function () {
            Route::get('/', 'LegplanController@index')->middleware("HasPermission:Legplannen beheren");
            Route::get('/items', 'LegplanController@itemsIndex')->middleware("HasPermission:Legplannen beheren");
            Route::get('/new', 'LegplanController@new')->middleware("HasPermission:Legplannen beheren");
            Route::post('/upload', 'LegplanController@upload')->middleware("HasPermission:Legplannen beheren");
            Route::post('/new', 'LegplanController@store');
            Route::get('/pdf/{id}', 'LegplanController@viewPdf')->middleware("HasPermission:Legplannen beheren");
            Route::post('/merge', 'LegplanController@merge');
            Route::post('/ajax/store', 'LegplanController@ajaxStore');
            Route::post('/ajax/items', 'LegplanController@ajaxItems');
            Route::get('/items/delete/{id}', 'LegplanController@deleteItem')->middleware("HasPermission:Legplannen beheren");
        });
        Route::group(['prefix' => 'pictures'], function () {
            Route::get('/', 'PicturesController@galerij')->middleware("HasPermission:Foto's beheren,Alle Foto's bekijken");
            Route::get('/user/{user}', 'PicturesController@userGalerij')->middleware("HasPermission:Foto's beheren,Alle Foto's bekijken");
            Route::get('/deleted', 'PicturesController@deleted')->middleware("HasPermission:Foto's beheren,Alle Foto's bekijken");
            Route::get('/delete/{id}', 'PicturesController@delete')->middleware("HasPermission:Foto's beheren");
            Route::post('/', 'PicturesController@store');
        });
        Route::group(['prefix' => 'abonnementen'], function () {
            Route::get('/', 'AbonnementenController@index')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/expired', 'AbonnementenController@expired')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/active', 'AbonnementenController@active')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/deleted', 'AbonnementenController@deleted')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/create', 'AbonnementenController@create')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/create', 'AbonnementenController@store');
            Route::post('/delete', 'AbonnementenController@delete');
            Route::post('/enable', 'AbonnementenController@enable');
            Route::get('/betaald/{id}/{date}', 'AbonnementenController@betaald')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/items', 'AbonnementenController@items')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/items', 'AbonnementenController@storeItem');
            Route::post('/items/uitgaves', 'AbonnementenController@storeUitgaves');
            Route::get('/items/delete/{id}', 'AbonnementenController@deleteItem')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/formulieren', 'AbonnementenController@formulieren')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/formulieren/items', 'AbonnementenController@setItem');
            Route::post('/formulieren/prefix', 'AbonnementenController@prefixStore');
            Route::post('/cdn/new', 'AbonnementenController@newCdn');
            Route::get('/cdn/reload/{token}', 'AbonnementenController@reloadCdn')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/cdn/delete/{token}', 'AbonnementenController@deleteCdn')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/factuur/new', 'AbonnementenController@newFactuur');
            Route::post('/factuur/confirm', 'AbonnementenController@confirmFactuur');
            Route::post('/factuur/send', 'AbonnementenController@sendFactuur');
            Route::post('/wp/levels', 'AbonnementenController@wpLevels');
            Route::get('/betaald/{abonnement}', 'AbonnementenController@betaald')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/access', 'AbonnementenController@toegang');
            Route::get('/korting', 'AbonnementenController@kortingIndex')->middleware("HasPermission:Abonnementen beheren");
            Route::post('/korting', 'AbonnementenController@storeKorting');
            Route::get('/korting/select/{korting}/{abonnement}/{item}', 'AbonnementenController@selectKorting')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/korting/delete/{id}', 'AbonnementenController@deleteKorting')->middleware("HasPermission:Abonnementen beheren");

            Route::post('/excel/upload', 'AbonnementenController@upload')->middleware("HasPermission:Abonnementen beheren");
            Route::get('/excel/download/uitgave/{itemId}/{date}', 'AbonnementenController@downloadUitgave')->middleWare('HasPermission:Abonnementen beheren');

        });
        Route::group(['prefix' => 'aanvragen'], function () {
            Route::get('/', 'AanvragenController@index')->middleware("HasPermission:Alle aanvragen bekijken,Eigen aanvragen bekijken");
            Route::get('/klant/{id}', 'AanvragenController@indexByKlant')->middleware("HasPermission:Alle aanvragen bekijken,Eigen aanvragen bekijken");
            Route::get('/connect/{aanvraag}/{klant}', 'AanvragenController@connect')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/afgeronde', 'AanvragenController@afgerondeIndex')->middleware("HasPermission:Alle aanvragen bekijken,Eigen aanvragen bekijken");
            Route::get('/cdn', 'AanvragenController@cdn')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/cdn/reload/{token}', 'AanvragenController@reloadCdn')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/cdn/delete/{token}', 'AanvragenController@deleteCdn')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/select', 'AanvragenController@select')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/show/{id}', 'AanvragenController@show')->middleware("HasPermission:Alle aanvragen bekijken,Eigen aanvragen bekijken");
            Route::get('/status/{status}/{id}', 'AanvragenController@updateStatus')->middleware("HasPermission:Aanvragen beheren");
            Route::get('/new/{token}', 'AanvragenController@new')->middleware("HasPermission:Aanvragen beheren");
            Route::post('/users/relation', 'AanvragenController@setUserRelation');
            Route::post('/cdn/klant/info', 'AanvragenController@cdnKlantInfo');
            Route::post('/cdn/new', 'AanvragenController@newCdn');
            Route::post('/new/{token}', 'AanvragenController@store');
            Route::post('/cdn/update', 'AanvragenController@updateInputs');
            Route::get('/status', 'AanvragenController@statusIndex')->middleware("HasPermission:Aanvragen beheren");
            Route::post('/status/store', 'AanvragenController@statusStore');
            Route::post('/status/delete', 'AanvragenController@statusDelete');
            Route::post('/status/primair', 'AanvragenController@statusPrimair');
            Route::post('/status/select', 'AanvragenController@statusSelect');
        });
        Route::group(['prefix' => 'rapporten'], function () {
            Route::get('/', 'VerslagenController@index')->middleware("HasPermission:Rapporten inzien,Eigen rapporten inzien");
            Route::get('/klant/{klant}', 'VerslagenController@indexByKlant')->middleware("HasPermission:Rapporten inzien,Klanten beheren");
            Route::get('/edit/{id}', 'VerslagenController@edit')->middleware("HasPermission:Rapporten aanmaken");
            Route::post('/edit/{id}', 'VerslagenController@update');
            Route::get('/new', 'VerslagenController@new')->middleware("HasPermission:Rapporten aanmaken");
            Route::post('/new', 'VerslagenController@store');
            Route::get('/delete/{id}', 'VerslagenController@delete')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/show/{id}', 'VerslagenController@pdf');
            Route::get('/items', 'VerslagenController@items')->middleware("HasPermission:Rapporten aanmaken");
            Route::post('/items', 'VerslagenController@storeItems');
            Route::get('/items/hide/{id}', 'VerslagenController@hideItem')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/items/show/{id}', 'VerslagenController@showItem')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/items/delete/{id}', 'VerslagenController@deleteItem')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/templates', 'VerslagenController@templates')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/template/save/{id}', 'VerslagenController@saveTemplate')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/template/delete/{id}', 'VerslagenController@deleteTemplate')->middleware("HasPermission:Rapporten aanmaken");
            Route::get('/template/new/{id}', 'VerslagenController@newByTemplate')->middleware("HasPermission:Rapporten aanmaken");
            Route::post('/send', 'VerslagenController@send')->middleware("HasPermission:Rapporten aanmaken");

        });
        Route::group(['prefix' => 'aanmeldingen'], function () {
            Route::get('/', 'AanmeldingenController@index')->middleware("HasPermission:Aanmeldingen beheren");
            Route::get('/deleted', 'AanmeldingenController@deleted')->middleware("HasPermission:Aanmeldingen beheren");
            Route::get('/edit/{id}', 'AanmeldingenController@edit')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/edit/{id}', 'AanmeldingenController@update');
            Route::post('/herinnering', 'AanmeldingenController@herinnering');
            Route::post('/crediteren', 'AanmeldingenController@crediteren');
            Route::post('/delete', 'AanmeldingenController@delete');
            Route::post('/confirm', 'AanmeldingenController@confirm');
            Route::get('/korting', 'AanmeldingenController@kortingen')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/korting', 'AanmeldingenController@storeKorting');
            Route::get('/korting/select', 'AanmeldingenController@selectKorting')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/korting/select/delete', 'AanmeldingenController@selectKortingDelete');
            Route::post('/korting/select/save', 'AanmeldingenController@selectKortingSave');
            Route::get('/korting/delete/{id}', 'AanmeldingenController@deleteKorting')->middleware("HasPermission:Aanmeldingen beheren");
            Route::get('/items', 'AanmeldingenController@items')->middleware("HasPermission:Aanmeldingen beheren");
            Route::get('/items/archive', 'AanmeldingenController@itemsArchive')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/items', 'AanmeldingenController@storeItems');
            Route::post('/items/restore', 'AanmeldingenController@restoreItems');
            Route::post('/items/delete', 'AanmeldingenController@deleteItems');
            Route::post('/items/subitems/delete', 'AanmeldingenController@deleteSubitems');
            Route::post('/items/subitems/store', 'AanmeldingenController@storeSubitems');
            Route::post('/items/group/store', 'AanmeldingenController@storeSubitemsGroup');
            Route::post('/items/group/delete', 'AanmeldingenController@deleteSubitemsGroup');
            Route::get('/formulieren', 'AanmeldingenController@formulieren')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/formulieren/items/', 'AanmeldingenController@setItem');
            Route::post('/formulieren/korting/set', 'AanmeldingenController@formulierSetKorting');
            Route::post('/formulieren/betaalmethode', 'AanmeldingenController@betaalmethode');
            Route::post('/formulieren/inputs/store', 'AanmeldingenController@inputStore');
            Route::post('/formulieren/inputs/delete', 'AanmeldingenController@inputDelete');
            Route::post('/formulieren/css', 'AanmeldingenController@cssStore');
            Route::post('/formulieren/prefix', 'AanmeldingenController@prefixStore');
            Route::post('/cdn/new', 'AanmeldingenController@newCdn');
            Route::get('/cdn/reload/{token}', 'AanmeldingenController@reloadCdn')->middleware("HasPermission:Aanmeldingen beheren");
            Route::get('/cdn/delete/{token}', 'AanmeldingenController@deleteCdn')->middleware("HasPermission:Aanmeldingen beheren");
            Route::post('/factuur/store', 'AanmeldingenController@storeFactuur');
            Route::post('/factuur/confirm', 'AanmeldingenController@confirmFactuur');
        });
        Route::group(['prefix' => 'externen'], function () {
            Route::get('/', 'ExternenController@index')->middleware("HasPermission:Externen beheren");
            Route::get('/deleted', 'ExternenController@deleted')->middleware("HasPermission:Externen beheren");
            Route::get('/create', 'ExternenController@create')->middleware("HasPermission:Externen beheren");
            Route::post('/create', 'ExternenController@store');
            Route::get('/edit/{id}', 'ExternenController@edit')->middleware("HasPermission:Externen beheren");
            Route::post('/edit/{id}', 'ExternenController@update');
            Route::post('/delete', 'ExternenController@delete');
            Route::post('/activate', 'ExternenController@activate');
            Route::post('/select', 'ExternenController@select');
            Route::post('/select/klant', 'ExternenController@selectKlant');
            Route::get('/rapporten', 'ExternenController@rapporten');
            Route::get('/offertes', 'ExternenController@offertes');
            Route::get('/facturen', 'ExternenController@facturen');
        });
        Route::group(['prefix' => 'checklists'], function () {
            Route::get('/', 'ChecklistsController@index')->middleware("HasPermission:Alle checklists bekijken,Eigen checklists bekijken");
            Route::get('/klant/{id}', 'ChecklistsController@indexByKlant')->middleware("HasPermission:Alle checklists bekijken");
            Route::get('/show/{id}', 'ChecklistsController@show')->middleware("HasPermission:Alle checklists bekijken,Eigen checklists bekijken");
            Route::post('/klant/confirm', 'ChecklistsController@confirmKlant');
            Route::get('/create/{template}/{id?}', 'ChecklistsController@create')->middleware("HasPermission:Checklists aanmaken");
            Route::post('/create/{template}/{id?}', 'ChecklistsController@store');
        });
        Route::group(['prefix' => 'acties'], function () {
            Route::get('/', 'ActielijstController@index')->middleware("HasPermission:Acties bekijken");
            Route::get('/templates', 'ActielijstController@templates')->middleware("HasPermission:Acties toevoegen");
            Route::get('/new', 'ActielijstController@create')->middleware("HasPermission:Acties toevoegen");
            Route::post('/new', 'ActielijstController@store')->middleware("HasPermission:Acties toevoegen");
            Route::post('/delete', 'ActielijstController@delete')->middleware("HasPermission:Acties toevoegen");
            Route::post('/aanvragen', 'ActielijstController@aanvragen')->middleware("HasPermission:Acties toevoegen");
            Route::post('/reminder/update', 'ActielijstController@updateReminder');
        });
        Route::group(['prefix' => 'explorer'], function () {
            Route::get('/', 'ExplorerController@window')->middleware("HasPermission:Alle bestanden bekijken,Eigen bestanden bekijken,Eigen medewerker map bekijken");
        });
        Route::group(['prefix' => 'snelstart'], function () {
            Route::post('/post/klant', 'SnelstartController@postKlant');
            Route::post('/post/factuur', 'SnelstartController@postFactuur');
            Route::post('/sync/grootboeken', 'SnelstartController@syncGrootboeken');
            Route::post('/sync/facturen', 'SnelstartController@syncFacturen');
            Route::post('/store/factuurregels-grootboeken', 'SnelstartController@storeFactuurregelsGrootboeken');
            Route::get('/sync/klanten', 'SnelstartController@syncKlanten');
            Route::get('/show/klanten/{bv}', 'SnelstartController@showKlanten');
            Route::get('/get/klanten/{bv}', 'SnelstartController@getKlanten');
        });
        Route::group(['prefix' => 'vestigingen'], function () {
            Route::get('/', 'VestigingenController@index')->middleware("HasPermission:Vestigingen beheren");
            Route::get('/create', 'VestigingenController@create')->middleware("HasPermission:Vestigingen beheren");
            Route::get('/edit/{id}', 'VestigingenController@edit')->middleware("HasPermission:Vestigingen beheren");
            Route::post('/edit/{id}', 'VestigingenController@update')->middleware("HasPermission:Vestigingen beheren");
            Route::post('/create', 'VestigingenController@store')->middleware("HasPermission:Vestigingen beheren");
            Route::post('/delete', 'VestigingenController@delete')->middleware("HasPermission:Vestigingen beheren");
        });
        Route::group(['prefix' => 'machines'], function () {
            Route::get('/', 'MachinesController@index')->middleware("HasPermission:Machines beheren");
            Route::get('/create/{id?}', 'MachinesController@create')->middleware("HasPermission:Machines beheren");
            Route::post('/create/{id?}', 'MachinesController@store')->middleware("HasPermission:Machines beheren");
            Route::get('/groepen', 'MachinesController@groups')->middleware("HasPermission:Machines beheren");
        });
        Route::group(['prefix' => 'tarieven'], function () {
            Route::get('/', 'TarievenController@index')->middleware("HasPermission:Tarieven beheren");
            Route::get('/create/{id?}', 'TarievenController@create')->middleware("HasPermission:Tarieven beheren");
            Route::post('/create/{id?}', 'TarievenController@store')->middleware("HasPermission:Tarieven beheren");
            Route::post('/delete', 'TarievenController@delete')->middleware("HasPermission:Tarieven beheren");
        });
        Route::group(['prefix' => 'eenheden'], function () {
          Route::get('/', 'EenhedenController@index')->middleware("HasPermission:Eenheden beheren");
          Route::get('/create/{id?}', 'EenhedenController@create')->middleware("HasPermission:Eenheden beheren");
          Route::post('/create/{id?}', 'EenhedenController@store')->middleware("HasPermission:Eenheden beheren");
          Route::post('/delete', 'EenhedenController@delete')->middleware("HasPermission:Eenheden beheren");
        });
        Route::group(['prefix' => 'inkoopbonnen'], function () {
            Route::get('/', 'InkoopbonnenController@index')->middleware("HasPermission:Inkoopbonnen beheren,Eigen inkoopbonnen beheren");
            Route::get('/create', 'InkoopbonnenController@create')->middleware("HasPermission:Inkoopbonnen aanmaken");
            Route::post('/create', 'InkoopbonnenController@store')->middleware("HasPermission:Inkoopbonnen aanmaken");
            Route::get('/edit/{id}', 'InkoopbonnenController@edit')->middleware("HasPermission:Inkoopbonnen aanmaken");
            Route::post('/edit/{id}', 'InkoopbonnenController@update')->middleware("HasPermission:Inkoopbonnen aanmaken");
            Route::get('/token/{token}', 'InkoopbonnenController@pdf');
        });
        Route::group(['prefix' => 'inkoopfacturen'], function () {
            Route::get('/', 'InkoopfacturenController@index')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
            Route::get('/create', 'InkoopfacturenController@create')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
            Route::post('/create', 'InkoopfacturenController@store')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
            Route::get('/edit/{id}', 'InkoopfacturenController@edit')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
            Route::post('/edit/{id}', 'InkoopfacturenController@update')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
            Route::get('/files/{id}', 'InkoopfacturenController@files')->middleware("HasPermission:Inkoopfacturen beheren,Eigen inkoopfacturen beheren");
        });
        Route::group(['prefix' => 'accorderen'], function () {
            Route::get('', 'AccorderenController@index')->middleware("HasPermission:Accorderen overzicht");
            Route::get('/offertes', 'AccorderenController@offertes')->middleware("HasPermission:Offertes accorderen");
            Route::get('/proformas', 'AccorderenController@proformas')->middleware("HasPermission:Proforma's accorderen");
            Route::get('/facturen', 'AccorderenController@facturen')->middleware("HasPermission:Facturen accorderen");
            Route::get('/inkoopbonnen', 'AccorderenController@inkoopbonnen')->middleware("HasPermission:Inkoopbonnen accorderen");
            Route::get('/inkoopfacturen', 'AccorderenController@inkoopfacturen')->middleware("HasPermission:Inkoopfacturen accorderen");
        });
        Route::group(['prefix' => 'lijsten'], function () {
            Route::get('/facturen', 'LijstenController@facturen')->middleware("HasPermission:Lijsten Facturen");
            Route::get('/proformas', 'LijstenController@proformas')->middleware("HasPermission:Lijsten Pro Forma's");
            Route::get('/inkoopbonnen', 'LijstenController@inkoopbonnen')->middleware("HasPermission:Lijsten Inkoopbonnen");
            Route::get('/inkoopbonregels', 'LijstenController@inkoopbonregels')->middleware("HasPermission:Lijsten Inkoopbonnen");
            Route::get('/inkoopfacturen', 'LijstenController@inkoopfacturen')->middleware("HasPermission:Lijsten Inkoopfacturen");
            Route::post('/proformas/export/excel', 'LijstenController@downloadProformaExcel')->middleware("HasPermission:Lijsten Pro Forma's");
            Route::post('/facturen/export/excel', 'LijstenController@downloadFacturenExcel')->middleware("HasPermission:Lijsten Facturen");
            Route::post('/inkoopfacturen/export/excel', 'LijstenController@downloadInkoopfacturenExcel')->middleware("HasPermission:Lijsten Inkoopfacturen");
            Route::post('/inkoopbonnen/export/excel', 'LijstenController@downloadInkoopbonnenExcel')->middleware("HasPermission:Lijsten Inkoopbonnen");
            Route::post('/inkoopbonregels/export/excel', 'LijstenController@downloadInkoopbonregelsExcel')->middleware("HasPermission:Lijsten Inkoopbonnen");
        });
        Route::group(['prefix' => 'exact'], function () {
            Route::group(['prefix' => 'globe'], function () {
                Route::get('/cronjobs', 'ExactGlobeController@cronjobs')->middleware("HasPermission:Synchronisatie overzicht");
            });
            Route::group(['prefix' => 'online'], function (){
                Route::get('/connect', 'ExactOnlineController@connect');
                Route::get('/logs', 'ExactOnlineController@logs');
                Route::get('/remote-data', 'ExactOnlineController@remoteData');
                Route::get('/divisions', 'ExactOnlineController@divisions');
            });
        });
        Route::group(['prefix' => 'support'], function () {
            Route::get('/dashboard/{id}', 'SupportController@dashboard')->middleware("HasPermission:Formulieren beheren");
            Route::get('/iframe/{id}', 'SupportController@iframe')->middleware("HasPermission:Formulieren beheren");
            Route::get('/archive/{id}', 'SupportController@archive')->middleware("HasPermission:Formulieren beheren");
        });
        Route::group(['prefix' => 'wachtwoordenkluis'], function() {
            Route::get('/', 'WachtwoordkluisController@index')->middleware("HasPermission:Wachtwoorden inzien,Wachtwoorden beheren");
            Route::get('/aanvragen', 'WachtwoordkluisController@aanvragen')->middleware("HasPermission:Wachtwoorden beheren");
            Route::get('/nieuw', 'WachtwoordkluisController@nieuw')->middleware("HasPermission:Wachtwoorden beheren");
            Route::post('/nieuw', 'WachtwoordkluisController@store')->middleware("HasPermission:Wachtwoorden beheren");
            Route::get('/edit/{id}', 'WachtwoordkluisController@edit')->middleware("HasPermission:Wachtwoorden beheren");
            Route::post('/edit/{id}', 'WachtwoordkluisController@store')->middleware("HasPermission:Wachtwoorden beheren");
            Route::get('/iframe/{id}', 'WachtwoordkluisController@iframe');
        });
        Route::group(['prefix' => 'toolbox'], function() {
            Route::get('/', 'ToolboxController@index');
            Route::post('/store', 'ToolboxController@store')->middleware("HasPermission:Toolbox beheren");
            Route::get('/create', 'ToolboxController@create')->middleware("HasPermission:Toolbox beheren");
            Route::post('/modal/{id}', 'ToolboxController@toolboxUsers')->middleware("HasPermission:Toolbox beheren");
            Route::post('/modalAnswers', 'ToolboxController@toolboxAnswersAndQuestions')->middleware("HasPermission:Toolbox beheren");
            Route::get('/edit/{id}', 'ToolboxController@edit')->middleware("HasPermission:Toolbox beheren");
            Route::post('/delete', 'ToolboxController@edit')->middleware("HasPermission:Toolbox beheren");
        });
        Route::group(['prefix' => 'declaraties'], function() {
            Route::get('/', 'DeclaratiesController@index')->middleware("HasPermission:Declaratie bekijken");
            Route::get('/create', 'DeclaratiesController@create')->middleware("HasPermission:Declaratie indienen");
            Route::post('/create', 'DeclaratiesController@createPost')->middleware("HasPermission:Declaratie indienen");
        });
        Route::group(['prefix' => 'business-central'], function() {
            Route::get('/logs', 'BusinessCentralController@logs');
            Route::get('/remote-data', 'BusinessCentralController@remoteData');
        });
        Route::group(['prefix' => 'whatsapp'], function() {


            Route::group(['prefix' => 'app', 'middleware' => 'HasPermission:Chat Applicatie'], function() {
                route::get('/', 'WhatsAppController@app');
                route::get('/file/{id}', 'WhatsAppController@file');
            });
            Route::group(['prefix' => 'messages', 'middleware' => 'HasPermission:Berichten versturen'], function() {
                route::get('/', 'WhatsAppController@messages');
                route::get('/sent', 'WhatsAppController@sentMessages');
                route::get('/scheduled', 'WhatsAppController@scheduledMessages');

                Route::get('/new', 'WhatsAppController@createMessage');
                Route::post('/new', 'WhatsAppController@storeMessage');
                Route::get('/edit/{id}', 'WhatsAppController@createMessage');
                Route::post('/edit/{id}', 'WhatsAppController@storeMessage');
            });
            Route::group(['prefix' => 'groups', 'middleware' => 'HasPermission:Contacten beheren'], function() {
                Route::get('/', 'WhatsAppController@groups');
            });
            Route::group(['prefix' => 'contacts', 'middleware' => 'HasPermission:Contacten beheren'], function() {
                Route::get('/', 'WhatsAppController@contacts');
                Route::get('/contact/{id?}', 'WhatsAppController@contact');
                Route::post('/contact/{id?}', 'WhatsAppController@storeContact');
                Route::get('/template', 'WhatsAppController@template');
            });


        });

        Route::group(['prefix' => 'imports'], function() {
          route::get('/', 'ImportController@index');
          route::get('/new/{id}', 'ImportController@new');
        });

        Route::get('/log', 'HomeController@log');
        Route::get('/log', 'HomeController@log');
        Route::get('/clear-cache', function () { $exitCode = \Artisan::call('cache:clear'); });
        Route::post('/google/calendar', 'HomeController@postCalendar');
    });


    //Non-auth

    Route::group(['prefix' => 'abonnementen'], function () {
        Route::post('refresh', 'AbonnementenController@refresh');
        Route::get('user/{token}', 'AbonnementenController@user');
        Route::post('user/account', 'AbonnementenController@account');
        Route::post('user/password/change', 'AbonnementenController@passwordStore');
        Route::get('user/password/reset/{token}', 'AbonnementenController@passwordReset');
    });
    Route::group(['prefix' => 'aanmeldingen'], function () {
        Route::post('refresh', 'AanmeldingenController@refresh');
    });
    Route::group(['prefix' => 'facturatie'], function () {
        Route::post('refresh', 'FacturatieController@refresh');
    });
    Route::group(['prefix' => 'mollie'], function () {
        Route::post('/attempt', 'MollieController@attempt');

        Route::get('/abonnementen/betalen/{token}', 'MollieController@abonnementenBetalen');
        Route::get('/abonnementen/success/{token}', 'MollieController@abonnementenRedirect');
        Route::post('/abonnementen/webhook/{token}', 'MollieController@abonnementenWebhook');

        Route::get('/aanmeldingen/betalen/{token}', 'MollieController@aanmeldingenBetalen');
        Route::get('/aanmeldingen/success/{token}', 'MollieController@aanmeldingenRedirect');
        Route::post('/aanmeldingen/webhook/{token}', 'MollieController@aanmeldingenWebhook');

        Route::get('/facturatie/betalen/{token}', 'MollieController@facturatieBetalen');
        Route::get('/facturatie/success/{token}', 'MollieController@facturatieRedirect');
        Route::post('/facturatie/webhook/{token}', 'MollieController@facturatieWebhook');


    });
    Route::group(['prefix' => 'cdn'], function () {
        Route::post('validate/token', 'CdnController@validateToken');
        Route::post('aanvragen', 'CdnController@aanvragen');
        Route::post('aanvragen/store', 'CdnController@storeAanvragen');
        Route::post('abonnementen', 'CdnController@abonnementen');
        Route::post('abonnementen/subscribe', 'CdnController@abonnementenSubscribe');
        Route::post('abonnementen/korting', 'CdnController@abonnementenKorting');
        Route::post('abonnementen/email/check', 'CdnController@abonnementenEmailCheck');
        Route::post('aanmeldingen', 'CdnController@aanmeldingen');
        Route::post('aanmeldingen/subscribe', 'CdnController@aanmeldingenSubscribe');
    });
    Route::group(['prefix' => 'iframe'], function () {
        Route::get('acties/{actieid}/{parent}', 'ActielijstController@iframe')->middleware("HasPermission:Acties bekijken");
        Route::group(['prefix' => 'planning'], function () {
            Route::get('{year}/{month}/{user}', 'IframeController@planning_index');
            Route::get('dagplanning/{date}', 'IframeController@planning_dagplanning');
        });
        Route::group(['prefix' => 'offertes'], function () {
            Route::get('/details', 'OffertesController@details');
            Route::post('/details', 'OffertesController@detailsStore');
        });
        Route::group(['prefix' => 'explorer'], function () {
            Route::get('/upload', 'ExplorerController@upload');
            Route::post('/upload', 'ExplorerController@store');
        });
        Route::group(['prefix' => 'klanten'], function () {
            Route::get('create/window', 'KlantenController@createWindow')->middleware("HasPermission:Klanten beheren");
            Route::post('create/window', 'KlantenController@storeWindow');
            Route::get('edit/window/{id}', 'KlantenController@editWindow')->middleware("HasPermission:Klanten beheren");
            Route::post('edit/window/{id}', 'KlantenController@updateWindow');
            Route::get('show/window/{id}', 'KlantenController@showWindow');

            Route::get('contactpersonen/edit/window/{id}', 'KlantenController@editContactpersonenWindow')->middleware("HasPermission:Klanten beheren,Klant contactpersonen beheren,Klant contactpersonen toevoegen");;
            Route::post('contactpersonen/edit/window/{id}', 'KlantenController@updateContactpersonenWindow')->middleware("HasPermission:Klanten beheren,Klant contactpersonen beheren,Klant contactpersonen toevoegen");;
        });
        Route::group(['prefix' => 'projecten'], function () {
            Route::get('taken/edit/{id}', 'Uren\ProjectenController@takenEdit')->middleware("HasPermission:Projecten toevoegen");
            Route::get('taken/templates', 'Uren\ProjectenController@takenTemplates')->middleware("HasPermission:Taken templates beheren");
            Route::get('taken/templates/{id}', 'Uren\ProjectenController@takenTemplatesEdit')->middleware("HasPermission:Taken templates beheren");
            Route::get('listinput/{project}/{keywordid}', 'Uren\ProjectenController@listSummary');
        });
        Route::group(['prefix' => 'support'], function () {
            Route::get('/{token}', 'SupportController@form');
            Route::post('/{token}', 'SupportController@storeForm');
        });
        Route::group(['prefix' => 'whatsapp'], function () {
            Route::get('/app', 'WhatsAppController@app');
        });

        Route::group(['prefix' => 'aanvragen'], function () {
            Route::get('/show/{code}', 'AanvragenController@showForm');
        });

        Route::get('/login', 'IframeController@login');
        Route::post('/login', 'IframeController@auth');

        Route::get('/file/upload', 'IframeController@fileUpload');
        Route::post('/file/upload', 'IframeController@fileStore');
    });
    Route::group(['prefix' => 'offertes'], function () {
        Route::get('ondertekenen/{offerteId}/{token}', 'OffertesController@ondertekenen');
        Route::post('ondertekenen', 'OffertesController@ondertekend');
        Route::get('pdf/{offerteId}/{templateId}', 'OffertesController@printPdf');
        Route::get('token/{token}', 'OffertesController@printPdfToken');
    });
    Route::group(['prefix' => 'werkbonnen'], function () {
        Route::get('ondertekenen/{token}', 'WerkbonnenController@ondertekenen');
        Route::post('ondertekenen/{token}', 'WerkbonnenController@ondertekend');
        Route::get('/token/{token}', 'WerkbonnenController@viewPdfToken');
    });
    Route::group(['prefix' => 'facturatie/'], function () {
        Route::get('facturen/open/{token}', 'FacturatieController@openEmailFactuur');
        Route::get('proforma/ondertekenen/{token}', 'FacturatieController@ondertekenenProforma');
        Route::post('proforma/ondertekenen/{token}', 'FacturatieController@ondertekendProforma');
    });
    Route::group(['prefix' => 'opdrachtbonnen/'], function () {
        Route::get('/ondertekenen/{token}', 'Uren\ProjectenController@ondertekenenOpdrachtbon');
        Route::post('/ondertekenen', 'Uren\ProjectenController@ondertekendOpdrachtbon');
    });
    Route::group(['prefix' => 'checklists/'], function () {
        Route::get('/pdf/project/{id}', 'ChecklistsController@projectPdf');
        Route::get('/pdf/{token}', 'ChecklistsController@pdf');
    });
    Route::group(['prefix' => 'inkoopbonnen/'], function () {
        Route::get('/open/{token}', 'InkoopbonnenController@open');
    });
    Route::group(['prefix' => 'uren/'], function () {
        Route::get('/projectnummers/opdrachtbon/view/{token}', 'Uren\ProjectenController@opdrachtbonByToken');
    });
    Route::group(['prefix' => 'redirect'], function () {
        Route::get('/exact/online', 'ExactOnlineController@redirect');
    });
    Route::group(['prefix' => 'explorer/'], function () {
        Route::get('/player/{player}/{src}', 'Api\FileController@player');
    });
    Route::group(['prefix' => 'users/'], function () {
      Route::group(['prefix' => 'intake/'], function () {
          Route::get('/fill/{src}', 'UserController@fillIntake');
          Route::post('/fill/{src}', 'UserController@storeFilledIntake');
          Route::get('/preview/{src}', 'UserController@previewIntake');
      });
    });


    Route::get('/force-login/{page}/{hash}', 'DashboardController@forceLogin')->where('hash', '(.*)');;

    Route::get('/debug/post', 'DebugController@viewPost')->middleware("beheer");
    Route::post('/debug/post/get', 'DebugController@filterPosts');

    Route::get('/test/error', 'DebugController@testError');
    Route::get('/test/notifications', function () { pushToDevelopers('Dit is een test notificatie', ''); });
    Route::get('/test', 'DebugController@testGet');
    Route::get('/test2', 'DebugController@testGet2');
    Route::get('/test/updateprojectfoldernames', 'DebugController@updateProjectFolderNames');
    Route::post('/test', 'DebugController@testPost');
    Route::get('/test/cronjob/{command}', 'DebugController@testCronjob');
    Route::get('/firstsetup', 'DebugController@firstTimeSetup');

    Route::get('/google/calendar/{origin}/{user}', 'HomeController@googlePermissionCalendar');
    Route::get('/google-redirect', 'HomeController@fetchCode');
    Route::get('/google/{id}/{redirect}/{userId}', 'HomeController@google');
    Route::get('/google/calendar', 'HomeController@postCalendar');
    Route::get('/unlink', 'HomeController@unlink');
    Route::view('/loading', 'loading');


    //Proxies
    Route::get('/ikf-file-proxy/{token}', 'InkoopfacturenController@fileProxy');


});
