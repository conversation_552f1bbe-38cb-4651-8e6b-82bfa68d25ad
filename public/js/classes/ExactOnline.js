class ExactOnline{

  connected;
  html = {
    spinner_gegevens: '<div class="text-center text-primary"><div class="spinner-border" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div> <div class="opacity-75 mt-4" >Gege<PERSON><PERSON> worden opgehaald</div> </div>',
    spinner_small: '<div class="spinner-border text-primary" role="status" style="width: 20px;height: 20px;"><span class="sr-only"></span></div>',
    icon_zoom: `<i class="fa fa-magnifying-glass m-0"></i>`,
    icon_edit: `<i class="far fa-edit m-0"></i>`,
    icon_minus: `<i class="fas fa-minus m-0"></i>`,
    icon_check: `<i class="fa fa-check m-0"></i>`,
    icon_xmark: `<i class="fa fa-xmark m-0"></i>`,
    icon_down: `<i class="fa fa-chevron-down m-0"></i>`,
  }

  //Init
  constructor(){
    this.connected = _global.exact_online_connected;
    this.defineListeners();
  }

  defineListeners(){
    const parent = this;

    //Revoke
    $(document).off('click', '[data-exact-online-klant-init]')
    $(document).off('click', '[data-exact-online-project-init]')
    $(document).off('click', '[data-exact-online-factuur-init]')

    //Init
    $(document).on('click', '[data-exact-online-klant-init]', function(){
      const id = $(this).attr('data-exact-online-klant-init');
      parent.uploadKlanten(id);
    })
    $(document).on('click', '[data-exact-online-project-init]', function(){
      const id = $(this).attr('data-exact-online-project-init');
      parent.uploadProjecten(id);
    })
    $(document).on('click', '[data-exact-online-factuur-init]', function(){
      const id = $(this).attr('data-exact-online-factuur-init');
      parent.uploadFacturen(id);
    })

    //Post
    $(document).on('click', '[data-exact-online-post]', function(){
      const target = $(this).attr('data-exact-online-post');
      const id = $(this).attr('data-id');
      parent.post(id, target);
    })
  }

  //Upload init
  async uploadKlanten(id){
    this.setModalLoader();

    const klant = await getKlant(id);

    if(!klant){
      notification(`Er is iets foutgegaan!`);
      return;
    }

    confirmModal({
      text: this.klantenContainer(klant),
      hideFooter: true,
    })
    tippyInit();
  }
  async uploadProjecten(id){
    this.setModalLoader();

    const project = await getProject(id);
    const klant = await getKlant(project?.klant_id);

    if(!project){
      notification('Er is iets foutgegaan!');
      return;
    }

    confirmModal({
      text: `
        ${this.klantenContainer(klant)}
        ${this.projectenContainer(project)}
      `,
      hideFooter: true,
    })
    tippyInit();
  }
  async uploadFacturen(id){
    this.setModalLoader();

    const factuur = await getFactuur(id, { relations: ['project'] });
    const klant = await getKlant(factuur?.klant_id);
    const project = factuur.project ? await getProject(factuur.project.id) : false;

    if(!factuur){
      notification('Er is iets foutgegaan!');
      return;
    }

    confirmModal({
      text: `
        ${this.klantenContainer(klant)}
        ${this.projectenContainer(project)}
        ${this.facturenContainer(factuur)}
      `,
      hideFooter: true,
    })
    tippyInit();
  }

  //Upload post
  async post(id, target){
    this.changeStatus({
      target: target,
      status: 'loader',
    })

    try{
      await ajax(`api/exact/online/upload/${target}`, {id: id});
      this.changeStatus({
        target: target,
        status: 'ok',
        id: id,
      });
    } catch(e){
      this.changeStatus({
        target: target,
        status: 'fail',
        error: e?.responseJSON?.message || null
      });
    }
  }

  //Dom
  setModalLoader(){
    confirmModal({
      text: this.html.spinner_gegevens,
      hideFooter: true,
    })
  }
  changeStatus(options){
    const {target, status, error, id} = options;

    const container = $(`[data-exact-online-status=${target}]`);
    let content = this.html.spinner_small

    this.setError(target, null);

    if(status == 'ok'){
      content = `<span class="text-success" >${this.html.icon_check}</span>`;
      $(`[data-exact-online-post=${target}]`).removeClass(['btn-primary', 'btn-warning']).addClass('btn-success');
      $(`[data-exact-online-${target}-init=${id}]`).removeClass('btn-primary').addClass('btn-success');
    }
    else if(status == 'fail'){
      content = `<span class="text-danger" >${this.html.icon_xmark}</span>`;
      this.setError(target, error || 'Onbekend');
    }

    container.html(content);
  }
  setError(target, error){
    const container = $(`[data-exact-online-error=${target}]`);

    container.empty();
    if(!error){
      return;
    }

    container.html(`
    <div data-toggle-container class="hover-mark rounded-5" >
      <div class="flex-center text-muted cursor-pointer" data-toggle-btn >
        <span>Errors</span>
        <a class="btn"  >${this.html.icon_down}</a>
      </div>
      <div data-toggle-content style="display: none" class="w-100" >
        <div class="p-2 font-size-075 opacity-75 text-danger" >${error}</div>
      </div>
    </div>
    `)
  }

  //HTML
  klantenHTML(klant){
    const data = {
      button: `<a class="btn btn-success text-white btn-block" data-exact-online-post="klant" data-id="${klant.id}" >Klant Updaten</a>`,
      status: `<span data-exact-online-status="klant" class="mx-2" ><span class="text-success" >${this.html.icon_check}</span></span>`
    }

    if(!klant.exact_online_metadata.id){
      data.button = `<a class="btn btn-primary text-white btn-block" data-exact-online-post="klant" data-id="${klant.id}" >Klant Uploaden</a>`;
      data.status = `<span data-exact-online-status="klant" class="mx-2"> ${this.html.icon_minus}</span>`;
    }
    return data;
  }
  projectenHTML(project){
    const data = {
      button: `<a class="btn btn-success text-white btn-block" data-exact-online-post="project" data-id="${project.id}" >Project Updaten</a>`,
      status: `<span data-exact-online-status="project" class="mx-2" ><span class="text-success" >${this.html.icon_check}</span></span>`
    }

    if(!project.exact_online_metadata.id){
      data.button = `<a class="btn btn-primary text-white btn-block" data-exact-online-post="project" data-id="${project.id}" >Project Uploaden</a>`;
      data.status = `<span data-exact-online-status="project" class="mx-2"> ${this.html.icon_minus}</span>`;
    }
    return data;
  }
  facturenHTML(factuur){
    const data = {
      button: `<a class="btn btn-success text-white btn-block opacity-50 cursor-unset" >Factuur geüpload</a>`,
      status: `<span data-exact-online-status="factuur" class="mx-2" ><span class="text-success" >${this.html.icon_check}</span></span>`
    }
    if(!factuur.exact_online_metadata?.id){
      data.button = `<a class="btn btn-primary text-white btn-block" data-exact-online-post="factuur" data-id="${factuur.id}" >Factuur Uploaden</a>`;
      data.status = `<span data-exact-online-status="factuur" class="mx-2"> ${this.html.icon_minus}</span>`;
    }
    return data;
  }

  //HTML Containers
  klantenContainer(klant){
    if(!klant){
      return '';
    }

    const klant_data = this.klantenHTML(klant);
    return `<div class="my-2" >
              <div class="my-2 d-flex align-items-center" >
                  ${klant_data.button}
                  <a class="btn btn-dark text-white ml-2" onclick="editKlant(${klant.id})" >${this.html.icon_edit}</a>
                  <a class="btn btn-dark text-white ml-2" onclick="showKlant(${klant.id})" >${this.html.icon_zoom}</a>
              </div>
              <div class="bg-inverse-secondary rounded p-2 border" >
                  <div class="d-flex justify-content-between align-items-center" >
                      <span>Status: </span>
                      ${klant_data.status}
                  </div>
                  <div data-exact-online-error="klant" ></div>
              </div>
            </div>`
  }
  projectenContainer(project){
    if(!project){
      return '';
    }

    const project_data = this.projectenHTML(project);
    return `<div class="my-2" >
              <div class="my-2 d-flex align-items-center" >
                  ${project_data.button}
                  <a class="btn btn-dark text-white ml-2" onclick="showProject(${project.id})" >${this.html.icon_zoom}</a>
              </div>
              <div class="bg-inverse-secondary rounded p-2 border" >
                  <div class="d-flex justify-content-between align-items-center" >
                      <span>Status: </span>
                      ${project_data.status}
                  </div>
                  <div data-exact-online-error="project" ></div>
              </div>
            </div>`
  }
  facturenContainer(factuur){
    if(!factuur){
      return '';
    }

    const factuur_data = this.facturenHTML(factuur);
    return `<div class="my-2" >
              <div class="my-2 d-flex align-items-center" >
                  ${factuur_data.button}
                  <a class="btn btn-dark text-white ml-2" href="${url}/facturatie/facturen/pdf/${factuur.id}" target="_blank" >${this.html.icon_zoom}</a>
              </div>
              <div class="bg-inverse-secondary rounded p-2 border" >
                  <div class="d-flex justify-content-between align-items-center" >
                      <span>Status: </span>
                      ${factuur_data.status}
                  </div>
                  <div data-exact-online-error="factuur" ></div>
              </div>
            </div>`
  }

  //Sync
  async sync(target){
    try{
      await ajax(`api/exact/online/sync/${target}`);
    } catch(e){
      handleCatchError(e);
    }
  }

  //Get
  async getDivisions(){
    try{
      const response = await ajax('api/exact/online/get/division')
      return response.divisions;
    }
    catch(e){
      handleCatchError(e);
      return null;
    }
  }

}
