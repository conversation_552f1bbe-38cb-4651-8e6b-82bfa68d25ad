// const url = 'https://'+window.location.hostname;

const timer = ms => new Promise(res => setTimeout(res, ms))

$.fn.rotate = function(degree){
  $(this).addClass('transition-03');

  if(degree === false){
    $(this).css({transform: ``});
  }

  const css = $(this).css('transform');
  if(css == 'none' || !css){
    $(this).css({transform: `rotate(${degree}deg)`});
  }
  else{
    $(this).css({transform: ``});
  }
}
$.fn.beat = function(){
  $(this).addClass('transition-025');

  $(this).css({transform: "scale(1.1)"})

  setTimeout(() => {
    $(this).css({transform: ""})
  }, 250)
}
$.fn.showModal = function(){
  const modal = $(this);
  return new Promise((resolve) => {
    modal.modal("show").on('shown.bs.modal', () => {resolve()});
  })
}
$.fn.hideModal = function(){
  const modal = $(this);
  return new Promise((resolve) => {
    modal.modal("hide").on('hidden.bs.modal', () => {resolve()});
  })
}

String.prototype.hexEncode = function(){
  var result = '';
  for (var i=0; i < this.length; i++) {
    result += this.charCodeAt(i).toString(16);
  }
  return result;
}
String.prototype.hexDecode = function(){
  var result = '';
  for (var i = 0; i < this.length; i += 2){
    result += String.fromCharCode(parseInt(this.substr(i, 2), 16));
  }
  return result;
}

String.prototype.htmlDecode = function(){
  const txt = document.createElement("textarea");
  txt.innerHTML = this;
  const value = txt.value;
  $(txt).remove();
  return value;
}
String.prototype.htmlEncode = function(){
  return this.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;').replace(/'/g, '&#39;');
}

Date.prototype.getWeek = function() {
  var date = new Date(this.getTime());
  date.setHours(0, 0, 0, 0);
  date.setDate(date.getDate() + 3 - (date.getDay() + 6) % 7);
  var week1 = new Date(date.getFullYear(), 0, 4);
  return 1 + Math.round(((date.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
}

function redirect(path, blank = false) {
  if (path.startsWith('/')) {
    path = path.substring(1);
  }

  redirectURL(`${url}/${path}`, blank);
}
function redirectURL(url, blank = false) {
  if (blank) {
    window.open(url, '_blank');
    return
  }

  location.href = url;
}

function _addMessageEventListener(fn){
  let method = window.addEventListener ? 'addEventListener' : 'attachEvent';
  let eventer = window[method];
  let trigger = method === 'attachEvent' ? 'onmessage' : 'message'
  eventer(trigger, fn);
}

function getOppositeNumber(number) {
  if(!isNumber(number)){ return 0; }
  number = Number(number)

  return -number;
}
function getParameter(parameterName) {
  var result = null,
      tmp = [];
  location.search
      .substr(1)
      .split("&")
      .forEach(function (item) {
        tmp = item.split("=");
        if (tmp[0] === parameterName) result = decodeURIComponent(tmp[1]);
      });
  return result;
}
function groupBy(arr, index){
  const group = {};

  for(const i in arr){
    const row = arr[i];
    const value = row[index]

    if(!group[value]){ group[value] = []; }

    group[value].push(row);
  }

  return group;
}
function sum(arr){
  let sum = 0;

  for(const row of arr){
    if(!isNumber(row)){ continue; }
    sum += row;
  }

  return sum;
}
function average(arr){
  let sum = 0;

  for(const row of arr){
    if(!isNumber(row)){ continue; }
    sum += row;
  }

  if(!sum && !arr.length){
    return 0;
  }

  return sum / (arr.length || 0);
}
function compareStrings(s1, s2){
  function editDistance(s1, s2) {
    s1 = s1.toLowerCase();
    s2 = s2.toLowerCase();

    const costs = [];
    for (let i = 0; i <= s1.length; i++) {
      let lastValue = i;
      for (let j = 0; j <= s2.length; j++) {
        if (i == 0)
          costs[j] = j;
        else {
          if (j > 0) {
            let newValue = costs[j - 1];
            if (s1.charAt(i - 1) != s2.charAt(j - 1))
              newValue = Math.min(Math.min(newValue, lastValue),
                  costs[j]) + 1;
            costs[j - 1] = lastValue;
            lastValue = newValue;
          }
        }
      }
      if (i > 0)
        costs[s2.length] = lastValue;
    }
    return costs[s2.length];
  }

  let longer = s1;
  let shorter = s2;
  if (s1.length < s2.length) {
    longer = s2;
    shorter = s1;
  }
  let longerLength = longer.length;
  if (longerLength == 0) {
    return 1.0;
  }
  return (longerLength - editDistance(longer, shorter)) / parseFloat(longerLength);
}
function toFixedNumber(number, precision){
  if (!isNumber(number)){ number = 0; }
  return Number(Number(number).toFixed(precision))
}

function actError(e, opmerking = ''){
  console.log(e);
  ajax(`/debug/post`, {
    js_error: true,
    route: _global.route,
    status: e.status || '',
    message: e.message || (e.responseText || ''),
    stack: e.stack || '',
    opmerking: opmerking || '',
  })
}
function handleCatchError(err, opmerking = null){
  if(err?.status === 0){ console.log('aborted'); return }
  if(err?.status === 401){
    notification('Unauthorized, ververs de pagina.', 'warning');
    return
  }

  actError(err, opmerking);
  errorLoader();
  displayCatchError(err);
}
function displayCatchError(err){
  console.log(err);
  if (err?.message){ notification(err.message); }
  if (err?.responseJSON?.message){ notification(err.responseJSON.message); }
  if(!err?.message && !err?.responseJSON?.message){ notification('Er is iets foutgegaan!'); }
}

function resetIndex(arr){
  const rtn = [];
  for(const index in arr){
    rtn.push(arr[index]);
  }
  return rtn;
}
function hasAttribute(element, attribute){
  for(const attr of $(element)[0].attributes){
    if(attr.name == attribute){ return true; }
  }
  return false;
}
function first(arr){
  for(const i in arr){
    return arr[i];
  }
  return null;
}
function firstKey(object){
  const keys =  Object.keys(object)
  return keys[0];
}
function lastKey(object){
  const keys =  Object.keys(object)
  return keys[keys.length - 1];
}
function getMaanden(maand = null){
  const maanden = [ "", "Januari", "Februari", "Maart", "April", "Mei", "Juni", "Juli", "Augustus", "September", "Oktober", "November", "December"]
  if(maand !== null){
    return maanden[Number(maand)]
  }

  return maanden;
}
function getShortMaanden(maand = null){
  const maanden = [ "", "Jan", "Feb", "Maa", "Apr", "Mei", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dec"]
  if(maand !== null){
    return maanden[Number(maand)]
  }

  return maanden;
}
function randomString(length = 10) {
  var result           = '';
  var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  var charactersLength = characters.length;
  for ( var i = 0; i < length; i++ ) {
    result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
  }

  localStorage.randomString = result;
  return result;
}
function lastString(){
  return localStorage.randomString;
}
function stringInText(string, text){
  string = string.toLowerCase();
  text = text.toLowerCase();

  const words = string.split(' ');
  for(const word of words){
    if(text.includes(word)){ return true; }
  }
  return false;
}
function deleteDiv(id) {
  $(id).remove();
}
function deleteDivTimeout(id, time) {
  setTimeout(() => {
    $(id).remove();
  }, time)
}
function emptyDiv(id) {
  $(id).empty();
}
function emptyDivTimeout(id, time) {
  setTimeout(() => {
    $(id).empty();
  }, time)
}
function euToUsDate(date){
  return date.split('-').reverse().join('-');
}
function convert(date, reverse = false) {
  let d = new Date(date);
  if(reverse){
    return ('00' + d.getFullYear()).slice(-4) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2);
  }
  return ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('00' + d.getFullYear()).slice(-4);
}
function convertWithMonthName(date) {
  let d = new Date(date);
  return `${d.getDate()} ${getMaanden()[(d.getMonth() + 1)]} ${d.getFullYear()}`;
}
function now(target = null) {
  const d = target ? new Date(target) : new Date();

  const time = `0${d.getHours()}`.slice(-2) + ':' + `0${d.getMinutes()}`.slice(-2);
  const date = {
    us: +d.getFullYear() + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + `0${d.getDate()}`.slice(-2),
    eu: `0${d.getDate()}`.slice(-2) + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + d.getFullYear(),
  };
  date.timestamp = new Date(date.us).getTime();

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const dayOfWeek = d.getDay();

  // Calculate the week number
  const weekNumber = getISOWeekNumber(d);

  const isWeekend = dayOfWeek == 0 || dayOfWeek == 6;

  return {
    time: time,
    timestamp: d.getTime(),
    date: date,
    year: year,
    month: month,
    month_name: getMaanden(month),
    month_short_name: getShortMaanden(month),
    day: day,
    day_full: `0${day}`.slice(-2),
    dayOfWeek: dayOfWeek,
    weekNumber: weekNumber,
    isWeekend: isWeekend,
    addMinutes: minutes => {
      const _date = d.setMinutes(d.getMinutes() + Number(minutes));
      return now(_date)
    },
    subMinutes: minutes => {
      const _date = d.setMinutes(d.getMinutes() - Number(minutes));
      return now(_date)
    },
    addHours: hours => {
      const _date = d.setHours(d.getHours() + Number(hours));
      return now(_date)
    },
    subHours: hours => {
      const _date = d.setHours(d.getHours() - Number(hours));
      return now(_date)
    },
    addDays: days => {
      const _date = d.setDate(d.getDate() + Number(days));
      return now(_date)
    },
    subDays: days => {
      const _date = d.setDate(d.getDate() - Number(days));
      return now(_date)    },
    addMonths: months => {
      const _date = d.setMonth(d.getMonth() + Number(months));
      return now(_date)
    },
    subMonths: months => {
      const _date = d.setMonth(d.getMonth() - Number(months));
      return now(_date)
    },
    addYears: years => {
      const _date = d.setFullYear(d.getFullYear() + Number(years));
      return now(_date)
    },
    subYears: years => {
      const _date = d.setFullYear(d.getFullYear() - Number(years));
      return now(_date)
    }
  };
}
function excelDateToJSDate(excelDate) {
  const excelEpoch = new Date(Date.UTC(1899, 11, 30));
  const msPerDay = 24 * 60 * 60 * 1000;
  const jsTimestamp = excelEpoch.getTime() + excelDate * msPerDay;

  const d = new Date(jsTimestamp);
  return now(d);
}
function getISOWeekNumber(date) {
  const tempDate = new Date(date);
  const year = tempDate.getFullYear();

  const janFirst = new Date(year, 0, 1);

  const janFirstDay = janFirst.getDay();

  if (janFirstDay <= 3) {
      janFirst.setDate(janFirst.getDate() - janFirstDay + 1);
  } else {
      janFirst.setDate(janFirst.getDate() + (7 - janFirstDay) + 1);
  }

  const diffDays = Math.floor((tempDate - janFirst) / (24 * 60 * 60 * 1000));
  return Math.ceil((diffDays + 1) / 7);
}

function stringIsDate(str){
  if(!checkDateFormat(str)){return false;}
  const date = new Date(str);
  return !isNaN(date.getTime());
}
function checkDateFormat(str){
    const hasTwoDashes = (str.match(/-/g) || []).length === 2;
    const hasTwoSlashes = (str.match(/\//g) || []).length === 2;
    const digitCount = (str.match(/\d/g) || []).length;
    return (hasTwoDashes || hasTwoSlashes) && digitCount >= 6 && digitCount <= 8;
}

function dateToString(date){
  return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;
}
function getDatesBetween(startDate, endDate) {
  const dates = [];
  let currentDate = new Date(startDate);
  const end = new Date(endDate);
  while (currentDate <= end) {
    dates.push(currentDate.toISOString().split('T')[0]);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return dates;
}
function toggleClass(id, cls){
  if(Array.isArray(id)){
    for(const index in id){
      $(id[index]).toggleClass(cls);
    }
    return;
  }

  $(id).toggleClass(cls);
}
function toggleCSSLink(css){
  const element = $(`.css-dynamic[data-css="${css}.css"]`);
  const data = element.attr('data-css');
  if(element.attr('href')){
    element.attr('href', '');
    removeCookie(`dynamic-css-${css}`);
  }
  else{
    element.attr('href', `${url}/client/css/${data}`);
    setCookie(`dynamic-css-${css}`, true);
  }
}
function clone(object){
  return JSON.parse(JSON.stringify(object))
}
function getApiData(target, ids){
  return $.ajax({
    type: "POST",
    url: `${url}/api/data`,
    data: {
      target: target,
      ids: ids,
      _token: csrf,
    },
  });
}
function ajax(route, data = {csrf: null}){
  if(route.slice(0, 1) == '/'){
    route = route.slice(1, route.length);
  }

  if(!data){data = {}}

  if(Array.isArray(data)){
    data.push({name: '_token', value: csrf})
    data.push({name: 'api_token', value: api_token})
  }
  else{
    data._token = csrf;
    data.api_token = api_token;
  }

  return $.ajax({
    type: "POST",
    url: `${url}/${route}`,
    data: data,
  });
}
function ajaxFile(route, data = {csrf: null}){
  if(route.slice(0, 1) == '/'){
    route = route.slice(1, route.length);
  }

  if(!data){data = {}}
  if(Array.isArray(data)){
    data.push({name: '_token', value: csrf})
  }
  else{
    data._token = csrf;
  }

  const formData = new FormData();

  for(const key in data){
    formData.append(key, data[key]);
  }

  return new Promise(resolve => {
    const request = new XMLHttpRequest();
    request.open('POST', `${url}/${route}`);
    request.onreadystatechange = () => {
      if (request.readyState === 4) {
        const { response } = request;
        resolve({
          status: request.status == 201 ? 1 : 0,
          data:  isJson(response) ? JSON.parse(request.response) : response,
        })
      }
    }
    request.send(formData);
  })
}
function serializeArrayToObject(arr){
  const obj = {};
  for (const row of arr){
    obj[row.name] = row.value;
  }

  return obj;
}

function beat(element){
  $(element).addClass('transition-025');

  $(element).css({transform: "scale(1.1)"})

  setTimeout(() => {
    $(element).css({transform: ""})
  }, 250)
}
function copyText(text) {
  notification(`Gekopieerd naar klembord <input value="${text}" class="copy-text form-control-plaintext p-0 text-primary" spellcheck="false" style=" width: 10px; height: 1px" > `, 'primary', 2.5)
  $('.copy-text').select().focus();
  document.execCommand('copy');
}
function uppercaseFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

function toPrice(price){
  price = Number(price);
  return price.toLocaleString('de-DE', { maximumFractionDigits: 2, minimumFractionDigits: 2 })
}
function reverseNumberFormat(number = '0'){
  return Number(number.replaceAll('.', '').replaceAll(',', '.'));
}

function toggleDropdown(dropdown){
  $(dropdown).toggleClass('show');
}
function filterDropdown(input, dropdown) {
  const val = $(input).val().toLowerCase();
  $(dropdown).find('a').each(function(){
    if($(this).text().toLowerCase().includes(val)){
      $(this).removeClass('d-none');
    }
    else{
      $(this).addClass('d-none');
    }
  });
}
function aantalSelect(l, s = 1){
  let options = '<option value="" disabled selected >Aantal</option>';
  for(let i = 0; i<=l; i++){

    let selected = '';

    if(s && i == s){
      selected = 'selected';
    }
    options += '<option '+selected+' value="'+i+'" >'+i+'</option>'

  }
  return options;
}
function dropifyInit(){
  $(".dropify").dropify();
}
function tableInit(target, options = {}){
  if (typeof target == 'object' || typeof target == 'array'){
    let rtn = [];
    for(const i in target){
      const table = $(target[i]).DataTable(options);
      rtn.push(table);
    }
    return rtn;
  }

  return $(target).DataTable(options);
}

function getCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for(var i=0;i < ca.length;i++) {
    var c = ca[i];
    while (c.charAt(0)==' ') c = c.substring(1);
    if (c.indexOf(nameEQ) != -1) return c.substring(nameEQ.length,c.length);
  }
  return null;
}
function setCookie(name, value, days = 30){
  const date = new Date();
  date.setTime(date.getTime() + (days*24*3600*1000))
  document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`;
}
function removeCookie(name){
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

function findContainer(cls, element){
  let container = null;

  $(element).parents().each(function(){
    if($(this).hasClass(cls)){
      container = $(this);
      return false;
    }
  });

  return container;
}
function findContainerByAttr(attr, element, value = null){
  let container = null

  $(element).parents().each(function(){
    const div = $(this);
    $.each(this.attributes, function() {
      if(this.name == attr){
        if(!value){ container = div }
        if(value == this.value){ container = div }
      }
    });
    if(container){ return false; }
  });

  return container;
}
function findContainerByTag(tag, element){
  let container = null;

  $(element).parents().each(function(){
    if(this.tagName == tag.toUpperCase()){
      container = $(this);
      return false;
    }
  });

  return container;
}

function findById(arr, id){
  for(let i in arr){
    if(arr[i].id == id){return arr[i];}
  }
}
function findInArray(array, col, value){
  for(const i in array){
    if(array[i][col] == value){
      return array[i];
    }
  }
  return null
}
function findInNestedArray(array, nested, col, value){
  for(const i in array) {
    for (const x in array[i][nested]) {
      const row = array[i][nested][x];
      if (row[col] == value) {
        return row;
      }
    }
  }
  return null
}

function bodyModalClass(time = 400){
  setTimeout(() => {
    $('body').addClass('modal-open');
  }, time)
}
function showModal(element){
  if(typeof element == 'object'){
    $(element).modal("show");
  }
  else{
    $(`#${element}`).modal("show");
  }
  bodyModalClass();
}
function hideModal(element){
  if(typeof element == 'object'){
    $(element).modal("hide");
  }
  else{
    $(`#${element}`).modal("hide");
  }
}
function getLocation(postcode = null, huisnummer = null){
  return $.ajax({
    type: "POST",
    url: `${url}/api/location`,
    data: {
      postcode: postcode,
      huisnummer: huisnummer,
    },
  });
}
function downloadLink(link, name = '') {
  const element = document.createElement('a');
  $(element).attr('href', link).attr('target', '_blank').attr('download', name).addClass('d-none');
  $('body').append(element);

  element.click();

  $(element).remove();
}
function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}
function randomHex(){
  let string = '0123456789ABCDEF';
  let s = '';
  for(let i = 0; i <6; i++){
    let x = Math.floor(Math.random() * string.length - 1)
    if(x < 0){x = 0;}
    s += string.slice(x, x+1);

  }
  return "#"+s;
}
function randomHexOpacity(){
  let string = '456789ABCDE';
  let s = '';
  for(let i = 0; i <2; i++){
    let x = Math.floor(Math.random() * string.length - 1)
    if(x < 0){x = 0;}
    s += string.slice(x, x+1);

  }
  return s;
}
function scrl(id, offset = 100){
  const top = $(id).offset().top - offset;
  window.scroll({top: top, behavior: 'smooth'});
}
function randomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min) + min);
}
function isMultipleOf(a, b){
  const value = new Decimal(a);
  const step = new Decimal(b);
  return value.mod(step).eq(0);
}
function notification(text = '', bg = 'danger', duration = 5){


  $('.corner-notifications').append(
      `<div class="w-100" id="${randomString(10)}" style="display: none" >
        <div class="alert alert-fill-${bg} shadow d-flex justify-content-between p-0 mb-1 overflow-hidden" >
          <div class="p-2" >${text}</div>
          <div class="p-2" ><a onclick="deleteNotification('#${lastString()}')" class="px-1 cursor-pointer" ><i class="fa fa-xmark m-0"></i></a></div>
      </div>
    </div>`
  )


  const element = $(`#${lastString()}`)

  element.toggle(250);

  setTimeout(() => {
    element.toggle(150);
    setTimeout(() => {
      element.remove();
    },150);
  },(duration * 1000))
}
function deleteNotification(string){
  $(string).toggle(250);
  setTimeout(() => { $(string).remove() },250)
}
function HTMLToText(html){
  var tempDivElement = document.createElement("div");
  tempDivElement.innerHTML = html;
  return tempDivElement.textContent || tempDivElement.innerText || "";
}
function htmlEscape(str) {
  if(typeof str != 'string'){ return str; }
  return str
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
}
function showLocationOnMaps(object){
  $('#location-modal .location').html(
      `<iframe
          class="w-100 rounded"
          frameborder="0"
          style="border:0; height: 65vh"
          referrerpolicy="no-referrer-when-downgrade"
          src="https://www.google.com/maps/embed/v1/place?key=${ENV.GOOGLE_MAPS}&q=${object.straat || ''}+${object.huisnummer || ''}${object.toevoeging || ''}+${object.straat || ''}+${object.plaats || ''}"
          allowfullscreen>
        </iframe>`
  )
  showModal('location-modal');
}
function timeToTimestamp(time){
  const arr = time.split(':');
  return (arr[0] * 3600) + arr[1] * 60;
}
function stringToNumber(strn){
  const abc = ['a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
  strn = strn.replaceAll(' ', '');
  const arr = strn.split('');
  let ret = '';
  for(let row of arr){

    if(isNumber(row)){
      ret += `${row}`;
      continue;
    }
    if(abc.indexOf(row) == -1){
      continue;
    }

    ret += abc.indexOf(row.toLowerCase()).toString();
  }

  ret += '0'.repeat(100 - ret.length);

  return Number(ret);
}
function stringToHexColor(string) {
  if(!string){ return '#404040' }

  let hash = 0;
  for (let i = 0; i < string.length; i++) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }
  let color = '#';
  for (let i = 0; i < 3; i++) {
    let value = (hash >> (i * 8)) & 0xff;
    color += ('00' + value.toString(16)).substr(-2);
  }
  return color;
}
function stringToMb(string) {
  return Number(
      ((new TextEncoder().encode(string)).length / 1000 / 1000).toFixed(2)
  );
}

function loadPage(l){
  localStorage.loading_page = url + l;
  location.href = `${url}/loading`;
}
function preloadImage(src){
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;

    img.onload = () => { resolve(); }
    img.onerror = () => { reject(); }

  })
}

function diffInHours(a, b){
  const time = {
    a: a.split(':'),
    b: b.split(':'),
  }

  let start = new Date(2000, 1, 1, Number(time.a[0]), Number(time.a[1])).getTime();
  let end = new Date(2000, 1, 1, Number(time.b[0]), Number(time.b[1])).getTime();

  return Math.abs((end - start) / 1000 / 60 / 60);
}

function dateDiffInDays(start, end){
  start = new Date(start).getTime();
  end = new Date(end).getTime();

  return Math.abs((end - start) / 1000 / 60 / 60 / 24)
}
function dateDiffInHours(start, end){
  start = new Date(start).getTime();
  end = new Date(end).getTime();

  return Math.abs((end - start) / 1000 / 60 / 60)
}
function dateDiffInMinutes(start, end){
  start = new Date(start).getTime();
  end = new Date(end).getTime();

  return Math.abs((end - start) / 1000 / 60)
}

function timeDiffInHours(start_time, end_time){
  return dateDiffInHours(`2000-01-01 ${start_time}`, `2000-01-01 ${end_time}`)
}
function timeDiffInMinutes(start_time, end_time){
  return dateDiffInMinutes(`2000-01-01 ${start_time}`, `2000-01-01 ${end_time}`)
}

function isBetween(target, start, end){
  return target >= start && target <= end;
}
function isTimeBetween(target, start, end){
  const time = {
    target: target.split(':'),
    start: start.split(':'),
    end: end.split(':'),
  }

  target = new Date(2000, 1, 1, Number(time.target[0]), Number(time.target[1])).getTime();
  start = new Date(2000, 1, 1, Number(time.start[0]), Number(time.start[1])).getTime();
  end = new Date(2000, 1, 1, Number(time.end[0]), Number(time.end[1])).getTime();

  return target >= start && target < end;
}
function isTimeBetweenEquals(target, start, end){
  const time = {
    target: target.split(':'),
    start: start.split(':'),
    end: end.split(':'),
  }

  target = new Date(2000, 1, 1, Number(time.target[0]), Number(time.target[1])).getTime();
  start = new Date(2000, 1, 1, Number(time.start[0]), Number(time.start[1])).getTime();
  end = new Date(2000, 1, 1, Number(time.end[0]), Number(time.end[1])).getTime();

  return target >= start && target <= end;
}
function isDateBetween(target, start, end){
  target = new Date(target.slice(0, 10)).getTime();
  start = new Date(start.slice(0, 10)).getTime();
  end = new Date(end.slice(0, 10)).getTime();
  return target > start && target < end;
}
function isDateBetweenEquals(target, start, end){
  target = new Date(target.slice(0, 10)).getTime();
  start = new Date(start.slice(0, 10)).getTime();
  end = new Date(end.slice(0, 10)).getTime();
  return target >= start && target <= end;
}
function isTimeBigger(time, compare){
  time = new Date(`2000-01-01 ${time}`).getTime();
  compare = new Date(`2000-01-01 ${compare}`).getTime();

  return time >= compare;
}
function isTimeSmaller(time, compare){
  time = new Date(`2000-01-01 ${time}`).getTime();
  compare = new Date(`2000-01-01 ${compare}`).getTime();

  return time <= compare;
}
function isNumber(number){
  return !(Number(number) == Infinity || isNaN(Number(number)));
}
function isJson(str) {
  if(!str){ return false; }

  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
function showJson(data){
  if(typeof data === 'string' && isJson(data)){
    data = JSON.parse(data);
  }

  confirmModal({
    text: `<div data-json-viewer class="bg-inverse-secondary p-2 rounded-5 border" ></div>`,
    hideFooter: true,
    large: true,
    execute: () => {
      $('[data-json-viewer]').JSONView(data);
    },
  })
}
function isMobile(){
  return $(document).width() <= 768;
}

function carbonToDate(date){
  return convert(date, true);
}

function validateSession(){
  return $.ajax({
    type: "POST",
    async: false,
    url: `${url}/api/session/validate`
  });
}

function loader(text = ''){
  if (text){text = `<span class="mr-2" >${text}</span>`}
  $(".corner-loader").html(`<div class="corner-loader-content text-primary" >${text} <div class="spinner-border" role="status"><span class="sr-only"></span></div>`)
}
function successLoader(text = '', time = 1.5){
  $(".corner-loader").html(`<div class="corner-loader-content text-success" >${text}<i class="fa-solid fa-check scale-2 m-2"></i></div>`);
  emptyDivTimeout('.corner-loader', time * 1000);
}
function errorLoader(error = 'Er is iets foutgegaan!', time = 5){

  error = error || 'Er is iets foutgegaan!';

  if (error){error = `<span class="mr-2" >${error}</span>`}
  $(".corner-loader").html(`<div class="corner-loader-content text-danger" >${error}<i class="fa-solid fa-xmark scale-2 m-2"></i></div>`);
  emptyDivTimeout('.corner-loader', time * 1000);
}
function clearLoader(){
  $(".corner-loader").empty();
}

function addDynamicRow(target, options = {remove: true, naam: true, aantal: true, prijs: true, btw: true, incl: true, extra: []}){
  const string = randomString();

  let {remove, naam, aantal, prijs, btw, incl, extra} = options;

  remove = remove ?? true;
  naam = naam ?? true;
  aantal = aantal ?? true;
  prijs = prijs ?? true;
  btw = btw ?? true;
  incl = incl ?? true;
  extra = extra ?? [];

  const select = {
    percs: _settings.factuur_btw_percs,
    default: _settings.factuur_btw_perc,
    options: '',
    select: '',
  }
  for(const perc of select.percs){
    select.options += `<span class="select_edit-value" data-value="${perc}">${perc}%</span>`
  }
  select.select = `<div class="select_edit-container">
                  <input type="text" autocomplete="off" name="btw[${string}]" value="${select.default}" class="select_edit-input form-select row-btw" placeholder="BTW %">
                  <div class="select_edit-values">
                    <div class="select_edit-box">${select.options}</div>
                  </div>
                </div>`

  extra = (extra.join('')).replaceAll('{string}', string);

  $(target).append(
      `<tr class="${string}" >
         ${ remove ?  `<td><div class='btns-container d-flex mx--1'><a class="btn btn-sm btn-inverse-danger w-100 mx-1 tippy" data-tippy-content="Regel verwijderen" onclick="deleteDiv('.${string}')" ><i class="fa fa-xmark m-0"></i></a></div></td>` : '' }
         ${extra}
         ${ naam ?  `<td><input type="text" class="form-control-custom row-naam" placeholder="Naam" name="naam[${string}]"></td>` : '' }
         ${ aantal ?  `<td><input type="number" step="0.01" class="form-control-custom row-aantal" placeholder="Aantal" name="aantal[${string}]"></td>` : '' }
         ${ prijs ?  `<td><input type="number" step="0.01" class="form-control-custom row-prijs" placeholder="Bedrag" name="prijs[${string}]"></td>` : '' }
         ${ btw ?  `<td>${select.select}</td>` : '' }
         ${ incl ?  `<td class="text-center" ><input ${Number(_settings.factuur_standaard_incl_btw) ? 'checked' : ''} name="incl[${string}]" class="form-check-custom row-incl" type="checkbox"></td>` : '' }
       </tr>`
  );

  return string;
}

function pageInteractive(func = () => {}){
  return new Promise((resolve) => {

    if(document.readyState == 'interactive'){resolve();}

    document.addEventListener('readystatechange', (event) => {
      if(document.readyState == 'interactive'){
        func();
        resolve();
      }
    });
  })
}
function pageComplete(func = () => {}){
  return new Promise((resolve) => {

    if(document.readyState == 'complete'){resolve(); func();}

    document.addEventListener('readystatechange', (event) => {
      if(document.readyState == 'complete'){
        func();
        resolve();
      }
    });
  })
}

function cssEditor(element) {
  const editor = CodeMirror.fromTextArea($(element).get(0), {
    mode: 'css',
    lineNumbers: true,
    indentUnit: 2,
    hintOptions: {
      hint: CodeMirror.hint.cssSuggestions,
      completeSingle: false, // Ensures multiple suggestions are shown
      completeOnSingleClick: true, // Enables automatic completion on typing
    },
    extraKeys: {
      "Ctrl-Space": "autocomplete"
    },
    addons: ["hint/show-hint"],
  });

  editor.on("inputRead", function(instance) {
    if (instance.state.completionActive) {
      return;
    }
    var cur = instance.getCursor();
    var token = instance.getTokenAt(cur);
    if (token.type && token.type != "comment") {
      CodeMirror.commands.autocomplete(instance);
    }
  });

  return editor;
}

// Signature
const _signature = {
  modal: $('#get-signature-modal'),
  canvas: $('#get-signature'),
  context: $('#get-signature').get(0).getContext('2d'),
  spinner: $('.signature-spinner'),
  signatures: $('.signature-user-signatures'),
  signatures_state: false,
  color: _settings.environment_signature_default_color || '#000000',
  width: _settings.environment_signature_default_width || 1,
  line: 'round',
}
window.addEventListener('resize', resizeSignatureCanvas);

$(document).ready(getSignatureInit)
$(document).on('click', '.signature-color', function(){
  const color = $(this).attr('data-color');

  if(color == 'custom'){
    signatureCustomColor();
    return;
  }

  $('.signature-color').removeClass('active');
  $(this).addClass('active');

  _signature.context.strokeStyle = color;
});
$(document).on('input change', '.signature-width', function(){
  const w = this.value;

  _signature.context.lineWidth = w;
  $('.signature-width-line').css({
    padding: `${w}px 25px`
  })
});

function getSignatureInit() {
  const canvas = _signature.canvas.get(0);
  const context = _signature.context;

  context.strokeStyle = _signature.color
  context.lineCap = _signature.line;
  context.lineWidth = _signature.width;

  if ('ontouchstart' in document.documentElement) {
    const drawer = {
      isDrawing: false,
      touchstart: function (coors) {
        context.beginPath();
        context.moveTo(coors.x, coors.y);
        this.isDrawing = true;
      },
      touchmove: function (coors) {
        if (this.isDrawing) {
          context.lineTo(coors.x, coors.y);
          context.stroke();
        }
      },
      touchend: function (coors) {
        if (this.isDrawing) {
          this.touchmove(coors);
          this.isDrawing = false;
        }
      }
    };
    function draw(event) {
      if(!event.targetTouches.length){return;}
      const coors = {
        x: event.targetTouches[0].pageX,
        y: event.targetTouches[0].pageY
      };

      coors.y -= _signature.canvas.offset().top;
      coors.x -= _signature.canvas.offset().left;

      drawer[event.type](coors);
    }
    canvas.addEventListener('touchstart', draw, false);
    canvas.addEventListener('touchmove', draw, false);
    canvas.addEventListener('touchend', draw, false);
    canvas.addEventListener('touchmove', function (event) { event.preventDefault(); }, false);

    return;
  }

  _signature.canvas.mousedown(function (event) {

    const position = getPosition(event);

    context.moveTo(position.X, position.Y);
    context.beginPath();
    $(this).mousemove(function (event) { drawLine(event); });
    $(this).mouseup(function (event) { finishDrawing(event); })
    $(this).mouseout(function (event) { finishDrawing(event); });
  });

}
function getSignature(){
  _signature.spinner.removeClass('d-none');
  _signature.canvas.addClass('d-none');
  _signature.signatures.empty().toggle(false);
  _signature.signatures_state = false;

  $(document).off('click', '.reject-signature')
  $(document).off('click', '.resolve-signature')
  clearSignature();

  _signature.modal.showModal()
      .then(() => {
        _signature.spinner.addClass('d-none');
        _signature.canvas.removeClass('d-none');
        resizeSignatureCanvas();
        setSignatureValues();
      });

  return new Promise((resolve) => {
    $(document).on('click', '.reject-signature', () => {
      resolve({
        status: 0,
        src: null,
      });
      _signature.modal.hideModal();
    })
    $(document).on('click', '.resolve-signature', () => {
      resolve({
        status: 1,
        src: _signature.canvas.get(0).toDataURL(),
      });
      _signature.modal.hideModal();
    })
  })
}
function setSignatureValues(){
  _signature.context.lineCap = 'round';
  _signature.context.lineJoin = 'round';
  $('.signature-width').trigger('change');
  $('.signature-color.active').click();
}
function clearSignature(){
  const ctx = _signature.canvas.get(0).getContext('2d');
  ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
}
function getPosition(event) {
  let x, y;

  x = event.pageX - _signature.canvas.offset().left;
  y = event.pageY - _signature.canvas.offset().top;

  return { X: x, Y: y};
}
function drawLine(event) {

  const pos = getPosition(event);

  _signature.context.lineTo(pos.X, pos.Y);
  _signature.context.stroke();
}
function finishDrawing(event) {
  drawLine(event);

  _signature.context.closePath();
  _signature.canvas.unbind("mousemove")
      .unbind("mouseup")
      .unbind("mouseout");
}
function resizeSignatureCanvas() {
  const { context, canvas, color, line, width } = _signature

  let cWidth = canvas.get(0).offsetWidth
  let cHeight = cWidth*0.35;

  canvas.attr('width', cWidth).attr('height', cHeight);

  context.strokeStyle = color
  context.lineCap = line;
  context.lineWidth = width;
}
function signatureCustomColor(){
  _signature.modal.hideModal()
  getHexColor()
      .then((response) => {
        $('.signature-color[data-color=custom]').before(`<div class="signature-color ${randomString()}" style="background-color: ${response}" data-color="${response}"></div>`);
        $(`.${lastString()}`).click();
        _signature.modal.showModal();
      })
      .catch(() => {
        _signature.modal.showModal();
      })

}
function userSignatures(){

  if(_signature.signatures_state){
    _signature.signatures_state = false;
    _signature.signatures.toggle(400);
    return;
  }


  _signature.signatures_state = true;
  _signature.signatures.html(
      `<div class="my-3 py-2">
      <div class="text-center my-5">
        <div class="spinner-border text-primary" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div>
      </div>
    </div>`
  )
  fillUserSignatures();
  _signature.signatures.toggle(400);

}
function fillUserSignatures(){
  $.ajax({
    type: 'POST',
    url: `${url}/api/users/signatures/get`,
    data: {
      _token: "{{csrf_token()}}",
    },
    success: function (response) {
      _signature.signatures.html(`<div class="row my-2 mx-0 ${randomString()}"></div>`);
      for(const signature of response.signatures){
        $(`.${lastString()}`).append(
            `<div class="col-lg-2 col-md-3 col-sm-4 col-6" ><img class="w-100 rounded bg-white my-2 cursor-pointer hoverDeselect" onclick="selectSignature('${signature.signature}')" style="min-height: 35px;" src="${url}/api/file/${signature.signature}" ></div>`
        )
      }
    },
    error: function () {
      errorLoader()
    }
  });
}
function storeUserSignature(){
  loader();
  $.ajax({
    type: 'POST',
    url: `${url}/api/users/signatures/store`,
    data: {
      data: _signature.canvas.get(0).toDataURL(),
      _token: "{{csrf_token()}}",
    },
    success: function () {
      if(_signature.signatures_state){fillUserSignatures()}
      else{userSignatures();}
      successLoader()
    },
    error: function () {
      errorLoader();
    }
  });
}
function selectSignature(src){
  const img = new Image();

  img.onload = () => {
    clearSignature();
    _signature.context.drawImage(img, 0, 0, img.width,    img.height, 0, 0, _signature.canvas.width(), _signature.canvas.height());
  }
  img.src = `${url}/api/file/${src}`
}

function compress(base, perc = 100){
  const img = new Image();
  const string = randomString();

  return new Promise(function(resolve){
    img.onload = function(){
      const w = img.width * (perc / 100)
      const h = img.height * (perc / 100)

      const canvas = document.createElement('canvas');
      canvas.id = string;
      canvas.width = w;
      canvas.height = h;
      canvas.style.display = 'none';
      $('body').append(canvas);

      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, w, h);

      $(`#${string}`).height(img.height);
      $(`#${string}`).width(img.width);

      resolve($(`#${string}`)[0].toDataURL());
    };
    base.then((b) => {img.src = b;})
  })
}

//Crop image
const _croppie =  {
  modal: $('#getCroppedImageModal'),
  instance: null,
  interval: null,
  options: null,
};
function getCroppedImage(options = {
  src: null,
  enableOrientation: true,
  viewport: {
    width: 500,
    height: 500,
  },
  boundary: { height: 550 },
  useImgRes: false,
  showResChange: false,
}) {
  clearExistingCroppieInstance();

  if (!options.enableOrientation) options.enableOrientation = true;
  if (!options.viewport) options.viewport = { width: 500, height: 500 };
  if (!options.boundary) options.boundary = { height: 550 };

  return new Promise(function (resolve, reject) {
    const initializeCroppie = (base) => {
      _croppie.modal.find('.modal-body').html(`
        <div class="my-2">
          <img id="getCroppedImageCroppie" src="${base}">
        </div>
        <div class="my-2 d-flex justify-content-between align-items-end">
          <div>
            <a class="btn btn-primary text-white cursor-pointer mx-1 getCroppedImageRotate" data-rotate="90"><i class="fas fa-undo-alt m-0"></i></a>
            <a class="btn btn-primary text-white cursor-pointer mx-1 getCroppedImageRotate" data-rotate="-90"><i class="fas fa-redo-alt m-0"></i></a>
          </div>
          <div class="d-flex">
            ${options.showResChange ? `
              <div class="d-flex mx-1">
                <div class="mx-1 max-w-150">
                  <span>Hoogte</span>
                  <input type="number" class="form-control-custom gci-res vp-h" placeholder="Hoogte">
                </div>
                <div class="mx-1 max-w-150">
                  <span>Breedte</span>
                  <input type="number" class="form-control-custom gci-res vp-w" placeholder="Breedte">
                </div>
              </div>` : ''
      }
            <div class="mx-1">
              <span>Kwaliteit</span>
              <select class="form-select gci-quality">
                <option value="100">Heel hoge kwaliteit</option>
                <option value="85">Hoge kwaliteit</option>
                <option value="60" selected>Gemiddeld</option>
                <option value="50">Lage kwaliteit</option>
                <option value="30">Heel lage kwaliteit</option>
              </select>
            </div>
          </div>
          <div>
            <a class="btn btn-danger text-white cursor-pointer mx-1 getCroppedImageReject"><i class="fas fa-times m-0"></i></a>
            <a class="btn btn-success text-white cursor-pointer ml-1 getCroppedImageResolve"><i class="fas fa-check m-0"></i></a>
          </div>
        </div>
      `);

      const img = new Image();
      img.onload = function () {
        if (options.useImgRes) {
          _croppie.modal.find('.vp-h').val(this.height);
          _croppie.modal.find('.vp-w').val(this.width);
          options.viewport.height = 500;
          options.viewport.width = Number((500 / this.height * this.width).toFixed(0));
          options.boundary = { height: 550 };
        } else {
          _croppie.modal.find('.vp-h').val(options.viewport.height);
          _croppie.modal.find('.vp-w').val(options.viewport.width);
        }

        _croppie.options = options;
        _croppie.instance = $("#getCroppedImageCroppie").croppie(options);

        _croppie.instance.croppie('bind', {
          url: base
        }).then(() => {
          const vpW = options.viewport.width;
          const vpH = options.viewport.height;
          const imgW = this.width;
          const imgH = this.height;

          const zoom = Math.min(vpW / imgW, vpH / imgH);

          _croppie.instance.croppie('setZoom', zoom);
        });

        $(document).off('click.getCroppedEvents');

        $(document).on('click.getCroppedEvents', '.getCroppedImageRotate', function () {
          const rotate = $(this).data('rotate');
          _croppie.instance.croppie("rotate", rotate);
          _croppie.instance.croppie("setZoom", 0);
        });

        $(document).on('click.getCroppedEvents', '.getCroppedImageResolve', function () {
          compress(_croppie.instance.croppie("result"), $('.gci-quality').val())
              .then((base) => {
                resolve(base);
                _croppie.modal.hideModal();
              });
        });

        $(document).on('click.getCroppedEvents', '.getCroppedImageReject', function () {
          reject(null);
          _croppie.modal.hideModal();
        });
      };
      img.src = base;
    };

    _croppie.modal.showModal();

    if (options.src) {
      _croppie.modal.find('.modal-body').html('<div class="text-center my-4">Afbeelding laden...</div>');
      initializeCroppie(options.src);
    } else {
      _croppie.modal.find('.modal-body').html(`
        <input id="getCroppedImageInput" type="file" class="dropify" data-height="400" data-allowed-file-extensions="jpeg jpg png"/>
      `);
      dropifyInit();

      _croppie.interval = setInterval(function () {
        const input = $('#getCroppedImageInput');
        if (input.prop('files').length) {
          clearInterval(_croppie.interval);
          const file = input.prop('files')[0];
          const reader = new FileReader();
          reader.onload = () => initializeCroppie(reader.result);
          reader.readAsDataURL(file);
        }
      }, 250);
    }
  });
}


function clearExistingCroppieInstance(){
  clearInterval(_croppie.interval);

  $(document).off('click', '.getCroppedImageRotate')
  $(document).off('click', '.getCroppedImageResolve')
  $(document).off('click', '.getCroppedImageReject')

  if(_croppie.instance){
    _croppie.instance.croppie("destroy");
    _croppie.instance = null;
  }
}
$(document).on('change', '.gci-res', function(){
  _croppie.instance.croppie('destroy')
  const h = _croppie.modal.find('.modal-body').find('.vp-h').val();
  const w = _croppie.modal.find('.modal-body').find('.vp-w').val();

  _croppie.options.viewport.height = 500
  _croppie.options.viewport.width = Number((500 / h * w).toFixed(0));
  _croppie.options.boundary = {height: 550};

  _croppie.instance = $("#getCroppedImageCroppie").croppie(_croppie.options);
  setTimeout(() => {_croppie.instance.croppie('setZoom', '0.1');}, 10)
});

//Hex color
const _hexColor = {
  modal: $('#getHexColorModal'),
  instance: null,
}
$(document).ready(getHexColorInit)
function getHexColorInit(){
  _hexColor.instance = new iro.ColorPicker("#getHexColorContainer", {
    display: "inline-block"
  });
}
function getHexColor(options = {color: null, extra_content: ''}){
  clearExistingHexInstance();

  let {color, extra_content} = options;

  color = color || '#FFFFFF';
  extra_content = extra_content || '';

  _hexColor.instance.color.hexString = color;
  _hexColor.modal.find('.extra-content').html(extra_content);
  _hexColor.modal.showModal();

  return new Promise(function(resolve, reject) {
    $(document).on('click', '.getHexColorResolve', function(){
      _hexColor.modal.hideModal();
      resolve(_hexColor.instance.color.hexString);
    })
    $(document).on('hidden.bs.modal', '#getHexColorModal', () => {
      _hexColor.modal.hideModal();
      reject('Color picker closed')
    })
  });
}
function clearExistingHexInstance(){
  $(document).off('click', '.getHexColorResolve')
  $(document).off('click', '.getHexColorReject')
}

//Exploer file
const _explorer = {
  upload_modal: $('#uploadExplorerFile'),
  upload_iframe_container: $('[data-explorer-upload-iframe-container]'),
  listener: false,
  callback: null,
}
function openExplorerPath(options = {}){
  const path = options.path || (_cookie('explorer_path') || '/');
  const action= options.action || 'DOWNLOAD';
  const select_key= options.select_key || null;

  _explorer.callback = options.callback || null;

  if(!_explorer.listener){
    _explorer.listener = true;
    _addMessageEventListener(event => {
      if(event?.data?.key != "EXPLORER_FILES_CHANGE" || typeof _explorer.callback !== 'function'){ return; }
      _explorer.callback();
    });
  }

  _cookieSet('explorer_path', path);

  window.open(`${url}/iframe/explorer?action=${action}${select_key ? `&select_key=${select_key}` : ''}`,'newwindow','width=1100,height=800');
}
function getExplorerFile(options= {}){
  return new Promise(resolve => {
    const select_key = randomString(15);

    openExplorerPath({
      path: options.path || (_cookie('explorer_path') || '/'),
      action: 'SELECT',
      select_key: select_key,
    });

    _addMessageEventListener(event => {
      if(event?.data?.key != select_key){ return; }
      resolve({
        status: 1,
        file: event.data.file,
      });
    })

  })
}
function uploadExplorerFile(options = {path: '/', captcha: false}){
  return new Promise(resolve => {
    const file_key = randomString(10);
    let { path, params, select, multiple } = options;

    path = path || '/';
    select = select || false;
    multiple = multiple || false;
    params = params || [];

    _explorer.upload_iframe_container.html(`<iframe src="${url}/iframe/explorer/upload?file_key=${file_key}&select=${select}&multiple=${multiple}${params.join()}&path=${path || ''}" style="height: 675px" ></iframe>`);
    _explorer.upload_modal.showModal();

    _addMessageEventListener(event => {
      if(event?.data?.key != file_key){ return; }

      resolve({
        status: 1,
        file: event.data.file,
        files: event.data.files,
        select: event.data.select || false,
      });
      _explorer.upload_modal.hideModal();
    })

    $(document).off('hidden.bs.modal', '#uploadExplorerFile');
    $(document).on('hidden.bs.modal', '#uploadExplorerFile', () => {
      _explorer.upload_iframe_container.empty();
      resolve({
        status: 0,
        file: null,
        files: [],
        select: false,
      });
    })
  });
}
function getExplorerFileInstance(options = {}){
  return new Promise(resolve => {
    ajax('api/explorer/get', options)
        .then(response => {
          resolve(response);
        })
        .catch(handleCatchError);
  })
}
function explorerPathExists(path){
  return new Promise(resolve => {
    ajax('api/explorer/pathexists', { path: path })
        .then(response => {
          const { exists } = response;

          resolve(!!Number(exists));
        })
        .catch(handleCatchError);
  })
}
function appendExplorerFile(options = {file: null, id: null, name: null}){
  let { file, id, name, callback } = options;
  const string = randomString(15);

  callback = callback || `deleteDiv('#${string}')`;

  $(id).append(
      `<div id="${string}" class="p-1 d-flex justify-content-between text-decoration-none" >
      <div class="d-flex align-items-center" >
        <img height="40" src="${url}/client/public/img/explorer/files/${file.icon}" >
        <div>
          <a class="my-0 mx-2 font-size-09 d-block" href="${url}/api/file/explorer/files/${file.src}" target="_blank" >${file.name}</a>
          <a class="my-0 mx-2 font-size-075 text-muted cursor-pointer hover-text-underline" onclick="openExplorerPath({ path: '${file.path}', })" >${file.path}</a>
        </div>
      </div>
      <a class="btn text-danger align-self-center" onclick="${callback}" ><i class="fa fa-xmark m-0"></i></a>
      ${name ? `<input type="hidden" value="${file.id}" name="${name}">` : ''}
    </div>`
  );
}
function getExtensions(){
  return {
    img: {jpg: true, jpeg: true, png: true, gif: true, svg: true, webp: true, tiff: true, bitmap: true, EPS: true},
    mp3: {mp3: true, aac: true, flac: true, alac: true, wav: true, aiff: true, dsd: true, pcm: true},
    mp4: {mp4: true, mov: true, wmv: true, avi: true, avchd: true, flv: true,  f4v: true, swf: true, mkv: true, webm: true},
    pdf: {pdf: true},
    stat: {csv: true, dat: true, db: true, dbf: true, log: true, mdb: true, sav: true, sql: true, tar: true, xml: true},
    txt: {doc: true, docx: true, odt: true, rtf: true, tex: true, txt: true, wpd: true},
    xlsx: {ods: true, xls: true, xlsm: true, xlsx: true},
    zip: {"7z": true, arj: true, deb: true, pkg: true, rar: true, rpm: true, tar: true, gz: true, z: true, zip: true}
  };
}
function getExtensionIcon(ext){
  ext = ext.toLowerCase();

  const extensions = getExtensions();
  for(const group in extensions){
    if(extensions[group][ext]){ return `${group}.png`; }
  }

  return 'und.png';
}

//Search
function searchProjecten(string){
  return ajax('/api/projecten/search', {search: string});
}
function searchOffertes(string){
  return ajax('/api/offertes/search', {search: string});
}
function searchKluisregels(string){
  return ajax('/api/wachtwoordkluis/search', {search: string});
}
function searchWerkbonnen(string){
  return ajax('/api/werkbonnen/search', {search: string});
}
function searchKlanten(string){
  return ajax('/api/klanten/search', {search: string});
}
function searchFacturen(string){
  return ajax('/api/facturatie/search', {search: string, proforma: 0});
}
function searchProformas(string){
  return ajax('/api/facturatie/search', {search: string, proforma: 1});
}
function searchLeveranciers(string){
  return ajax('/api/leveranciers/search', {search: string});
}
function searchInkoopbonnen(string){
  return ajax('/api/inkoopbonnen/search', {search: string});
}
function searchInkoopfacturen(string){
  return ajax('/api/inkoopfacturen/search', {search: string});
}

//Iframe
function iframeInit(){
  parent.postMessage(JSON.stringify({iframe: true}), '*', );
}
function isIframe() {
  return window.self !== window.top;
}

//Abonnementen
function showVerenigingen(){
  window.open(`iframe/abonnementen/verenigingen`, 'newwindow','width=515,height=800');
}

//Offertes
function getOffertes(options = {}){
  return ajax('api/offertes/get', options)
}
function showOfferte(id){
  window.open(`${url}/offertes/edit/${id}/preview`, '_blank').focus();
}

//Klanten
function getKlant(id, options = {}){
  options['id'] = id || 0;
  return ajax('api/klanten/get', options);
}
function getKlanten(options = {}){
  return ajax('api/klanten/get', options)
}
function newKlant(){
  window.open(`${url}/iframe/klanten/create/window`,'newwindow','width=515,height=800');
}
function editKlant(id){
  window.open(`${url}/iframe/klanten/edit/window/${id}`,'newwindow','width=515,height=800');
}
function showKlant(id){
  window.open(`${url}/iframe/klanten/show/window/${id}`,'newwindow','width=515,height=800');
}
function editKlantContactpersonen(id){
  window.open(`${url}/iframe/klanten/contactpersonen/edit/window/${id}`,'newwindow','width=515,height=800');
}

//Leveranciers
function getLeverancier(id, options = {}){
  options['id'] = id || 0;
  return ajax('api/leveranciers/get', options);
}
function getLeveranciers(options = {}){
  return ajax('api/leveranciers/get', options)
}
function showLeverancier(id){
  window.open(`${url}/iframe/leveranciers/show/${id}`,'newwindow','width=515,height=800');
}

//Pojecten
function getProject(id, options = {}){
  options['id'] = id || 0;
  return ajax('api/projecten/get', options);
}
function getProjecten(options = {}){
  return ajax('/api/projecten/get', options);
}
function getProjectRelation(id, relation){ return ajax('/api/projecten/getrelation', { id: id, relation: relation }); }
function showProject(id){
  window.open(`${url}/iframe/uren/projectnummers/${id}/preview`,'newwindow','width=515,height=800');
}
function showProjectUren(id, startDate, endDate){

  confirmModal({
    text: `<div style="height: 600px"><iframe src="${url}/iframe/uren/projecturen/${id}/${startDate}/${endDate}"></iframe></div>`,
    large: true,
    hideFooter: true,
  });
}
//Checklisten
function showChecklist(template_id, id){
  window.open(`${url}/iframe/checklists/${template_id}/${id}/preview`,'newwindow','width=515,height=800');
}
function editProjectTaken(id){
  window.open(`${url}/iframe/projecten/taken/edit/${id}`,'newwindow','width=515,height=800');
}

//Uren
function getUren(options = {}){
  return ajax('api/urenregistratie/get', options);
}

//Werkbonnen
function getWerkbonnen(options = {}){
  return ajax('/api/werkbonnen/get', options);
}
function showWerkbonnen(ids){
  confirmModal({
    text: `<div style="height: 600px"><iframe src="${url}/iframe/werkbonnen/show?ids=${ids}"></iframe></div>`,
    large: true,
    hideFooter: true,
  });
}

//Facturen
function getFactuur(id, options = {}){
  options['id'] = id || 0;
  return ajax('/api/facturatie/get', options);
}
function getFacturen(options = {}){
  return ajax('/api/facturatie/get', options);
}

//Inkoopbonnen
function getInkoopbon(id, options = {}){
  options['id'] = id;
  return ajax('/api/inkoopbonnen/get', options);
}
function getInkoopbonnen(options = {}){
  return ajax('/api/inkoopbonnen/get', options);
}

//Inkoopfacturen
function getInkoopfactuur(id, options = {}){
  options['id'] = id || 0;
  return ajax('/api/inkoopfacturen/get', options);
}
function getInkoopfacturen(options = {}){
  return ajax('/api/inkoopfacturen/get', options);
}

//Planning
function getPlanning(options = {}){
  return ajax('/api/planning/get', options);
}
function showPlanning(id){
  window.open(`${url}/iframe/planning/preview/${id}`,'newwindow','width=515,height=800');
}

//Users
function getUsers(options = {}){
  return ajax('api/users/get', options)
}

//Dynamic confirm modal
const _dynamicConfirmModal = {
  modal: $('#dynamic-confirm-modal'),
  content: $('#dynamic-confirm-modal').find('.modal-body'),
  footer: $('#dynamic-confirm-modal').find('.modal-footer'),
}
const _dynamicConfirmModalLarge = {
  modal: $('#dynamic-confirm-modal-large'),
  content: $('#dynamic-confirm-modal-large').find('.modal-body'),
  footer: $('#dynamic-confirm-modal-large').find('.modal-footer'),
}
function confirmModal(options = {text: '', large: false, btnColor: '', btnText: '', btnRejectState: false, btnRejectColor: false, btnRejectText: false, hideFooter: false, execute: null, validate: null}) {
  const string = randomString();

  let {text, large, btnColor, btnText, btnRejectState, btnRejectColor, btnRejectText, hideFooter, execute, validate} = options;

  large = large || false;

  btnColor = btnColor || 'btn-success';
  btnText = btnText || 'Bevestigen';

  btnRejectState = btnRejectState || false;
  btnRejectColor = btnRejectColor || 'btn-danger';
  btnRejectText = btnRejectText || 'Annuleren';

  hideFooter = hideFooter || false;

  const modal = large ? _dynamicConfirmModalLarge : _dynamicConfirmModal;

  modal.content.html(text);
  modal.footer.html(
    `<div>
      ${btnRejectState ? `<a class="btn ${btnRejectColor} text-white reject-${string}" >${btnRejectText}</a>` : ''}
      <a class="btn ${btnColor} text-white resolve-${string}" >${btnText}</a>
    </div>`
  );

  modal.footer.toggleClass('d-none', hideFooter);
  modal.modal.showModal();

  if(execute){
    execute();
  }

  return new Promise(function(resolve){
    $(`.reject-${string}`).click(() => {
      modal.modal.hideModal();
      resolve({status: false});
    });

    $(`.resolve-${string}`).click(() => {
      const inputs = {};

      modal.modal.find('input').each((index, input) => {
        input = $(input);
        const name = input.attr('name');
        const type = input.attr('type');
        if(!name){return}

        let value = input.val();
        if(type === 'checkbox'){
          value = input.prop('checked');
        }
        else if(type === 'file'){
          value = input.prop('files');
        }

        inputs[name] = value;
      });

      if (typeof validate === 'function') {
        const isValid = validate(inputs);
        if (!isValid) {
          return;
        }
      }

      modal.modal.hideModal();
      resolve({
        status: true,
        inputs: inputs
      });
    });

    modal.modal.on('hidden.bs.modal', () => {
      resolve({status: false});
    });
  });
}

//Page loader
$(".append-loader").each(function(){
  const string = 'loader-'+randomString(15);
  const no_margin = $(this).data('no-margin') !== undefined;

  $(this).addClass([string]).after(
      `<div class="${no_margin ? '' : 'my-3'} py-2 loader_div" data-target=".${string}"  >
      <div class="text-center ${no_margin ? '' : 'my-5'}">
        <div class="spinner-border text-primary" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div>
      </div>
    </div>`
  )
})
document.addEventListener('readystatechange', (event) => {
  if(document.readyState == 'complete'){
    $('.loader_div').each(function(){
      $($(this).attr('data-target')).removeClass(['append-loader', 'd-none']);
      $(this).remove();
    });
  }
});

//Autosize
$(document).on('input', '.autosize', function(e){
  const textarea = $(this);

  textarea.css({'height': ''})

  const diff =  textarea.outerHeight() - textarea.innerHeight();
  const scrollHeight = textarea.prop('scrollHeight');

  $(this).css({
    height: `${scrollHeight + diff}px`,
  });
});

$(document).click(hideSearchables);
function hideSearchables(){
  selectSearchVars.active = false;
  $(".select_group-values").removeClass('show');
  $(".select_search-values").removeClass('show');
  $(".select_edit-values").removeClass('show');
  $(".select_multiple-values").removeClass('show');
}

//Editable select
$(document).on('keyup', '.select_edit-input', function(){
  const string = $(this).val().replaceAll(' ', '').toLowerCase();
  const container = findContainer('select_edit-container', this);

  $('.select_edit-value').removeClass('select_edit-value-hide');
  $(container).find('.select_edit-value').each(function(){
    const text = $(this).text().replaceAll(' ', '').toLowerCase();
    if(!text.includes(string)){
      $(this).addClass('select_edit-value-hide');
    }
  })
})
$(document).on('click', '.select_edit-input', function(event){
  event.stopPropagation();
  hideSearchables();

  const container = findContainer('select_edit-container', $(this));

  $('.select_edit-value').removeClass('d-none');
  $('.select_edit-values').removeClass('show');
  $(container).find('.select_edit-values').addClass('show');
  overflowXOnlyInit();
})
$(document).on('click', '.select_edit-value', function(){
  const value = $(this).attr('data-value');
  const container = findContainer('select_edit-container', this);
  $(container).find('.select_edit-input').val(value).trigger('change');
  overflowXOnlyRemove();
})

//Searchable select
var selectSearchVars = {
  active: false,
  container: null,
  index: null,
}
function initSelectSearch(){
  $(".select_search-hidden-input").each(function(){
    const initiated = $(this).attr('select_search-initiated');

    if(initiated){return true}

    const container = findContainer('select_search-container', this);
    const box = $(container).find(".select_search-box");

    const placeholder = $(this).attr("data-placeholder") || '';
    const required = $(this).attr("data-required") || '';

    $(this).attr("select_search-initiated", true);
    $(box).prepend(`<div><input class="border-0 form-control form-control-plaintext select_search-search" placeholder="Zoeken..." ></div>`)

    let value = "";
    let name = "";

    $(container).find('.select_search-value').each(function(){
      const selected = $(this).attr('data-selected');
      if(!selected){return true;}

      name = $(this).attr('data-name');
      value = $(this).attr('data-value');

    });

    $(this).before(`<input value="${name}" class="form-select select_search-input" placeholder="${placeholder}" readonly ${required} >`)
    $(this).val(value);
  });
}
function correctSelectSearchDisplay(){
  $(".select_search-hidden-input").each(function() {
    const container = findContainer('select_search-container', this);

    const value = container.find('.select_search-hidden-input').val();
    const option = container.find(`.select_search-value[data-value='${value}']`);

    if (option.length){
      container.find('.select_search-input').val(option.attr('data-name') || '');
    }
  })
}
$(document).ready(initSelectSearch);
$(document).keydown(function(event){
  if(!selectSearchVars.active){return;}
  if(event.key != "ArrowUp" && event.key != "ArrowDown" && event.key != "Enter"){return;}

  const children = $(selectSearchVars.container).find('.select_search-value').not('.select_search-value-hide');

  if(event.key == "Enter"){
    event.preventDefault();
    $(children).eq(selectSearchVars.index).click();
    return false;
  }

  if(event.key == 'ArrowDown'){
    if(selectSearchVars.index === null || selectSearchVars.index == ($(children).length - 1)){
      selectSearchVars.index = 0;
    }
    else{
      selectSearchVars.index += 1
    }
  }
  else if(event.key == 'ArrowUp'){
    if(selectSearchVars.index === null || selectSearchVars.index === 0){
      selectSearchVars.index = ($(children).length - 1);
    }
    else{
      selectSearchVars.index += -1;
    }
  }


  $(children).removeClass('focus');
  $(children).eq(selectSearchVars.index).addClass('focus')

  $(selectSearchVars.container).find('.select_search-box').get(0).scroll({top: ((selectSearchVars.index * 40) - 160), behavior: 'smooth'})

});
$(document).on('click', '.select_search-search', function(event){
  event.stopPropagation();
})
$(document).on('input', '.select_search-search', function(event){
  const string = $(this).val().toLowerCase().replaceAll(_global.regex_ignore, '');
  const words = string.split(' ');
  const container = findContainer('select_search-container', this);

  if(event.key != "ArrowUp" && event.key != "ArrowDown"){
    selectSearchVars.index = null;
    $('.select_search-value').removeClass(['select_search-value-hide', 'focus']);

    if(!words.length){ return; }

    $(container).find('.select_search-value').each(function(){
      const text = $(this).text().toLowerCase().replaceAll(_global.regex_ignore, '');
      for(const word of words){
        if (!text.includes(word)){
          $(this).addClass('select_search-value-hide');
        }
      }
    })
  }

})
$(document).on('click', '.select_search-input', function(event){
  event.stopPropagation();
  hideSearchables();

  const container = findContainer('select_search-container', this);
  const valuesContainer = container.find('.select_search-values');

  $('.select_search-values').not(valuesContainer).removeClass('show')

  selectSearchVars.container = container;
  selectSearchVars.index = null;

  if(valuesContainer.hasClass('show')){
    valuesContainer.removeClass('show');
    selectSearchVars.active = false;
    overflowXOnlyInit();
    return;
  }

  selectSearchVars.active = true;

  $(container).find('.select_search-box').css({minWidth: `calc(${$(this).width()}px + 3rem + 2px)`});
  $(container).find('.select_search-value').removeClass(['select_search-value-hide', 'focus']);
  $(container).find('.select_search-values').addClass('show');
  $(container).find('.select_search-search').val("").focus();
  overflowXOnlyInit();

})
$(document).on('click', '.select_search-value', function(){
  const container = findContainer('select_search-container', this);
  const disabled = $(this).attr('data-disabled');

  if(disabled){
    $(container).find('.select_search-values').removeClass("show");
    return false;
  }

  const name = $(this).attr('data-name');
  const value = $(this).attr('data-value');

  $(container).find('.select_search-input').val(name).trigger('change');
  $(container).find('.select_search-hidden-input').val(value).trigger('change');
  overflowXOnlyRemove();
})

//Multiple select
var multipleSelect = {
  active: false,
  container: null,
  index: null,
}
function initSelectMultiple(){
  $(".select_multiple-container").each(function(){
    const container = $(this);

    if(container.data('initiated')){ return true; }

    const values = container.find('.select_multiple-value').detach();
    const placeholder = container.data('placeholder');
    const rounded = container.data('rounded') !== undefined;

    let font_size = container.data('font-size');


    container.html(`
        <input class="form-select select_multiple-input bg-unset ${rounded ? 'rounded-10' : ''}" readonly placeholder="${placeholder || ''}" style="${font_size ? `font-size: ${font_size}rem` : ''}" >
        <div class="select_multiple-values" >
          <div class="select_multiple-box ${rounded ? 'rounded-10' : ''}" style="${font_size ? `font-size: ${font_size}rem` : ''}" >
            <div><input class="border-0 form-control form-control-plaintext select_multiple-search" placeholder="Zoeken..." ></div>
        </div>
      </div>
    `)

    container.find('.select_multiple-box').append(values);
    container.find('.select_multiple-value').prepend('<div class="select_multiple-value-indicator text-success v-center" > <i class="fa fa-check m-0"></i> </div>')
    container.attr('data-initiated', true);
  });

  refreshSelectMultipleIndicators();
}
function refreshSelectMultipleIndicators(){
  $(".select_multiple-container").each(function() {
    const container = $(this);
    const nodes = container.find('.select_multiple-value');
    const values = [];

    nodes.each(function(){
      const node = $(this);
      const input = $(this).find('input');
      const indicator = $(this).find('.select_multiple-value-indicator');
      const checked = input.prop('checked');

      node.toggleClass('bg-inverse-success', checked);
      indicator.toggleClass('active', checked);
      if(checked){ values.push(input.data('name')); }
    })

    container.find('.select_multiple-input').val(values.join(', '));
  })
}
$(document).ready(initSelectMultiple);
$(document).on('click', '.select_multiple-input', function(event){
  event.stopPropagation();
  hideSearchables();

  const container = findContainer('select_multiple-container', this);

  $(container).find('.select_multiple-values').addClass('show');
  $(container).find('.select_multiple-box').css({minWidth: `calc(${$(this).width()}px + 3rem + 2px)`});
  $(container).find('.select_multiple-search').focus().val('').trigger('keyup');
  overflowXOnlyInit();


});
$(document).on('click', '.select_multiple-value', function(event){
  refreshSelectMultipleIndicators();
  event.stopPropagation();
});
$(document).on('click', '.select_multiple-search', function(event){
  event.stopPropagation();
})
$(document).on('input', '.select_multiple-search', function(){
  const string = $(this).val().toLowerCase().replaceAll(_global.regex_ignore, '');
  const words = string.split(' ');
  const container = findContainer('select_multiple-container', this);

  $('.select_multiple-value').removeClass(['select_multiple-value-hide']);
  if(!words.length){ return; }

  $(container).find('.select_multiple-value').each(function(){
    const text = $(this).text().toLowerCase().replaceAll(_global.regex_ignore, '');
    for(const word of words){
      if (!text.includes(word)){
        $(this).addClass('select_multiple-value-hide');
      }
    }
  })

});


//Group select
function initSelectGroup(){
  $(".select_group-hidden-input").each(function(){
    const initiated = $(this).attr('select_group-initiated');
    if(!initiated){
      const container = findContainer('select_group-container', this);
      const box = $(container).find(".select_group-box");


      const prefill = $(this).attr("data-input-prefill") || '';
      const placeholder = $(this).attr("data-placeholder");
      const required = $(this).attr("data-required");

      $(this).attr("select_group-initiated", true);
      $(box).append(`<div><input class="form-control select_group-search" placeholder="Zoeken..." ></div>`)

      $(container).find(".select_group-value").each(function(){
        const name = $(this).attr('data-group');
        const group = name.toLowerCase().replaceAll(' ', '');
        const optionValue = $(this).attr('data-value');
        const optionName = $(this).attr('data-name');

        if(!$(box).find('.group-'+group).length){
          $(box).append(
              `<div class="group-container group-${group}">
            <div class="group-header">
                <span>${name}</span>
                <span><i class="fa fa-chevron-down m-0"></i></span>
            </div>
            <div class="group-content"></div>
          </div>`
          );
        }
        const element = $(this).detach();
        const groupBox = $(box).find(`.group-${group}`).find(".group-content");
        $(element).appendTo(groupBox);
      });
      $(this).before(`<input value="${prefill}" class="form-select select_group-input" placeholder="${placeholder}" readonly ${required} >`)
    }
  });
}
function resetSelectGroupSearch(container){
  $(container).find(".group-container").removeClass(['active', 'd-none']);
  $(container).find(".group-content").removeClass('show');
  $(container).find(".select_group-value").removeClass('d-none');
  $(container).find(".select_group-search").val('');
}
$(document).ready(initSelectGroup);
$(document).on('keyup', '.select_group-search', function(){
  const string = this.value.replaceAll(' ', '').toLowerCase();
  const container = findContainer('select_group-container', this);

  if(!string || string == ''){
    resetSelectGroupSearch(container);
    return;
  }

  $(container).find(".group-container").each(function(){
    let oneIsVisible = false;

    $(this).addClass("active");
    $(this).find('.group-content').addClass("show");

    $(this).find('.select_group-value').each(function(){
      const text = this.innerText.replaceAll(' ', '').toLowerCase();
      if(text.includes(string)){
        oneIsVisible = true;
        $(this).removeClass('d-none');
      }
      else{
        $(this).addClass('d-none');
      }
    });

    if(oneIsVisible){
      $(this).removeClass("d-none");
    }
    else{
      $(this).addClass("d-none");
    }

  });

})
$(document).on('click', '.select_group-search', function(e){
  e.preventDefault();
  e.stopPropagation();

})
$(document).on('click', '.select_group-container .group-container', function(e){
  e.preventDefault();
  e.stopPropagation();

  const container = findContainer('select_group-container', this);

  const element = $(this).find('.group-content');
  $(container).find('.group-content').not(element).removeClass("show");
  $(container).find('.group-container').not(this).removeClass("active");

  $(element).toggleClass("show");
  $(this).toggleClass("active");

  overflowXOnlyInit();

})
$(document).on('click', '.select_group-input', function(event){
  const container = findContainer('select_group-container', $(this));
  hideSearchables();
  resetSelectGroupSearch(container);

  $('.select_group-values').removeClass('show');
  $(".group-content").removeClass("show");
  $(".group-container").removeClass("active");

  $(container).find('.select_group-values').addClass('show');
  $(container).find('.select_group-box').css({minWidth: `calc(${$(this).width()}px + 3rem + 2px)`});
  $(container).find('.select_group-search').focus();
  overflowXOnlyInit();

  event.stopPropagation();
})
$(document).on('click', '.select_group-value', function(){
  const name = $(this).attr('data-name');
  const value = $(this).attr('data-value');
  const container = findContainer('select_group-container', this);

  $(container).find(".select_group-values").removeClass("show");
  $(container).find('.select_group-input').val(name).trigger('change');
  $(container).find('.select_group-hidden-input').val(value).trigger('change');
  overflowXOnlyRemove();

})

//Display select
var _displaySelect = {}

// Display Select mus be initiated before $(document).ready
// Since the DOM is not fully loaded the dimensions are incorrent
// therefore correctDisplay is required once the DOM is fully loaded
initDisplaySelect()
pageComplete()
    .then(res => correctDisplaySelect());

function initDisplaySelect(){
  $('[data-display-select-container]').each(function(){
    const container = $(this);
    const id = container.data('display-select-container');
    const can_be_empty = this.hasAttribute('data-can-be-empty');

    if(!container.find('.ds-node[data-selected]').length){
      container.find('.ds-node').eq(0).attr('data-selected', '')
    }
    const node = container.find('.ds-node[data-selected]');
    if(!node.length && !can_be_empty){
      container.html('<div class="ds-placeholder" >Geen Data</div>')
    }

    const base = {
      nodeValue: node.length ? node.data('value') : null,
      nodeLeft: node.length ? node.position().left : 0,
      nodeMarginLeft: node.length ? Number(node.css('margin-left').replace('px', '')) : 0,
    }
    const dimensions = {
      h: node.outerHeight(),
      w: node.outerWidth(),
      left: base.nodeLeft + base.nodeMarginLeft + container.scrollLeft(),
    }

    const {h, w, left} = dimensions;

    container.append(`<div class="ds-selector" style="width: ${w}px; height: ${h}px; top: calc(50% - ${h / 2}px); left: ${left}px;" ></div>`)
    const selector = container.find('.ds-selector');

    _displaySelect[id] = {
      container: container,
      selector: selector,
      value: base.nodeValue,
      onchange: (value) => {  },
      select: (value) => {
        container.find(`.ds-node[data-value=${value}]`).click()
      },
      selectEq: (index) => {
        container.find(`.ds-node`).eq(index).click()
      },
      silentSelect: (value) => {
        const node = container.find(`.ds-node[data-value=${value}]`);
        if(!node.length){
          console.log(`Display select node not found, value: ${value}, see _displaySelect for more informations`);
          return;
        }
        movePointerToNode(node);
      },
      values: () => {
        const arr = [];
        container.find('.ds-node').each(function(){
          arr.push({
            name: $(this).text(),
            value: $(this).data('value'),
          })
        })
        return arr;
      },
      removeValue: (value) => {
        container.find(`.ds-node[data-value=${value}]`).remove();
        const nodes = container.find('.ds-node');
        if(!nodes.length){
          container.html('<div class="ds-placeholder" data-empty-ds >Geen Data</div>')
        }
      },
      addValue: (options) => {
        const { name, value, classes} = options;
        if(!name || value === undefined || value === null){ return }

        container.find('[data-empty-ds]').remove();
        container.append(`<a class="ds-node ${classes || ''}" data-value="${value}">${name}</a>`)
      }
    }

  });
}
function correctDisplaySelect(){
  for(const id in _displaySelect){
    const dsi = _displaySelect[id];
    dsi.select(dsi.value);
  }
}
function silentCorrectDisplaySelect(){
  for(const id in _displaySelect){
    const dsi = _displaySelect[id];
    dsi.silentSelect(dsi.value);
  }
}
function movePointerToNode(node){
  const id = findContainer('ds-container', node).data('display-select-container');

  const instance = _displaySelect[id];
  const { container, selector } = instance;

  const dimensions = {
    h: node.outerHeight(),
    w: node.outerWidth(),
    left: node.position().left + Number(node.css('margin-left').replace('px', '')) + container.scrollLeft(),
  }

  const {h, w, left} = dimensions;

  container.find('.ds-node').removeAttr('data-selected');
  node.attr('data-selected', '');

  selector.attr('style', `width: ${w}px; height: ${h}px; top: calc(50% - ${h / 2}px); left: ${left}px; transform: scaleX(1.15) scaleY(.9)`);
  setTimeout(() => {
    selector.attr('style', `width: ${w}px; height: ${h}px; top: calc(50% - ${h / 2}px); left: ${left}px;`);
  }, 200)

}
$(document).on('click', '.ds-node', function(){
  const id = findContainer('ds-container', this).data('display-select-container');
  const node = $(this);

  if(!id || !_displaySelect[id]){
    notification('Er is iets foutgegaan!');
    console.log(`Display select instance not found, ID: ${id}, see _displaySelect for more informations`);
    return;
  }

  const instance = _displaySelect[id];

  movePointerToNode(node);

  instance.value = node.data('value');
  instance.onchange(instance.value);
});


const _tippy = {
  instances: [],
}
function tippyInit(){
  $('.tippy, [data-tippy-content]').each(function(){
    const instance = tippy(this);
    if(instance){
      _tippy.instances.push(instance);
    }
  });
}
function tippyUpdate(){
  for(const ins of _tippy.instances){
    if(!ins){continue;}
    ins.setContent($(ins.reference).attr('data-tippy-content'));
  }
}

// Sub nav
var subNavVars = {
  active: false,
  duration: 250,
}
$(".sub-nav-item").click(function(){
  if(subNavVars.active){return false}

  const target = $(this);

  subNavVars.active = true;
  target.find('.sub-nav-content').toggle({
    duration: subNavVars.duration,
    complete: () => {
      subNavVars.active = false;
    }
  });
  target.find('.sub-nav-icon').rotate(90);

  $('.sub-nav-item').not(target).each(function(){
    if($(this).find('.sub-nav-content').css('display') == 'none'){ return true; }

    $(this).find('.sub-nav-content').toggle({
      duration: subNavVars.duration,
    });
    $(this).find('.sub-nav-icon').rotate(90);
  });
});

// label-btn
$(document).ready(() => {
  $('.label-btn').each(function(){
    const input = $(this).find('input[type=radio]');
    if(input.length && input.prop('checked')){
      $(this).addClass('active');
    }

  });
})
$(document).on('click', '.label-btn', function(){
  const container = findContainer('label-btn-container', this);
  container.find('.label-btn').removeClass('active');

  $(this).addClass('active');
});

//Draggable modals
const _draggableModals = {
  grab: false,
  modal: null,
  offset: {
    x: null,
    y: null,
  }
}
pageComplete().then(() => {
  $('.modal-content').each(function(){
    $(this).addClass('transition-02').css({opacity: '1'})
    $(this).prepend(
        `<div class="m-2" >
              <div class="hover-shadow rounded cursor-pointer modal-grab text-center" ><i class="fa-solid fa-grip m-0 pointer-event-none"></i></div>
            </div>`)
  })
})
$(document).on('mousedown', '.modal-grab', function(event){
  _draggableModals.grab = true;
  _draggableModals.modal = findContainer('modal-dialog', this);

  _draggableModals.modal.find('.modal-content').css({opacity: '.25'})

  const { offsetX, offsetY } = event
  _draggableModals.offset.x = offsetX;
  _draggableModals.offset.y = offsetY;
})
$(document).on('mouseup', function(){
  if(!_draggableModals.grab){ return }

  _draggableModals.grab = false
  _draggableModals.modal.find('.modal-content').css({opacity: '1'})
})
$(document).on('mousemove', function(event){
  if(!_draggableModals.grab){ return }

  const { offset } = _draggableModals;
  const grab = _draggableModals.modal.find('.modal-grab');

  const container = findContainer('modal', _draggableModals.modal);
  const scrollY = container.scrollTop();

  const {clientX, clientY} = event;
  const x = clientX - grab.position().left - offset.x;
  const y = clientY - grab.position().top - offset.y + scrollY;

  _draggableModals.modal.css({
    margin: '0 0 100px 0',
    paddingBottom: '100px',
    positon: 'absolute',
    top: y,
    left: x,
  })
})

//Dropdown buttons sub dropdowns
$(document).on('click', '[data-toggle-sub-dropdown]', function(e){
  e.stopPropagation();
  e.preventDefault();

  const dropdown = $(this).data('toggle-sub-dropdown');
  const target = $(this).data('dropdown-target')

  $(`[data-sub-dropdown='${dropdown}'][data-sub-dropdown-id='${target}']`).toggle(250)
  $(this).find('.fa-chevron-down, .fa-chevron-up').addClass('transition-025').toggleClass('rotate-180')
})

//Main form append loader after submit
function resetMainForm(){
  $('[data-main-form]').find('[data-main-form-spinner]').remove();
  $('[data-main-form]').find('[type=submit]').removeClass('d-none');
}
$('[data-main-form]').submit(function(){
  const form  = $(this);

  if(form.find('[data-main-form-spinner]').length){ return }

  form.find('[type=submit]').addClass('d-none').after(`<div class="spinner-border text-success" data-main-form-spinner role="status"><span class="sr-only"></span></div>`);
})

//Synthetic overflow-x: auto & overflow-y: visible
$(document).click(function(){
  overflowXOnlyRemove()
});
function overflowXOnlyRemove(){
  $('[data-overflow-x-only]').css({height: ''})
}
function overflowXOnlyInit(){
  $('[data-overflow-x-only]').each(function(){
    const div = $(this);

    const height = div.outerHeight();
    const scroll = div.prop("scrollHeight");

    if(scroll > Math.ceil(height)){
      div.css({height: scroll + 35})
    }
  });
}

//Tutorial [Showcase only]
let _tutorial = {
  active: false,
  index: null,
  code: null,
  div: null,
  data: {
    1: {
      text: 'orem ipsum dolor sit ame'
    },
    2: {
      timeout: 250,
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec ut sollicitudin nibh. Donec condimentum dapibus interdum. Pellentesque a molestie diam. Aenean ac i.",
    },
    3: {
      text: "us. Suspendisse sit amet accumsan lectus. Mauris efficitur porttitor vulputate. Morbi a diam a massa suscipit hendrerit. Nullam pretium in se.",
    },
    4: {
      click: true,
      text: "Praesent sed nulla eleifend, tempus metus a, ultrices tortor. Nulla massa ante, tincid.",
    },
    5: {
      timeout: 500,
      text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
    },
    6: {
      timeout: 250,
      text: 'Praesent sed nulla eleifend, tempus metus a, ultrices tort'
    }
  }
}
$(document).on('click', '[data-tutorial-fab]', function(){
  if(!_tutorial.active){ return; }
  if(!_tutorial.data[_tutorial.index + 1]){
    $('[data-tutorial-text]').remove();

    _tutorial.div.remove();
    _tutorial = {
      active: false,
      index: null,
      code: null,
      div: null,
      data: {
        1: {
          text: 'orem ipsum dolor sit ame'
        },
        2: {
          timeout: 250,
          text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec ut sollicitudin nibh. Donec condimentum dapibus interdum. Pellentesque a molestie diam. Aenean ac i.",
        },
        3: {
          text: "us. Suspendisse sit amet accumsan lectus. Mauris efficitur porttitor vulputate. Morbi a diam a massa suscipit hendrerit. Nullam pretium in se.",
        },
        4: {
          click: true,
          text: "Praesent sed nulla eleifend, tempus metus a, ultrices tortor. Nulla massa ante, tincid.",
        },
        5: {
          timeout: 500,
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
        },
        6: {
          timeout: 250,
          text: 'Praesent sed nulla eleifend, tempus metus a, ultrices tort'
        }
      }
    };

    return;
  }
  initTutorial(_tutorial.code, _tutorial.index + 1);
});
function initTutorial(code, i = 1){
  const { data } = _tutorial;

  _tutorial.active = true;
  _tutorial.index = i;
  _tutorial.code = code;

  $('[data-tutorial-text]').remove();

  if(!_tutorial.div){
    $('html').append(`<div class="transition-03 position-absolute rounded z-index-9999 pointer-event-none ${randomString(15)}" style="box-shadow: 0 0 100rem 100rem rgb(0 0 0 / 30%);" ></div>`)

    _tutorial.div = $(`.${lastString()}`);
    _tutorial.btn = $(`[data-tutorial-next]`);

    const target = $(`[data-tutorial-${code}=1]`);

    const {left, top} = target.offset();
    const height = target.outerHeight();
    const width = target.outerWidth();

    if(data[i].text){
      $('html').append(`<div data-tutorial-text class="position-absolute rounded pointer-event-none v-center" style="bottom: 200px; left: 0; right: 0; margin: 0 auto; width: 75%; z-index: 99999" >
            <div  style="background-color: rgba(255, 255, 255, .6)" class="px-5 py-3 font-size-12 rounded" >
                ${data[i].text}
            </div>
        </div>`);
    }

    _tutorial.btn.css({
      left: (left + width + 170) > $('html').outerWidth() ? left - 170 : left + width + 50,
      top: top + height + 25,
    })
    _tutorial.div.css({
      height: height+ 20,
      width: width + 20,
      left: left - 10,
      top: top - 10,
    })
    return;
  }

  const target = $(`[data-tutorial-${code}=${i}]`).eq(0);

  if(data[i].timeout && !data[i].timeout_completed){
    data[i].timeout_completed = true;
    setTimeout(() => {
      initTutorial(code, i);
    }, data[i].timeout);
    return;
  }
  if(data[i].text){
    $('html').append(`<div data-tutorial-text class="position-absolute rounded pointer-event-none v-center" style="bottom: 200px; left: 0; right: 0; margin: 0 auto; width: 75%; z-index: 99999" >
            <div  style="background-color: rgba(255, 255, 255, .6)" class="px-5 py-3 font-size-12 rounded" >
                ${data[i].text}
            </div>
        </div>`);
  }

  const {left, top} = target.offset();
  const height = target.outerHeight();
  const width = target.outerWidth();

  _tutorial.div.css({
    height: height + 20,
    width: width + 20,
    left: left - 10,
    top: top - 10,
  })

}

//Route hotkey
const _routes = {
  modal: $('#select-route-shortcut'),
}
$(document).ready(routeShortcutInit);
$(document).on('keyup', function(e){
  if(e.shiftKey && e.ctrlKey && e.key.toLowerCase() == 's'){
    _routes.modal.showModal()
        .then(() => _routes.modal.find('.select_search-input').click());
  }
})
$(document).on('change', '#select-route-shortcut .select_search-hidden-input', function(){
  window.location.href = this.value;
  _routes.modal.find('[data-select-page]').html(`<div class="text-center mt-2 mb-3">
                                                    <div class="spinner-border text-primary" role="status"><span class="sr-only"></span></div>
                                                  </div>`
  )
});
function routeShortcutInit(){
  $('nav.sidebar').find('.nav-link[data-toggle]').each(function(){
    const container = findContainer('nav-item', this)

    const header = container.find('.menu-title').eq(0).text();
    container.find('.nav-item').each(function(){

      const a = $(this).find('a');
      const text = a.text();
      const href = a.attr('href');

      let subContainer = findContainer('sub-nav-item', this);
      let subHeader = '';
      if(subContainer){
        subHeader = subContainer.find('.menu-title').eq(0).text();
      }

      if(!href || href.slice(0, 1) === '#' || !text){ return true; }

      _routes.modal.find('.select_search-box').append(`
        <span class="select_search-value d-flex align-items-center" data-value="${a.attr('href')}">
          <div class="badge badge-primary mx-1" >${header}</div>
          ${subHeader ? `<div class="badge badge-inverse-primary mx-1" >${subHeader}</div>` : ''}
          <div class="mx-1" >${text}</div>
        </span>`
      );

    })
  })
  $('nav.sidebar').find('.nav-item[data-nav-non-dropdown]').each(function(){
    const a = $(this).find('a');
    const text = a.text();
    const href = a.attr('href');

    if(!href || href.slice(0, 1) === '#' || !text){ return true; }

    _routes.modal.find('.select_search-box').append(`
        <span class="select_search-value d-flex align-items-center" data-value="${a.attr('href')}">
          <div class="mx-1" >${text}</div>
        </span>`
    );

  })


  _routes.modal.find('.select_search-box').append(`
        <span class="select_search-value d-flex align-items-center" data-value="${url}/explorer">
          <div class="badge badge-primary mx-1" >Environment</div>
          <div class="mx-1" >Bestanden</div>
        </span>`
  );
  _routes.modal.find('.select_search-box').append(`
        <span class="select_search-value d-flex align-items-center" data-value="${url}/settings">
          <div class="badge badge-primary mx-1" >Environment</div>
          <div class="mx-1" >Settings</div>
        </span>`
  );
  _routes.modal.find('.select_search-box').append(`
        <span class="select_search-value d-flex align-items-center" data-value="${url}/logout">
          <div class="badge badge-primary mx-1" >Environment</div>
          <div class="mx-1" >Logout</div>
        </span>`
  );
}

//Dynamic container toggle
const _dynamicToggle = {
  toggling: {}
}
$(document).on('click', '[data-toggle-btn]', function(){
  const btn = $(this);
  let container = $(this);

  if(!container.attr('data-toggle-container')){
    container = findContainerByAttr('data-toggle-container', container);
  }

  let id_string = container.attr('data-toggle-container');
  if (!id_string){
    id_string = randomString();
    container.attr('data-toggle-container', id_string);
  }

  if (_dynamicToggle.toggling[id_string]){ return; }

  const content = container.find('[data-toggle-content]').eq(0);

  _dynamicToggle.toggling[id_string] = true;
  setTimeout(() => { _dynamicToggle.toggling[id_string] = false }, 310);

  content.toggle(300);
  btn.find('i').rotate(180);
});

//Generate Select JSON on input
$(document).ready(initSJV);
function initSJV(){
  $(`[data-sjv]`).each(function(){
    const input = $(this);
    let string = $(this).attr('data-sjv');

    if(string){
      setSJV(string);
      return true;
    }

    if(!input.val()){
      input.val(JSON.stringify({ options: [] }));
    }

    string = randomString(15);

    $(this).addClass(['d-none']).attr('data-sjv', string);

    setSJV(string);
  });
}
function setSJV(string){
  const input = $(`[data-sjv=${string}]`);
  $(`[data-sjv-container="${string}"]`).remove()

  if(!input.length){
    console.log(`[data-select-json=${string}] Niet gevonden`);
    return;
  }

  let arr = JSON.parse(input.val());

  let values = '';
  for (const index in arr['options']){
    const value = arr.options[index];
    values += `<div class="px-2 py-1 nobr m-1 bg-inverse-secondary cursor-pointer rounded-pill tippy" data-tippy-content="Waarde: ${value.value}" data-delete-sjv="${string}" data-index="${index}" >${value.name || ''}</div>`
  }

  values += `<div class="btn-inverse-primary cursor-pointer m-1 nobr px-2 py-1 rounded-pill" data-add-sjv="${string}" >Toevoegen</div>`
  input.after(`<div class="form-control-custom d-flex flex-wrap m--1 font-size-075" data-sjv-container="${string}" > ${values} </div>`);

  tippyInit();
}

$(document).on('click','[data-delete-sjv]', function(){
  try{
    const string = $(this).data('delete-sjv');
    const index = $(this).data('index');
    const input = $(`[data-sjv=${string}]`);

    if(!input.length){
      console.log(`[data-sjv=${string}] niet gevonden`);
      return;
    }

    const arr = JSON.parse(input.val());

    arr.options.splice(index, 1);

    input.val(JSON.stringify(arr));
    setSJV(string);
  }
  catch (e) {
    actError(e);
    notification('Er is iets foutgegaan!');
  }
});
$(document).on('click', '[data-add-sjv]', function(){
  try{
    const string = $(this).data('add-sjv');
    const input = $(`[data-sjv=${string}]`);

    if(!input.length){
      console.log(`[data-sjv=${string}] niet gevonden`);
      return;
    }

    confirmModal({
      text: `
        <div class="my--2" >
          <div class="my-2" >
            <label>Naam</label>
            <input class="form-control-custom" placeholder="Naam" data-sj-name >
          </div>
          <div class="my-2" >
            <label>Waarde</label>
            <input class="form-control-custom" placeholder="Waarde" data-sj-value >
          </div>
        </div>
      `
    })
        .then(response => {
          if(!response.status){ return; }

          const name = $('[data-sj-name]').val();
          const value = $('[data-sj-value]').val();

          if(!name){ notification('Naam kan niet leeg zijn'); return; }
          if(!value){ notification('Waarde kan niet leeg zijn'); return; }

          const arr = JSON.parse(input.val());

          arr.options.push({
            name: name,
            value: value,
          })

          input.val(JSON.stringify(arr));
          setSJV(string);

        })
  }
  catch (e) {
    actError(e);
    notification('Er is iets foutgegaan!');
  }
});

const _editor = {
  modal: $('#dynamic-editor-text-modal'),
}
function editorInit(id, value = null){
  try{

    if(!$(id).length){return;}

    return new Promise((resolve) => {
      ClassicEditor.create( document.querySelector(id) ,{
        toolbar: [ 'heading',"|",'bold', 'italic', 'link', "|", 'undo', 'redo',"|", 'numberedList', 'bulletedList',"insertTable", '|', 'imageUpload', "imageStyle:wrapText", "imageStyle:breakText"],
      })
          .then((ckEditor) => {
            if(value){ ckEditor.setData(value); }
            ckEditor.plugins.get( 'FileRepository' ).createUploadAdapter = ( loader ) => { return new ckEditorUploadAdapter( loader ); };
            resolve(ckEditor);
          })
          .catch((error) => console.log(error));
    })
  }
  catch(e){
    console.log('Editor catch: ' + id);
  }
}
function getEditorText(options = {}){
  $('[dynamic-editor-text-modal-iframe]').html(`<iframe style="height: 65vh" class="w-100" data-dynamic-editor-iframe src="${url}/iframe/editor"></iframe>`)
  const iframe = $("[data-dynamic-editor-iframe]");

  if(options.text){
    const setDataInterval = setInterval(() => {
      const editor = iframe.get(0).contentWindow.editor
      if(editor){
        editor.setData(options.text);
        clearInterval(setDataInterval);
        console.log('interval');
      }
    }, 100)
  }

  _editor.modal.showModal();

  $(document).off('click', '[data-editor-text-modal-reject]')
  $(document).off('click', '[data-editor-text-modal-resolve]')
  return new Promise(resolve => {
    $(document).on('click', '[data-editor-text-modal-reject]', () => {
      _editor.modal.hideModal();
      resolve({status: false})
    })
    $(document).on('click', '[data-editor-text-modal-resolve]', () => {
      const data = iframe.get(0).contentWindow.editor.getData()

      _editor.modal.hideModal();
      resolve({ status: true, text: data })
    })
  });

}

const _datasetItem = {columns: null, id: null};
function addDatasetItem(id){
  return new Promise(resolve => {
    loader();
    ajax('api/offertes/datasets/get', {id: id})
        .then(response => {
          if(!response.dataset.items.length){errorLoader('Geen dataset kolommen gevonden!'); resolve({status: false})}
          successLoader();
          _datasetItem.columns = response.dataset.items[0].value;
          _datasetItem.id = id;
          addDatasetItemFillColumns(resolve);
        })
        .catch(err => errorLoader());
  })
}
function addDatasetItemFillColumns(resolve){
  let text = `<div class="my-2">
                Dataset item toevoegen
              </div>`;
  let tr = '';

  for(const name in JSON.parse(_datasetItem.columns)){
    tr += `
    <td class="min-w-200"><input type="text" name="${name}" class="form-control-custom" placeholder="${name}"></td>
    `;
  }
  text += `<form data-dataset-item-form class="my-2 overflow-auto">
            <table class="table">
              <tbody>
                <tr>
                <td class="min-w-200 border-right"><input type="text" name="name" class="form-control-custom" placeholder="naam*"></td>
                  ${tr}
                </tr>
              </tbody>
            </table>
          </form>`;
  confirmModal({text: text, large: true})
      .then(response => {
        if(!response.status){resolve({status: false}); return;}
        addDatasetItemPost(resolve)
      })
}
function addDatasetItemPost(resolve){
  const data = {};
  data.id = _datasetItem.id;
  data.columns = $('[data-dataset-item-form]').serializeArray();
  const name = data.columns.find(row => row.name == 'name')
  if(!name || !name.value){
    notification('Naam is verplicht!');
    addDatasetItemFillColumns(resolve);
    return;
  }
  ajax('offertes/datasets/item/store', data).then( response => {
    resolve({ status: true, data: response })
  })
}

function getDatasetItem(id){
  return ajax('api/offertes/datasetitem/get', {id: id});
}

//custom-file-input
$(document).on('change', 'input[type=file].custom-file-input', function(){
  const files = Array.from($(this).prop('files'));
  const name = files.map(file => file.name).join(', ');

  const container = findContainer('custom-file', this);

  container.find('.custom-file-label').html(name);
});

// Horizontal Scroll Calback
const _hzs = {
  last_scroll: 0,
  callbacks: [],
}
$('div, section').scroll(function() {
  const scroll_left = $(this).scrollLeft();
  if (_hzs.last_scroll != scroll_left) {
    _hzs.last_scroll = scroll_left;
    for(const callback of _hzs.callbacks){
      try{ console.log(callback); callback(); }
      catch (e) { console.log(e.message); }
    }
  }
});

//Dynamic order button
$(document).on('click', '[data-dynamic-order-button]', function(){
  const string = $(this).attr('data-dynamic-order-button')
  const selected = $(this).attr('data-value');
  const target = selected == 'ASC' ? 'DESC' : 'ASC';

  $(`[data-dynamic-order-button=${string}]`).addClass('d-none');
  $(`[data-dynamic-order-button=${string}][data-value=${target}]`).removeClass('d-none');
  $(`[data-dynamic-order-input=${string}]`).val(target).trigger('change');
});
initDynamicOrderButton();
function initDynamicOrderButton(){
  $('[data-dynamic-order-button]').each(function(){
    const name = $(this).data('name');
    const value = $(this).data('prefill') || 'ASC';
    const input_class = $(this).data('input-class');
    const string = randomString();

    const ASC = $(this);
    const DESC = ASC.clone();

    ASC.html(`<i class="fa-solid fa-arrow-down-short-wide m-0"></i>`).attr('data-dynamic-order-button', string).attr('data-value', 'ASC')
    if(value != 'ASC'){ ASC.addClass('d-none'); }

    DESC.html(`<i class="fa-solid fa-arrow-down-wide-short m-0"></i>`).attr('data-dynamic-order-button', string).attr('data-value', 'DESC')
    if(value != 'DESC'){ DESC.addClass('d-none'); }

    ASC.after(DESC);
    DESC.after(`<input class="${input_class || ''}" data-dynamic-order-input="${string}" type="hidden" name="${name}" value="${value}" >`);
  })

}

//Dyamic mouse position
const _mousePosition = {
  x: 0,
  y: 0,
}
$(document).mousemove(function(event) {
  _mousePosition.x = event.pageX;
  _mousePosition.y = event.pageY;
});

function weekdayAsShortedString(weekday){
  const weekDays = ["Zo", "Ma", "Di", "Wo", "Do","Vr","Za"];
  return weekDays[weekday];
}

async function getSettingValue(key, json = false){
  return new Promise( resolve => {
    ajax("/api/setting", {key: key})
        .then(response => {
          resolve(json ? JSON.parse(response.value) : response.value);
        });
  });
}

//make draggable
var grabbable = {
  closestSpot: null,
  grab: 0,
  grabDiv: null,
  grabIndex: null,
  container: null,
}

function initDraggable(){
  $('[data-grab-container]').each(function(){

    const container = $(this);
    const is_table = container.prop('tagName') === 'TBODY';

    const grab_btn = `
      ${is_table ? '<td class="grabTd w-0">' : ''}
        <a class="btn text-dark mx-2" data-grab><i class="fa-solid fa-grip-vertical"></i></a>
      ${is_table ? '</td>' : ''}
    `;
    const drop_spot = `
      ${is_table ? '<tr><td class="grabTd border-0 p-0 h-0" colspan="100">' : ''}
        <div class="z-index-99 py-1 px-3 rounded bg-white w-100 position-relative" style="display: none" data-grab-dropspot></div>
      ${is_table ? '</td></tr>' : ''}
    `;

    for (let child of container.find('[data-grab-element]')) {
      child = $(child);
      if(child.attr('data-grab-initiated')){ continue; }

      $(child).attr('data-grab-initiated', true);

      if($(child).find('[data-grab-btn-loc]').length){
        $(child).find('[data-grab-btn-loc]').append(grab_btn);
      } else {
        (child.attr('data-drag-btn') == 'start') ? child.prepend(grab_btn) : child.append(grab_btn);
      }
      child.after(drop_spot);
    }

    if (!container.attr('data-initiated')) {
      if (is_table) {
        container.prepend($(drop_spot));
      } else {
        container.prepend(drop_spot);
      }
    }
    container.attr('data-initiated', true);
  });
}
$(document).on('mousemove', function(e){
  if(!grabbable.grab || !grabbable.grabDiv){ return; }

  let yp = e.clientY - grabbable.grabDiv.height() / 2;
  let xp = e.clientX - grabbable.grabDiv.width() / 2;
  grabbable.grabDiv.css({
    "left": xp+"px",
    "top": (yp - grabbable.grabDiv.height() / 2)+"px",
  });
  findClosestSpotGrab(e);
})
$(document).on("mouseup", function(){
  if(!grabbable.grab || !grabbable.grabDiv){ return }

  grabbable.grabDiv.attr("style", "");

  if(grabbable.closestSpot){

    const is_table = (grabbable.container.prop('tagName') === 'TBODY');
    const place_behind = is_table ? findContainerByTag('tr', grabbable.closestSpot) : grabbable.closestSpot;

    const div = grabbable.grabDiv.detach();
    place_behind.after(div);
    div.after(`
      ${is_table ? '<tr><td class="grabTd border-0 p-0 h-0" colspan="100">' : ''}
        <div class="z-index-99 py-1 px-3 rounded bg-white w-100 position-relative" style="display: none" data-grab-dropspot></div>
      ${is_table ? '</td></tr>' : ''}
    `);
  }

  grabbable.container.find('[data-grab-dropspot]').css({display: 'none'});
  grabbable.container.find('[data-grab-fade]').remove();
  grabbable.grab = false;
  grabbable.grabDiv = null;
  grabbable.closestSpot = null;
  grabbable.grabIndex = null;
  grabbable.container = null;
})

$(document).on("mousedown", "[data-grab]", function(e) {
  const container = findContainerByAttr('data-grab-container', this);
  const is_table = (container.prop('tagName') === 'TBODY');

  grabbable.container = container;
  grabbable.container.find($('[data-grab-dropspot]')).show(); // Show all dropspots

  container.addClass('position-relative').append(`<div class="h-100 w-100 opacity-25 bg-dark top-0 start-0 position-absolute rounded-5 z-index-9" data-grab-fade></div>`);

  grabbable.grab = true;
  grabbable.grabDiv = findContainerByAttr('data-grab-element', this);

  grabbable.grabIndex = is_table ? grabbable.grabDiv.closest('tr').index() : container.find('[data-grab]').index(this);

  grabbable.grabDiv.css({
    "background-color": "white",
    "z-index": "99",
    "transform": "scale(0.45)",
    "position": "fixed",
    "cursor": "pointer"
  });

  let dropspot;
  if (is_table) {
    dropspot = container.find('tr').eq(grabbable.grabIndex + 1).find('[data-grab-dropspot]');
  } else {
    dropspot = container.find('[data-grab-dropspot]').eq(grabbable.grabIndex + 1);
  }

  dropspot.remove();

  $(document).trigger('mousemove');
});

$(document).on('click', '[data-grabbable-remove]', function(){
  deleteGrabDropspot(this);
});
$(document).bind('selectstart dragstart', function(e) {
  if(grabbable.grab && grabbable.grabDiv){
    e.preventDefault();
    return false;
  }
});

function findClosestSpotGrab(e){
  let y = e.clientY + document.documentElement.scrollTop - grabbable.grabDiv.height() / 2;
  let currentDis = 99999;

  grabbable.container.find('[data-grab-dropspot]').each((i, element) => {
    const dropspot = $(element);
    let distance = Math.abs((dropspot.offset().top  - y) - grabbable.grabDiv.height() / 2);
    if(distance < currentDis){
      grabbable.closestSpot = dropspot;
      currentDis = distance;
    }
  });

  if(grabbable.closestSpot){
    grabbable.container.find("[data-grab-dropspot]").removeClass("bg-white");
    grabbable.closestSpot.addClass("bg-white");
  }
}
function deleteGrabDropspot(pointer){
  const container = findContainerByAttr('data-grab-container', pointer);
  const is_table = (container.prop('tagName') === 'TBODY');
  const grab_element = findContainerByAttr('data-grab-element', pointer);

  const index = container.find('[data-grab-element]').index(grab_element);

  let dropspot = container.find('[data-grab-dropspot]').eq(index + 1 )
  dropspot = is_table ? findContainerByTag('tr', dropspot) : dropspot;

  dropspot.remove();
}

//Modules
const _cachedModules = {}
async function hasModules(module){
  if(_cachedModules[module] !== undefined){ return _cachedModules[module]; }

  const { has_module } = await ajax('api/users/has-module', { module })
  _cachedModules[module] = has_module;

  return has_module;
}


function showExplorerFile(uri, name){
  var link = document.createElement("a");
  link.setAttribute('target', '_blank');
  link.href = uri;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  delete link;
}

function addDaysToDate(date, days){
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() + days);
}

function lowerKeys(obj) {
  const newObj = {};
  for (let key in obj) {
    newObj[key.toLowerCase()] = obj[key];
  }
  return newObj;
}

function getColspan(tr) {
  let totalColspan = 0;
  tr.find('td').each(function() {
    const colspan = $(this).attr('colspan');
    totalColspan += colspan ? parseInt(colspan) : 1;
  });
  return totalColspan;
}

async function getRoles() {
  try {
    const response = await ajax('api/roles/get');
    return response.roles;
  } catch (e) { handleCatchError(e) }
}

function findKeyOfObject(obj, value){
  for(const key in obj){
    if(obj[key] == value){
      return key;
    }
  }
}

function sortObj(obj) {
  const entries = Object.entries(obj);
  entries.sort((a, b) => a[1] - b[1]);
  return Object.fromEntries(entries);
}
