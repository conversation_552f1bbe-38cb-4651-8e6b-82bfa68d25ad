html{
  background-color: #f3f4fa;
}
nav{
  z-index: 1000;
}
.nav-link.active {
  color: #4a4a4a !important;
}
.modal{
  padding: 0!important;
}

.card {
  margin-bottom: 25px;
}
.card::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
.card::-webkit-scrollbar-thumb {
    background: #888;
    border: 4px solid transparent;
    border-radius: 50rem;
}
.card::-webkit-scrollbar-track {
    margin: 1rem .5rem;
}
.card::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.small-scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
.small-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border: 4px solid transparent;
    border-radius: 50rem;
}
.small-scrollbar::-webkit-scrollbar-track {
    margin: 1rem .5rem;
}
.small-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
}

body{
  -webkit-print-color-adjust: exact;
}
table{
  width: 100%;
}
iframe{
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
}

.CodeMirror {
  height: 300px !important;
  padding: 0!important;
}
.code-block{
  padding: 0.5rem;
  border-radius: 0.25rem;
  background-color: #252c46;
}

.content-m-0 *{
  margin: 0!important;
}
.content-p-0 *{
  padding: 0!important;
}
.content-py-0 *{
  padding-top: 0!important;
  padding-bottom: 0!important;
}
.content-pl-0 *{
  padding-left: 0!important;
}
.content-pr-0 *{
  padding-right: 0!important;
}
.content-border-0 *{
  border: none!important;
}
.sidebar .nav .nav-item .nav-link {
  align-items: flex-start;
}
.thumbnail {
  width: 50px !important;
  height: 50px !important;
}
.main-panel {
  margin-top: -25px !important;
}
.brand-logo {
  width: 50px !important;
  height: 50px !important;
}
.navbar.default-layout .navbar-brand-wrapper .navbar-brand img {
  width: 50px !important;
  height: 50px !important;
  margin: auto;
  vertical-align: middle;
}
.thumbnail {
  border-radius: 0px !important;
}
.nobr{
  white-space: nowrap;
}
.wite-space-pl{
  white-space: pre-line !important;
}
.disabledTR {
  opacity: 0.5;
}


.hoverSelect, .hoverDeselect, .hover-shadow, .hover-border, .hover-shadow-inset{
  transition: 0.35s;
}
.hover-shadow:hover{
  box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-shadow-inset:hover{
  box-shadow: inset 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-border:hover{
  border-color: #dee2e6!important;
}
.hoverDeselect:hover{
  transform: scale(.9);
}
.hoverSelect:hover {
  transform: scale(1.1);

}

.hover-border{
  border: 1px solid rgba(0, 0, 0, 0);
}
.hover-shadow-active{
  box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-shadow-inset-active{
  box-shadow: inset 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}

.hover-text-primary:hover{
  color: #2196f3 !important;
}
.hover-text-danger:hover{
  color: #ff6258 !important;
}
.hover-text-warning:hover{
  color: #ffaf00 !important;
}
.hover-text-underline:hover{
    text-decoration: underline!important;
}

.tdImg {
  border-radius: 0 !important;
  height: 50px !important;
  width: auto !important;
  display: inline-block;
}
.imgTd {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  text-align: center;
}

.removeDecoration{
  text-decoration: none !important;
}

#myInput {
  box-sizing: border-box;
  background-image: url('searchicon.png');
  background-position: 14px 12px;
  background-repeat: no-repeat;
  font-size: 16px;
  padding: 14px 20px 12px 45px;
  border: none;
  border-bottom: 1px solid #ddd;
}
#myInput:focus {
  outline: 3px solid #ddd;
}

.dropdown {
  position: relative;
  display: inline-block;
  color: white;
}
.dropdown-menu{
  max-height: 300px;
  overflow: auto;
}
.dropdown-item{
  cursor: pointer!important;
  line-height: 21px;
}
.dropdown-item-toggle{
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dropdown-item-toggle i:first-of-type{
  margin-left: .65rem!important;
  color: #858585 !important;
}
.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f6f6f6;
  min-width: 230px;
  border: 1px solid #ddd;
  z-index: 1;
}

.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #f1f1f1
}
[data-sub-dropdown]{
  width: 100%!important;
}

.show {
  display: block;
}

.center {
  text-align: center;
}

.dataTables_wrapper .dataTable .btn {
  padding: 0.56rem 1.375rem !important;
}
.dataTables_wrapper .dataTable .btn.btn-img {
  padding: .25rem .75rem!important;
}

#offertesTable_wrapper {
  padding: 15px;
  overflow: auto;
}

.trHover {
  transition: 0.15s;
}

.trHover:hover {
  background-color: rgba(20, 20, 20, 0.08);
}

.cursor-pointer {
  cursor: pointer;
}
.cursor-unset {
  cursor: unset!important;
}

.rounded-right-0{
  border-top-right-radius: 0!important;
  border-bottom-right-radius: 0!important;
}
.rounded-left-0{
  border-top-left-radius: 0!important;
  border-bottom-left-radius: 0!important;
}
.rounded-top-0{
  border-top-left-radius: 0!important;
  border-top-right-radius: 0!important;
}
.rounded-bottom-0{
  border-bottom-left-radius: 0!important;
  border-bottom-right-radius: 0!important;
}

.rounded-4{
  border-radius: .4rem!important;
}
.rounded-5{
  border-radius: .5rem!important;
}
.rounded-6{
  border-radius: .6rem!important;
}
.rounded-7{
  border-radius: .7rem!important;
}
.rounded-8{
  border-radius: .8rem!important;
}
.rounded-9{
  border-radius: .9rem!important;
}
.rounded-10{
  border-radius: 1rem!important;
}

.rotate-45 {
  transform: rotate(45deg)
}
.rotate-90 {
  transform: rotate(90deg)
}
.rotate-180 {
  transform: rotate(180deg)
}
.rotate-270 {
  transform: rotate(270deg)
}
.rotate-315 {
  transform: rotate(315deg)
}
.rotate-360 {
  transform: rotate(360deg)
}

.table-header-rotated {
  border-collapse: collapse;
}
.table-header-rotated td {
  width: 90px;
}
 .table-header-rotated th {
  padding: 5px 11px;
}
.table-header-rotated td {
  text-align: center;
  padding: 11px 5px;
  border: 1px solid #ccc;
}
 .table-header-rotated th.rotate {
  height: 146px;
  white-space: nowrap;
}
 .table-header-rotated th.rotate > div {
  -webkit-transform: translate(63px, 51px) rotate(315deg);
      -ms-transform: translate(63px, 51px) rotate(315deg);
          transform: translate(63px, 51px) rotate(315deg);
  width: 30px;
}
 .table-header-rotated th.rotate > div > span {
  border-bottom: 1px solid #ccc;
  padding: 5px 10px;
}
.table-header-rotated th.row-header {
  padding: 0 10px;
  border-bottom: 1px solid #ccc;
}

.input-disabled{
  background-color: #e9ecef;
  border: 1px solid #dee2e6;
  font-family: "Poppins", sans-serif;
  font-size: 0.75rem;
  height: 45px;
  color: rgb(73, 80, 87)!important;
  border-radius: 0.25rem;;
}
.text-input-disabled{
  color: rgb(73, 80, 87)!important;
}

.bg-input-disabled{
  background-color: #E9ECEF!important;
}
.bg-input-readonly{
  background-color: #FAFAFA!important;
}
.bg-modal{
  background-color: #f3f4fa;
}
.bg-aqua{
  background-color: #14c9cb;
}

.bg-white-important{
  background: #FFFFFF!important;
}
.bg-img-center{
  background-size: cover!important;
  background-position: center!important;
}
.bg-img-contain{
  background-position: center !important;
  background-size: contain;
  background-repeat: no-repeat;
}

.hover-opacity-1{
  transition: .25s;
}
.hover-opacity-1:hover{
  opacity: 1!important;
}

.hover-border-dark:hover{
  border-color: #252C46 !important;
}
.hover-border-success:hover{
  border-color: #19d895 !important;
}
.hover-border-danger:hover{
  border-color: #ff6258 !important;
}
.hover-border-primary:hover{
  border-color: #2196f3 !important;
}
.hover-border-warning:hover{
  border-color: #ffaf00 !important;
}


.hover-bg-danger:hover {
  background-color: #ff6258!important;
}
.hover-bg-success:hover {
  background-color: #19d895!important;;
}
.hover-bg-inverse-primary:hover{
  background: rgba(33, 150, 243, 0.2)!important;;
}
.hover-bg-inverse-success:hover{
  background: rgba(25, 216, 149, 0.2)!important;;
}
.hover-bg-inverse-danger:hover{
  background: rgba(255, 98, 88, 0.2)!important;;
}
.hover-bg-inverse-secondary:hover{
  background: rgba(192, 194, 195, 0.2)!important;;
}

.hover-border-danger:hover {
  border-color: #ff6258!important;
}
.hover-border-success:hover {
  border-color: #19d895!important;;
}
.hover-border-primary:hover {
  border-color: #2196f3!important;;
}

.hoverTranslateY, .hoverTranslateYRev{
  transition: 0.3s;
}

.hoverTranslateY:hover {
  transform: translateY(-10px);
}
.hoverTranslateYRev:hover{
  transform: translateY(10px);
}

.text-placeholder{
  width: 75px;
  height: 24px;
  background-color: rgba(150, 150, 150, 0.1);
  border-radius: .3rem;
}
.text-placeholder-md{
  width: 150px;
  height: 30px;
  background-color: rgba(150, 150, 150, 0.1);
  border-radius: .3rem;
}

.bg-success-fade {
  background-image: linear-gradient(to bottom right, #19d89580, #19d895);
}
.bg-succes-dark{
  background-color: #158752 !important;
}

.bg-inverse-success {
    background: #19D89533!important;
}
.bg-inverse-danger {
    background: #FF625833!important;
}
.bg-inverse-primary{
    background-color: #13AFF933!important;
}
.bg-inverse-warning {
    background: #FFAF0033!important;
}
.bg-inverse-secondary{
    background: #C0C2C333!important;
}
.bg-inverse-secondary.hover-mark:hover{
    background: #c0c2c378!important;
}
.bg-light-grey{
  background-color: rgba(0,0,0,0.015);
}
.bg-whatsapp{
  background-color: #00D757!important;
}
.bg-danger-fade {
  background-image: linear-gradient(to bottom right, #ff625880, #ff6258);
}
.bg-unset{
  background-color: unset!important;
}

.code-string {
  color: #60A477;
}

.code-tag {
  color: #D5B969;
}

.code-attr {
  color: #929292
}

.translateY {
  transform: translateY(-10px);
}

.opacity-1 {
  opacity: 1!important;
}
.opacity-85 {
  opacity: 0.85!important;
}
.opacity-75 {
  opacity: 0.75!important;
}
.opacity-50 {
  opacity: 0.5!important;
}
.opacity-25 {
  opacity: 0.25!important;
}
.opacity-33 {
  opacity: 0.33!important;
}
.opacity-0 {
  opacity: 0!important;
}

.z-index-9{
  z-index: 9;
}
.z-index-99{
  z-index: 99;
}
.z-index-999{
  z-index: 999;
}
.z-index-9999{
  z-index: 9999;
}

.btn-reserveerplein {
  background-color: #29474D;
  color: white;
}
.btn-img{
  padding: .25rem .75rem!important;
}

.shadow-md{
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);
}
.shadow-inset {
  box-shadow: inset -0.4rem -0.4rem 1rem rgb(0 0 0 / 15%) !important;
}
.shadow-regular{
  box-shadow: 0 0rem 1rem rgb(0 0 0 / 20%) !important;
}

.hover-mark:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.profile-tab-nav {
  min-width: 250px;
}

.tab-content {
  flex: 1;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-select:disabled {
  color: #000000;
  background-color: #FAFAFA!important;
}

.form-control[type=file]{
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.65;
    border: 1px solid #ced4da;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.4rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
}
.form-control-custom{
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.form-control-custom:focus {
  border-color: #86b7fe;
}
.form-control-custom[readonly] {
  background-color: #e9ecef;
}
.form-control-custom-sm{
  display: block;
  width: 100%;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1;
  color: #212529;
  border: 1px solid #ced4da;
  border-radius: 0.2rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.form-control-custom-sm:focus {
  border-color: #86b7fe;
}
.form-control-custom-sm[readonly] {
  background-color: #e9ecef;
}

.form-control-left{
  border-right: none;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.form-control-right{
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.form-control-middle{
  border-left: none;
  border-right: none;
  border-radius: 0;
}
.form-control-divider{
  width: auto;
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 0;

  display: flex;
  justify-content: center;
  align-items: center;
}
.form-control-divider:after{
  content: "";
  border-right: 1px solid #ced4da;
  left: calc(50% - 1px);
  width: 1px;
  height: 20px;
}

.form-control-icon, .form-control-icon-reverse{
  display: flex;
  align-items: center;
}
.form-control-icon-divider{
  height: 22px;
  position: relative;
}
.form-control-icon-divider:after{
  content: "";
  border-right: 1px solid #ced4da;
  position: absolute;
  left: calc(50% - 1px);
  width: 1px;
  height: 100%;
}
.form-control-icon span, .form-control-icon-reverse input, .form-control-icon-reverse select{
border-right: none;
border-top-right-radius: 0;
border-bottom-right-radius: 0;
}
.form-control-icon input, .form-control-icon select, .form-control-icon-reverse span{
border-left: none;
border-top-left-radius: 0;
border-bottom-left-radius: 0;
}
.form-control-icon span, .form-control-icon-reverse span{
width: auto;
}


.nav-pills a.nav-link {
padding: 15px 20px;
border-bottom: 1px solid #ddd;
border-radius: 0;
color: #333;
}

.nav-pills a.nav-link i {
width: 20px;
}

.transition-0 {
  transition: 0s!important;
}
.transition-01 {
transition: .1s;
}
.transition-02 {
transition: .2s;
}
.transition-025 {
transition: .25s;
}
.transition-03 {
transition: .3s;
}
.transition-04 {
transition: .4s;
}
.transition-05 {
transition: .5s;
}

.auth-button {
font-family: "Poppins", sans-serif;
font-size: 13px;
padding: 11px 33px;
font-weight: 600;
background-image: linear-gradient(
120deg, #556cdc, #128bfc, #18bef1);
}

.shadow-light {
box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 5%) !important;
}

.text-wrap {
overflow-wrap: anywhere;
}
.text-transparent{
  color: transparent;
}
.text-aqua{
  color: #14c9cb;
}
.text-whatsapp{
  color: #00D757!important;
}

.explorer-div {
  width: 100px;
}
.explorer-div small{
  overflow: hidden;
}
.explorer-div img {
width: 100px;
height: 100px;
}
.explorer-thumbnail{
    background-size: cover;
    background-position: center;
    height: 100px;
    width: 100px;
    border-radius: 0.75rem;
}
.explorer-path{
    font-size: .75rem;
    color: #858585 !important;
    overflow: hidden;
    white-space: nowrap;
}
.explorer-label{
    font-size: .9rem;
    margin: .25rem 0;
    overflow: visible;
    word-wrap: break-word;
}

.commit-message:first-line{
  font-weight: 600;
}

.position-unset{
position: unset;
}
.pointer-event-none{
pointer-events: none;
}

.border-top-black{
border-top-color: black!important;
}
.border-bottom-black{
border-bottom-color: black!important;
}
.border-right-black{
border-right-color: black!important;
}
.border-left-black{
border-left-color: black!important;
}

.border-semibold{
  border: 2px solid;
}

.border-2{
  border: 2px solid #dee2e6 !important;
}
.border-3{
  border: 3px solid #dee2e6 !important;
}
.border-4{
  border: 4px solid #dee2e6 !important;
}
.border-5{
  border: 5px solid #dee2e6 !important;
}

.border-bold{
border: 4px solid;
}
.border-dashed{
  border-style: dashed!important;
}

.border-top-bold{
border-top: 4px solid
}
.border-right-bold{
border-right: 4px solid
}
.border-bottom-bold{
border-bottom: 4px solid
}
.border-left-bold{
border-left: 4px solid
}

.border-bottom-primary{
border-bottom-color: #2196f3 !important;
}
.border-bottom-danger{
border-bottom-color: #ff6258 !important;
}
.border-bottom-warning{
border-bottom-color: #ffaf00 !important;
}
.border-bottom-success{
border-bottom-color: #19d895 !important;
}

.shadow-hover:hover{
box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.shadow-inset-hover:hover{
box-shadow: inset -0.4rem -0.4rem 1rem rgb(0 0 0 / 15%) !important;
}

.min-h-px-100{
  min-height: 100px;
}
.min-h-px-125{
  min-height: 125px;
}
.min-h-px-150{
  min-height: 150px;
}
.min-h-px-200{
  min-height: 200px;
}
.min-h-px-250{
  min-height: 250px;
}
.min-h-px-300{
  min-height: 300px;
}
.min-h-px-400{
  min-height: 400px;
}
.min-h-px-500{
  min-height: 500px;
}

.min-vh-25{
  min-height: 25vh;
}
.min-vh-50{
  min-height: 50vh;
}
.min-vh-75{
  min-height: 75vh;
}

.h-px-5{
  height: 5px;
}
.h-px-10{
  height: 10px;
}
.h-px-15{
  height: 15px;
}
.h-px-20{
  height: 20px;
}
.h-px-38{
  height: 38px;
}
.h-px-40{
  height: 40px;
}
.h-px-50{
  height: 50px;
}
.h-px-75{
  height: 75px;
}
.h-px-100{
  height: 100px;
}
.h-px-125{
  height: 125px;
}
.h-px-150{
  height: 150px;
}
.h-px-200{
  height: 200px;
}
.h-px-250{
  height: 250px;
}
.h-px-300{
  height: 300px;
}
.h-px-400{
  height: 400px;
}
.h-px-500{
  height: 500px;
}

.w-px-10{
  width: 10px!important;
}
.w-px-20{
  width: 20px!important;
}
.w-px-25{
  width: 25px!important;
}
.w-px-30{
  width: 30px!important;
}
.w-px-40{
  width: 40px!important;
}
.w-px-50{
  width: 50px!important;
}
.w-px-75{
  width: 75px!important;
}
.w-px-100{
  width: 100px!important;
}
.w-px-125{
  width: 125px!important;
}
.w-px-150{
  width: 150px!important;
}
.w-px-175{
  width: 175px!important;
}
.w-px-200{
  width: 200px!important;
}
.w-px-225{
  width: 225px!important;
}
.w-px-250{
  width: 250px!important;
}
.w-px-300{
  width: 300px!important;
}
.w-px-400{
  width: 400px!important;
}
.w-px-500{
  width: 500px!important;
}

.min-w-50 {
    min-width: 50px;
}
.min-w-75 {
    min-width: 75px;
}
.min-w-100 {
    min-width: 100px;
}
.min-w-125 {
    min-width: 125px;
}
.min-w-150 {
    min-width: 150px;
}
.min-w-175 {
    min-width: 175px;
}
.min-w-200 {
    min-width: 200px;
}
.min-w-225 {
    min-width: 225px;
}
.min-w-250 {
    min-width: 250px;
}
.min-w-300 {
    min-width: 300px;
}
.min-w-350 {
    min-width: 350px;
}
.min-w-400 {
    min-width: 400px;
}
.min-w-500 {
    min-width: 500px;
}

.max-w-100{
  max-width: 100px;
}
.max-w-125{
  max-width: 125px;
}
.max-w-150{
  max-width: 150px;
}
.max-w-175{
  max-width: 175px;
}
.max-w-200{
  max-width: 200px;
}
.max-w-250{
  max-width: 250px;
}
.max-w-300{
  max-width: 300px;
}
.max-w-400{
  max-width: 400px;
}
.max-w-500{
  max-width: 500px;
}

.max-h-100{
  max-height: 100px;
}
.max-h-150{
  max-height: 150px;
}
.max-h-200{
  max-height: 200px;
}
.max-h-300{
  max-height: 300px;
}
.max-h-400{
  max-height: 400px;
}
.max-h-500{
  max-height: 500px;
}
.max-h-750{
  max-height: 750px;
}
.max-h-1000{
  max-height: 1000px;
}

.line-h-1{
    line-height: 1rem;
}
.line-h-11{
    line-height: 1.1rem;
}
.line-h-115{
    line-height: 1.15rem;
}
.line-h-12{
    line-height: 1.2rem;
}
.line-h-125{
    line-height: 1.25rem;
}

.vertical-align-top{
vertical-align: top!important;
}
.vertical-align-bottom{
vertical-align: bottom!important;
}
.vertical-align-center{
vertical-align: center!important;
}


.tessa-svg{
  height: 13px;
  width: auto!important;
  display: inline-block;
}
.svg-white g{
fill: white;
}

.svg-dark g{
  fill: #818181;
}

.h-0{
  height: 0px!important;
}
.h-1{
height: 1px;
}
.h-5{
height: 5px;
}
.h-10{
height: 10px;
}


.w-0{
width: 0%;
}
.w-33{
width: 33.3%;
}
.w-66{
width: 66.6%;
}

.w-1-rem{
width: .25rem;
}
.w-2-rem{
width: .5rem;
}
.w-3-rem{
width: 1rem;
}
.w-4-rem{
width: 1.5rem;
}
.w-5-rem{
width: 3rem;
}

.post-detail .detail-container{
margin: 0.75rem 0!important;
background-color: #FBFBFB!important;
border: 1px solid #dee2e6 !important;
}

.corner-notifications{
  position: fixed;
  right: 1rem;
  top: 80px;
  z-index: 9999;
}

.corner-loader{
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 9999;
}
.corner-loader-content{
  display: flex;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 2555, .2);
  border-radius: 0.25rem !important;
  backdrop-filter: blur(.25rem);
}
.dagplanning-table{
width: 100%;
}
.dagplanning-table td:not(:first-of-type){
padding: 5px 0!important;
}

.table-fixed{
  table-layout: fixed!important;
}
.table-custom{
  width: 100%;
}
.table-custom td, .table-custom th{
  padding: 0.5rem 0.5rem;
}
.table-border td, .table-border th{
  border-bottom: 1px solid #dee2e6 !important;
}

.table-sm{

}
.table-sm *{
  font-size: .7rem!important;
}
.table-sm td, .table-sm th{
  padding: .5rem!important;
}

.section-container{
margin-bottom: 0.5rem !important;
margin-top: 0.5rem !important;
padding: 0.5rem !important;
box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.section-container .section-content{
border-bottom: 1px solid #dee2e6;
padding-bottom: .5rem;
}
.section-container .section-toggle{
font-size: 14px;
font-weight: 400;
display: block;
text-align: center;
cursor: pointer;
padding: .5rem;
border-radius: 0.25rem !important;
}
.section-container .section-toggle:hover {
background: rgba(192, 194, 195, 0.2)!important;
}

@media (max-width: 768px) {
.w-m-100 {
width: 100% !important;
}

.explorer-div {
width: 80px;
}
.explorer-div h5 {
font-size: 1rem;
}
.explorer-div img, .explorer-thumbnail {
    width: 80px;
    height: 80px;
}
}
.ck-balloon-panel{
z-index: 9999!important;
}


/*Editable select*/
.select_edit-input{
  cursor: pointer;
}
.select_edit-input[readonly]{
  background-color: #e9ecef;
}
.select_edit-values{
  position: relative;
  display: none;
}
.select_edit-values.show{
  display: block!important;
}
.select_edit-box{
  position: absolute;
  z-index: 999;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  width: 100%;
  max-height: 500px;
  overflow: auto;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.select_edit-value{
  cursor: pointer;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  display: block;
}
.select_edit-value:hover{
  background: rgba(33, 150, 243, 0.2);
}
.select_edit-value-hide{
  display: none!important;
}


.select_search-input{
  cursor: pointer;
}
.select_search-values{
  /*position: relative;*/
  display: none;
}
.select_search-values.show{
  display: block!important;
}
.select_search-box{
  position: absolute;
  z-index: 999;
  background-color: white;
  border: 1px solid #dee2e6;
  max-height: 500px;
  overflow: auto;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.select_search-box:has(.select_search-value:not(.select_search-value-hide):not(.d-none)){
    padding-bottom: .5rem;
}
.select_search-value{
  cursor: pointer;
  display: block;
  padding: 0.25rem 0.5rem;
  margin: 0 0.5rem;
  border-radius: .75rem;
  overflow: hidden;
}
.select_search-value[data-disabled]{
  color: #858585 !important;
}
.select_search-value:hover{
  background: rgba(33, 150, 243, 0.2);
}
.select_search-value.focus{
  background: rgba(33, 150, 243, 0.2);
}
.select_search-value-hide{
  display: none!important;
}

.select_multiple-input{
  cursor: pointer;
}
.select_multiple-values{
  /*position: relative;*/
  display: none;
}
.select_multiple-values.show{
  display: block!important;
}
.select_multiple-box{
  position: absolute;
  z-index: 999;
  background-color: white;
  border: 1px solid #dee2e6;
  max-height: 500px;
  overflow: auto;
  padding-bottom: .5rem;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.select_multiple-value{
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  margin: .1rem 0.5rem;
  border-radius: .75rem;
  overflow: hidden;
}
.select_multiple-value[data-disabled]{
  color: #858585 !important;
}
.select_multiple-value:hover{
  background: rgba(33, 150, 243, 0.2);
}
.select_multiple-value.focus{
  background: rgba(33, 150, 243, 0.2);
}
.select_multiple-value-hide{
  display: none!important;
}
.select_multiple-value-indicator{
  transition: .3s;
  opacity: 0;
  margin-right: .25rem;
  width: 0;
  overflow: hidden;
}
.select_multiple-value-indicator.active{
  opacity: 1;
  width: 14px;
}


.label-btn{
  cursor: pointer;
  border-bottom: 1px solid #dee2e6 !important;
  border-right: 1px solid #dee2e6 !important;
  border-top: unset!important;
  border-left: unset!important;
}
.label-btn.active{
  border-color: #19d895 !important;
  border-width: 2px !important;
}
.label-btn:hover {
  box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}


.select_group-input{
  cursor: pointer;
}
.select_group-values{
  /*position: relative;*/
  display: none;
}
.select_group-values.show{
  display: block!important;
}
.select_group-box{
  position: absolute;
  z-index: 999;
  background-color: white;
  border-radius: 0.25rem;
  min-width: 100%;
  max-height: 500px;
  overflow: auto;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.select_group-value{
  cursor: pointer;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  display: block;
  /*font-weight: 300;*/
}
.select_group-value:hover{
  background: rgba(33, 150, 243, 0.1);
}
.select_group-value-hide{
  display: none!important;
}
.select_group-box .group-container .group-header{
  cursor: pointer;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  display: flex;
  align-content: center;
  justify-content: space-between;
}
.select_group-box .group-container:hover .group-header{
  background: rgba(33, 150, 243, 0.3);
}
.select_group-box .group-container.active{
  border-left: 3px solid #2196f3;
}
.select_group-box .group-container.active .group-header{
  background: rgba(33, 150, 243, 0.3);
  /*border-bottom: 1px solid #2196f3 !important;*/
}
.select_group-box .group-content{
  display: none;
}
.select_group-box .group-content.show{
  display: block!important;
}

.btn-inverse-danger,.btn-outline-danger{
  color: #ff6258!important;
}
.btn-inverse-success,.btn-outline-success{
  color: #19d895!important;
}
.btn-inverse-warning,.btn-outline-warning{
  color: #ffaf00!important;
}
.btn-inverse-primary,.btn-outline-primary{
  color: #2196f3!important;
}
.btn-inverse-info,.btn-outline-info{
  color: #8862e0!important;
}
.btn-inverse-facebook{
  color: #648ACA;
  background-color: rgba(100, 138, 202, 0.2);
  background-image: none;
  border-color: rgba(255, 175, 0, 0);
}
.btn-inverse-facebook:hover{
  color: white;
  background-color: #648ACA;
}

.btn-dark {
  color: #FFFFFF!important;
}

.btn-inverse-danger:hover, .btn-inverse-success:hover, .btn-inverse-warning:hover, .btn-inverse-primary:hover, .btn-inverse-dark:hover, .btn-inverse-info:hover,
.btn-inverse-danger:active, .btn-inverse-success:active, .btn-inverse-warning:active, .btn-inverse-primary:active, .btn-inverse-dark:active .btn-inverse-info:active,
.btn-outline-danger:hover, .btn-outline-success:hover, .btn-outline-warning:hover, .btn-outline-primary:hover, .btn-outline-dark:hover, .btn-outline-info:hover,
.btn-outline-danger:active, .btn-outline-success:active, .btn-outline-warning:active, .btn-outline-primary:active, .btn-outline-dark:active .btn-outline-info:active{
  color: #FFF!important;
}

.badge-xl{
  font-size: .75rem;
  padding: .5rem 1.3rem;
}
.badge-aqua{
  border: 1px solid #14c9cb;
  background-color: #14c9cb;
  color: white;

}

.alert ul{
  margin: 0;
  padding-left: 1rem;
}

alert-danger{
  display: block;
  font-size: 0.875rem;
  color: #c24a43;
  background-color: rgba(255, 98, 88, 0.2);
  border: 1px solid #eb5a51;

  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}
alert-primary{
  display: block;
  font-size: 0.875rem;
  color: #1972b9;
  background-color: rgba(33, 150, 243, 0.2);
  border: 1px solid #1e8ae0;

  position: relative;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
}
alert-success{
  display: block;
  font-size: 0.875rem;
  color: #13a471;
  background-color: rgba(25, 216, 149, 0.2);
  border: 1px solid #17c789;

  position: relative;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
}
alert-warning{
  display: block;
  font-size: 0.875rem;
  color: #c28500;
  background-color: rgba(255, 175, 0, 0.2);
  border: 1px solid #eba100;

  position: relative;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
}

.flex-between{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-between-top{
  display: flex;
  justify-content: space-between;
  align-items: start;
}
.flex-between-bottom{
  display: flex;
  justify-content: space-between;
  align-items: end;
}
.flex-center{
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-align{
  display: flex;
  align-items: center;
}
.flex-start{
  display: flex;
  align-items: center;
  justify-content: start;
}
.flex-end{
  display: flex;
  align-items: center;
  justify-content: end;
}

.v-center{
  display: flex;
  justify-content: center;
  align-items: center;
}

.ratio-1-1{
  aspect-ratio: 1/1;
}

.live-dot{
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: red;
  box-shadow: 0px 0px 0.5rem 0.15rem rgba(255, 0, 0, 0.25);
  display: block;
}

.dot-glow{
    display: inline-block;
    width: 10px;
    height: 10px;
    min-width: 10px;
    border-radius: 50rem;
    transition: .3s;
}
.dot-glow-sm{
    width: 7.5px;
    height: 7.5px;
    min-width: 7.5px;
}
.dot-glow-success{
    box-shadow: 0 0 0 0.25rem #19d89533;
    background-color: #19d895;
}
.dot-glow-danger{
    box-shadow: 0 0 0 .25rem #ff625833;
    background-color: #ff6258;
}
.dot-glow-secondary{
    box-shadow: 0 0 0 .25rem #c0c2c333;
    background-color: #c0c2c3;
}
.dot-glow-primary{
    box-shadow: 0 0 0 .25rem #2196f333;
    background-color: #2196f3;
}
.dot-glow-warning{
    box-shadow: 0 0 0 .25rem #ffaf0033;
    background-color: #ffaf00;
}

.p-05{
    padding: .125rem !important;
}
.pt-05{
    padding-top : .125rem!important;
}
.pb-05{
    padding-bottom : .125rem!important;
}
.pr-05{
    padding-right : .125rem!important;
}
.pl-05{
    padding-left : .125rem!important;
}
.px-05{
    padding-left: .125rem !important;
    padding-right: .125rem !important;
}
.py-05 {
    padding-top: .125rem !important;
    padding-bottom: .125rem !important;
}

.m-05{
  margin: .125rem !important;
}
.mt-05{
  margin-top : .125rem!important;
}
.mb-05{
  margin-bottom : .125rem!important;
}
.mr-05{
  margin-right : .125rem!important;
}
.ml-05{
  margin-left : .125rem!important;
}
.mx-05{
  margin-left: .125rem !important;
  margin-right: .125rem !important;
}
.my-05 {
  margin-top: .125rem !important;
  margin-bottom: .125rem !important;
}

.m--1{
  margin: -0.25rem !important;
}
.mt--1{
  margin-top : -.25rem!important;
}
.mb--1{
  margin-bottom : -.25rem!important;
}
.mr--1{
  margin-right : -.25rem!important;
}
.ml--1{
  margin-left : -.25rem!important;
}
.mx--1{
  margin-left: -0.25rem !important;
  margin-right: -0.25rem !important;
}
.my--1{
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}


.m--2{
  margin: -0.5rem !important;
}
.mt--2{
  margin-top : -.5rem!important;
}
.mb--2{
  margin-bottom : -.5rem!important;
}
.mr--2{
  margin-right : -.5rem!important;
}
.ml--2{
  margin-left : -.5rem!important;
}
.mx--2{
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}
.my--2{
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}

.m--3{
  margin: -1rem !important;
}
.mt--3{
  margin-top : -1rem!important;
}
.mb--3{
  margin-bottom : -1rem!important;
}
.mr--3{
  margin-right : -1rem!important;
}
.ml--3{
  margin-left : -1rem!important;
}
.mx--3{
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}
.my--3{
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}

.m--4{
  margin: -1.5rem !important;
}
.mt--4{
  margin-top : -1.5rem!important;
}
.mb--4{
  margin-bottom : -1.5rem!important;
}
.mr--4{
  margin-right : -1.5rem!important;
}
.ml--4{
  margin-left : -1.5rem!important;
}
.mx--4{
  margin-left: -1.5rem !important;
  margin-right: -1.5rem !important;
}
.my--4{
  margin-top: -1.5rem !important;
  margin-bottom: -1.5rem !important;
}

.scale-0_5{
  transform: scale(.5);
}
.scale-0_75{
  transform: scale(.75);
}
.scale-1_25{
  transform: scale(1.25);
}
.scale-1_5{
  transform: scale(1.5);
}
.scale-2{
  transform: scale(2);
}

.vertical-divider{
  width: auto;
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 0;

  display: flex;
  justify-content: center;
  align-items: center;
}
.vertical-divider:after{
  content: "";
  border-right: 1px solid #ced4da;
  left: calc(50% - 1px);
  width: 1px;
  height: 100%;
}

.tessa-fab{
  box-shadow: 0 0 .5rem .1rem rgba(0, 0, 0, .2)!important;
  display: flex;
  padding: 1rem;
  border-radius: 50% !important;
  color: white!important;
}

.table-td{
  width: 100%;
}
.table-td td, .table-td th{
  vertical-align: middle;
  font-size: 0.875rem;
  line-height: 1;
  white-space: nowrap;
  height: 35px;
  padding: 18px 15px;
}

/*Explorer*/
.explorer-div h5{
  overflow: hidden;
  max-height: 75px;
  word-break: break-all;
  white-space: nowrap;
}

.settings-content{
  cursor: auto;
}

.form-check-custom{
  width: 1em;
  height: 1em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0,0,0,.25);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  border-radius: 0.25em;
  cursor: pointer;
  margin-top: .25rem;
}
.form-check-custom:checked[type=checkbox] {
  background-image: url("https://test.mijntessa.nl/client/public/svg/checkbox.svg");
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.form-check-custom:focus{
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgb(13 110 253 / 25%);
}
.form-check-custom:active{
  filter: brightness(90%);
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.form-check-custom:disabled {
  pointer-events: none;
  filter: none;
  opacity: .5;
}

.form-switch-custom{
  cursor: pointer;
  width: 2em;
  min-width: 2em;
  background-image: url("https://test.mijntessa.nl/client/public/svg/switch.svg");
  background-position: left center;
  border-radius: 2em;
  transition: background-position .15s ease-in-out;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  margin-top: 0.2rem;
  height: 1em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-size: contain;
  border: 1px solid rgba(0,0,0,.25);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
}
.form-switch-custom:checked[type=checkbox] {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-position: right center;
  background-image: url("https://test.mijntessa.nl/client/public/svg/switch-checked.svg");
}
.form-switch-custom:active {
  filter: brightness(90%);
}
.form-switch-custom:focus {
  background-image: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e);
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgb(13 110 253 / 25%);
}
.form-switch-custom:disabled {
  pointer-events: none;
  filter: none;
  opacity: .5;
}

.tessa-switch-outside{
  display: inline-block;
}
.tessa-switch-container{
  display: flex;
  position: relative;
  overflow: auto;
  border-radius: 50rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
  background: #009BFF;
  color: #FFF;
  cursor: pointer;
}
.tessa-switch-toggle-container{
  position: absolute;
  height: 100%;
  width: 50%;
  padding: 0.25rem;
  transition: .3s;
}
.tessa-switch-toggle{
  background-color: #FFF;
  border-radius: 50rem;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #252C46;
  font-weight: 500;
}

.font-size-unset{
  font-size: unset!important;
}
.font-size-025{
  font-size: .25rem!important;
}
.font-size-05{
  font-size: .5rem!important;
}
.font-size-06{
  font-size: .6rem!important;
}
.font-size-07{
  font-size: .7rem!important;
}
.font-size-075{
  font-size: .75rem!important;
}
.font-size-08{
  font-size: .8rem!important;
}
.font-size-085{
  font-size: .85rem!important;
}
.font-size-09{
  font-size: .9rem!important;
}
.font-size-1{
  font-size: 1rem!important;
}
.font-size-11{
  font-size: 1.1rem!important;
}
.font-size-12{
  font-size: 1.2rem!important;
}
.font-size-125{
  font-size: 1.25rem!important;
}
.font-size-15{
  font-size: 1.5rem!important;
}
.font-size-155{
  font-size: 1.55rem!important;
}
.font-size-175{
  font-size: 1.75rem!important;
}
.font-size-2{
  font-size: 2rem!important;
}
.font-size-25{
  font-size: 2.5rem!important;
}
.font-size-3{
  font-size: 3rem!important;
}
.font-size-35{
  font-size: 3.5rem!important;
}
.font-size-4{
  font-size: 4rem!important;
}

.sub-nav-content{
  padding-left: 1rem;
}
.sub-nav-item{
  cursor: pointer;
}
.sub-nav-item .sub-nav-icon{
  text-align: center;
}
.sub-nav-item .menu-icon{
  margin-right: 0!important;
}
.sub-nav-item .menu-title{
  padding: 0!important;
}
.sub-nav-item .nav-link{
  display: flex!important;
  justify-content: space-between !important;
  align-items: center!important;
}
.sub-nav-item .nav-item:hover{
  background-color: #f0f0f0;!important;
}
.sub-nav-item .menu-title{
  width: auto!important;
  position: unset!important;
  height: auto!important;
}

/*Get Signature*/
.signature-color{
  padding: 0.75rem;
  margin: 0 0.25rem;
  border-radius: 0.25rem !important;
  cursor: pointer;
  transition: .1s;
  opacity: .35;
}
.signature-color:hover{
  transform: scale(1.1);
  opacity: 1;
}
.signature-color.active{
  opacity: 1;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}

/*Stats nav*/
[data-stats-navigation][data-rounded="true"] [data-label]{
  font-size: .9rem;
  padding-left: .5rem;
}
[data-stats-navigation][data-rounded="true"] input:not(.select_search-search),
[data-stats-navigation][data-rounded="true"] select{
  font-size: .85rem;
  min-width: 150px;
  border-radius: 50rem;
}

/*Datatables*/
.dataTables_wrapper div.dataTables_info {
  padding-left: 0.85em;
}
.dataTables_wrapper div.dataTables_paginate {
  padding-right: 0.85em;
  padding-bottom: 0.85em;
}
.dataTables_wrapper div.dataTables_length {
  padding: 0.85em;
}
.dataTables_wrapper div.dataTables_filter {
  padding: 0.85em;
}

/*Offetes*/
.dataset-total, dataset-kostprijs{
  margin-left: 0.5rem !important;
}

/*Tippy*/
.tippy[type='number']::after{
  content: "";
  display: inline-block;
  cursor: pointer;
  width: 10px;
  height: 10px;
}
.tippy-tooltip[data-animatefill] {
  background-color: #333;
}

/*Accorderen*/
.accorderen-flow-container{
  white-space: nowrap;
}
.accorderen-flow-node{
  white-space: nowrap;
}
.accorderen-flow-node i{
  position: relative;
}

.accorderen-flow-node i:first-of-type:after{
  content: "";
  height: 7px;
  width: 1px;
  position: absolute;
  right: 0;
  top: 100%;
  background: #0000004a;
  left: 50%;
  transform: translate(-50%, 0);
}
.accorderen-flow-node.success{
  color: #19d895 !important;
}
.accorderen-flow-node.danger{
  color: #ff6258 !important;
}
.accorderen-flow-node.primary{
  color: #2196f3 !important;
}
.accorderen-flow-node.muted{
  color: #b4b4b4 !important;
}
.accorderen-flow-node:last-of-type i:after{
  content: "";
  display: none!important;
}

/*Display select*/
.ds-container{
  display: inline-flex;
  padding: .5rem;
  border-radius: 50rem;
  position: relative;
  box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
  overflow: auto;
  max-width: 100%;
}
.ds-container::-webkit-scrollbar {
  width: 10px;
  height: 5px;
}
.ds-container::-webkit-scrollbar-thumb {
  background: #888;
  border: 4px solid transparent;
  border-radius: 50rem;
}
.ds-container::-webkit-scrollbar-track {
  margin: 0 1.5rem;
}
.ds-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.ds-node{
  border-radius: 50rem;
  transition: .25s;
  padding: .25rem .75rem;
  cursor: pointer;
  position: relative;
  z-index: 2;
  margin: 0 .25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ds-node-label{
  padding: .25rem .75rem;
  position: relative;
  z-index: 2;
  margin: 0 .25rem;
}
.ds-placeholder{
  margin: 0 0.75rem;
  color: #858585;
  font-style: italic;
}
.ds-node[data-selected]{
  color: white!important;
}
.ds-node:hover{
  background-color: rgba(222, 226, 230, 0.4);
}
.ds-node:hover[data-selected]{
  background-color: unset;
}
.ds-selector{
  transition: .3s;
  position: absolute;
  background: #009bff;;
  z-index: 1;
  border-radius: 50rem;
}

.modal-dialog.modal-lg {
  width: 90vw;
  max-width: unset!important;
}

.absolute-center{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 1200px) {
    .modal-dialog{
        margin-top: 1rem!important;
    }
}
@media (max-width: 768px) {
  .rounded-m-unset{
    border-radius: unset!important;
  }

}
@media (max-width: 576px) {
  .explorer-div {
    width: 50px;
  }
  .explorer-div h5 {
    font-size: .7rem;
    max-height: 50px;
  }
  .explorer-div img, .explorer-thumbnail{
    width: 50px;
    height: 50px;
  }
}

/*Fixes*/
.show > .btn-inverse-primary.dropdown-toggle {
  color: #ffffff!important;
}

/*Draggable modals*/
.modal-header{
  display: none!important;
}

/*Dynamic loader*/
.append-loader{
  display: none;!important;
}
.dashboard-alert{
  position: absolute !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: red !important;
  opacity: 0.6;
  z-index: 10000 !important;
  margin-left: -16px;
  margin-top: 25px;
  display: none;
}

/*Hides text */
.hidetext {
  -webkit-text-security: disc; /* Default */
}

@media print {
  .no-print{
    display: none!important;
  }
  .page-body-wrapper{
    margin-top: 0!important;
  }
  .main-panel{
    width: 100%!important;
  }
}

.dashboard-progress-iframe{
  position: absolute;
  margin-left: 92.5%;
  margin-top: 40px;
}

.dashboard-table-header{
  position: fixed;
  margin: 0;
  width: 48.5%;
  background-color: rgba(255,255,255,255);
}

.dashboard-table {
  overflow: hidden;
  border: 1px solid #ccc;
  margin-top: 50px;
}

.dashboard-scroll-animate {
  animation: scroll 15s linear infinite;
}
.dashboard-scroll-planning-animate {
  animation: planningScroll 35s linear infinite;
}

.dashboard-table tbody {
  margin: 0;
  padding: 10px;
}

.dashboard-error-users{
  max-width: 610px;
  overflow: hidden;
  white-space: normal !important;
}

.dashboard-calendar-iframe{
  margin-top: 10px;
}

.modal-toolbox-md {
  max-width: 60%;
}

.dropdown-menu li {
  position: relative;
}
.dropdown-menu .dropdown-submenu {
  display: none;
  position: absolute;
  left: 100%;
  top: -7px;
}
.dropdown-menu .dropdown-submenu-left {
  right: 100%;
  left: auto;
}
.dropdown-menu > li:hover > .dropdown-submenu {
  display: block;
}


/*Whatsapp*/
.whatsapp-container{
  margin-top: 1.5rem;
  background-color: white;
  height: 85vh;
  color: black;
  border-radius: .5rem;
  overflow: hidden;
}
.whatsapp-container .text-muted{
  color: #E9EDEF;
}
.whatsapp-chat-preview{
  padding: .5rem 1rem;
  display: flex;
  justify-content: space-between;
}
.whatsapp-chat-container{
  background-image: url("https://infordb.ikbentessa.nl/client/public/img/whatsapp/wa_chat_background.jpg");
}

.whatsapp-chat-container{
  overflow: auto;
}

.whatsapp-message-container{
  display: flex;
  justify-content: start;
  transition: .15s;
}
.whatsapp-message-container[data-direction=to]{
  justify-content: end;
}

.whatsapp-message{
  max-width: 75%;
  padding: .35rem;
  background-color: white;
  border-radius: .5rem;
  margin: .15rem .25rem;
  min-width: 75px;
}
.whatsapp-message-container[data-direction=to] .whatsapp-message{
  background-color: #D9FDD3;
}
.whatsapp-message-highlight{
  background-color: rgba(6, 207, 156, 0.3);
}
.whatsapp-message-content img, .whatsapp-message-content video{
  max-width: 100%;
  max-height: 40vh;
}
.whatsapp-message-status{
  text-align: right;
  font-size: .65rem;
  margin: -.15rem 0;
  color: #393939;
}

.whatsapp-reply-container{
  border-left: 4px solid;
  padding: .5rem;
  background-color: #C0C2C333 !important;
  position: relative;
}
.whatsapp-reply-container[data-direction="to"]{
  border-color: #06cf9c;
}
.whatsapp-reply-container[data-direction="from"]{
  border-color: #A792FE;
}
.whatsapp-reply-redirect{
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.whatsapp-unread-count{
  display: flex;
  aspect-ratio: 1 / 1;
  background-color: #00D757 !important;
  color: white;
  border-radius: 100%;
  padding: .25rem;
  width: 20px;
  line-height: 0;
  align-items: center;
  justify-content: center;
}
.whatsapp-chats-sidebar{
  transition: .35s;
  background-color: #FFF;
  z-index: 9;
}

.whatsapp-iframe .whatsapp-container{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
}

.whatsapp-iframe .whatsapp-chats-sidebar{
  position: absolute;
  top: 0;
  left: 0;
}
.whatsapp-iframe .whatsapp-chats-sidebar[data-show=false]{
  left: -100%;
}


/*Nacalculatie*/
.table-nacalculatie *{
  font-size: .65rem!important;
}
.table-nacalculatie tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.03);
}
.table-nacalculatie thead tr{
  background-color: inherit!important;
}
.table-nacalculatie td{
  border: unset!important;
}
.table-nacalculatie td, .table-nacalculatie th{
  padding: .5rem!important;
}
