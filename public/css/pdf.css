.page-break{
  page-break-after: always;
}

.content-m-0 *{
  margin: 0!important;
}
.content-p-0 *{
  padding: 0!important;
}
.content-py-0 *{
  padding-top: 0!important;
  padding-bottom: 0!important;
}
.content-pl-0 *{
  padding-left: 0!important;
}
.content-pr-0 *{
  padding-right: 0!important;
}
.content-border-0 *{
  border: none!important;
}

.nobr{
  white-space: nowrap;
}

.avoid-break{
  page-break-inside: avoid;
}

.word-break{
  word-wrap: break-word;
}

table{
  border-collapse: collapse;
  width: 100%;
}
td, th{
  padding: 5px;
  vertical-align: top;
}
.table-py-0 td, .table-py-0 th{
  padding-top: 0;
  padding-bottom: 0;
}
.table-border-0 td, .table-border-0 th{
  border: none;
}

.my-0{
  margin-top: 0;
  margin-bottom: 0;
}
.my-10{
  margin-top: 10px;
  margin-bottom: 10px;
}
.my-20{
  margin-top: 20px;
  margin-bottom: 20px;
}
.my-40{
  margin-top: 40px;
  margin-bottom: 40px;
}
.my-60{
  margin-top: 60px;
  margin-bottom: 60px;
}

.py-0{
  padding-top: 0;
  padding-bottom: 0;
}
.py-20{
  padding-top: 20px;
  padding-bottom: 20px;
}
.py-40{
  padding-top: 40px;
  padding-bottom: 40px;
}
.py-60{
  padding-top: 60px;
  padding-bottom: 60px;
}

.d-inline{
  display: inline-block;
}
.d-block{
  display: block;
}

.border{
  border: 1px solid black;
}
.border-bottom{
  border-bottom: 1px solid black;
}
.border-right{
  border-right: 1px solid black;
}

.border-bottom-50{
  border-bottom: 1px solid rgba(0,0,0,0.5);
}
.border-right-50{
  border-right: 1px solid rgba(0,0,0,0.5);
}

.border-bottom-0{
  border-bottom: unset;
}
.border-top-0{
  border-top: unset;
}
.border-left-0{
  border-left: unset;
}
.border-right-0{
  border-right: unset;
}

.center{
  text-align: center;
}
.text-right{
  text-align: right
}
.text-left{
  text-align: left
}
.text-underline{
  text-decoration: underline !important;
}

.rounded{
  border-radius: 0.25rem !important;
}

.vertical-align-top{
  vertical-align: top;
}
.vertical-align-middle{
  vertical-align: middle;
}
.vertical-align-bottom{
  vertical-align: bottom;
}

.badge {
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  font-size: 11px;
  line-height: 1;
  padding: 4px 6px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
}
.badge:empty {
  display: inline-block;
  min-width: 10px;
  min-height: 10px;
  padding: 0;
  margin-right: 10px;
  border-radius: 100%;
}
.badge-success{
  background-color: #19d895;
  border: 1px solid #19d895;
  color: #ffffff;
}
.badge-inverse-success{
  background: rgba(25, 216, 149, 0.3);
  color: #19d895;
}
.badge-primary{
  background-color: #2196f3;
  border: 1px solid #2196f3;
  color: #ffffff;
}
.badge-inverse-primary{
  background: rgba(33, 150, 243, 0.3);
  color: #2196f3;
}
.badge-warning{
  background-color: #ffaf00;
  border: 1px solid #ffaf00;
  color: #ffffff;
}
.badge-inverse-warning{
  background: rgba(255, 175, 0, 0.3);
  color: #ffaf00;
}
.badge-danger{
  background-color: #ff6258;
  border: 1px solid #ff6258;
  color: #ffffff;
}
.badge-inverse-danger{
  background: rgba(255, 98, 88, 0.3);
  color: #ff6258;
}
.badge-secondary{
  background-color: #c0c2c3;
  border: 1px solid #c0c2c3;
  color: #ffffff;
}
.badge-inverse-secondary{
  background: rgba(192, 194, 195, 0.3);
  color: #c0c2c3;
}
.badge-dark{
  background-color: #252C46;
  border: 1px solid #252C46;
  color: #ffffff;
}
.badge-inverse-dark{
  background: rgba(37, 44, 70, 0.3);
  color: #252C46;
}

.w0{width: 0;}
.w5{width: 4.5%;}
.w10{width: 9%;}
.w15{width: 14.5%;}
.w20{width: 20%;}
.w25{width: 24%;}
.w30{width: 30%}
.w33{width: 31%;}
.w35{width: 35%;}
.w40{width: 40%;}
.w45{width: 44%;}
.w50{width: 49%;}
.w55{width: 54%;}
.w60{width: 60%;}
.w75{width: 73.5%;}
.w70{width: 69.5%;}
.w80{width: 80%;}
.w85{width: 85%;}
.w90{width: 90%;}
.w95{width: 95%;}
.w100{width: 100%;}

.opacity-25{
  opacity: .25;
}
.opacity-50{
  opacity: .5;
}
.opacity-75{
  opacity: .75;
}

.table-fixed{
  table-layout: fixed;
}
.table-auto{
  table-layout: auto;
}

.table-bordered th{
  border: 1px solid;
}
.table-bordered td{
  border: 1px solid;
}

.table-header-rotated {
  border-collapse: collapse;
}
.table-header-rotated td {
  width: 90px;
}
 .table-header-rotated th {
  padding: 5px 11px;
}
.table-header-rotated td {
  text-align: center;
  padding: 11px 5px;
  border: 1px solid #ccc;
}
 .table-header-rotated th.rotate {
  height: 146px;
  white-space: nowrap;
}
 .table-header-rotated th.rotate > div {
  -webkit-transform: translate(63px, 51px) rotate(315deg);
      -ms-transform: translate(63px, 51px) rotate(315deg);
          transform: translate(63px, 51px) rotate(315deg);
  width: 30px;
}
 .table-header-rotated th.rotate > div > span {
  border-bottom: 1px solid #ccc;
  padding: 5px 10px;
}
.table-header-rotated th.row-header {
  padding: 0 10px;
  border-bottom: 1px solid #ccc;
}

.table-nopad th, .table-nopad td{
  padding: 0!important;
}


.flex-nowrap{
  flex-wrap: nowrap;
}
.d-none{
  display: none;
}
.d-block{
  display: block;
}
.d-inline-block{
  display: inline-block;
}
.d-flex{
  display: flex;
  flex-wrap: nowrap;
}

.border-black{
  border: 1px solid #000 !important;
}

.border{
  border: 1px solid #dee2e6 !important;
}
.border-top{
  border-top: 1px solid #dee2e6 !important;
}
.border-bottom{
  border-bottom: 1px solid #dee2e6 !important;
}
.border-right{
  border-right: 1px solid #dee2e6 !important;
}
.border-left{
  border-left: 1px solid #dee2e6 !important;
}


.border-top-0{
  border-top: none!important;
}
.border-bottom-0{
  border-bottom: none!important;
}
.border-right-0{
  border-right: none!important;
}
.border-left-0{
  border-left: none!important;
}

.border-success{
  border-color: #19d895!important;
}
.border-danger{
  border-color: #ff6258!important;
}
.border-warning{
  border-color: #ffaf00!important;
}
.border-dark {
  border-color: #252C46 !important;
}


.rounded{
  border-radius: 0.25rem !important;
}
.rounded-top{
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}
.rounded-bottom{
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-circle{
  border-radius: 100% !important;
}

.w-0{
  width: 0;
}
.w-25{
  width: 25%;
}
.w-40{
  width: 40%;
}
.w-50{
  width: 50%;
}
.w-75{
  width: 75%;
}
.w-100{
  width: 100%;
}

.w-px-120{
  width: 120px
}

.shadow {
  box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 15%);
}

.m-0{
  margin: 0!important;
}
.my-0{
  margin-top: 0;
  margin-bottom: 0;
}
.mx-0{
  margin-left: 0;
  margin-right: 0;
}
.mr-0{
  margin-right: 0;
}
.ml-0{
  margin-left: 0;
}
.mt-0{
  margin-top: 0;
}
.mb-0{
  margin-bottom: 0;
}


.m-1{
  margin: .25rem;
}
.my-1{
  margin-top: .25rem;
  margin-bottom: .25rem;
}
.mx-1{
  margin-left: .25rem;
  margin-right: .25rem;
}

.mr-1{
  margin-right: .25rem;
}
.ml-1{
  margin-left: .25rem;
}
.mt-1{
  margin-top: .25rem;
}
.mb-1{
  margin-bottom: .25rem;
}

.m-2{
  margin: .5rem;
}
.my-2{
  margin-top: .5rem;
  margin-bottom: .5rem;
}
.mx-2{
  margin-left: .5rem!important;
  margin-right: .5rem!important;
}

.mr-2{
  margin-right: .5rem;
}
.ml-2{
  margin-left: .5rem;
}
.mt-2{
  margin-top: .5rem;
}
.mb-2{
  margin-bottom: .5rem;
}

.m-3{
  margin: 1rem;
}
.my-3{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mx-3{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mr-3{
  margin-right: 1rem;
}
.ml-3{
  margin-left: 1rem;
}
.mt-3{
  margin-top: 1rem;
}
.mt-4{
  margin-top: 1.5rem;
}
.mb-4{
  margin-bottom: 1.5rem;
}
.mb-3{
  margin-bottom: 1rem;
}

.m-5 {
  margin: 3rem !important;
}
.my-5{
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;

}
.mx-5{
  margin-left: 3rem !important;
  margin-right: 3rem !important;
}
.mb-5{
  margin-bottom: 3rem !important;
}
.mt-5{
  margin-top: 3rem !important;
}

.mt--1px{
  margin-top: -1px;
}
.mb--1px{
  margin-bottom: -1px;
}
.mr--1px{
  margin-right: -1px;
}
.ml--1px{
  margin-right: -1px;
}



.p-0{
  padding: 0!important;
}
.py-0{
  padding-top: 0!important;
  padding-bottom: 0!important;
}
.px-0{
  padding-left: 0!important;
  padding-right: 0!important;
}
.pr-0{
  padding-right: 0!important;
}
.pl-0{
  padding-left: 0!important;
}
.pt-0{
  padding-top: 0!important;
}
.pb-0{
  padding-bottom: 0!important;
}

.p-1{
  padding: .25rem;
}
.py-1{
  padding-top: .25rem;
  padding-bottom: .25rem;
}
.px-1{
  padding-left: .25rem;
  padding-right: .25rem;
}
.pr-1{
  padding-right: .25rem
}
.pl-1{
  padding-left: .25rem
}
.pt-1{
  padding-top: .25rem
}
.pb-1{
  padding-bottom: .25rem
}

.p-2{
  padding: .5rem;
}
.py-2{
  padding-top: .5rem;
  padding-bottom: .5rem;
}
.px-2{
  padding-left: .5rem;
  padding-right: .5rem;
}
.pr-2{
  padding-right: .5rem
}
.pl-2{
  padding-left: .5rem
}
.pt-2{
  padding-top: .5rem
}
.pb-2{
  padding-bottom: .5rem
}

.p-3{
  padding: 1rem;
}
.py-3{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.px-3{
  padding-left: 1rem;
  padding-right: 1rem;
}
.pr-3{
  padding-right: 1rem
}
.pl-3{
  padding-left: 1rem
}
.pt-3{
  padding-top: 1rem
}
.pb-3{
  padding-bottom: 1rem
}

.p-4{
  padding: 1.5rem;
}
.py-4{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.px-4{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.pr-4{
  padding-right: 1.5rem
}
.pl-4{
  padding-left: 1.5rem
}
.pt-4{
  padding-top: 1.5rem
}
.pb-4{
  padding-bottom: 1.5rem
}

.p-5{
  padding: 3rem;
}
.py-5{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.px-5{
  padding-left: 3rem;
  padding-right: 3rem;
}
.pr-5{
  padding-right: 3rem
}
.pl-5{
  padding-left: 3rem
}
.pt-5{
  padding-top: 3rem
}
.pb-5{
  padding-bottom: 3rem
}

.text-white{
  color: #FFF;
}
.text-success{
  color: #19d895B2;
}
.text-warning {
  color: #ffaf00 !important;
}
.text-danger {
  color: #ff6258 !important;
}
.text-primary {
  color: #2196f3 !important;
}
.text-dark {
  color: #252C46 !important;
}
.text-black {
  color: #000 !important;
}
.text-secondary {
  color: #c0c2c3 !important;
}
.text-muted {
  color: #6c757d!important;
}

.bg-white {
  background-color: #FFFFFF!important;
}
.bg-success {
  background-color: #19d895!important;
}
.bg-inverse-success {
  background: rgba(25, 216, 149, 0.2);
}
.bg-danger{
  background-color: #ff6258!important;
}
.bg-inverse-danger {
  background: rgba(255, 98, 88, 0.2);
}
.bg-primary{
  background-color: #13AFF9!important;
}
.bg-inverse-primary{
  background-color: #13AFF933!important;
}
.bg-warning{
  background-color: #ffaf00!important;
}
.bg-inverse-warning {
  background: rgba(255, 175, 0, 0.2);
}
.bg-secondary{
  background-color: #C0C2C3!important;
}
.bg-inverse-secondary{
  background: #C0C2C333!important;
}
.bg-playstore{
  background-color: #009367!important;
}
.bg-reverse{
  background-color: black;
}
.bg-unset{
  background-color: unset;
}
.bg-light-grey{
  background: #F2F3F3!important;
}

.overflow-hidden{
  overflow: hidden;
}
.overflow-auto{
  overflow: auto;
}

.text-input{
  color: black;
  font-size: 16px;
}

.text{
  color: black;
}

.flex-between{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.m-h-25{
  min-height: 25%;
}
.m-h-50{
  min-height: 50%;
}
.m-h-100{
  min-height: 100%;
}

.h-auto{
  height: auto;
}
.h-25{
  height: 25%;
}
.h-50{
  height: 50%;
}
.h-100{
  height: 100%;
}

.h-10{
  height: 10px;
}
.h-5{
  height: 5px;
}
.h-1{
  height: 1px;
}

.rotate-45 {
  transform: rotate(45deg)
}
.rotate-90 {
  transform: rotate(90deg)
}
.rotate-180 {
  transform: rotate(180deg)
}
.rotate-270 {
  transform: rotate(270deg)
}
.rotate-360 {
  transform: rotate(360deg)
}

.transition-01 {
  transition: .1s;
}
.transition-02 {
  transition: .2s;
}
.transition-025 {
  transition: .25s;
}
.transition-03 {
  transition: .3s;
}
.transition-04 {
  transition: .4s;
}
.transition-05 {
  transition: .5s;
}

.font-size-025{
  font-size: .25rem!important;
}
.font-size-05{
  font-size: .5rem!important;
}
.font-size-075{
  font-size: .75rem!important;
}
.font-size-08{
  font-size: .8rem!important;
}
.font-size-09{
  font-size: .9rem!important;
}
.font-size-1{
  font-size: 1rem!important;
}
.font-size-11{
  font-size: 1.1rem!important;
}
.font-size-12{
  font-size: 1.2rem!important;
}
.font-size-125{
  font-size: 1.25rem!important;
}
.font-size-15{
  font-size: 1.5rem!important;
}
.font-size-175{
  font-size: 1.75rem!important;
}
.font-size-2{
  font-size: 2rem!important;
}

.pointer-event-none {
  pointer-events: none;
}

ion-card{
  background-color: rgb(245, 245, 245);
}
.card-color{
  background-color: rgb(250, 250, 250);
}

.alert{
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.alert-success {
  color: #13a471;
  background-color: rgba(25, 216, 149, 0.2);
  border-color: #17c789;
}
.alert-danger {
  color: #c24a43;
  background-color: rgba(255, 98, 88, 0.2);
  border-color: #eb5a51;
}
.alert-warning {
  color: #c28500;
  background-color: rgba(255, 175, 0, 0.2);
  border-color: #eba100;
}

.opacity-25{
  opacity: 0.25;
}
.opacity-50{
  opacity: 0.50;
}
.opacity-75{
  opacity: 0.75;
}

.content-bg-unset, .content-bg-unset *{
  background-color: unset!important;
}

.modal-container{
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center
}

.position-relative{
  position: relative;
}
.position-absolute{
  position: absolute;
}

.top-0{
  top: 0;
}
.bottom-0{
  bottom: 0;
}

.btn{
  text-decoration: none;
  display: inline-block;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 0.56rem 1.375rem;
  line-height: 1;
  border-radius: 0.1875rem;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
.btn-sm{
  padding: 0.4rem 0.81rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.1875rem;
  text-decoration: none;
  display: inline-block;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.btn-light {
  color: #212529;
  background-color: #fbfbfb;
  border-color: #fbfbfb;
}
.btn-light:hover {
  color: #212529;
  background-color: #e8e8e8;
  border-color: #e2e2e2;
}

.btn-primary{
  color: #fff;
  background-color: #2196f3;
  border-color: #2196f3;
}
.btn-primary:hover{
  color: #fff;
  background-color: #0c83e2;
  border-color: #0c7cd5;
}
.btn-inverse-primary {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.2);
  background-image: none;
  border-color: rgba(33, 150, 243, 0);
}
.btn-inverse-primary:hover {
  color: #ffffff;
  background-color: #2196f3;
  border-color: #2196f3;
}

.btn-danger {
  color: #fff;
  background-color: #ff6258;
  border-color: #ff6258;
}
.btn-danger:hover {
  color: #fff;
  background-color: #ff3e32;
  border-color: #ff3225;
}
.btn-inverse-danger{
  color: #ff6258;
  background-color: rgba(255, 98, 88, 0.2);
  background-image: none;
  border-color: rgba(255, 98, 88, 0);
}
.btn-inverse-danger:hover {
  color: #ffffff;
  background-color: #ff6258;
  border-color: #ff6258;
}

.btn-warning {
  color: #fff;
  background-color: #ffaf00;
  border-color: #ffaf00;
}
.btn-warning:hover {
  color: #FFF;
  background-color: #d99500;
  border-color: #cc8c00;
}
.btn-inverse-warning {
  color: #ffaf00;
  background-color: rgba(255, 175, 0, 0.2);
  background-image: none;
  border-color: rgba(255, 175, 0, 0);
}
.btn-inverse-warning:hover {
  color: #ffffff;
  background-color: #ffaf00;
  border-color: #ffaf00;
}

.btn-success {
  color: #FFF;
  background-color: #19d895;
  border-color: #19d895;
}
.btn-success:hover {
  color: #fff;
  background-color: #15b67d;
  border-color: #14aa75;
}
.btn-inverse-success {
  color: #19d895;
  background-color: rgba(25, 216, 149, 0.2);
  background-image: none;
  border-color: rgba(25, 216, 149, 0);
}
.btn-inverse-success:hover {
  color: #ffffff;
  background-color: #19d895;
  border-color: #19d895;
}

.btn-playsotre {
  color: #fff;
  background-color: #009367;
  border-color: #009367;
}
.btn-applestore {
  color: #fff;
  background-color: #13AFF9;
  border-color: #13AFF9;
}

.card{
  margin: 1rem;
  padding: 1rem;
  border-radius: 0.25rem;
}

.no-ripple{
  --ripple-color: transparent;
}
.modal-select-bottom{
  width: 100%;
  align-self: flex-end!important;
}

.fr-second-toolbar, .fr-newline{
  display: none!important;
}
.fr-wrapper{
  border-radius: 0 0 10px 10px!important;
}
.fr-element{
  border-top: 1px solid;
}
.fr-box{
  border: 1px solid;
}

.mh-25-vh{
  max-height: 25vh;
}
.mh-33-vh{
  max-height: 33vh;
}
.mh-50-vh{
  max-height: 50vh;
}
.mh-66-vh{
  max-height: 66vh;
}
.mh-75-vh{
  max-height: 75vh;
}

.m--1{
  margin: -0.25rem !important;
}
.mt--1{
  margin-top : -.25rem!important;
}
.mb--1{
  margin-bottom : -.25rem!important;
}
.mr--1{
  margin-right : -.25rem!important;
}
.ml--1{
  margin-left : -.25rem!important;
}
.mx--1{
  margin-left: -0.25rem !important;
  margin-right: -0.25rem !important;
}
.my--1{
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}


.m--2{
  margin: -0.5rem !important;
}
.mt--2{
  margin-top : -.5rem!important;
}
.mb--2{
  margin-bottom : -.5rem!important;
}
.mr--2{
  margin-right : -.5rem!important;
}
.ml--2{
  margin-left : -.5rem!important;
}
.mx--2{
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}
.my--2{
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}

.m--3{
  margin: -1rem !important;
}
.mt--3{
  margin-top : -1rem!important;
}
.mb--3{
  margin-bottom : -1rem!important;
}
.mr--3{
  margin-right : -1rem!important;
}
.ml--3{
  margin-left : -1rem!important;
}
.mx--3{
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}
.my--3{
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}
