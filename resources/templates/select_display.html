<div class="ds-container" data-display-select-container="bvs" >
  <a class="ds-node" data-value="1" >InforDB</a>
  <a class="ds-node" data-value="2" data-selected >Wabbes</a>
  <a class="ds-node" data-value="3" >3DPL</a>
</div>



<!--
- First value will be selected automatically if no value is specified
- $(document).ready generates an instance based on the provided id: _displaySelect['bvs']
- To specify the onchange event: instance.onchange = (value) => { console.log(value) };  function will be triggerred with the selecte value as parameter
- To select an option via the code: instance.select(value);

-->
