@extends('layouts.app')

@section('title', 'Alle gebruikers')

@section('content')
  @php
    $kopjes = getUserOptieVelden();
    $usersById = getUsers()->keyBy('id');
  @endphp
  <div class="card p-2 my-2">
      <div class="overflow-auto append-loader">
        <a class="btn btn-light rounded-9 border" href="{{url('users/export/excel')}}" >Excel export  @icon_wrench</a>
        <table id="users-table">
          <thead>
            <tr>
              <th>#</th>
              <th>Naam</th>
              <th>Email</th>
              <th>Rol</th>
              <th>Vestiging</th>
              <th>Wachtwoord gewijzigd op</th>
              @if(count(getBvs()) > 1)
                <th>BV</th>
              @endif
              @foreach($kopjes as $kopKey=>$velden)
                @foreach($velden as $key=>$val)
                  @if(getSettingCheckbox("user_overzicht_tonen_".$key))
                    <th>{{$val}}</th>
                  @endif
                @endforeach
              @endforeach
              <th>Laatst online op</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            @php
              $users = isset($_GET['inactive']) ? getInactiveUsers() : getUsers();
            @endphp
            @foreach ($users as $user)
              <tr data-tr-user="{{$user->id}}" >
                <td data-sort="{{$user->pnumber}}" class="w-0" > <span class="badge badge-lg badge-dark w-100" >{{ $user->pnumber ?? '-' }}</span> </td>
                <td>{{ $user->name }} {{$user->lastname}}</td>
                <td>{{ $user->email  }}</td>
                <td>{{ $user->role->name ?? '-' }}</td>
                <td>{{ $user->vestiging->naam ?? '' }}</td>
                <td>{{ $user->password_reset_at ? CarbonDmy($user->password_reset_at) : 'Nooit' }}</td>
                @if(count(getBvs()) > 1)
                  <td>{{$user->bv->name ?? ''}}</td>
                @endif
                @foreach($kopjes as $kopKey=>$velden)
                  @foreach($velden as $key=>$val)
                    @if(getSettingCheckbox("user_overzicht_tonen_".$key))
                      @if($key == 'leidinggevende')
                        <td>{{ $usersById[$user->$key]->name ?? ''}} {{$usersById[$user->$key]->lastname ?? ''}}</td>
                      @elseif($key == 'dateofbirth')
                        <td>{{ CarbonDmy($user->$key) ?? ''}}</td>
                      @elseif($key == 'leverancier_id')
                        <td>{{ $user->leverancier->naam ?? ''}}</td>
                      @else
                        <td>{{$user->$key}}</td>
                      @endif
                    @endif
                  @endforeach
                @endforeach
                <td>
                  @if(!$user->is_admin)
                    @if($user->lastOnline())
                      @if(Carbon()->diffInMinutes( $user->lastOnline() ) < 5)
                        <span class="w-100 rounded-pill badge badge-success" >Online</span>
                      @else
                        <span class="w-100 rounded-pill badge badge-primary" >{{$user->lastOnline()->format('d-m-Y H:i')}}</span>
                      @endif
                    @else
                      <span class="w-100 rounded-pill badge badge-danger" >Nooit</span>
                    @endif
                  @endif
                </td>
                <td class="w-0" >
                  <div class="d-flex justify-content-end mx--1" >
                    @if(hasPermission('Machines beheren') && !$user->machines->isEmpty())
                      <a class="btn btn-light tippy mx-1" data-tippy-content="Machines" onclick="showMachines({{$user->id}})"><i class="menu-icon bi bi-printer-fill"></i></a>
                    @endif
                    @if(hasModule('Bestanden'))
                      <a class="btn btn-light tippy mx-1" data-tippy-content="Bestanden" onclick="openExplorerPath({path: '/Gebruikers/({{$user->id}})'})" >@icon_file</a>
                    @endif
                    @if(hasModule('Urenregistratie'))
                      <div class="dropdown mx-1">
                        <button class="btn btn-inverse-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">Urenregistratie</button>
                        <div class="dropdown-menu">
                          <a class="dropdown-item" href="{{ url('/users/overview/'.$user->id.'?page=1') }}" >Urenoverzicht</a>
                          <a class="dropdown-item" href="{{ url('/users/default/'.$user->id) }}" >Standaarduren</a>
                        </div>
                      </div>
                    @endif

                    <div class="dropdown mx-1">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">Acties</button>
                        <div class="dropdown-menu">
                          <a class="dropdown-item" href="{{ url('/users/create/'.$user->id) }}" >Wijzigen</a>
                          @if(Auth::user()->hasPermissionTo('Wachtwoorden beheren') && hasModule('Wachtwoordenkluis'))
                          <a class="dropdown-item" onclick="ontneemWachtwoordRechten({{$user->id}})" >Ontneem wachtwoord rechten</a>
                          @endif
                          <a class="dropdown-item" onclick="deleteUser({{$user->id}})" >Verwijderen</a>
                        </div>
                    </div>
                  </div>
                </td>
              </tr>
            @endforeach
          </tbody>
        </table>
      </div>
  </div>
@endsection
@section('script')
  <script>
    const users = @json($users);

    $(document).ready(() => {
      tableInit('#users-table', {
        columnDefs: [{orderable: false, targets: [-1]}],
        order: [[0, 'asc']],
        pageLength: 100,
      })
    })

    function deleteUser(id){
      const user = users.find(row => row.id == id);

      if(!user){
        notification('Er is iets foutgegaan!');
        return;
      }

      confirmModal({
        text: `Weet je zeker dat je <b>${user.name || ''} ${user.lastname || ''}</b> wilt verwijderen?`,
        btnColor: 'btn-danger',
      })
        .then(response => {
          if(!response.status){ return; }

          loader();
          ajax('api/users/delete', {user: id})
            .then(() => {

              $(`[data-tr-user=${id}]`).remove();
              successLoader();
            })
            .catch(() => errorLoader());

        })

    }

    function ontneemWachtwoordRechten(id){
      const user = users.find(row => row.id == id);

      if(!user){
        notification('Er is iets foutgegaan!');
        return;
      }

      confirmModal({
        text: `Weet je zeker dat je gebruiker <b>${user.name || ''} ${user.lastname || ''}</b> de rechten van het inzien van wachtwoorden wilt ontnemen? `,
        btnColor: 'btn-danger',
      })
        .then(response => {
          if(!response.status){ return; }

          loader();
          ajax('api/wachtwoordkluis/ontneemwachtwoordrechten', {userId: id})
            .then(() => {
              successLoader();
            })
            .catch(() => errorLoader());

        })

    }

    function showMachines(user_id) {
      const user = users.find(row => row.id == user_id);

      // Maak 1 array van alle parameters
      const allParametersFlat = user.machines.flatMap(machine =>
        machine.parameters.map(param => param.parameter)
      );

      // Filter alle dubbele parameters eruit
      const allParameters = allParametersFlat.filter((param, index, array) =>
        array.indexOf(param) === index
      );

      let tableHeaders = allParameters.map(param => `<th>${param}</th>`).join('');
      tableHeaders = `<tr><th>Machine naam</th>${tableHeaders}</tr>`;

      let tableRows = user.machines.map(machine => {
        const paramValues = Object.fromEntries(machine.parameters.map(param => [param.parameter, param.value]));

        let row = allParameters.map(param => `<td>${paramValues[param] || ''}</td>`).join('');
        return `<tr><td>${machine.name}</td>${row}</tr>`;
      }).join('');

      const tableHTML = `
        <table class='table table-striped'>
            <thead>${tableHeaders}</thead>
            <tbody>${tableRows}</tbody>
        </table>
    `;

      confirmModal({
        text:  `<div style="overflow-x: auto; max-width: 100%;">${tableHTML}</div>`,
        hideFooter: true,
        large: true });
    }

  </script>
@endsection
