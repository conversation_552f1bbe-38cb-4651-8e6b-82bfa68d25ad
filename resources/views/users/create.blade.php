@extends('layouts.app')

@if(isset($user))
  @section('title', 'Gebruiker ' . $user->name . ' ' . $user->lastname . ' wijzigen')
@else
  @section('title', "Gebruiker aanmaken")
@endif

@section('content')

  <form data-overflow-x-only class="append-loader my-2" method="POST" enctype="multipart/form-data" data-main-form >

    {{--Verplichte velden--}}
    <section>
      <div class="font-weight-semibold opacity-50" >Verplichte velden</div>
      <div class="card p-2 mb-3">
        <div class="row">
          <div class="col-md-6 col-12 my-2">
            <label>Voornaam</label>
            <input name="name" class="form-control-custom" value="{{ $user->name ?? ''}}" required placeholder="Voornaam"/>
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>Achternaam</label>
            <input name="lastname" class="form-control-custom" value="{{ $user->lastname ?? ''}}" required placeholder="Achternaam"/>
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>Email</label>
            <input name="email" class="form-control-custom" value="{{ $user->email ?? ''}}" required placeholder="<EMAIL>" pattern="^[^\s@]+@[^\s@]+\.[^\s@]+$" title="Het ingevoerde Email adres is ongeldig" />
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>Wachtwoord</label>
            <input name="password" type="password" class="form-control-custom" placeholder="Wachtwoord"/>
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>Rol</label>
            <select name="role" class="form-select" required>
              @foreach($roles as $role)<option @if(isset($user) && $user->role_id == $role->id)selected @endif value="{{ $role->id }}">{{ $role->name }}</option>@endforeach
            </select>
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>BV</label>
            <select name="bv" class="form-select" required>
              @foreach($bvs as $bv)<option @if(isset($user) && $user->bv_id == $bv->id)selected @endif value="{{ $bv->id }}">{{ $bv->name }}</option>@endforeach
              <option @if(isset($user) && !$user->bv_id) selected @endif disabled>Maak een keuze</option>
            </select>
          </div>
          @if(getSettingCheckbox('user_koppelen_aan_leverancier'))
            <div class="col-md-6 col-12 my-2">
              <label>Leverancier</label>
              <select name="leverancier" class="form-select">
                <option value="">Geen Leverancier</option>
                @foreach(getLeveranciers() as $leverancier)
                  <option @if(isset($user) && $user->leverancier_id == $leverancier->id) selected @endif value="{{ $leverancier->id }}">{{ $leverancier->naam }}</option>
                @endforeach
              </select>
            </div>
          @endif
        </div>
      </div>
    </section>

    @if(!getSettingCheckbox("user_optioneel_veld_label_personeelsgegevens"))
      <div class="font-weight-semibold opacity-50" >Persoonsgegevens</div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_geslacht"))
          <div class="col-md-6 col-12 my-2">
              <label>Geslacht</label>
              <select name="geslacht" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_geslacht")) required @endif>
                <option @if(isset($user) && $user->geslacht == 'man') selected @endif value="man">man</option>
                <option @if(isset($user) && $user->geslacht == 'vrouw') selected @endif value="vrouw">vrouw</option>
                <option @if(isset($user) && $user->geslacht == 'anders') selected @endif value="anders">anders</option>
              </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_burgerlijke_staat"))
          <div class="col-md-6 col-12 my-2">
              <label>Burgerlijke staat</label>
              <select name="burgerlijkestaat" class="form-select"  @if(getSettingCheckbox("user_verplicht_veld_burgerlijke_staat")) required @endif >
                <option @if(isset($user) && $user->burgerlijkestaat == 'ongehuwd') selected @endif value="ongehuwd">ongehuwd</option>
                <option @if(isset($user) && $user->burgerlijkestaat == 'gehuwd') selected @endif value="gehuwd">gehuwd</option>
                <option @if(isset($user) && $user->burgerlijkestaat == 'gescheiden') selected @endif value="gescheiden">gescheiden</option>
                <option @if(isset($user) && $user->burgerlijkestaat == 'samenwonend') selected @endif value="samenwonend">samenwonend</option>
              </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_voorletters"))
            <div class="col-md-6 col-12 my-2">
              <label>Voorletters</label>
              <input name="voorletters" type="text" value="{{ $user->voorletters ?? '' }}" placeholder="Voorletters" class="form-control-custom"
                     @if(getSettingCheckbox("user_verplicht_veld_voorletters")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_initialen"))
          <div class="col-md-6 col-12 my-2">
            <label>Inititalen</label>
            <input name="initialen" type="text" value="{{ $user->initialen ?? '' }}" placeholder="initialen" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_initialen")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_telefoon"))
          <div class="col-md-6 col-12 my-2">
            <label>Telefoon</label>
            <input name="phone" type="text" value="{{ $user->phone ?? '' }}" placeholder="Telefoon" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_telefoon")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_telefoon_prive"))
          <div class="col-md-6 col-12 my-2">
            <label>Telefoon privé</label>
            <input name="phoneprivate" type="text" value="{{ $user->phone_private ?? '' }}" placeholder="Telefoon privé" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_telefoon_prive")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_telefoon_thuis"))
          <div class="col-md-6 col-12 my-2">
            <label>Telefoon thuis</label>
            <input name="telefoonthuis" type="text" value="{{ $user->telefoonthuis ?? '' }}" placeholder="Telefoon thuis" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_telefoon_thuis")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_email_prive"))
          <div class="col-md-6 col-12 my-2">
            <label>Email privé</label>
            <input name="emailprivate" type="text" value="{{ $user->email_private ?? '' }}" placeholder="Email privé" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_email_prive")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_straat_naam"))
          <div class="col-md-6 col-12 my-2">
            <label>Straatnaam</label>
            <input name="straat" type="text" value="{{ $user->straat ?? '' }}" placeholder="Straatnaam"  class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_straat_naam")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_huisnr"))
          <div class="col-md-6 col-12 my-2">
            <label>Huisnummer</label>
            <input name="huisnummer" type="text" value="{{ $user->huisnummer ?? '' }}" placeholder="Huisnummer"  class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_huisnr")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_toevoeging"))
          <div class="col-md-6 col-12 my-2">
            <label>Toevoeging</label>
            <input name="toevoeging" type="text" value="{{ $user->toevoeging ?? '' }}" placeholder="Toevoeging"  class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_toevoeging")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_postcode"))
          <div class="col-md-6 col-12 my-2">
            <label>Postcode</label>
            <input name="postcode" type="text" value="{{ $user->postcode ?? '' }}" placeholder="Postcode"  class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_postcode")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_woonplaats"))
          <div class="col-md-6 col-12 my-2">
            <label>Woonplaats</label>
            <input name="woonplaats" type="text" value="{{ $user->woonplaats ?? '' }}" placeholder="Woonplaats" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_woonplaats")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_geboorte_datum"))
          <div class="col-md-6 col-12 my-2">
            <label>Geboortedatum</label>
            <input name="dateofbirth" @if(isset($user) && $user->dateofbirth) value="{{ Carbon()->parse($user->dateofbirth)->format('Y-m-d') }}" @endif  type="date" class="date form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_geboorte_datum")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_geboorte_plaats"))
          <div class="col-md-6 col-12 my-2">
            <label>Geboorteplaats</label>
            <input name="geboorteplaats" type="text" value="{{ $user->geboorteplaats ?? '' }}" placeholder="Geboorteplaats" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_geboorte_plaats")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_bsn"))
          <div class="col-md-6 col-12 my-2">
            <label>BSN</label>
            <input name="bsn" type="text" value="{{ $user->bsn ?? '' }}" placeholder="BSN" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_bsn")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_kvknr"))
          <div class="col-md-6 col-12 my-2">
            <label>Kvk nr.</label>
            <input name="kvknr" type="text" value="{{ $user->kvknr ?? '' }}" placeholder="kvk nr" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_kvknr")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_link_kvk"))
          <div class="col-md-6 col-12 my-2">
            <label>Link KVK</label>
            <input name="link_kvk" value="{{ $user->link_kvk ?? '' }}" type="text" placeholder="Link kvk" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_link_kvk")) required @endif/>
          </div>
          @endif

        </div>
      </div>
    @endif


    @if(!getSettingCheckbox("user_optioneel_veld_label_contract"))
      <div class="font-weight-semibold opacity-50" >Contract</div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_vestiging"))
            <div class="col-md-6 col-12 my-2">
              <label>Vestiging</label>
              <select name="vestiging" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_vestiging")) required @endif>
                <option disabled hidden @if(isset($user) && !$user->vestiging_id) selected @endif >Selecteer vestiging</option>
                @foreach(getVestigingen() as $vestiging)
                  <option value="{{$vestiging->id}}" @if(isset($user) && $user->vestiging_id == $vestiging->id) selected @endif >{{$vestiging->naam ?? "$vestiging->plaats" }}</option>
                @endforeach
              </select>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_type"))
            <div class="col-md-6 col-12 my-2">
              <label>Type</label>
              <select name="jobtype" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_type")) required @endif>
                <option value="kantoor">Kantoor</option>
                <option @if(isset($user) && $user->jobtype == "buitendienst") selected @endif value="buitendienst">Buitendienst</option>
              </select>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_functie"))
            <div class="col-md-6 col-12 my-2">
              <label>Functie</label>
              <input name="function" type="text" value="{{ $user->functie ?? '' }}" placeholder="Functie"  class="form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_functie")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_datum_in_dienst"))
            <div class="col-md-6 col-12 my-2">
              <label>Datum in dienst</label>
              <input name="date_start" @if(isset($user) && $user->start_date) value="{{ Carbon()->parse($user->start_date)->format('Y-m-d') }}" @endif type="date" class="date form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_datum_in_dienst")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_datum_uit_dienst"))
            <div class="col-md-6 col-12 my-2">
              <label>Datum uit dienst</label>
              <input name="enddate" value="{{ $user->end_date ?? null }}" type="date" class="date form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_datum_uit_dienst")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_uur_tarief"))
            <div class="col-md-6 col-12 my-2">
              <label>Uurtarief</label>
              <input name="uurtarief" value="{{ $user->uurtarief ?? '' }}" type="number" step="0.01" placeholder="0" class="form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_uur_tarief")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_dagen_uren_per_week"))
            <div class="col-md-6 col-12 my-2">
              <label>Dagen/uren per week</label>
              <input name="dagenurenperweek" type="text" value="{{ $user->dagenurenperweek ?? '' }}" placeholder="Dagen/uren per week" class="form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_dagen_uren_per_week")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_standaard_verlofsaldo"))
            <div class="col-md-6 col-12 my-2">
              <label>Standaard verlof</label>
              <input name="standaard_verlofsaldo" type="number" step="0.01" min="0" value="{{ $user->standaard_verlofsaldo ?? '' }}" placeholder="Standaard verlofsaldo" class="form-control-custom"
                     @if(getSettingCheckbox("user_verplicht_veld_standaard_verlofsaldo")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_atv_dagen"))
            <div class="col-md-6 col-12 my-2">
              <label>ATV uren</label>
              <input name="atv_uren" type="number" step="0.01" min="0" value="{{ $user->atv_uren ?? '' }}" placeholder="ATV uren" class="form-control-custom"
                 @if(getSettingCheckbox("user_verplicht_veld_atv_dagen")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_overeengekomen_bruto_salaris"))
            <div class="col-md-6 col-12 my-2">
              <label>Overeengekomen bruto salaris</label>
              <input name="overeengekomenbrutosalaris" type="text" value="{{ $user->overeengekomenbrutosalaris ?? '' }}" placeholder="Overeengekomen bruto salaris" class="form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_overeengekomen_bruto_salaris")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_contact_duur"))
            <div class="col-md-6 col-12 my-2">
              <label>Contract duur</label>
              <input name="contractduur" type="text" value="{{ $user->contractduur ?? '' }}" placeholder="Contract duur" class="form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_contact_duur")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_personeels_nummer"))
            <div class="col-md-6 col-12 my-2">
              <label>Personeelsnummer</label>
              <input name="pnumber" value="{{ $user->pnumber ?? '' }}" type="text" placeholder="Personeelsnummer" class="form-control-custom"
              @if(getSettingCheckbox("user_verplicht_veld_contact_duur")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_nul_uren_contract"))
            <div class="col-md-6 col-12 my-2">
              <label>Nul uren contract</label>
              <select name="nul_uren" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_nul_uren_contract")) required @endif>
                <option value="0">Nee</option>
                <option @if(isset($user) && $user->nul_uren == 1) selected @endif  value="1">Ja</option>
              </select>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_all_in_contract"))
            <div class="col-md-6 col-12 my-2">
              <label>All-in contract</label>
              <select name="all_in" class="form-select"  @if(getSettingCheckbox("user_verplicht_veld_all_in_contract")) required @endif>
                <option @if(isset($user) && $user->all_in == 0) selected @endif value="0">Nee</option>
                <option @if(isset($user) && $user->all_in == 1) selected @endif value="1">Ja</option>
              </select>
            </div>
          @endif

        </div>
      </div>
    @endif



    @if(!getSettingCheckbox("user_optioneel_veld_label_documenten"))
      <div class="flex-align">
        <div class="font-weight-semibold opacity-50" >Documenten</div>
        @if(hasModule('Bestanden') && hasPermission('Alle bestanden bekijken') && isset($user))
          <a class="btn btn-sm mx-1 tippy" data-tippy-content="Bestanden" onclick="openExplorerPath({path: '/Gebruikers/({{$user->id}})'})" >@icon_file</a>
        @endif
      </div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_bhv"))
          <div class="col-md-6 col-12 my-2">
            <label>BHV</label>
            <select name="bhv" class="form-select"  @if(getSettingCheckbox("user_verplicht_veld_bhv")) required @endif>
              <option @if(isset($user) && $user->bhv == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->bhv == 1) selected @endif value="1">Ja</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_verloop_datum_bhv"))
          <div class="col-md-6 col-12 my-2">
            <label>Verloopdatum BHV</label>
            <input name="expiration_bhv" type="date" @if(isset($user) && $user->expiration_date_bhv) value="{{ Carbon()->parse($user->expiration_date_bhv)->format('Y-m-d') }}" @endif class="date form-control-custom"  @if(getSettingCheckbox("user_verplicht_veld_verloop_datum_bhv")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_vca"))
          <div class="col-md-6 col-12 my-2">
            <label>VCA</label>
            <select name="vca" class="form-select"  @if(getSettingCheckbox("user_verplicht_veld_vca")) required @endif>
              <option @if(isset($user) && $user->vca == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->vca == 1) selected @endif value="1">Basis</option>
              <option @if(isset($user) && $user->vca == 2) selected @endif value="2">Vol</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_verloop_datum_vca"))
          <div class="col-md-6 col-12 my-2">
            <label>Verloopdatum VCA</label>
            <input @if(isset($user) && $user->expiration_date_vca) value="{{ Carbon()->parse($user->expiration_date_vca)->format('Y-m-d') }}" @endif name="expiration_vca" type="date" class="date form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_verloop_datum_vca")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_verloop_link_vca"))
          <div class="col-md-6 col-12 my-2">
            <label>Link VCA</label>
            <input name="link_vca" value="{{ $user->link_vca ?? '' }}" type="text" placeholder="Link vca" class="form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_verloop_link_vca")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_verloop_datum_limosa"))
            <div class="col-md-6 col-12 my-2">
              <label>Verloopdatum Limosa</label>
              <input @if(isset($user) && $user->expiration_date_limosa) value="{{ $user->expiration_date_limosa }}" @endif name="expiration_limosa" type="date" class="date form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_verloop_datum_limosa")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_datum_behaald_hoogwerker"))
          <div class="col-md-6 col-12 my-2">
            <label>Datum behaald hoogwerker</label>
            <input @if(isset($user) && $user->expiration_date_hoogwerker) value="{{ Carbon()->parse($user->expiration_date_hoogwerker)->format('Y-m-d') }}" @endif name="expiration_date_hoogwerker" type="date" class="date form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_datum_behaald_hoogwerker")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_veilig_werken_langs_de_weg"))
          <div class="col-md-6 col-12 my-2">
            <label>Veilig werken langs de weg</label>
            <input @if(isset($user) && $user->veilig_werken_langs_de_weg) value="{{ Carbon()->parse($user->veilig_werken_langs_de_weg)->format('Y-m-d') }}" @endif name="veilig_werken_langs_de_weg" type="date" class="date form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_veilig_werken_langs_de_weg")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_ttg_verloop_datum"))
          <div class="col-md-6 col-12 my-2">
            <label>TTG verloop datum</label>
            <input @if(isset($user) && $user->ttg) value="{{ Carbon()->parse($user->ttg)->format('Y-m-d') }}" @endif name="ttg" type="date" class="date form-control-custom" @if(getSettingCheckbox("user_verplicht_veld_ttg_verloop_datum")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_rijbewijs"))
          <div class="col-md-6 col-12 my-2" data-input-container="driver_license" >
            <label>Rijbewijs</label>
            <infor-select-multiple class="form-control-custom" name="driver" placeholder="Rijbewijs" >
              <infor-select-option data-value="B" @if(isset($user) && $user->hasDrivingLicense("B")) data-selected @endif >B</infor-select-option>
              <infor-select-option data-value="BE" @if(isset($user) && $user->hasDrivingLicense("BE")) data-selected @endif >BE</infor-select-option>
              <infor-select-option data-value="C" @if(isset($user) && $user->hasDrivingLicense("C")) data-selected @endif >C</infor-select-option>
              <infor-select-option data-value="CE" @if(isset($user) && $user->hasDrivingLicense("CE")) data-selected @endif >CE</infor-select-option>
              <infor-select-option data-value="C1" @if(isset($user) && $user->hasDrivingLicense("C1")) data-selected @endif >C1</infor-select-option>
              <infor-select-option data-value="C1E" @if(isset($user) && $user->hasDrivingLicense("C1E")) data-selected @endif >C1E</infor-select-option>
            </infor-select-multiple>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_rijbewijs_verloop_datum"))
          <div class="col-md-6 col-12 my-2">
            <label>Rijbewijs verloop datum</label>
            <input @if(isset($user) && $user->expiration_date_driver_license) value="{{ Carbon()->parse($user->expiration_date_driver_license)->format('Y-m-d') }}" @endif name="expiration_date_driver_license" type="date" class="date form-control-custom"
            @if(getSettingCheckbox("user_verplicht_veld_rijbewijs_verloop_datum")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_inwerk_checklist_d9"))
          <div class="col-md-6 col-12 my-2">
            <label>Inwerkchecklist D9</label>
            <select name="inwerkchecklist_d9" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_inwerk_checklist_d9")) required @endif>
              <option @if(isset($user) && $user->inwerkchecklist_d9 == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->inwerkchecklist_d9 == 1) selected @endif value="1">Ja</option>
              <option @if(isset($user) && $user->inwerkchecklist_d9 == null) selected @endif value="">N.V.T.</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_pbm_d8"))
          <div class="col-md-6 col-12 my-2">
            <label>PBM D8</label>
            <select name="pbm_d8" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_pbm_d8")) required @endif>
              <option @if(isset($user) && $user->pbm_d8 == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->pbm_d8 == 1) selected @endif value="1">Ja</option>
              <option @if(isset($user) && $user->pbm_d8 == null) selected @endif value="">N.V.T.</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_personeel_dossier_d13"))
          <div class="col-md-6 col-12 my-2">
            <label>Personeeldossier D13</label>
            <select name="personeeldossier_d13" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_personeel_dossier_d13")) required @endif>
              <option @if(isset($user) && $user->personeeldossier_d13 == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->personeeldossier_d13 == 1) selected @endif value="1">Ja</option>
              <option @if(isset($user) && $user->personeeldossier_d13 == null) selected @endif value="">N.V.T.</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_calamiteiten_form"))
          <div class="col-md-6 col-12 my-2">
            <label>Calamiteiten form.</label>
            <select name="calamiteiten_form" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_calamiteiten_form")) required @endif>
              <option @if(isset($user) && $user->calamiteiten_form == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->calamiteiten_form == 1) selected @endif value="1">Ja</option>
            </select>
          </div>
          @endif

        </div>
      </div>
    @endif


    @if(!getSettingCheckbox("user_optioneel_veld_label_salarisverwerking"))
      <div class="font-weight-semibold opacity-50" >Salarisverwerking</div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_cao_groep"))
          <div class="col-md-6 col-12 my-2">
            <label>CAO-groep</label>
            <input name="caogroep" type="text" class="form-control-custom" value="{{ $user->caogroep ?? '' }}" placeholder="CAO-groep"
            @if(getSettingCheckbox("user_verplicht_veld_cao_groep")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_kilometer_tarief"))
          <div class="col-md-6 col-12 my-2">
            <label>Kilometer tarief</label>
            <input name="reistarief" type="number" step="0.01" min="0" class="form-control-custom" value="{{ $user->reistarief ?? '' }}" placeholder="Kilometer tarief"  @if(getSettingCheckbox("user_verplicht_veld_kilometer_tarief")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_reis_uurtarief"))
          <div class="col-md-6 col-12 my-2">
            <label>Reis uurtarief</label>
            <input name="uurreistarief" type="number" step="0.01" min="0" class="form-control-custom" value="{{ $user->uurreistarief ?? '' }}" placeholder="Reis uurtarief"  @if(getSettingCheckbox("user_verplicht_veld_reis_uurtarief")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_reiskosten"))
          <div class="col-md-6 col-12 my-2">
            <label>Reiskosten</label>
            <select name="reiskosten" class="form-select"  @if(getSettingCheckbox("user_verplicht_veld_reiskosten")) required @endif>
              <option @if(isset($user) && $user->reistarief == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->reistarief == 1) selected @endif value="1">Ja</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_iban_nr"))
          <div class="col-md-6 col-12 my-2">
            <label>IBAN nr.</label>
            <input name="iban" type="text" value="{{ $user->iban ?? '' }}" class="form-control-custom" placeholder="IBAN nr."
            @if(getSettingCheckbox("user_verplicht_veld_iban_nr")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_reisuren_uitschakelen"))
          <div class="col-md-6 col-12 my-2">
            <label>Reisuren uitschakelen</label>
            <select name="disable_reisuren" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_reisuren_uitschakelen")) required @endif>
              <option @if(isset($user) && $user->disable_reisuren == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->disable_reisuren == 1) selected @endif value="1">Ja</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_auto_van_de_zaak"))
          <div class="col-md-6 col-12 my-2">
            <label>Auto van de zaak</label>
            <select name="autovdzaak" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_auto_van_de_zaak")) required @endif>
              <option @if(isset($user) && $user->autovdzaak == 0) selected @endif value="0">Nee</option>
              <option @if(isset($user) && $user->autovdzaak == 1) selected @endif value="1">Ja</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_loon_heffings_korting"))
          <div class="col-md-6 col-12 my-2">
            <label>Loonheffingskorting</label>
            <select name="loonheffingskorting" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_loon_heffings_korting")) required @endif>
              <option @if(isset($user) && $user->loonheffingskorting == 0) selected @endif value="0"><b>NIET</b> toe laten passen</option>
              <option @if(isset($user) && $user->loonheffingskorting == 1) selected @endif value="1"><b>WEL</b> loonheffingskorting toepassen, <b>GEEN</b> andere werkgever</option>
            </select>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_functie_loon_heffings_korting"))
          <div class="col-md-6 col-12 my-2">
            <label>Functie loonheffingskorting</label>
            <input name="functieloonheffing" type="text" value="{{ $user->functieloonheffing ?? '' }}" class="form-control-custom" placeholder="Functie loonheffingskorting"
            @if(getSettingCheckbox("user_verplicht_veld_functie_loon_heffings_korting")) required @endif/>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_functie_loon_periode"))
          <div class="col-md-6 col-12 my-2">
            <label>Loonperiode</label>
            <select class="form-select" name="loonperiode" @if(getSettingCheckbox("user_verplicht_veld_functie_loon_periode")) required @endif>
              <option @if(isset($user) && $user->loonperiode == "week") selected @endif value="week">Week</option>
              <option @if(isset($user) && $user->loonperiode == "maand") selected @endif value="maand">Maand</option>
              <option @if(isset($user) && $user->loonperiode == "4weken") selected @endif value="4weken">4 Weken</option>
            </select>
          </div>
          @endif<br>
          @if(!getSettingCheckbox("user_optioneel_veld_onkosten_vergoeding"))
          <div class="col-md-6 col-12 my-2">
            <label>Onkostenvergoeding</label>
            <textarea name="onkostenvergoeding" class="form-control-custom" placeholder="Onkostenvergoeding" @if(getSettingCheckbox("user_verplicht_veld_onkosten_vergoeding")) required @endif >{{ $user->onkostenvergoeding ?? '' }}</textarea>
          </div>
          @endif

        </div>
      </div>
    @endif


    @if(!getSettingCheckbox("user_optioneel_veld_label_legitimatiebewijs"))
      <div class="font-weight-semibold opacity-50" >Legitimatiebewijs</div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_identity_document_type"))
            <div class="col-md-6 col-12 my-2" >
              <label>Legitimatie type</label>
              <select name="identity_document_type" type="date" class="date form-select" @if(getSettingCheckbox("user_verplicht_veld_identity_document_type")) required @endif >
                <option disabled @if(isset($user) && !$user->identity_docuemnt_type) selected @endif >Selecteer legitimatie type</option>
                <option value="ID" @if(isset($user) && $user->identity_document_type == 'ID') selected @endif >Identiteitskaart</option>
                <option value="passport" @if(isset($user) && $user->identity_document_type == 'passport') selected @endif >Paspoort</option>
              </select>
            </div>
          @endif

          {{--Passport--}}
          @if(!getSettingCheckbox("user_optioneel_veld_passport_number"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="passport" @if(getSettingCheckbox("user_verplicht_veld_passport_number")) data-required @endif >
              <label>Paspoort nummer</label>
              <input name="passport_number" type="text" value="{{ $user->passport_number ?? '' }}" placeholder="Paspoort nummer" class="form-control-custom" />
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_expiration_date_passport"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="passport" @if(getSettingCheckbox("user_verplicht_veld_expiration_date_passport")) data-required @endif >
              <label>Verloopdatum paspoort</label>
              <input name="expiration_date_passport" type="date" @isset($user->expiration_date_passport) value="{{ Carbon()->parse($user->expiration_date_passport)->format('Y-m-d') }}" @endisset class="form-control-custom" />
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_passport_file_id"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="passport" @if(getSettingCheckbox("user_verplicht_veld_passport_file_id")) data-required @endif >
              <label>Paspoort uploaden</label>
              <div class="form-control-custom flex-align hover-mark cursor-pointer" data-file-upload >
                <span class="mr-2" >@icon_cloud_upload</span>
                <input data-file-title class="cursor-pointer form-control-plaintext p-0 pointer-event-none" @isset($user->passport_file) value="{{$user->passport_file->name}} @endif" placeholder="Selecteer bestand" >
                <input data-file-id name="passport_file_id" type="hidden"  class="date form-control-custom" value="{{$user->passport_file_id ?? ''}}" />
              </div>
            </div>
          @endif

          {{--ID--}}
          @if(!getSettingCheckbox("user_optioneel_veld_id_card_number"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="ID" @if(getSettingCheckbox("user_verplicht_veld_id_card_number")) data-required @endif >
              <label>Identiteitskaartnummer</label>
              <input name="id_card_number" type="text" value="{{ $user->id_card_number ?? '' }}" placeholder="Identiteitskaartnummer" class="form-control-custom"/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_expiration_date_id_card"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="ID" @if(getSettingCheckbox("user_verplicht_veld_expiration_date_id_card")) data-required @endif >
              <label>Verloopdatum identiteitskaart</label>
              <input name="expiration_date_id_card" type="date" @isset($user->expiration_date_id_card) value="{{ Carbon()->parse($user->expiration_date_id_card)->format('Y-m-d') }}" @endisset class="form-control-custom"/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_id_card_file_id"))
            <div class="col-md-6 col-12 my-2" data-identity-input-container="ID" @if(getSettingCheckbox("user_verplicht_veld_id_card_file_id")) data-required @endif >
              <label>Identiteitskaart uploaden</label>
              <div class="form-control-custom flex-align hover-mark cursor-pointer" data-file-upload >
                <span class="mr-2" >@icon_cloud_upload</span>
                <input data-file-title class="cursor-pointer form-control-plaintext p-0 pointer-event-none" @isset($user->id_card_file) value=" {{$user->id_card_file->name}} @endif" placeholder="Selecteer bestand" >
                <input data-file-id name="id_card_file_id" type="hidden"  class="date form-control-custom" value="{{$user->id_card_file_id ?? ''}}" />
              </div>
            </div>
          @endif

        </div>
      </div>
    @endif


    @if(!getSettingCheckbox("user_optioneel_veld_label_overig"))
      <div class="font-weight-semibold opacity-50" >Overig</div>
      <div class="card p-2 mb-3">
        <div class="row">
          @if(!getSettingCheckbox("user_optioneel_veld_leiding_gevende"))
            <div class="col-md-6 col-12 my-2">
              <label>Leidinggevende</label>
              <select name="leidinggevende" class="form-select" @if(getSettingCheckbox("user_verplicht_veld_leiding_gevende")) required @endif>
                @foreach($users as $loopUser)<option @if(isset($user) && $user->leidinggevende == $loopUser->id)selected @endif value="{{ $loopUser->id }}">{{ $loopUser->name . " " . $loopUser->lastname}}</option>@endforeach
                <option @if(isset($user) && !$user->leidinggevende) selected @endif disabled>Maak een keuze</option>
              </select>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_onderaannemer"))
            <div class="col-md-6 col-12 my-2">
              <label>Onderaannemer</label>
              <input name="onderaannemer" type="text" value="{{ $user->onderaannemer ?? '' }}" class="form-control-custom" placeholder="Onderaannemer"
                     @if(getSettingCheckbox("user_verplicht_veld_onderaannemer")) required @endif/>
            </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_subcontractor_employment_type"))
            <div class="col-md-6 col-12 my-2">
              <label>Dienstverband bij onderaannemer</label>
              <select name="Dienstverband bij onderaannemer"  class="form-select">
                <option disabled @if(isset($user) && $user->subcontractor_employment_type === null) selected @endif >Maak een keuze</option>
                <option value="zzp" @if(isset($user) && $user->subcontractor_employment_type === 'zzp') selected @endif >ZZP'er</option>
                <option value="loondienst" @if(isset($user) && $user->subcontractor_employment_type === 'loondienst') selected @endif >Loondienst</option>
                <option value="eigenaar" @if(isset($user) && $user->subcontractor_employment_type === 'eigenaar') selected @endif >Eigenaar</option>
              </select>
            </div>
          @endif

          @if(!getSettingCheckbox("user_optioneel_veld_opmerking"))
          <div class="col-12 mt-3">
            <label>Opmerking</label>
            <textarea  @if(getSettingCheckbox("user_verplicht_veld_opmerking")) required @endif class="form-control-custom" name="opmerking">{{ $user->opmerking ?? ''}}</textarea>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_handtekening"))
          <div class="col-12 my-2">
            <label>Handtekening</label>
            <input type="file" class="dropify" name="handtekening" @if(isset($user) && $user->handtekening) data-default-file="{{url("client/public/img/handtekeningen/".$user->handtekening)}}" @endif data-allowed-file-extensions="jpg jpeg png" data-max-file-size="10M"
            @if(getSettingCheckbox("user_verplicht_veld_handtekening")) required @endif>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_veld_foto"))
          <div class="col-12 my-2">
            <label>Foto</label>
            <input type="file" class="dropify" name="foto" @if(isset($user) && $user->foto) data-default-file="{{url("api/file/users/".$user->id."/headshot/".$user->foto)}}" @endif data-allowed-file-extensions="jpg jpeg png" data-max-file-size="10M"
            @if(getSettingCheckbox("user_verplicht_veld_foto")) required @endif>
          </div>
          @endif
          @if(!getSettingCheckbox("user_optioneel_mail_footer"))
            <div class="col-12 my-2">
              <label>Footer</label>
              <input type="file" class="dropify" name="footer" @if(isset($user) && $user->footer) data-default-file="{{url("api/file/medewerkers/footers/".$user->footer)}}" @endif data-allowed-file-extensions="jpg jpeg png" data-max-file-size="10M"
             @if(getSettingCheckbox("user_verplicht_mail_footer")) required @endif>
            </div>
          @endif
        </div>
      </div>
    @endif


    @if(exactGlobe()->connected)
      <div class="font-weight-semibold opacity-50" >Exact Globe</div>
      <div class="card p-2 mb-3">
        <div class="row">
          <div class="col-md-6 col-12 my-2 select_search-container">
            <label>Medewerker</label>
            <input type="hidden" name="exact_resource" class="select_search-hidden-input" data-placeholder="Medewerker">
            <div class="select_search-values" >
              <div class="select_search-box" >
                @foreach(exactGlobe(['no_curl' => false])->getStoredResources() as $resource)
                  <span @if(($user->exact_globe_id ?? null) == $resource->exact_id) data-selected="true" @endif class="select_search-value" data-value="{{$resource->exact_id}}" data-name="{{$resource->name}}" >{{$resource->name}}</span>
                @endforeach
              </div>
            </div>
          </div>
        </div>
      </div>
    @endif

    @include('users.components.custom-fields', [
        'user' => $user ?? null,
    ])

    <div class="my-4 text-center">
      <input type="submit" class="btn btn-success" value="Opslaan">
      @csrf
    </div>

  </form>

@endsection
@section("script")
  <script>

    const user = @json($user);

    $(document).ready(function(){
      $(".dropify").dropify();
      toggleIdentityFields();
    });
    $(document).on('change', '[name=identity_document_type]', toggleIdentityFields)
    $(document).on('click', '[data-file-upload]', async function(){
      const title_input = $(this).find('[data-file-title]');
      const id_input = $(this).find('[data-file-id]');

      const { status, file } = await uploadExplorerFile({path: `/Gebruikers/${user?.id ? `(${user.id})` : 'Tijdelijk'}`})
      if(!status){ return }

      title_input.val(file.name).removeClass('text-muted');
      id_input.val(file.id);
    });

    //Custom Fields
    function toggleIdentityFields(){
      const identity = $(`[name=identity_document_type]`).val();

      $(`[data-identity-input-container]`).hide();
      $(`[data-identity-input-container]`).find('input[name], select[name]').prop('required', false)
      if(!identity){ return }

      $(`[data-identity-input-container=${identity}]`).show();
      $(`[data-identity-input-container=${identity}][data-required]`).find('input[name], select[name]').prop('required', true)
    }

  </script>
@endsection
