@extends('layouts.app')

@section('title', 'Settings')

@section('content')
  <form class="append-loader" method="post" enctype="multipart/form-data" data-main-form>

    <section class="my-2 h-px-100" data-stats-navigation >
      <div class="py-3 bg-modal z-index-999" data-content >
        <div class="card flex-between flex-row m-0 p-2">
            <div class="select_search-container w-100" data-settings-search-container >
              <input type="hidden" name="" class="select_search-hidden-input" data-placeholder="Zoeken" data-settings-search >
              <div class="select_search-values" >
                <div class="select_search-box" data-settings-select ></div>
              </div>
            </div>

            <div class="flex-between" >
              <a class="btn btn-inverse-primary mx-2 nobr tippy" data-tippy-content="Alles verbergen" data-hide-all-containers >@icon_collapse</a>
              <button type="submit" class="btn btn-inverse-success tippy" data-tippy-content="Opslaan" data-hide-all-containers>@icon_save</button>
              @csrf
            </div>

        </div>
      </div>
    </section>

    <div class="row align-items-center">

              @include('settings.blades.environment')
              @include('settings.blades.actielijsten')
              @include('settings.blades.bestanden')
              @include('settings.blades.mails')
              @include('settings.blades.user')
              @include('settings.blades.rollen')
              @include('settings.blades.urenregistratie')
              @include('settings.blades.verlof')
              @include('settings.blades.aanvragen')
              @include('settings.blades.offertes')
              @include('settings.blades.datasets')
              @include('settings.blades.leveranciers')
              @include('settings.blades.klanten')
              @include('settings.blades.rapporten')
              @include('settings.blades.checklists')
              @include('settings.blades.projecten')
              @include('settings.blades.facturatie')
              @include('settings.blades.werkbonnen')
              @include('settings.blades.accorderen')
              @include('settings.blades.inkoopbonnen')
              @include('settings.blades.inkoopfacturen')
              @include('settings.blades.planning')
              @include('settings.blades.abonnementen')
              @include('settings.blades.aanmeldingen')
              @include('settings.blades.legplannen')
              @include('settings.blades.beschikbaarheid')
              @include('settings.blades.statistieken')
              @include('settings.blades.app')
              @include('settings.blades.businesscentral')
              @include('settings.blades.tweeba')
              @include('settings.blades.exactonline')
              @include('settings.blades.king')
              @include('settings.blades.koppelingen')
              @include('settings.blades.api')
              @include('settings.blades.wachtwoordenkluis')
              @include('settings.blades.whatsapp')
              @include('settings.blades.declaratie')
              @include('settings.blades.toolbox')
              @include('settings.blades.zenvoices')
              @include('settings.blades.dashboard')
            </div>

  </form>


  {{--    Modals--}}
  <section>
    {{--  Api modal--}}
    <div class="modal fade" id="api-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <form action="{{url('settings/requests/store')}}" method="post">
            @csrf
            <div class="modal-body">
              <div class="my-2">
                <label>Naam</label>
                <input type="text" class="form-control-custom" name="name" placeholder="Naam" required>
              </div>
              <div class="my-2" >
                <label>Permissions</label>
                @if(hasModule("Klanten"))
                  <div class="flex-between">
                    <span class="font-size-09">Klanten uitlezen</span>
                    <input type="checkbox" name="permissions[klanten_uitlezen]" class="form-check-custom permission" >
                  </div>
                  <div class="flex-between">
                    <span class="font-size-09">Klanten toevoegen</span>
                    <input type="checkbox" name="permissions[klanten_toevoegen]" class="form-check-custom permission" >
                  </div>
                @endif
                @if(hasModule("Projecten"))
                  <div class="flex-between">
                    <span class="font-size-09">Projecten toevoegen</span>
                    <input type="checkbox" name="permissions[projecten_toevoegen]" class="form-check-custom permission" >
                  </div>
                @endif
              </div>
            </div>
            <div class="modal-footer">
              <input class="btn btn-success" type="submit" onclick="confirmApi()" value="Opslaan" >
              <input type="hidden" name="id">
            </div>
          </form>
        </div>
      </div>
    </div>
    {{--  Delete Api modal--}}
    <div class="modal fade" id="api-delete-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <form action="{{url('settings/requests/delete')}}" method="post">
            <div class="modal-body">
              Weet je zeker dat je <b class="name" ></b> API Token wilt verwijderen?
            </div>
            <div class="modal-footer">
              <input type="submit" class="btn btn-danger" value="Verwijderen" onclick="removeApi();">
              <input type="hidden" name="id">
              @csrf
            </div>
          </form>
        </div>
      </div>
    </div>
    {{--  modal--}}
    <div class="modal fade" id="colorModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content bg-light">
          <div class="modal-header p-3">
            <h5>Email achtergrond</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div id="colorWheel" class="text-center"></div>
          </div>
          <div class="modal-footer">
            <a class="btn btn-success text-white confirmColor" id="confirmColorBtn" data-dismiss="modal" >Bevestig</a>
          </div>
        </div>
      </div>
    </div>
    {{--  Export modal--}}
    <div class="modal fade" id="export-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <form action="{{url('settings/projecten/export/store')}}" method="post">
            <div class="modal-body">
              <div class="my-2" >
                <label>Naam</label>
                <input name="name" class="form-control" required >
              </div>
              @if(hasModule("Offertes"))
                <div class="my-2" >
                  <label>Offerte template</label>
                  <select class="form-select" name="template">
                    <option selected value="" >Selecteer template</option>
                    @foreach($templates as $template)
                      <option value="{{$template->id}}" >{{$template->naam}}</option>
                    @endforeach
                  </select>
                </div>
              @endif
            </div>
            <div class="modal-footer">
              <input type="submit" class="btn btn-success text-white" value="Opslaan" >
              @csrf
            </div>
          </form>
        </div>
      </div>
    </div>
    {{--  Export modal fields--}}
    <div class="modal fade" id="export-modal-fields" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <form action="{{url('settings/projecten/export/fields/store')}}" method="post">
            <div class="modal-body">

              <div class="my-2 row">
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="projecten-export-active-projects" name="allProjects" value="0">
                  <label class="btn btn-outline-primary d-block" for="projecten-export-active-projects">Open projecten</label>
                </div>
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="projecten-export-all-projects" name="allProjects" value="1">
                  <label class="btn btn-outline-primary d-block" for="projecten-export-all-projects">Alle projecten</label>
                </div>
              </div>

              <div>
                <h4 class="my-3" >Project</h4>
                <div class="my-3 export-container">
                  <label class="w-100" >Code</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[code]" value="Code">
                    <select class="form-select mx-2 export-column-select" data-type="project" data-target="code" name="export[code]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Projectnummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[projectnr]" value="Projectnummer">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="projectnr" name="export[projectnr]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">projectnaam</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[projectnaam]" value="Projectnaam">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="projectnaam" name="export[projectnaam]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Opdrachtgever</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[opdrachtgever]" value="Opdrachtgever">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="opdrachtgever" name="export[opdrachtgever]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Adres</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[adres]" value="Adres">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="adres" name="export[adres]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Plaats</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[woonplaats]" value="Woonplaats">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="woonplaats" name="export[woonplaats]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Projectleider</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[projectleider]" value="Projectleider">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="projectleider" name="export[projectleider]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Opdrachtnummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[opdrachtnummer]" value="Opdrachtnummer">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="opdrachtnummer" name="export[opdrachtnummer]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Taaknummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[taaknummer]" value="Taaknummer">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="taaknummer" name="export[taaknummer]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Reistijd</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[reis]" value="Reistijd">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="reis" name="export[reis]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Status</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[status]" value="Status">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="status" name="export[status]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100">Boekingstatus</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExport[boekingstatus]" value="Boekingstatus">
                    <select  class="form-select mx-2 export-column-select"  data-type="project" data-target="boekingstatus" name="export[boekingstatus]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
              </div>

              <div>
                <h4 class="my-3" >Klant</h4>
                <div class="my-3 export-container">
                  <label class="w-100" >Soort klant</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[soort_klant]" value="Soort klant">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="soort_klant" name="exportKlant[soort_klant]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Naam</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[naam]" value="Naam">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="naam" name="exportKlant[naam]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon titel</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_titel]" value="Contactpersoon Titel">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_titel" name="exportKlant[contactpersoon_titel]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon voornaam</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_voornaam]" value="Contactpersoon Voornaam">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_voornaam" name="exportKlant[contactpersoon_voornaam]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon achternaam</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_achternaam]" value="Contactpersoon Achternaam">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_achternaam" name="exportKlant[contactpersoon_achternaam]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon functie</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_functie]" value="Contactpersoon functie">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_functie" name="exportKlant[contactpersoon_functie]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon email</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_email]" value="Contactpersoon E-mail">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_email" name="exportKlant[contactpersoon_email]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon telefoon</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_telefoon]" value="Contactpersoon Telefoon">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_telefoon" name="exportKlant[contactpersoon_telefoon]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Contactpersoon mobiel</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[contactpersoon_mobiel]" value="Contactpersoon Mobiel">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="contactpersoon_mobiel" name="exportKlant[contactpersoon_mobiel]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Email</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[email]" value="E-mail">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="email" name="exportKlant[email]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Telefoonnmummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[telefoonnummer]" value="Telefoonnummer">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="telefoonnummer" name="exportKlant[telefoonnummer]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Website</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[website]" value="Website">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="website" name="exportKlant[website]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Straat</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[straat]" value="Straat">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="straat" name="exportKlant[straat]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Huisnummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[huisnummer]" value="Huisnummer">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="huisnummer" name="exportKlant[huisnummer]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Toevoeging</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[toevoeging]" value="Toevoeging">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="toevoeging" name="exportKlant[toevoeging]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Postcode</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[postcode]" value="Postcode">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="postcode" name="exportKlant[postcode]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Plaats</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[plaats]" value="Plaats">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="plaats" name="exportKlant[plaats]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Land</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[land]" value="Land">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="land" name="exportKlant[land]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >KVK nummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[kvk]" value="KVK nummer">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="kvk" name="exportKlant[kvk]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >BTW nummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[btw_nr]" value="BTW nummer">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="btw_nr" name="exportKlant[btw_nr]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >BTW percentage</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[btw_perc]" value="BTW percentage">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="btw_perc" name="exportKlant[btw_perc]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Betalingstermijn</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[betalingstermijn]" value="Betalingstermijn">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="betalingstermijn" name="exportKlant[betalingstermijn]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >IBAN</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[iban]" value="IBAN">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="iban" name="exportKlant[iban]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Bic code</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[bic]" value="Bic code">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="bic" name="exportKlant[bic]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="my-3 export-container">
                  <label class="w-100" >Debiteurnummer</label>
                  <div class="d-flex justify-content-between align-items-center">
                    <input class="export-column-name form-control" name="hiddenExportKlant[debiteurnummer]" value="Debiteurnummer">
                    <select class="form-select mx-2 export-column-select"  data-type="klant" data-target="debiteurnummer" name="exportKlant[debiteurnummer]" >
                      <option selected value="" >Selecteer kolom</option>
                      @foreach(aazArr() as $letter)
                        <option value="{{$letter}}" >{{$letter}}</option>
                      @endforeach
                    </select>
                  </div>
                </div>
              </div>

              @if(hasModule("Offertes"))
                <h4 class="my-3" >Offerte</h4>
                <div id="projectenOfferteInputs"></div>
              @endif
            </div>
            <div class="modal-footer">
              <input type="submit" class="btn btn-success text-white" value="Opslaan" >
              <input type="hidden" name="template" >
              @csrf
            </div>
          </form>
        </div>
      </div>
    </div>
    {{--  XMl Export modal--}}
    <div class="modal fade" id="xmlExportModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            @php
              $selectedOptionName = "";
              $excludes = ["id", "tijdelijk", "remember_token", "created_at", "updated_at", "active", "google_token", "google_info", "disabled", "password"];
            @endphp
            <div class="row" id="xmlExportSelect"></div>
            <div class="my-2"><a class="btn btn-primary text-white" onclick="addXmlRow()">toevoegen</a><a class="ml-2 btn btn-primary text-white" onclick="addXmlSelect()">Keuzeveld toevoegen</a></div>
          </div>
        </div>
      </div>
    </div>
    {{-- Datasets XML Export modal--}}
    <div class="modal fade" id="datasets-xml-export-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">

          </div>
        </div>
      </div>
    </div>
  </section>
@endsection
@section('script')
  <script>
    var xmlExportregelsId = @json($xmlExportregelsId ?? 0);
    if(xmlExportregelsId > -1){
      var xmlRowId = xmlExportregelsId;
    }
    else{
      var xmlRowId = 0;
    }

    var customi = 0;
    var currentColor = 0;
    var currentBv = 0;
    var roles = @json(getRoles());
    var users = @json(getUsers());
    var keywords = @json($keywords);
    var datasets = @json($datasets);
    var settings = @json($settings);
    var aanvraagInputs = @json($aanvraagInputs);
    var aanvraagTypes = @json($aanvraagTypes);
    var aanvraagInputsIndex = aanvraagInputs.length;
    var factuurReminderIndex = @json(count(json_decode(getSettingValue("factuur_reminders")) ?? []));
    var colorWheel = new iro.ColorPicker("#colorWheel", {
      display: "inline-block"
    });
    var projectenExportTemplates = @json($projectenExportTemplates);
    var snelstartGrootboeken = @json($snelstartGrootboeken);

    var checklistTemplates = @json($checklistTemplates);

    const preTarget =  @json($_GET['target'] ?? null);
    const requests = {
      tokens: @json($requestTokens),
      posts: @json($requestPosts),
      period: @json($requestPeriod),
    }
    const _api = {
      modal: $("#api-modal"),
      deleteModal: $("#api-delete-modal"),
      id: null,
    }
    const _bc = {
      instance: new BusinessCentral(),
    }
    const _eo = {
      instance: new ExactOnline(),
    }
    const _zenvoices = {
        //ik ga dit 100% vergeten te un-commenten
        //instance: new Zenvoices(),
    }

    $(document).ready(function(){
      editorInit("#editor");
      editorInit("#offerte-bevestiging");
      editorInit("#factuurInleiding");
      editorInit("#factuurSlot");
      editorInit("#factuurEditor");
      editorInit("#proformaEditor");
      editorInit("#abonnementEmail");
      editorInit("#abonnementBeeindigd");
      editorInit("#abonnementVernieuwd");
      editorInit("#aanmeldingBetalingsBevestiging");
      editorInit("#aanmeldingBevestiging");
      editorInit("#aanmeldingFactuur");
      editorInit("#aanmeldingHerinnering");
      editorInit("#mailsFooterText");
      editorInit("#rapportEditor");
      editorInit("#planningMail");
      editorInit("#planningMailUpdate");
      editorInit("#werkbonSignatureContent");
      editorInit("#facturatie-reminder-proforma");
      editorInit("#facturatie-reminder-factuur");
      editorInit("#inkoopbonnen-email-content");
      initRequests();
      initCustomMenu();
      showCustomMenuVolgorde();

      $('.state-checkbox').trigger('change');

    })

    pageComplete(() => {
      initSearch();
    })


    $(document).on("change", ".export-column-select", disableProjectExportOptions);
    $(document).on("click", ".dropify-clear", function(){
      $("input[name=mailsClearFooterImage]").val(1);
    })
    $(document).on("change", '.state-checkbox', function(){
      const state = $(this).prop('checked');
      const container = findContainer('checkbox-container', this);

      container.find('select, input').not(this).prop('disabled', !state);
      container.find('.checkbox-toggle-element').toggleClass('d-none', !state);
    });
    $(document).on('change', '[data-settings-search]', function(){
      let value = $(this).val();
      let container;

      if(!value){ return;}

      value = value.split('_splitter_');


      if(value[0]){
        container = findContainer('settings-container', $(`h4:contains('${value[0]}')`));

        if(!container){
          notification('Er is iets foutgegaan!');
          return;
        }

        container.click();
      }

      if(value[1] && container){
        const label = container.find(`[data-settings-sub-header]:contains('${value[1]}')`);

        if(!label.length){
          notification('Er is iets foutgegaan!');
          return;
        }

        scrl(label, 165);
      }

    });
    $(document).on('keydown', function(e){
      if(e.ctrlKey && e.key.toLowerCase() == 'f'){
        e.preventDefault();
        $('[data-settings-search-container]').find('.select_search-input').click();
      }
    })


    $('[data-hide-all-containers]').click(function(){
      $('.settings-container').addClass(['col-lg-3', 'col-md-6']);
      $('.settings-content').addClass('d-none')
      scrl('body');
    });
    $('.settings-container').click(function(){
      const container = $(this);
      const content = container.find('.settings-content');

      if(content.css('display') != 'none'){ return }

      $('.settings-container').addClass(['col-lg-3', 'col-md-6']);
      $('.settings-content').addClass('d-none')

      container.removeClass(['col-lg-3', 'col-md-6']);
      content.removeClass('d-none');

      scrl(container, 155);
    });


    $(".addCustomCheckbox").click(function(){
      const string = randomString(10);
      $("#customCheckboxen").append('<div class="row" id="checkbox'+string+'">'+
        '<div class="col-md-11 col-9 mt-2"><input type="text" name="inhoud[]" placeholder="inhoud" class="form-control"> </div>'+
        '<div class="col-md-1 col-3 mt-2"><a class="btn btn-danger my-1 text-white" onclick="deleteDiv(\'#checkbox'+string+'\')"><i class="bi bi-trash"></i></a></div>'+
        '</div>'
      )
      index++;
    });
    $(".confirmColor").click(function(){
      $("#"+currentBv+"colorVal").val(colorWheel.color.hexString)
      bg = "linear-gradient(135deg,"+colorWheel.color.hexString+","+colorWheel.color.hexString+"80,"+colorWheel.color.hexString+",#000)";
      $("#"+currentBv+"color").css({"background":bg})
      $("#previewBtn"+currentBv).attr("href", "{{url('settings/preview')}}/"+currentBv+"/"+colorWheel.color.hexString.replace("#",""));
    });
    $(".colorPicker").click(function (){
      colorWheel.color.hexString = settings["email_color_bv_"+currentBv].value;
    });
    $("#mailFooterLogoWidthRange").on("mousemove", function(){
      $("#mailFooterLogoWidth").html($(this).val());
    });

    function initSearch(){
      const containers = $('.settings-container');
      const select = $('[data-settings-select]');

      containers.each(function(){
        const container = $(this);
        const header = container.find('[data-settings-header]').text()

        select.append(`<span class="select_search-value" data-value="${header}" data-name="${header}"> <span class="badge badge-primary badge-lg" >${header}</span> </span>`)

        container.find('[data-settings-sub-header]').each(function(){
          const label = $(this).text()
          if(!label){ return true; }

          select.append(`<span class="select_search-value nobr" data-value="${header}_splitter_${label}" data-name="${label}"> <span class="badge badge-primary badge-lg" >${header}</span> ${label} </span>`)
        })
      })

      if(preTarget){ $('[data-settings-search]').val(preTarget).trigger('change'); }
    }

    async function syncBusinessCentral(target){
      loader();
      try{
        const response = await _bc.instance.sync(target);
        successLoader();
      }
      catch (e) {handleCatchError(e);}
    }
    async function syncExactOnline(target){
      loader();
      try{
        const response = await _eo.instance.sync(target);
        successLoader();
      }
      catch (e) {handleCatchError(e);}
    }

    function addInkoopbonEenheid(){
      $('[data-inkoopbonnen-eenheden]').append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
          <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_eenheden][${lastString()}][eenheid]" placeholder="Eenheid" >
          <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
      </div>`
      );
    }

    function addInkoopbonCustomRow(){
      $('[data-inkoopbonnen-custom-rows]').append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
          <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_custom_rows][${lastString()}][keyword]" placeholder="Keyword" >
          <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_custom_rows][${lastString()}][name]" placeholder="Naam" >
          <select class="form-select mx-1 w-100" name="json[inkoopbonnen_custom_rows][${lastString()}][type]" >
              <option value="text">Tekst</option>
              <option value="number">Getal</option>
          </select>
          <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
      </div>`
      );
    }

    function addLeverancierCategory(){
      $('[data-leveranciers-categories]').append(
        `<div class="bg-light-grey border p-2 my-3" data-leverancier-category="${randomString(20)}" >
              <div class="my-2">
                <div class="flex-between my-2" >
                  <label class="m-0" >Hoofdgroep</label>
                  <a class="text-danger cursor-pointer mx-2" onclick="deleteDiv('[data-leverancier-category=${lastString()}]')" >@icon_close</a>
                </div>
                <input type="text" class="form-control-custom" name="json[leveranciers_tarieven_categories][${lastString()}][name]" placeholder="Hoofdgroep" >
              </div>
              <div data-sub=${lastString()} class="overflow-auto py-2" >
                <table class="table" >
                    <thead>
                        <th></th>
                        <th class="w-100" >Omschrijving</th>
                    </thead>
                    <tbody></tbody>
                </table>
              </div>
              <div class="text-center my-2">
                  <a class="btn btn-inverse-primary" onclick="addLeverancierSubCategory('${lastString()}')" >Subgroep @icon_plus</a>
              </div>
        </div>`
      );
      addLeverancierSubCategory(lastString());
    }
    function addLeverancierSubCategory(string){
      const container = $(`[data-leverancier-category=${string}]`);
      const name_string = randomString();

      container.find('tbody').append(
        `<tr data-leverancier-sub-category="${name_string}" >
            <td> <a class="btn btn-inverse-danger" onclick="deleteDiv('[data-leverancier-sub-category=${name_string}]')" >@icon_close</a> </td>
            <td> <input class="form-control-custom" placeholder="Omschrijving" name="json[leveranciers_tarieven_categories][${string}][sub][${name_string}][omschrijving]" > </td>
        </tr>`
      )
    }

    function addProformaSignAttach(){
      $('[data-proforma-sign-attach]').append(`
        <div class="my-1 d-flex align-items-center" data-proforma-sign-attach-node="${randomString()}" >
          <input type="text" class="form-control-custom w-100" placeholder="Label" name="json[facturatie_proforma_sign_attachments][${lastString()}][name]" >
          <input type="checkbox" class="form-check-custom mx-2 tippy" data-tippy-content="Verplicht" name="json[facturatie_proforma_sign_attachments][${lastString()}][required]" >
          <a class="btn text-danger" onclick="deleteDiv('[data-proforma-sign-attach-node=${lastString()}]')" >@icon_close</a>
        </div>
      `);

      tippyInit();
    }
    function addOfferteStatusOptie(id){
      const string = randomString();

      confirmModal({
        text: `<div>
                <label>Type</label>
                <select class='form-select ${string}' >
                  <option value='email' >E-mail</option>
                  <option value='role' >Rol</option>
                  <option value='user' >User</option>
                  <option value='anders' >Anders</option>
                </select>
              </div>`,
        btnText: 'Toevoegen'
      }).then((res) => {
        if(!res.status){return}

        const type = $(`.${string}`).val();

        if(type == 'email'){
          $(`.offerte-status-opties[data-id=${id}]`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>E-mail</label>
                <input name="json[offertes_status_change_notifications][${id}][email][]" class="form-control-custom" placeholder="E-mail" >
            </div>`
          )
        }
        else if (type == 'role'){
          let options = '';
          for(const role of roles){
            options += `<option value='${role.id}' >${role.name}</option>`
          }

          $(`.offerte-status-opties[data-id=${id}]`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>Rol</label>
                <select name="json[offertes_status_change_notifications][${id}][role][]" class="form-select" >
                    ${options}
                </select>
            </div>`
          )
        }
        else if (type == 'user'){
          let options = '';
          for(const user of users){
            options += `<option value='${user.id}' >${user.name || ''} ${user.lastname || ''}</option>`
          }

          $(`.offerte-status-opties[data-id=${id}]`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>User</label>
                <select name="json[offertes_status_change_notifications][${id}][user][]" class="form-select" >
                    ${options}
                </select>
            </div>`
          )
        }
        else if (type == 'anders'){
          $(`.offerte-status-opties[data-id=${id}]`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>Anders</label>
                <select name="json[offertes_status_change_notifications][${id}][anders][]" class="form-select" >
                    <option value="vestigingsmanager">Vestigingsmanager</option>
                </select>
            </div>`
          )
        }

      })
    }
    function addFactuurProformaStatusOptie(){
      const string = randomString();

      confirmModal({
        text: `<div>
                <label>Type</label>
                <select class='form-select ${string}' >
                  <option value='email' >E-mail</option>
                  <option value='role' >Rol</option>
                  <option value='user' >User</option>
                  <option value='anders' >Anders</option>
                </select>
              </div>`,
        btnText: 'Toevoegen'
      }).then((res) => {
        if(!res.status){return}

        const type = $(`.${string}`).val();

        if(type == 'email'){
          $(`.facturatie-proforma-status-opties`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>E-mail</label>
                <input name="json[facturatie_proforma_status_change_notifications][email][]" class="form-control-custom" placeholder="E-mail" >
            </div>`
          )
        }
        else if (type == 'role'){
          let options = '';
          for(const role of roles){
            options += `<option value='${role.id}' >${role.name}</option>`
          }

          $(`.facturatie-proforma-status-opties`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>Rol</label>
                <select name="json[facturatie_proforma_status_change_notifications][role][]" class="form-select" >
                    ${options}
                </select>
            </div>`
          )
        }
        else if (type == 'user'){
          let options = '';
          for(const user of users){
            options += `<option value='${user.id}' >${user.name || ''} ${user.lastname || ''}</option>`
          }

          $(`.facturatie-proforma-status-opties`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>User</label>
                <select name="json[facturatie_proforma_status_change_notifications][user][]" class="form-select" >
                    ${options}
                </select>
            </div>`
          )
        }
        else if (type == 'anders'){
          $(`.facturatie-proforma-status-opties`).append(
            `<div class="col-md-6 col-12 my-2" >
                <label>Anders</label>
                <select name="json[facturatie_proforma_status_change_notifications][anders][]" class="form-select" >
                </select>
            </div>`
          )
        }

      })
    }


    function initRequests(){
      for(const token of requests.tokens){

        const labels = [];
        const posts = {
          failed: [],
          succeeded: [],
        }
        for (const day of requests.period) {
          const date = convert(new Date(day), true);
          let f = 0;
          let s = 0;

          if(requests.posts[token.token] && requests.posts[token.token][date]){
            const row = requests.posts[token.token][date];
            if(row[1]){f = row[1];}
            if(row[0]){s = row[0];}
          }

          labels.push(date.slice(5, 10));
          posts.failed.push(f)
          posts.succeeded.push(s)
        }

        const data = {
          labels: labels,
          datasets: [
            {
              label: 'Failed requests',
              data: posts.failed,
              borderColor: "#F64D2ABF",
              backgroundColor: "rgba(246,77,42,0.05)",
              fill: true,
              tension: .2
            }, {
              label: 'Succeeded requests',
              data: posts.succeeded,
              borderColor: "#2D95ECBF",
              backgroundColor: "rgba(45,149,236,0.05)",
              fill: true,
              tension: .2,
            }
          ]
        };
        const config = {
          type: 'line',
          data: data,
          options: {
            bezierCurve: true,
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                min: 0,
                ticks: {
                  stepSize: 1
                }
              }
            }
          },
        };
        const chart = new Chart(document.getElementById(`request-${token.token}`), config);
      }
    }

    function wooCommerceKey(){
      const key = $(`[name='settings[woocommerce_secret]']`).val();

      if(key){
        confirmModal({text: 'WooCommerce key bestaat al, wilt u een nieuwe key genereren?'})
          .then((response) => {
            if(response.status){
              $(`[name='settings[woocommerce_secret]']`).val(randomString(50))
            }
          });
        return;
      }

      $(`[name='settings[woocommerce_secret]']`).val(randomString(50))
    }
    function addWoocommerceMetadata(){
      $('.woocommerce-metadata').append(
        `<div class="flex-between my-2 ${randomString()}">
          <input type="text" class="form-control-custom" name="json[woocommerce_metadata][]" placeholder="Meta data">
          <a class="btn btn-inverse-danger ml-1" onclick="deleteDiv('.${lastString()}')">@icon_close</a>
        </div>`
      );
    }

    function addProjectCustomRow(){
      $('.project-custom-rows').append(
        `<div class="flex-between mx--1 my-2 custom-row-container" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_cusotm_rows][${lastString()}][keyword]" placeholder="Keyword" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_cusotm_rows][${lastString()}][name]" placeholder="Naam" >
            <select class="form-select mx-1 w-100 project_custom_type" name="json[projecten_cusotm_rows][${lastString()}][type]" >
                <option value="text">Tekst</option>
                <option value="number">Getal</option>
                <option value="date">Datum</option>
                <option value="time">Tijd</option>
                <option value="select">Select</option>
                <option value="file">File</option>
                <option value="contact">Contactpersoon</option>
                <option value="user">Gebruiker</option>
            </select>
            <div class="m-1 w-100 project_custom_optionfield" >
              <input data-sjv class="form-control-custom mx-1 w-100" name="json[projecten_cusotm_rows][${lastString()}][data]" placeholder="Data" >
            </div>
            <input type="checkbox" class="form-switch-custom tippy" name="json[projecten_cusotm_rows][${lastString()}][show]" data-tippy-content="Tonen in projectoverzicht" >
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
      );
      initSJV();
    }
    function addLeverancierCustomDocumenten(){
      $('.leveranciers-custom-documenten').append(
        `<div class="flex-between mx--1 my-2 custom-row-container" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[leveranciers_custom_documenten][${lastString()}][keyword]" placeholder="Keyword" >
            <input class="form-control-custom mx-1 w-100" name="json[leveranciers_custom_documenten][${lastString()}][name]" placeholder="Naam" >
        </div>`
      );
      initSJV();
    }

    $(document).on('change', '.project_custom_type', async function(){
      const container = findContainer('custom-row-container', this);
      const string = container.attr('id');

      if($(this).val() == 'user'){
        const settingRoles = await getRoles();

        container.find('.project_custom_optionfield').html(`
        <infor-select-multiple name="json[projecten_cusotm_rows][${string}][data]" id="rol${string}" class="form-select mx-1 w-100" placeholder="Selecteer rol" data-sj-rol>
          ${settingRoles.map(rol =>
          `<infor-select-option data-name="${rol.name}" data-value="${rol.id}" >${rol.name}</infor-select-option>`
        ).join('')}
        </infor-select-multiple>
      `);
      } else {
        container.find('.project_custom_optionfield').html(`
        <input data-sjv class="form-control-custom" name="json[projecten_cusotm_rows][${string}][data]" placeholder="Data">
      `);
        initSJV()
      }

      initInforSelectMultiple();
    });

    function addFactuurCustomRow(){
      $('.factuur-custom-rows').append(
        `
        <div class="flex-between mx--1 my-2" id="${randomString()}" >
          <input class="form-control-custom mx-1 w-100" name="json[factuur_custom_rows][${lastString()}][naam]" placeholder="Naam" value="" >
          <input type="number" class="form-control-custom mx-1 w-100" min="0" name="json[factuur_custom_rows][${lastString()}][aantal]" placeholder="Aantal" value="" >
          <input type="number" class="form-control-custom mx-1 w-100" min="0" step="0.1" name="json[factuur_custom_rows][${lastString()}][bedrag]" placeholder="Bedrag" value="" >
          <input type="number" class="form-control-custom mx-1 w-100" min="0" max="100" step="0.1" name="json[factuur_custom_rows][${lastString()}][btw]" placeholder="BTW %" value="" >
          <div class="col-md-3 col-sm-4 col-6 mt-2"><label class="cursor-pointer"><input type="checkbox" name="json[factuur_custom_rows][${lastString()}][inclBtw]" @if(isset($velden["offerteupload"])) checked @endif > incl. BTW</label></div>
          <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>
        `);
      initSJV();
    }

    function addFactuurRowsCustomField(){
      $('.factuur-rows-custom-fields').append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][${lastString()}][keyword]" placeholder="Keyword" >
            <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][${lastString()}][label]" placeholder="Label" >
            <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][${lastString()}][default]" placeholder="Standaard waarde" >
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
      );
    }

    function addPlanningCustomRow(){
      $('.planning-custom-rows').append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[planning_custom_rows][${lastString()}][keyword]" placeholder="Keyword" >
            <input class="form-control-custom mx-1 w-100" name="json[planning_custom_rows][${lastString()}][name]" placeholder="Naam" >
            <select class="form-select mx-1 w-100" name="json[planning_custom_rows][${lastString()}][type]" >
                <option value="text">Tekst</option>
                <option value="number">Getal</option>
                <option value="date">Datum</option>
                <option value="time">Tijd</option>
                <option value="select">Select</option>
                <option value="tekstveld">Groot Tekstveld</option>
            </select>
            <div class="m-1 w-100" >
              <input data-sjv class="form-control-custom mx-1 w-100" name="json[planning_custom_rows][${lastString()}][data]" placeholder="Data" >
            </div>
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`);
      initSJV();
    }


    function addOfferteXmlBtwLaag(){
      const aantallagen = parseInt($('#aantaloffertexmlbtwlagen').val())+1;
      $('#aantaloffertexmlbtwlagen').val(aantallagen);
      $('.offerte-xml-btw-rows').append(
        `<label>Laag ${aantallagen}</label>
         <input type="text" class="form-control-custom" name="json[offerte_xml_btw][laag_${aantallagen}]">`
      )
    }

    function addOfferteXmlTotaalLaag(){
      const aantallagen = parseInt($('#aantaloffertexmltotaallagen').val())+1;
      $('#aantaloffertexmltotaallagen').val(aantallagen);
      $('.offerte-xml-totaal-rows').append(
        `<label>Laag ${aantallagen}</label>
         <input type="text" class="form-control-custom" name="json[offerte_xml_totaal][laag_${aantallagen}]">`
      )
    }

    function addProjectTakenCustomRow(){
      $('.project-taken-custom-rows').append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_taken_custom_rows][${lastString()}][keyword]" placeholder="Keyword" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_taken_custom_rows][${lastString()}][name]" placeholder="Naam" >
            <select class="form-select mx-1 w-100" name="json[projecten_taken_custom_rows][${lastString()}][type]" >
                <option value="text">Tekst</option>
                <option value="number">Getal</option>
                <option value="date">Datum</option>
                <option value="time">Tijd</option>
                <option value="select">Select</option>
                <option value="checkbox">Checkbox</option>
                <option value="user">Medewerker</option>
                <option value="machine">Machine</option>
                <option value="checklist">Checklist</option>
            </select>
            <div class="m-1 w-100">
              <input data-sjv class="form-control-custom mx-1 w-100" name="json[projecten_taken_custom_rows][${lastString()}][data]" placeholder="Data" >
            </div>
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
      );

      initSJV();
    }

    function addChecklistCustomStatus(div){
      $(`.${div}`).append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_taken_checklists_custom_statuses][${lastString()}][name]" placeholder="Naam" >
            <input type="hidden" class='color-input' name="json[projecten_taken_checklists_custom_statuses][${lastString()}][color]" value="#55bfff" >
            <div class="p-3 color-sphere rounded-circle cursor-pointer mx-1 cursor-pointer hover-shadow" onclick="selectChecklistStatusColor('${lastString()}')" style="background-image: linear-gradient(to bottom right, #55bfff80, #55bfff);"></div>
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
      )
    }

    function addplanningCustomColor(){
      $(`#planningkleuren`).append(
        `<div class="flex-between mx--1 my-2" id="${randomString()}" >
            <input class="form-control-custom mx-1 w-100" name="json[projecten_planning_custom_kleuren][${lastString()}][name]" placeholder="Naam" >
            <input type="hidden" class='color-input' name="json[projecten_planning_custom_kleuren][${lastString()}][color]" value="#55bfff" >
            <div class="p-3 color-sphere rounded-circle cursor-pointer mx-1 cursor-pointer hover-shadow" onclick="selectChecklistStatusColor('${lastString()}')" style="background-image: linear-gradient(to bottom right, #55bfff80, #55bfff);"></div>
            <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
      )
    }

    function addFacturatieBtwPerc(){
      $('.facturatie-btw-percs').append(
        `<div class="col-lg-2 col-md-3 col-4">
            <input type="number" placeholder="Percentage" class="form-control" name="factuurBtwPercs[]">
        </div>`
      )
    }

    function addFacturatieSplitsing(){
      $('.facturatie-standaard-splitsing').append(
        `<div class="col-lg-2 col-md-3 col-4 d-flex align-items-center" id="splitsing_${randomString()}">
          <input type="number" class="form-control" name="json[factuur_standaard_splitsing][]" placeholder="Percentage">
            <a onclick="deleteDiv('#splitsing_${lastString()}')" class="btn btn-danger text-white ml-2" style="line-height: 23px;">@icon_trash</a>
        </div>`
      )
    }

    function addDatasetXMLAttr(id){

      const dataset = datasets.find(d => d.id == id);
      let options = '<option value="" >Custom</option>';
      if(dataset.items.length){
        const items = JSON.parse(dataset.items[0].value);
        for(const name in items){
          options += `<option value="${name}" >${name}</option>`
        }
      }

      $(`[data-dataset-rows=${id}]`).append(
        `<div class="my-2" id="${randomString()}" >
              <div class="flex-between align-items-end mx--1" >
                  <div class="w-100 mx-1" >
                    <label>Parent</label>
                    <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][attrs][${lastString()}][parent]" placeholder="Parent">
                  </div>
                  <div class="w-100 mx-1" >
                    <label>Naam</label>
                    <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][attrs][${lastString()}][name]" placeholder="Naam">
                  </div>
                  <div class="w-100 mx-1" >
                    <label>Element</label>
                    <select class="form-select" name="json[datasets_export_xml][${id}][attrs][${lastString()}][index]">${options}</select>
                  </div>
                  <div class="w-100 mx-1" >
                    <label>Waarde</label>
                    <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][attrs][${lastString()}][value]" placeholder="Invullen bij custom waardes">
                  </div>
                  <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
              </div>
          </div>`
      );
    }
    function addDatasetXMLRegelAttr(id){

      const dataset = datasets.find(d => d.id == id);
      let options = '<option value="" >Custom</option>';
      if(dataset.items.length){
        const items = JSON.parse(dataset.items[0].value);
        for(const name in items){
          options += `<option value="${name}" >${name}</option>`
        }
      }

      $(`[data-dataset-rows=${id}]`).append(
        `<div class="my-2" id="${randomString()}" >
              <div class="flex-between align-items-end mx--1" >
                  <div class="w-100 mx-1" >
                    <label>Naam</label>
                    <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][regelattrs][${lastString()}][name]" placeholder="Naam">
                  </div>
                  <div class="w-100 mx-1" >
                    <label>Element</label>
                    <select class="form-select" name="json[datasets_export_xml][${id}][regelattrs][${lastString()}][index]">${options}</select>
                  </div>
                  <div class="w-100 mx-1" >
                    <label>Waarde</label>
                    <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][regelattrs][${lastString()}][value]" placeholder="Invullen bij custom waardes">
                  </div>
                  <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
              </div>
          </div>`
      );
    }
    function addDatasetXMLValue(id){
      const dataset = datasets.find(d => d.id == id);
      let options = '<option value="" >Custom</option>';
      if(dataset.items.length){
        const items = JSON.parse(dataset.items[0].value);
        for(const name in items){
          options += `<option value="${name}" >${name}</option>`
        }
      }


      $(`[data-dataset-rows=${id}]`).append(
        `<div class="my-2" id="${randomString()}" >
                  <div class="flex-between align-items-end mx--1" >
                      <div class="w-100 mx-1" >
                        <label>Container</label>
                        <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][values][${lastString()}][container]" placeholder="Container">
                      </div>
                      <div class="w-100 mx-1" >
                        <label>Naam</label>
                        <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][values][${lastString()}][name]" placeholder="Naam">
                      </div>
                      <div class="w-100 mx-1" >
                        <label>Element</label>
                        <select class="form-select" name="json[datasets_export_xml][${id}][values][${lastString()}][index]">${options}</select>
                      </div>
                      <div class="w-100 mx-1" >
                        <label>Waarde</label>
                        <input type="text" class="form-control-custom" name="json[datasets_export_xml][${id}][values][${lastString()}][value]" placeholder="Invullen bij custom waardes">
                      </div>
                      <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
                  </div>
            </div>`
      );


    }
    function chooseDatasetXMLValue(dataset){
      const modal = $('#datasets-xml-export-modal');


      modal.find('.modal-body').empty();
      modal.showModal();

      if(!dataset.items.length){
        modal.find('.modal-body').html('<h4 class="text-center text-muted" >Geen items gevongen</h4>');
      }

      const items = JSON.parse(dataset.items[0].value);
      for(const name in items){
        modal.find('.modal-body').append(`<a class="btn btn-inverse-success d-block datasets-xml-choose-element my-2" data-dataset-xml="${name}" >${name}</a>`);
      }
      modal.find('.modal-body').append(`<a class="btn btn-inverse-success d-block datasets-xml-choose-element my-2" data-dataset-xml="" >Custom</a>`);

      $(document).off('click', '.datasets-xml-choose-element')
      return new Promise(function(resolve){
        $(document).on('click', '.datasets-xml-choose-element', function(){
          resolve($(this).attr('data-dataset-xml'));
        })

      });



    }

    function newApi(){
      _api.modal.find('input[name=name]').val('');
      _api.modal.find('input[name=id]').val('');
      _api.modal.find('.permission').prop('checked', false);


      _api.modal.showModal();
    }
    function confirmApi(){
      _api.modal.find('.modal-footer').addClass('d-none');
      loader();
    }
    function editApiModal(id){
      const request = requests.tokens.find(t => t.id == id);
      const permissions = JSON.parse(request.permissions);

      _api.modal.find('input[name=name]').val(request.name);
      _api.modal.find('input[name=id]').val(request.id);
      _api.modal.find('.permission').prop('checked', false);

      for(const permission in permissions){
        _api.modal.find(`input[name='permissions[${permission}]']`).prop('checked', true)
      }

      _api.modal.showModal();
    }
    function removeApiModal(id){
      const request = requests.tokens.find(token => token.id == id);

      _api.deleteModal.find('input[name=id]').val(request.id);
      _api.deleteModal.find('.name').html(request.name);

      _api.deleteModal.showModal();
    }
    function removeApi(){
      _api.deleteModal.find('.modal-footer').addClass('d-none');
      loader();
    }

    function addProjectTaakColor(){
      randomString();

      $('.projecten-taken-colors').append(
        `<div class="col-12 my-2 ${lastString()}">
            <div class="bg-white border flex-between rounded mx--1 p-2" >
                <input type="text" class="form-control-custom mx-1" name="json[projecten_taken_standard_colors][${lastString()}][name]" placeholder="Naam">
                <input type="text" class="form-control-custom mx-1" name="json[projecten_taken_standard_colors][${lastString()}][custom]" placeholder="Custom keyword">
                <input type="text" class="form-control-custom mx-1" name="json[projecten_taken_standard_colors][${lastString()}][custom_value]" placeholder="Custom value">
                <input type="hidden" class='color-input' name="json[projecten_taken_standard_colors][${lastString()}][color]" value="#55bfff" >
                <div class="p-3 color-sphere rounded-circle cursor-pointer mx-1 cursor-pointer hover-shadow" onclick="selectProjectTaakColor('${lastString()}')" style="background-image: linear-gradient(to bottom right, #55bfff80, #55bfff);"></div>
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('.${lastString()}')" >@icon_close</a>
            </div>
        </div>`
      )
    }
    function selectProjectTaakColor(string){
      const input = $(`.${string}`).find('.color-input');
      const sphere = $(`.${string}`).find('.color-sphere');

      getHexColor({color: input.val()})
        .then((color) => {
          input.val(color)
          sphere.attr('style', `background-image: linear-gradient(to bottom right, ${color}80, ${color})`)
        })
        .catch()
    }

    function selectChecklistStatusColor(string){
      const input = $(`#${string}`).find('.color-input');
      const sphere = $(`#${string}`).find('.color-sphere');

      getHexColor({color: input.val()})
        .then((color) => {
          input.val(color)
          sphere.attr('style', `background-image: linear-gradient(to bottom right, ${color}80, ${color})`)
        })
        .catch()
    }

    function addProjectExport(){
      $("#export-modal").find("input[name=name]").val('');
      @if(hasModule("Offertes"))
      $("#export-modal").find("select[name=template]").val('');
      @endif

      $("#export-modal").modal("show");
    }
    function editProjectExport(id){
      const template = projectenExportTemplates[id];
      const letters = @json(aazArr());

      if(template.all_projects === '1'){
        $("#projecten-export-all-projects").click();
      }
      else{
        $("#projecten-export-active-projects").click();
      }

      let options = '';
      for(const letter of letters){
        options += `<option value="${letter}" >${letter}</option>`
      }

      $("#export-modal-fields").find('input[name=template]').val(template.id);
      $("#projectenOfferteInputs").empty();

      for(const k in (keywords[template.offerte_template_id] || [])){
        const keyword = keywords[template.offerte_template_id][k];

        $("#projectenOfferteInputs").append(
          `<div class="my-3 export-container">
          <label class="w-100" >${keyword.naam}</label>
          <div class="d-flex justify-content-between align-items-center" >
            <input class="export-column-name form-control" name="hiddenExportOfferte[${keyword.keyword}]" value="${keyword.naam}">
            <select class="form-select ml-2 export-column-select" data-type="offerte" data-target="${keyword.keyword}" name="exportOfferte[${keyword.keyword}]" >
            <option selected value="" >Selecteer kolom</option>
              ${options}
            </select>
          </div>
        </div>`
        )
      }

      const columns = $(".export-column-select");
      columnsLoop: for(const column of columns){
        const container = findContainer('export-container', column);
        for(const field of template.fields){
          if(field.target == $(column).attr('data-target') && field.type == $(column).attr('data-type')){
            $(container).find('.export-column-select').val(field.kolom);
            $(container).find('.export-column-name').val(field.name);
            continue columnsLoop
          }
        }
      }

      fillProjectExportClasses();
      disableProjectExportOptions();
      $("#export-modal-fields").modal("show");

    }
    function fillProjectExportClasses(){
      $(".export-column-select").each(function(){
        $(this).find("option").each(function(){
          const letter = $(this).prop('value')
          $(this).addClass(`export-option-${letter}`)
        });
      })
    }
    function disableProjectExportOptions(){
      $(".export-column-select").each(function(){
        const value = this.value;
        const previous = $(this).attr('data-previous');

        if(previous){
          $(`.export-option-${previous}`).prop('disabled', false).removeClass("d-none");
        }

        if(this.value){
          $(`.export-option-${value}`).prop('disabled', true).addClass("d-none");
          $(this).find(`.export-option-${value}`).prop('disabled', false);
        }

        $(this).attr("data-previous", value);
      });
    }

    function disconnectSnelstart(bv_id){

      confirmModal({
        text: 'Weet je zeker dat je de koppeling met Snelstart ongedaan wilt maken?'
      })
        .then(response => {
          if(!response.status){ return; }
          loader();
          $.ajax({
            type: "POST",
            url: "{{url("api/settings/store")}}",
            data: {
              name: `snelstart_subscription_key_${bv_id}`,
              value: null,
              _token: "{{csrf_token()}}",
            },
            success: function () {
              redirect('/settings?target=Koppelingen_splitter_Snelstart')
              successLoader();
            },
            error: function () {
              errorLoader();
            }
          });
        })
    }
    function selectStandaardGrootboek(perc, bv_id){
      let options = '';
      for(const grootboek of snelstartGrootboeken.filter(gb => gb.bv_id == bv_id)){
        const value = JSON.stringify({
          name: grootboek.omschrijving,
          value: grootboek.snelstart_id,
        })
        options += `<span class='select_group-value' data-group="${grootboek.functie}" data-value='${value}' data-name='${grootboek.omschrijving}'><div class="d-inline-block w-5-rem" >${grootboek.nummer}</div>${grootboek.omschrijving}</span>`
      }
      const input = `<div class="select_group-container" >
                       <label>Standaard ${perc}% BTW grootboek</label>
                       <input type="hidden" value=""  data-placeholder="Grootboeken" data-required="required"  class="select_group-hidden-input snelstart-standaard-grootboek">
                       <div class="select_group-values" >
                         <div class="select_group-box" >
                            ${options}
                         </div>
                       </div>
                     </div>`

      confirmModal({
        text: input,
      })
        .then((response) => {
          if(!response.status){return;}

          const v = $('.snelstart-standaard-grootboek').val();
          if(!v){return;}

          const value = JSON.parse(v);
          $(`[name='settings[facturatie_snelstart_grootboek_bv${bv_id}_perc${perc}]']`).val(v);
          $(`[data-snelstart-grootboek-perc=${perc}][data-bv=${bv_id}]`).find('[data-label]').html(value.name);
        });
      initSelectGroup();
    }

    function addAanvraagRow(){
      $("#aanvraagRow").append(
        '<div class="row my-2" id="aanvraagRow'+aanvraagInputsIndex+'">'+
        '<div class="col-md-7 col-12 my-1">'+
        '<input class="form-control" type="text" name="aanvragenInputs['+aanvraagInputsIndex+'][naam]" placeholder="Naam">'+
        '</div>'+
        '<div class="col-md-2 col-6 my-1">'+
        '<select class="form-select" id="aanvragenSelect'+aanvraagInputsIndex+'" name="aanvragenInputs['+aanvraagInputsIndex+'][type]">'+
        '</select>'+
        '</div>'+
        '<div class="col-md-2 col-6 my-1">'+
        '<select class="form-select" name="aanvragenInputs['+aanvraagInputsIndex+'][required]">'+
        '<option value="1" >Ja</option>'+
        '<option value="0" >Nee</option>'+
        '</select>'+
        '</div>'+
        '<div class="col-md-1 col-12 my-1">'+
        '<a class="btn btn-danger text-white" onclick="deleteDiv(\'#aanvraagRow'+aanvraagInputsIndex+'\')"><i class="bi bi-trash"></i></a>'+
        '</div>'+
        '</div>'
      )
      for(let type of aanvraagTypes){
        $("#aanvragenSelect"+aanvraagInputsIndex).append(
          '<option value="'+type.type+'" >'+type.type+'</option>'
        )
      }
      aanvraagInputsIndex++;
    }
    function deleteDiv(id){
      $(id).remove();
    }
    function toggleDiv(id){
      $(".toggleDiv").not(id).addClass("d-none")
      $(id).toggleClass("d-none");
    }
    function addFactuurReminder(){
      $("#factuurReminders").append(
        '<div id="factuurReminder'+factuurReminderIndex+'" class="d-flex justify-content-between my-2" >' +
        '<input type="text" placeholder="Reminder" class="form-control mx-2" name="factuurReminders[]">'+
        '<a onclick="deleteDiv(\'#factuurReminder'+factuurReminderIndex+'\')" class="btn btn-danger text-white align-self-center" >@icon_trash</a>' +
        '</div>'
      );
      factuurReminderIndex++;
    }
    function addKlantenReminder(){
      const string = randomString(10);
      $("#klantenReminders").append(
        `<div id="${string}" class="d-flex justify-content-between my-2" >
      <input type="text" placeholder="Reminder" class="form-control mx-2" name="klantenReminders[]">
      <a onclick="deleteDiv('#${string}')" class="btn btn-danger text-white align-self-center" >@icon_trash</a>
      </div>`
      );
    }
    function addSmsTemplate(){
      let string = randomString(15);
      $("#smsTemplates").append(
        '<div class="my-2" id="'+string+'" >'+
        '<div class="d-flex justify-content-between" >'+
        '<label class="my-2" >Naam</label>'+
        '<a class="btn btn-danger text-white align-self-baseline" onclick="deleteDiv(\'#'+string+'\')" >@icon_trash</a>'+
        '</div>'+
        '<input type="text" name="klantenSmsName[]" class="form-control" placeholder="Naam">'+
        '<label class="my-2" >Inhoud</label>'+
        '<textarea name="klantenSmsContent[]" class="form-control" rows="4"></textarea>'+
        '</div>'+
        '<div>'+
        '<hr>'+
        '</div>'
      )
    }
    function addEmailTemplate(){
      let string = randomString(15);
      $("#emailTemplates").append(
        '<div class="my-2" id="'+string+'" >'+
        '<div class="d-flex justify-content-between" >'+
        '<label class="my-2" >Naam</label>'+
        '<a class="btn btn-danger text-white align-self-baseline" onclick="deleteDiv(\'#'+string+'\')" >@icon_trash</a>'+
        '</div>'+
        '<input type="text" name="klantenEmailName[]" class="form-control" placeholder="Naam">'+
        '<label class="my-2" >Afzender</label>'+
        '<input type="text" name="klantenEmailAfzender[]" class="form-control" placeholder="Afzender">'+
        '<label class="my-2" >Onderwerp</label>'+
        '<input type="text" name="klantenEmailOnderwerp[]" class="form-control" placeholder="Onderwerp">'+
        '<label class="my-2" >Inhoud</label>'+
        '<textarea name="klantenEmailContent[]" class="form-control" rows="4"></textarea>'+
        '</div>'+
        '<div>'+
        '<hr>'+
        '</div>'
      )
    }
    function beschikbaarheidOptie(){
      const string = randomString(15);
      $("#beschikbaarheid-opties").append(
        '<div class="d-flex justify-content-between align-items-end my-2" id="'+string+'" >' +
        '<div class="w-100 m-1"><label>Naam</label><input type="text" class="form-control" name="beschikbaarheidOptieName[]" placeholder="Naam"></div>' +
        '<div class="w-100 m-1"><label>Van</label><input type="time" class="form-control" name="beschikbaarheidOptieStart[]"></div>' +
        '<div class="w-100 m-1"><label>Tot</label><input type="time" class="form-control" name="beschikbaarheidOptieEnd[]"></div>' +
        '<div class="m-1"><a class="btn btn-danger text-white my-2" onclick="deleteDiv(\'#'+string+'\')" >@icon_trash</a></div>' +
        '</div>'
      );
    }


    function showXmlModal(){
      $("#xmlExportSelect").empty();
      $("#xmlExportModal").showModal();
    }
    function addXmlRow(){
      var values = $("input[name='xmlSelect[]']")
        .map(function(){return $(this).val();}).get();
      let label = values.join(', ');
      if(label == "custom"){
        var custom = "custom"+customi;
        customi++;
        $("#xmlExportRow").append(
          `<div id="${xmlRowId}" class="my-2" >
          <label><b>Custom</b></label><br>
          <label>Custom label</label>
          <div class="flex-between" >
            <input type="text" placeholder="Vul hier de naam in die dit record moet krijgen in de Xml-export" class="form-control" name="controlelijstExport[${custom}][]">
            <a class="btn btn-danger text-white" onclick="deleteDiv(${xmlRowId})">@icon_trash</a>
          </div>
          <label>Custom data</label
          <div class="flex-between" >
            <input type="text" placeholder="Vul hier de data in die ingevuld moet worden in de Xml-export" class="form-control" name="controlelijstExport[${custom}][]">
          </div>
        </div>`
        );
      }else{
        $("#xmlExportRow").append(
          `<div id="${xmlRowId}" class="my-2" >
          <label><b>${label}</b></label>
          <div class="my-2 flex-between" >
            <input type="text" placeholder="Vul hier de naam in die dit record moet krijgen in de Xml-export" class="form-control" name="controlelijstExport[${values}]">
            <a class="btn btn-danger text-white" onclick="deleteDiv(${xmlRowId})">@icon_trash</a>
          </div>
        </div>`
        )
      }
      $("#xmlExportModal").hideModal();
      xmlRowId++;
    }
    function addXmlSelect(){
      $("#xmlExportSelect").append(
        `<div class="my-2 select_group-container" >
        <input name="xmlSelect[]" type="hidden" data-input-prefill="{{$selectedOptionName}}" data-placeholder="Selecteer een item" data-required="required" class="select_group-hidden-input form-select">
        <div class="select_group-values" >
            <div class="select_group-box" >
              @foreach ($urenregistratieColumns as $row)
        @if (!in_array($row, $excludes))
        <span class="select_group-value" data-group="Urenregistratie" data-value="{{$row}}" data-name="{{$row}}">{{$row}}</span>
                @endif
        @endforeach
        @foreach ($usersColumns as $row)
        @if (!in_array($row, $excludes))
        <span class="select_group-value" data-group="Users" data-value="{{$row}}" data-name="{{$row}}">{{$row}}</span>
                @endif
        @endforeach
        @foreach ($verlofredenenColumns as $row)
        @if (!in_array($row, $excludes))
        <span class="select_group-value" data-group="Verlofredenen" data-value="{{$row}}" data-name="{{$row}}">{{$row}}</span>
                @endif
        @endforeach
        @foreach ($naca_codesColumns as $row)
        @if (!in_array($row, $excludes))
        <span class="select_group-value" data-group="Naca_codes" data-value="{{$row}}" data-name="{{$row}}">{{$row}}</span>
                @endif
        @endforeach
        <span class="select_group-value" data-group="Custom" data-value="custom" data-name="custom">custom</span>
      </div>
  </div>
</div>`
      )
      initSelectGroup();
    }

    function customKlantKey(id){
      if($('#select_klanten_xml_key'+id).val() == 'custom'){
        $('#klanten_export_xml_key_custom'+id).removeClass('d-none')
      }else{
        $('#klanten_export_xml_key_custom'+id).addClass('d-none')
      }
    }

    $(`input[name='checkbox[custom_menu_volgorde]']`).on('change', function(){
      showCustomMenuVolgorde();
    })

    function initCustomMenu(){
      if(@json(getSettingCheckbox('custom_menu_volgorde'))){
        var volgorde = @json(json_decode(getSettingValue('custom_volgorde_menu')));
      }
      let i = 0;
        $('[data-menu-title]').each(function(){
          const text = $(this).text();
          if(volgorde){
            i = volgorde[text];
          }
          else{
            i++;
          }
          $('#custommenuvolgorde').append(`
            <div class="my-1 flex-between col-12">
              <span class="mx-1" >${text}</span>
              <span class="mx-1" ><input name="json[custom_volgorde_menu][${text}]" type="number" step="1" class="form-control-custom" value="${i}" data-custom-menu-volgorde="${i}"></span>
            </div>`
          );

        })
    }

    function showCustomMenuVolgorde(){
      if ($(`input[name='checkbox[custom_menu_volgorde]']`).prop('checked') == true) {
        $('#custommenuvolgorde').removeClass('d-none');
      }
      else{
        $('#custommenuvolgorde').addClass('d-none');
      }
    }

    function addStandaardProjectMap(){
      let viewRoleLabels = '';
      let editRoleLabels = '';
      let string = randomString();
      for (let role of roles) {
        viewRoleLabels += `<label class="select_multiple-value" >${role.name} <input type="checkbox" class="d-none" name="json[standaard_project_mappen][${string}][viewRollen][]" data-name="${role.name}" value="${role.id}"></label>`;
        editRoleLabels += `<label class="select_multiple-value" >${role.name} <input type="checkbox" class="d-none" name="json[standaard_project_mappen][${string}][editRollen][]" data-name="${role.name}" value="${role.id}"></label>`;
      }
      $('#projecten-mappen').append(`
      <div class="flex-between mx--1 my-2" id="${string}">
      <input class="form-control-custom mx-1 w-20" name="json[standaard_project_mappen][${string}][naam]" placeholder="Naam"" >
      <div class="select_multiple-container tippy" data-tippy-content="View rechten" data-placeholder="Rollen" >
        ${viewRoleLabels}
      </div>
      <div class="select_multiple-container tippy" data-tippy-content="Edit rechten" data-placeholder="Rollen" >
        ${editRoleLabels}
      </div>
      <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${string}')" >@icon_close</a>
      </div>
      `)
      initSelectMultiple();
      tippyInit();
    }

    function addContactTitle(){
      let string = randomString();
      $('.contact-titels').append(`
      <div class="flex-between mx--1 my-2" id="${string}">
      <input class="form-control-custom mx-1 w-20" name="json[klanten_contactpersoon_titels][${string}]"" >
      <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${string}')" >@icon_close</a>
      </div>
      `)
    }

    function addOpvolgStap(){
      let string = randomString();
      $('.werkbon_opvolgstappen').append(`
        <div id="${string}">
          <div class="flex-between my-1">
            <div class="mx-1 w-100">
              <label>Naam</label>
              <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][${string}][naam]" placeholder="Naam">
            </div>
            <div class="mx-1 w-100">
              <label>Status bij aanmaak</label>
              <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][${string}][status]" placeholder="Status">
            </div>
            <div class="mx-1 w-100">
              <label>Status bij stap gereed</label>
              <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][${string}][statusgereed]" placeholder="Status">
            </div>
          </div>
          <div class="flex-between my-1">
            <div class="mx-1 w-100">
              <label>Stadium</label>
              <select name="json[werkbon_opvolgstappen][${string}][stadium]" class="form-select">
                <option value="Opvolgen">Opvolgen</option>
                <option value="Afgerond">Afgerond</option>
                <option value="Verwijderd">Verwijderd</option>
                <option value="Open">Open</option>
              </select>
            </div>
            <div class="mx-1 w-100">
              <label>Doorlooptijd (in dagen)</label>
              <input type="number" class="form-control-custom" name="json[werkbon_opvolgstappen][${string}][doorlooptijd]" placeholder="Doorlooptijd">
            </div>
            <div class="mx-1 w-100">
              <label>Reminder na ... dagen</label>
              <input type="number" class="form-control-custom" name="json[werkbon_opvolgstappen][${string}][reminder]" placeholder="reminder">
            </div>
            <a class="btn btn-inverse-danger mt-4" onclick="deleteDiv('#${string}')">@icon_close</a>
          </div>
          <div class="opvolgvelden_${string} my-1"></div>
          <div class="center my-1">
            <a class="btn btn-inverse-primary" onclick="addOpvolgVeld('${string}')">Velden @icon_plus</a>
          </div>
        </div>`);
    }

    function addOpvolgVeld(id){
      let string = randomString();
      $(`.opvolgvelden_${id}`).append(`
        <div class="flex-between my-1" id="${string}">
          <div class="mx-1 w-100">
            <label>Keyword</label>
            <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][${id}][velden][${string}][keyword]" placeholder="Keyword">
          </div>
          <div class="mx-1 w-100">
            <label>Naam</label>
            <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][${id}][velden][${string}][naam]" placeholder="Naam">
          </div>
          <div class="mx-1 w-100">
            <label>Type</label>
            <select class="form-control-custom" name="json[werkbon_opvolgstappen][${id}][velden][${string}][type]">
              <option value="text">Tekst</option>
              <option value="number">Getal</option>
              <option value="date">Datum</option>
              <option value="datedone">Datum gereed</option>
              <option value="time">Tijd</option>
              <option value="select">Select</option>
              <option value="selectedit">Select edit</option>
              <option value="checkbox">Checkbox</option>
              <option value="user">Medewerker</option>
              <option value="textarea">Tekstveld</option>
            </select>
          </div>
          <div class="mx-1 w-100">
            <label>Opties</label>
            <input data-sjv class="form-control-custom mx-1 w-100" name="json[werkbon_opvolgstappen][${id}][velden][${string}][data]" placeholder="Data" >
          </div>
          <a class="btn btn-inverse-danger mt-4" onclick="deleteDiv('#${string}')">@icon_close</a>
        </div>`);

      initSJV();
    }

    // custom checklist flow
    function addChecklistStap() {
      let string = randomString();
      $('.checklist_custom_project_flow_stappen').append(`
        <div class="row my-2" id="${string}">
          <div class="col-12 my-2">
            <label>Checklist template</label>
            <select class="form-select" name="json[checklist_custom_project_flow_stappen][${string}][template]" >
              ${checklistTemplates.map(template => `<option value="${template.id}" >${template.name}</option>`).join('')}
            </select>
          </div>
          <div class="col-6 my-2">
            <label>Vereiste checklists</label>
            <infor-select-multiple class="form-control-custom" name="json[checklist_custom_project_flow_stappen][${string}][vereiste_templates]" placeholder="Selecteer checklists">
              ${checklistTemplates.map(template => `<infor-select-option data-name="${template.name}" data-value="${template.id}">${template.name}</infor-select-option>`).join('')}
            </infor-select-multiple>
          </div>
          <div class="col-6 my-2">
            <label>Vereist antwoord</label>
            <select class="form-select" name="json[checklist_custom_project_flow_stappen][${string}][vereist_antwoord]" >
              <option value="">Geen vereist antwoord</option>
              ${checklistTemplates.map(template =>
                template.keywords.map(keyword =>
                  keyword.type === 'select' ? JSON.parse(keyword.data).options.map(option =>
                    `<option value='{"template": "${template.id}", "keyword": "${keyword.id}", "answer": "${option.value}"}'>${template.name} - ${keyword.name} - ${option.name}</option>`
                  ).join('') : ''
                ).join('')
              ).join('')}
          </select>
          </div>
        </div>`);
        initInforSelectMultiple();
    }


  </script>
@endsection
