@if(hasModule("Inkoopbonnen"))
  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3 py-2" ><i class="fa-solid fa-receipt" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Inkoopbonnen</h4>
      </div>
      <div class="row m-3 d-none settings-content">
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Inkoopbonnen instellingen</div>
        <div class="col-12 my-2">
          <div class="row">
            <div class="col-md-6 col-12 my-2">
              <label>Rapportagedatum</label>
              <select name="settings[inkoopbonnen_reporting_date]" class="form-select">
                <option @if(getSettingValue('inkoopbonnen_reporting_date') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('inkoopbonnen_reporting_date') == 'aan') selected @endif value="aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Standaard rapportagedatum <span class="text-muted">7, -7</span> </label>
              <input type="number" step="1" name="settings[inkoopbonnen_reporting_date_default]" class="form-control-custom" placeholder="Standaard rapportagedatum" value="{{getSettingValue('inkoopbonnen_reporting_date_default') ?? 0}}" >
            </div>
            <div class="col-12 my-2">
              <label>BTW Verbergen</label>
              <select class="form-select" name="settings[inkoopbonnen_hide_btw]" >
                <option @if(getSettingValue('inkoopbonnen_hide_btw') == 'nee') selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('inkoopbonnen_hide_btw') == 'ja') selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-12 my-2">
              <label>Offertes prefillen</label>
              <select class="form-select" name="settings[inkoopbonnen_offertes_prefill]" >
                <option @if(getSettingValue('inkoopbonnen_offertes_prefill') == 'nee') selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('inkoopbonnen_offertes_prefill') == 'ja') selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="my-2">
              <label class="cursor-pointer" >
                <input type="checkbox" name="checkbox[inkoopbonnen_vestiging_select]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_vestiging_select')) checked @endif >
                <span class="mx-1" >Vestigingen kunnen selecteren</span>
              </label>
            </div>
            <div class="col-12 my-2">
              <label>Geldigheidsduur (in dagen)</label>
              <input type="number" step="1" class="form-control-custom" name="settings[inkoopbonnen_geldigheidsduur]" placeholder="30" value="{{getSettingValue('inkoopbonnen_geldigheidsduur', 30)}}">
            </div>
            <div class="row">
              <div class="col-md-4 col-6 my-2">
                <label class="cursor-pointer" >
                  <input type="checkbox" name="checkbox[inkoopbonnen_pdf_niet_meer_inzien_na_versturen]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_pdf_niet_meer_inzien_na_versturen')) checked @endif >
                  <span class="mx-1" >Pdf niet meer inzien na versturen</span>
                </label>
              </div>
              <div class="col-md-4 col-6 my-2">
                <label class="cursor-pointer" >
                  <input type="checkbox" name="checkbox[inkoopbonnen_nul_euro_bonnen_niet_toestaan]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_nul_euro_bonnen_niet_toestaan')) checked @endif >
                  <span class="mx-1" >0 euro niet toestaan</span>
                </label>
              </div>
              <div class="col-md-4 col-6 my-2">
                <label class="cursor-pointer" >
                  <input type="checkbox" name="checkbox[inkoopbonnen_negatieve_bedragen_niet_toestaan]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_negatieve_bedragen_niet_toestaan')) checked @endif >
                  <span class="mx-1" >Negatieve bedragen niet toestaan</span>
                </label>
              </div>
              <div class="col-md-4 col-6 my-2">
                <label class="cursor-pointer" >
                  <input type="checkbox" name="checkbox[inkoopbonnen_status_voltooid]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_status_voltooid')) checked @endif >
                  <span class="mx-1" >Status voltooid optie</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Bonnummer</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue('inkoopbonnen_bonnummer') ?? '{}'); @endphp
          <div class="my-2">
            <label>Bonnummer prefix</label>
            <input type="text" name="json[inkoopbonnen_bonnummer][prefix]" class="form-control" value="{{$data->prefix ?? ''}}" placeholder="Prefix">
          </div>
          <div class="my-2">
            <label>Bonnummer datum format</label>
            <select name="json[inkoopbonnen_bonnummer][date]" class="form-select">
              <option value="long" @if(($data->date ?? '') == 'long') selected @endif >2022</option>
              <option value="short" @if(($data->date ?? '') == 'short') selected @endif >22</option>
              <option value="none" @if(($data->date ?? '') == 'none') selected @endif >Leeg</option>
            </select>
          </div>
          <div class="my-2">
            <label>Afterfix lengte</label>
            <select name="json[inkoopbonnen_bonnummer][afterfix_length]" class="form-select">
              @for($i = 0; $i < 11; $i++)
                <option value="{{$i}}" @if(($data->afterfix_length ?? 3) == $i) selected @endif >{{$i}}</option>
              @endfor
            </select>
          </div>
          <div class="my-2">
            <label>Bonnummer ophogen met</label>
            <input type="number" step="1" class="form-control" name="json[inkoopbonnen_bonnummer][increase]" value="{{$data->increase ?? ''}}" placeholder="Ophoging" >
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Email instellingen</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label>Afzender</label>
            <input type="text" class="form-control" name="settings[inkoopbonnen_email_afzender]" value="{{getSettingValue('inkoopbonnen_email_afzender')}}" placeholder="Afzender" >
          </div>
          <div class="col-md-4 col-6 my-2">
            <label class="cursor-pointer" >
              <input type="checkbox" name="checkbox[inkoopbonnen_aanmaker_afzender]" class="form-switch-custom" @if(getSettingCheckbox('inkoopbonnen_aanmaker_afzender')) checked @endif >
              <span class="mx-1" >Standaard aanmaker als afzender</span>
            </label>
          </div>
          <div class="my-2">
            <label>Inhoud</label>
            <textarea name="settings[inkoopbonnen_email_content]" id="inkoopbonnen-email-content" >{!! getSettingValue('inkoopbonnen_email_content') !!}</textarea>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Eenheden</div>
        <div class="col-12 my-2">
          <div class="my-2" data-inkoopbonnen-eenheden >
            @php $data = getSettingJson('inkoopbonnen_eenheden'); @endphp
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_eenheden][{{$string}}][eenheid]" placeholder="Eenheid" value="{{$row['eenheid'] ?? ''}}" >
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach

          </div>
          <div class="my-2 text-center ">
            <a class="btn btn-inverse-primary" onclick="addInkoopbonEenheid()" >@icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Custom velden</div>
        <div class="col-12 my-2">
          <div class="my-2" data-inkoopbonnen-custom-rows >
            @php $data = json_decode(getSettingValue('inkoopbonnen_custom_rows') ?? '{}') @endphp
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_custom_rows][{{$string}}][keyword]" placeholder="Keyword" value="{{$row->keyword ?? ''}}" >
                <input class="form-control-custom mx-1 w-100" name="json[inkoopbonnen_custom_rows][{{$string}}][name]" placeholder="Naam" value="{{$row->name ?? ''}}" >
                <select class="form-select mx-1 w-100" name="json[inkoopbonnen_custom_rows][{{$string}}][type]" >
                  <option @if(($row->type ?? null) == 'text') selected @endif value="text">Tekst</option>
                  <option @if(($row->type ?? null) == 'number') selected @endif value="number">Getal</option>
                </select>
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="my-2 text-center ">
            <a class="btn btn-inverse-primary" onclick="addInkoopbonCustomRow()" >@icon_plus</a>
          </div>
        </div>
      </div>
    </div>
  </div>
@endif
