@if(Auth::user()->hasPermissionTo('Checklists aanmaken') && hasModule("Checklists"))
  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="menu-icon far fa-list-alt py-2" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Checklists</h4>
      </div>
      <div class="row m-3 d-none settings-content">
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white" data-settings-sub-header>Overzicht</div>
        <div class="col-12 mt-3">
          @php $data = json_decode((getSettingValue('checklists_index_show_keywords') ?? '[]'), true) @endphp
          @foreach($checklistTemplates as $template)
            <label>{{$template->name}}</label>
            <div class="my-2 row mx-3 border-right border-bottom">
              @foreach($template->keywords as $keyword)
                @if(!isset($keyword->keyword)) @continue @endif
                <div class="col-xl-3 col-md-4 col-6 my-2">
                  <label class="cursor-pointer"><input @isset($data[$template->id][$keyword->keyword]) checked @endisset type="checkbox" value="{{$keyword->name}}" name="json[checklists_index_show_keywords][{{$template->id}}][{{$keyword->keyword}}]">&nbsp;{{$keyword->name}}</label>
                </div>
              @endforeach
            </div>
          @endforeach
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white" data-settings-sub-header>Vervolgafspraken</div>
        <div class="col-12 mt-3">
          @php
            $setting = getSettingJson('checklist_verv_afspraken');
          @endphp
          @foreach($checklistTemplates as $template)
          @php
            $data = isset($setting[$template->id]) ? $setting[$template->id] : [];
          @endphp
            <label class="col-12">Vanuit welke velden in: "{{$template->name}}" een vervolgafspraak inplannen?</label>
            <infor-select-search class="m-2 form-control-custom" name="json[checklist_verv_afspraken][{{$template->id}}][date]" placeholder="Selecteer datum">
              @foreach ($template->keywords as $keyword)
                @if ($keyword->type != 'date') @continue @endif
                <infor-select-option @if($keyword->keyword == ($data['date'] ?? '')) data-selected @endif data-value="{{$keyword->keyword}}" data-name="{{$keyword->name}}">{{$keyword->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
            <infor-select-search class="m-2 form-control-custom" name="json[checklist_verv_afspraken][{{$template->id}}][start]" placeholder="Selecteer begintijd">
              @foreach ($template->keywords as $keyword)
                @if ($keyword->type != 'time') @continue @endif
                <infor-select-option @if($keyword->keyword == ($data['start'] ?? '')) data-selected @endif data-value="{{$keyword->keyword}}" data-name="{{$keyword->name}}">{{$keyword->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
            <infor-select-search class="m-2 form-control-custom" name="json[checklist_verv_afspraken][{{$template->id}}][end]" placeholder="Selecteer eindtijd">
              @foreach ($template->keywords as $keyword)
                @if ($keyword->type != 'time') @continue @endif
                <infor-select-option @if($keyword->keyword == ($data['end'] ?? '')) data-selected @endif data-value="{{$keyword->keyword}}" data-name="{{$keyword->name}}">{{$keyword->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
            <infor-select-search class="m-2 form-control-custom" name="json[checklist_verv_afspraken][{{$template->id}}][description]" placeholder="Selecteer omschrijving">
              @foreach ($template->keywords as $keyword)
                @if ($keyword->type != 'text') @continue @endif
                <infor-select-option @if($keyword->keyword == ($data['description'] ?? '')) data-selected @endif data-value="{{$keyword->keyword}}" data-name="{{$keyword->name}}">{{$keyword->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
          @endforeach
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white" data-settings-sub-header>Prefillen</div>
        <div class="row">
            <div class="col-md-3 col-6 my-2">
                <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" @if(getSettingCheckbox("checklist_prefill_obv_klant_locatie")) checked @endif name="checkbox[checklist_prefill_obv_klant_locatie]" value="true">&nbsp;Vanuit eerdere checklist obv klant locatie</label>
            </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white" data-settings-sub-header>Custom project flow</div>
        <div class="my-2">
          <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" @if(getSettingCheckbox("checklist_custom_project_flow")) checked @endif name="checkbox[checklist_custom_project_flow]" value="true"> Inschakelen</label>
        </div>
        <div class="my-2">
          <label>Email</label>
          <input type="text" class="form-control-custom" name="settings[checklist_custom_project_flow_mail]" placeholder="<EMAIL>" value="{{getSettingValue('checklist_custom_project_flow_mail')}}">
        </div>
        <div class="checklist_custom_project_flow_stappen">
          @php
              $stappen = getSettingJson('checklist_custom_project_flow_stappen');
          @endphp
        </div>
        <a class="btn btn-inverse-primary col-12" onclick="addChecklistStap()">Stap @icon_plus</a>
      </div>
    </div>
  </div>
@endif
