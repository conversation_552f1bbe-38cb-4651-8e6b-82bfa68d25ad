<div class="settings-container my-2 col-lg-3 col-md-6 col-12">
  <div class="card cursor-pointer hover-shadow m-0" >
    <div class="text-center" >
      <span class="d-block my-3" ><i class="menu-icon mdi mdi-beach py-2" style="font-size: 35px"></i></span>
      <h4 data-settings-header >Verlof</h4>
    </div>
    <div class="row m-3 d-none settings-content" >
      <div class="col-12 my-1 bg-secondary rounded p-2 text-white" data-settings-sub-header >App instellingen</div>
      <div class="col-12 my-2">
        <div class="mb-2">
          <label class="flex-align cursor-pointer" >
            <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[verlof_saldo_weergeven_app]" @if(getSettingCheckbox('verlof_saldo_weergeven_app')) checked @endif >
            <span>Ver<PERSON>fsaldo weergeven bij aanvraag</span>
          </label>
          <label class="flex-align cursor-pointer" >
            <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[verlof_opmerking_verplicht]" @if(getSettingCheckbox('verlof_opmerking_verplicht')) checked @endif >
            <span>Verlof opmerking verplicht</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>
