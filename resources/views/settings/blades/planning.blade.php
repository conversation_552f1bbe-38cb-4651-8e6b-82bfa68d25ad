@if(Auth::user()->hasPermissionTo('Planning bewerken') && hasModule("Planning"))
  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="bi bi-calendar-date-fill" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Planning</h4>
      </div>
      <div class="row m-3 d-none settings-content" id="planningDiv">
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Planning instellingen</div>
        <div class="col-12">
          <div class="my-2">
            <label>Medewerkers groeperen op</label>
            <select name="settings[planning_group_users_by]" class="form-select" >
              <option value="niet" @if(getSettingValue("planning_group_users_by") == "niet") selected @endif>Niet</option>
              <option value="vestiging" @if(getSettingValue("planning_group_users_by") == "vestiging") selected @endif>Vestiging</option>
            </select>
          </div>
          <div class="my-2">
            <label>Google koppeling verbergen</label>
            <select name="settings[planning_google_hide]" class="form-select" >
              <option value="nee" @if(getSettingValue("planning_google_hide") == "nee") selected @endif>Nee</option>
              <option value="ja" @if(getSettingValue("planning_google_hide") == "ja") selected @endif>Ja</option>
            </select>
          </div>
          <div class="my-2">
            <label>Mail naar klant</label>
            <select name="settings[planning_klant_mail]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_klant_mail") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_klant_mail") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Werkuren planning</label>
            <select name="settings[planning_werkuren]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_werkuren") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_werkuren") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard personeelsplanning weergave</label>
            <select name="planningStandaardPeriod" class="form-select" >
              <option value="dag" @if(getSettingValue("planning_standaard_period") == "dag") selected @endif>Dag</option>
              <option value="week" @if(getSettingValue("planning_standaard_period") == "week") selected @endif>Week</option>
              <option value="maand" @if(getSettingValue("planning_standaard_period") == "maand") selected @endif>Maand</option>
              <option value="kwartaal" @if(getSettingValue("planning_standaard_period") == "kwartaal") selected @endif>Kwartaal</option>
              <option value="jaar" @if(getSettingValue("planning_standaard_period") == "jaar") selected @endif>Jaar</option>
              <option value="komende30" @if(getSettingValue("planning_standaard_period") == "komende30") selected @endif>Komende 30 dagen</option>
              <option value="60dagen" @if(getSettingValue("planning_standaard_period") == "60dagen") selected @endif>30 dagen terug en vooruit</option>
              <option value="periode" @if(getSettingValue("planning_standaard_period") == "periode") selected @endif>Periode (hieronder invullen)</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard periode personeelsplanning</label>
            <div class="row">
              <div class="col-6">
                <label>Vandaag - ... dagen</label>
                <input type="number" name="settings[planning_period_start]" class="form-control" value="{{getSettingValue("planning_period_start", '')}}" placeholder="0">
              </div>
              <div class="col-6">
                <label>Vandaag + ... dagen</label>
                <input type="number" name="settings[planning_period_end]" class="form-control" value="{{getSettingValue("planning_period_end", '')}}" placeholder="30">
              </div>
            </div>
          </div>
          <div class="my-2">
            <label>Standaard projectenplanning weergave</label>
            <select name="settings[planning_standaard_projecten_period]" class="form-select" >
              <option value="week" @if(getSettingValue("planning_standaard_projecten_period") == "week") selected @endif>Week</option>
              <option value="maand" @if(getSettingValue("planning_standaard_projecten_period") == "maand") selected @endif>Maand</option>
              <option value="kwartaal" @if(getSettingValue("planning_standaard_projecten_period") == "kwartaal") selected @endif>Kwartaal</option>
              <option value="jaar" @if(getSettingValue("planning_standaard_projecten_period") == "jaar") selected @endif>Jaar</option>
            </select>
          </div>
          <div class="my-2">
            <label>Projectenplanning layout</label>
            <select name="settings[projectplanning_layout]" class="form-select" >
              <option value="standaard" @if(getSettingValue("projectplanning_layout") == "standaard") selected @endif>Standaard</option>
              <option value="week" @if(getSettingValue("projectplanning_layout") == "week") selected @endif>Per Week</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard uitgebreide info weergeven</label>
            <select name="planningStandaardInfo" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_standaard_info") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_standaard_info") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard pushmelding</label>
            <select name="planningStandaardPushmelding" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_standaard_pushmelding") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_standaard_pushmelding") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard Google agenda</label>
            <select name="planningStandaardGoogleAgenda" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_standaard_google_agenda") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_standaard_google_agenda") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Alleen projecten in opdracht kunnen inplannen</label>
            <select name="settings[planning_projecten_alleen_in_opdracht]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_projecten_alleen_in_opdracht") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_projecten_alleen_in_opdracht") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Standaard Project</label>
            <select name="planningStandaardProject" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_standaard_project") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_standaard_project") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Adres</label>
            <select name="planningAdres" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_adres") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_adres") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Aantal medewerkers</label>
            <select name="settings[planning_aantal_medewerkers]" class="form-select" >
              <option value="uit" @if(getSettingValue("planning_aantal_medewerkers") == "uit") selected @endif>Uit</option>
              <option value="aan" @if(getSettingValue("planning_aantal_medewerkers") == "aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Planning prijs</label>
            <select name="planningPrijs" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_prijs") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_prijs") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Bijlagen</label>
            <select name="planningBijlagen" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_bijlagen") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_bijlagen") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Machines</label>
            <select name="settings[planning_machines]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_machines") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_machines") == "Aan") selected @endif>Aan</option>
              <option value="pp" @if(getSettingValue("planning_machines") == "pp") selected @endif>Per medewerker</option>
            </select>
          </div>
          <div class="my-2">
            <label>Werkbon</label>
            <select name="settings[planning_werkbonnen]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_werkbonnen") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_werkbonnen") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Afgeronden projecten tonen</label>
            <select name="settings[planning_afgeronden_projecten_tonen]" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_afgeronden_projecten_tonen") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_afgeronden_projecten_tonen") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Planning item kleuren</label>
            <select name="settings[planning_item_kleuren]" class="form-select" >
              <option value="Project" @if(getSettingValue("planning_item_kleuren") == "Project") selected @endif>Project</option>
              <option value="Taak" @if(getSettingValue("planning_item_kleuren") == "Taak") selected @endif>Taak</option>
            </select>
          </div>
          <div class="mb-2">
            <label>Standaard personeel selecteren</label>
            <select name="settings[standaard_personeel_selecteren]" class="form-select">
              <option value="-" selected>uit</option>
              @foreach($users as $user) <option value="{{ $user->id }}" @if(isset($user->id) && getSettingValue('standaard_personeel_selecteren') == $user->id) selected @endif>{{ $user->name }} {{$user->lastname}}</option>@endforeach
            </select>
          </div>
          <div class="mb-2">
            <label>Verlof blok weergeven</label>
            <select name="settings[planning_verlof_blok_weergeven]" class="form-select">
              <option value="uit" @if(getSettingValue("planning_verlof_blok_weergeven") == "uit") selected @endif>Uit</option>
              <option value="aan" @if(getSettingValue("planning_verlof_blok_weergeven") == "aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Automatisch afronden dag</label>
            <select name="settings[automatisch_afronden_dag]" class="form-select" >
              <option value="Uit" @if(getSettingValue("automatisch_afronden_dag") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("automatisch_afronden_dag") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="mt-2 w-100">
            <div class="col-md-12 col-12 mt-2 ml--2 px-2">
              <label>Automatisch afronden dag tijd</label>
              <input type="time" class="form-control" style="max-width: 99%" @if(getSettingValue('automatisch_afronden_dag_tijd')) data-test="" value="{{ Carbon()->parse(getSettingValue('automatisch_afronden_dag_tijd'))->format('H:i') }}" @endif name="settings[automatisch_afronden_dag_tijd]">
            </div>
          </div>
          <div class="d-flex my-2">
            <div class="col-6 my-2 px-2">
              <label>Prefill begintijd</label>
              <input type="time" class="form-control" @if(getSettingValue('planning_prefill_begin')) value="{{ Carbon()->parse(getSettingValue('planning_prefill_begin'))->format('H:i') }}" @endif name="settings[planning_prefill_begin]">
            </div>
            <div class="col-6 my-2">
              <label>Prefill eindtijd</label>
              <input type="time" class="form-control" @if(getSettingValue('planning_prefill_eind')) value="{{ Carbon()->parse(getSettingValue('planning_prefill_eind'))->format('H:i') }}" @endif name="settings[planning_prefill_eind]">
            </div>
          </div>

          <div class="row">
            <div class="mb-2 col-md-4 col-6 ">
              <label class="flex-align cursor-pointer" >
                <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_tonen_werkbon_opvolgen]" @if(getSettingCheckbox('planning_tonen_werkbon_opvolgen')) checked @endif >
                <span>Tonen werkbon opvolgen</span>
              </label>
            </div>
            <div class="mb-2 col-md-4 col-6">
              <label class="flex-align cursor-pointer" >
                <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_legenda_urenregistratie]" @if(getSettingCheckbox('planning_legenda_urenregistratie')) checked @endif >
                <span>Optie legenda planning opslaan in urenregistratie</span>
              </label>
            </div>
            <div class="mb-2 col-md-4 col-6">
              <label class="flex-align cursor-pointer" >
                <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_project_planning]" @if(getSettingCheckbox('planning_project_planning')) checked @endif >
                <span>Project planning</span>
              </label>
            </div>
          </div>

          <div class="d-flex my-2">
            <div class="col-6 my-2 px-2">
              <label>Resource begintijd</label>
              <input type="time" class="form-control" @if(getSettingValue('planning_resource_begin')) value="{{ Carbon()->parse(getSettingValue('planning_resource_begin'))->format('H:i') }}" @endif name="settings[planning_resource_begin]">
            </div>
            <div class="col-6 my-2">
              <label>Resource eindtijd</label>
              <input type="time" class="form-control" @if(getSettingValue('planning_resource_eind')) value="{{ Carbon()->parse(getSettingValue('planning_resource_eind'))->format('H:i') }}" @endif name="settings[planning_resoource_eind]">
            </div>
          </div>
          <div class="mb-2">
            <label class="flex-align cursor-pointer" >
              <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_tonen_werkbon_opvolgen]" @if(getSettingCheckbox('planning_tonen_werkbon_opvolgen')) checked @endif >
              <span>Tonen werkbon opvolgen</span>
            </label>
          </div>
          <div class="mb-2">
            <label class="flex-align cursor-pointer" >
              <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_legenda_urenregistratie]" @if(getSettingCheckbox('planning_legenda_urenregistratie')) checked @endif >
              <span>Optie legenda planning opslaan in urenregistratie</span>
            </label>
          </div>
          <div class="mb-2">
            <label class="flex-align cursor-pointer" >
              <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_in_verleden]" @if(getSettingCheckbox('planning_in_verleden')) checked @endif >
              <span>Planning in verleden inschieten</span>
            </label>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Planning custom kleuren</div>
        <div class="col-12 my-1">
          @php
            $data = json_decode((getSettingValue('projecten_planning_custom_kleuren') ?? '[]'));
          @endphp
          <div class="my-2" id="planningkleuren">
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[projecten_planning_custom_kleuren][{{$string}}][name]" placeholder="Naam" value="{{$row->name}}" >
                <input type="hidden" class='color-input' name="json[projecten_planning_custom_kleuren][{{$string}}][color]" value="{{$row->color}}" >
                <div class="p-3 color-sphere rounded-circle cursor-pointer mx-1 cursor-pointer hover-shadow" onclick="selectChecklistStatusColor('{{$string}}')" style="background-image: linear-gradient(to bottom right, {{$row->color}}80, {{$row->color}});"></div>
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach
            <div class="my-2 text-center" >
              <a class="btn btn-inverse-primary" onclick="addplanningCustomColor()" >@icon_plus</a>
            </div>
          </div>
        </div>

        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Projectenplanning opmerking custom kleur</div>
        <div class="col-12 my-1">
          @php
            $data = (getSettingValue('Project_opmerking_custom_kleur') ?? '#FFFFFF');
            $opmerkingString = randomString();
          @endphp

          <div class="project-opmerking-custom-rows" id="projectOpmerkingCustomColor">
            <div class="flex-between mx--1 my-2" id="{{$opmerkingString}}" >
              <input type="hidden" class='color-input' name="settings[Project_opmerking_custom_kleur]" value="{{$data}}" >
              <div class="p-3 color-sphere border rounded-circle cursor-pointer mx-1 cursor-pointer hover-shadow" onclick="selectCustomColor('{{$opmerkingString}}')" style="background-image: linear-gradient(to bottom right, {{$data}}80, {{$data}});"></div>
            </div>
          </div>
        </div>

        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Custom Planning velden</div>
        <div class="col-12 my-1">
          @php $data = json_decode((getSettingValue('planning_custom_rows') ?? '[]')) @endphp
          <div class="planning-custom-rows">
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[planning_custom_rows][{{$string}}][keyword]" placeholder="Keyword" value="{{$row->keyword}}" >
                <input class="form-control-custom mx-1 w-100" name="json[planning_custom_rows][{{$string}}][name]" placeholder="Naam" value="{{$row->name}}" >
                <select class="form-select mx-1 w-100" name="json[planning_custom_rows][{{$string}}][type]">
                  <option @if($row->type == 'text') selected @endif value="text">Tekst</option>
                  <option @if($row->type == 'number') selected @endif value="number">Getal</option>
                  <option @if($row->type == 'date') selected @endif value="date">Datum</option>
                  <option @if($row->type == 'time') selected @endif value="time">Tijd</option>
                  <option @if($row->type == 'select') selected @endif value="select">Select</option>
                  <option @if($row->type == 'tekstveld') selected @endif value="tekstveld">Groot Tekstveld</option>
                </select>
                <div class="m-1 w-100" >
                  <input data-sjv class="form-control-custom" name="json[planning_custom_rows][{{$string}}][data]" placeholder="Data" value="{{$row->data ?? ''}}" >
                </div>
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="my-2 text-center" >
            <a class="btn btn-inverse-primary" onclick="addPlanningCustomRow()" >@icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Resource planning</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue('planning_resource_datasets') ?? '[]', true)@endphp
          <label>Datasets koppelen</label>
          <div class="row">
            @foreach($datasets as $dataset)
              <div class="col-ld-3 col-md-4 col-6" >
                <div class="bg-inverse-secondary border rounded my-2 p-2 checkbox-container" >
                  <label class="cursor-pointer"><input @if(isset($data[$dataset->id])) checked @endif type="checkbox" name="json[planning_resource_datasets][{{$dataset->id}}][state]" class="form-check-custom state-checkbox">  {{$dataset->naam}}</label>
                </div>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Datasets</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue('planning_datasets') ?? '[]', true)@endphp
          <label>Datasets koppelen</label>
          <div class="row">
            @foreach($datasets as $dataset)
              <div class="col-ld-3 col-md-4 col-6" >
                <div class="bg-inverse-secondary border rounded my-2 p-2 checkbox-container" >
                  <label class="cursor-pointer"><input @if(isset($data[$dataset->id])) checked @endif type="checkbox" name="json[planning_datasets][{{$dataset->id}}][state]" class="form-check-custom state-checkbox">  {{$dataset->naam}}</label>
                  <select @if(!isset($data[$dataset->id])) disabled @endif name="json[planning_datasets][{{$dataset->id}}][key]" class="form-select">
                    <option value="">Dataset key</option>
                    @foreach(json_decode(($dataset->items[0]->value ?? '[]'), true) as $name => $value)
                      <option @if(($data[$dataset->id]['key'] ?? null) == $name) selected @endif value="{{$name}}" >{{$name}}</option>
                    @endforeach
                  </select>
                </div>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Project taken</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label>Project inplannen verbergen</label>
            <select name="settings[planning_project_taken_hide_project_btn]" class="form-select" >
              <option value="nee" @if(getSettingValue("planning_project_taken_hide_project_btn") == "nee") selected @endif>nee</option>
              <option value="ja" @if(getSettingValue("planning_project_taken_hide_project_btn") == "ja") selected @endif>Ja</option>
            </select>
          </div>
          @php
            $taken = json_decode(getSettingValue('projecten_taken_custom_rows') ?? '[]');
            $data = json_decode(getSettingValue('planning_taken_inplannen_prefill') ?? '[]');
          @endphp
          <div class="my-2">
            <label>Datum prefill obv custom veld</label>
            <select name="json[planning_taken_inplannen_prefill][date]" class="form-select">
              <option value="" >Selecteer custom veld</option>
              @foreach($taken as $taak)
                <option @if(($data->date ?? '') == ($taak->keyword ?? '')) selected @endif value="{{$taak->keyword ?? ''}}" >{{$taak->name ?? ''}}</option>
              @endforeach
            </select>
          </div>
          <div class="my-2">
            <label>Begintijd prefill obv custom veld</label>
            <select name="json[planning_taken_inplannen_prefill][start]" class="form-select">
              <option value="" >Selecteer custom veld</option>
              @foreach($taken as $taak)
                <option @if(($data->start ?? '') == ($taak->keyword ?? '')) selected @endif value="{{$taak->keyword ?? ''}}" >{{$taak->name ?? ''}}</option>
              @endforeach
            </select>
          </div>
          <div class="my-2">
            <label>Eindtijd prefill obv custom veld</label>
            <select name="json[planning_taken_inplannen_prefill][end]" class="form-select">
              <option value="" >Selecteer custom veld</option>
              @foreach($taken as $taak)
                <option @if(($data->end ?? '') == ($taak->keyword ?? '')) selected @endif value="{{$taak->keyword ?? ''}}" >{{$taak->name ?? ''}}</option>
              @endforeach
            </select>
          </div>
          <div class="my-2">
            <label>Opmerking prefill obv custom veld</label>
            <select name="json[planning_taken_inplannen_prefill][opmerking]" class="form-select">
              <option value="" >Selecteer custom veld</option>
              @foreach($taken as $taak)
                <option @if(($data->opmerking ?? '') == ($taak->keyword ?? '')) selected @endif value="{{$taak->keyword ?? ''}}" >{{$taak->name ?? ''}}</option>
              @endforeach
            </select>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Planning volgorde</div>
        <div class="col-12 my-2">
          <div class="row" >
            @php $data = json_decode((getSettingValue('planning_roles_order') ?? '[]'), true) @endphp
            @foreach(getRoles() as $role)
              <div class="col-lg-3 col-md-4 col-6 my-2">
                <label>{{$role->name}}</label>
                <input type="number" class="form-control-custom" name="json[planning_roles_order][{{$role->id}}]" value="{{$data[$role->id] ?? ''}}" placeholder="Order value">
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >App</div>
        <div class="col-6 my-2 flex-between">
          <label class="flex-align cursor-pointer" >
            <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_app_taken_another_uers]" @if(getSettingCheckbox('planning_app_taken_another_uers')) checked @endif >
            <span>Andere medewerkers weergeven obv taken.</span>
          </label>
          <label class="flex-align cursor-pointer" >
            <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_app_verbergen_weekoverzicht]" @if(getSettingCheckbox('planning_app_verbergen_weekoverzicht')) checked @endif >
            <span>Verbergen weekoverzicht</span>
          </label>
          <label class="flex-align cursor-pointer" >
            <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[planning_app_blok_uren_incompleet]" @if(getSettingCheckbox('planning_app_blok_uren_incompleet')) checked @endif >
            <span>Blokkeer planning bij niet ingevulde uren</span>
          </label>
        </div>
        <div class="col-12 my-2">
          <label>Werkbonnen uitzetten</label>
          <select name="settings[app_planning_disable_werkbonnen]" class="form-select">
            <option value="nee" @if(getSettingValue("app_planning_disable_werkbonnen") == "nee") selected @endif>Nee</option>
            <option value="ja" @if(getSettingValue("app_planning_disable_werkbonnen") == "ja") selected @endif>Ja</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>dagplanning export periode aantal dagen</label>
          <input type="number" class="form-control" @if(getSettingValue('planning_dagplanning_periode_dagen')) value="{{ getSettingValue('planning_dagplanning_periode_dagen') }}" @endif name="settings[planning_dagplanning_periode_dagen]">
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Mail instellingen</div>
        <div class="col-12">
          <div class="my-2">
            <label>Standaard klant mailen</label>
            <select name="planningStandaardKlantMail" class="form-select" >
              <option value="Uit" @if(getSettingValue("planning_standaard_klant_mail") == "Uit") selected @endif>Uit</option>
              <option value="Aan" @if(getSettingValue("planning_standaard_klant_mail") == "Aan") selected @endif>Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Afzender</label>
            <input class="form-control" name="planningMailAfzender" value="{{getSettingValue("planning_mail_afzender")}}" placeholder="Afzender">
          </div>
          <div class="my-2">
            <label>Onderwerp</label>
            <input class="form-control" name="planningMailOnderwerp" value="{{getSettingValue("planning_mail_onderwerp")}}" placeholder="Onderwerp">
          </div>
          <div class="my-2">
            <label>Planning mail</label>
            <textarea id="planningMail" class="form-control" name="planningMail" >{!! getSettingValue("planning_mail") !!}</textarea>
          </div>
          <div class="my-2">
            <label>Update onderwerp</label>
            <input class="form-control" name="planningMailUpdateOnderwerp" value="{{getSettingValue("planning_mail_update_onderwerp")}}" placeholder="Onderwerp">
          </div>
          <div class="my-2">
            <label>Planning update</label>
            <textarea id="planningMailUpdate" class="form-control" name="planningMailUpdate" >{!! getSettingValue("planning_mail_update") !!}</textarea>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Reserveer Plein koppeling</div>
        <div class="col-12 my-2">
          <label>Reserveer Plein ID</label>
          <input type="number" name="reserveerpleinId" class="form-control" value="{{getSettingValue("reserveerpleinId")}}" placeholder="1" >
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Planning obv locatie</div>
        <div class="col-12 my-2">
          <label>Standaard locatie <small>Wordt gebruikt wanneer de locatie van de medewerker niet beschikbaar is.</small></label>
          <input class="form-control" type="text" value="{{getSettingValue("standaard_planning_adres")}}" placeholder="Engelmanstraat 13 6086 BA Neer" name="standaardPlanningLocatie">
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Elementen inplannen</div>
        <div class="col-12 my-2">
          <label>Planning elementen</label>
          <div class="row mx-3">
            @if(hasModule("Offertes"))
              <div class="col-md-3 col-6 my-2"><label class="cursor-pointer"><input type="checkbox" class="form-check-custom" name="planningElementen[offerteActiviteiten]" value="true" @if(isset(json_decode(getSettingValue('planning_elementen'), true)['offerteActiviteiten'])) checked @endif> Offerte activiteiten</label></div>
            @endif @if(hasModule("Aanvragen"))
              <div class="col-md-3 col-6 my-2"><label class="cursor-pointer"><input type="checkbox" class="form-check-custom" name="planningElementen[aanvragen]" value="true" @if(isset(json_decode(getSettingValue('planning_elementen'), true)['aanvragen'])) checked @endif> Aanvragen</label></div>
            @endif @if(hasModule("Projecten"))
              <div class="col-md-3 col-6 my-2"><label class="cursor-pointer"><input type="checkbox" class="form-check-custom" name="planningElementen[projecten]" value="true" @if(isset(json_decode(getSettingValue('planning_elementen'), true)['projecten'])) checked @endif> Projecten</label></div>
              <div class="col-md-3 col-6 my-2"><label class="cursor-pointer"><input type="checkbox" class="form-check-custom" name="planningElementen[projecttaken]" value="true" @if(isset(json_decode(getSettingValue('planning_elementen'), true)['projecttaken'])) checked @endif> Project taken</label></div>
            @endif
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Planning info</div>
        <div class="col-12">
          <div class="my-2">
            <label>Dagoverzicht, active uren</label>
            <div class="row my-2">
              @for($i = 0; $i <= 23; $i++)
                <div class="col-lg-2 col-md-3 col-4">
                  <label class="cursor-pointer">
                    <input class="form-check-custom" @if(isset(json_decode(getSettingValue('dagoverzicht_active_uren'), true)[$i])) checked @endif type="checkbox" name="activeUren[{{$i}}]" value="{{$i}}">
                    {{str_pad($i, 2,"0", STR_PAD_LEFT)}}:00
                  </label>
                </div>
              @endfor
            </div>
          </div>
          <div class="my-2" >
            <label>Aantal dagen vooruit kijken</label>
            <select name="settings[vooruit_planning]" class="form-select">
              <option>Selecteer optie</option>
              <option @if(($settings['vooruit_planning']->value ?? null) == 'released') selected @endif value="released">Vrijgegeven planning</option>
              <option @if(($settings['vooruit_planning']->value ?? null) == 'current') selected @endif value="current">Alleen eerst volgende planning</option>
              @for($i = 1; $i <= 30; $i++)
                <option @if(($settings['vooruit_planning']->value ?? null) == $i) selected @endif value="{{$i}}">{{$i}} dagen</option>
              @endfor
            </select>
          </div>
        </div>
        @if(hasModule("Offertes"))
          <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Offertes info</div>
          <div class="col-12 my-2">
            @foreach($keywords as $templateId => $keyword)
              <div class="my-2" >{{$templates[$templateId]->naam}}</div>
              <div class="my-2 row mx-3 border-right border-bottom">
                @foreach($keyword as $row)
                  <div class="col-xl-3 col-md-4 col-6 my-1">
                    <label class="cursor-pointer" ><input class="form-check-custom" @if(isset(json_decode(getSettingValue('planning_offerte_info'), true)[$templateId][$row["keyword"]])) checked @endif type="checkbox" value="{{$row["naam"]}}" name="planningOfferte[{{$templateId}}][{{$row['keyword']}}]">&nbsp;{{$row["naam"]}}</label>
                  </div>
                @endforeach
              </div>
            @endforeach
          </div>
        @endif
        @if(hasModule("Werkbonnen"))
          <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Werkbonnen</div>
          <div class="col-12 my-2">
            <label>Werkbonnen prefill</label>
            @php $data = json_decode((getSettingValue('planning_werkbon_keywords_prefill') ?? '[]'), true) @endphp
            @foreach($werkbonKeywords as $id => $group)
              <div class="my-2" >{{$group[0]->template->naam ?? ''}}</div>
              <div class="my-2 row mx-3 border-right border-bottom">
                @foreach($group as $row)
                  <div class="col-xl-3 col-md-4 col-6 my-1">
                    <label class="cursor-pointer" ><input class="form-check-custom" @if(isset($data[$id][$row->keyword])) checked @endif type="checkbox" value="{{$row->naam}}" name="json[planning_werkbon_keywords_prefill][{{$id}}][{{$row->keyword}}]">&nbsp;{{$row->naam}}</label>
                  </div>
                @endforeach
                <div class="col-xl-3 col-md-4 col-6 my-1 hide">
                  <label class="cursor-pointer" ><input class="form-check-custom" checked type="checkbox" value="default" name="json[planning_werkbon_keywords_prefill][{{$id}}][default]">&nbsp;default</label>
                </div>
              </div>
            @endforeach
          </div>
        @endif
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Acties (App)</div>
        <div class="col-12">
          <div class="my-2">
            <label>Google maps</label>
            <select name="planningActiesMaps" class="form-select">
              <option @if(getSettingValue('planning_acties_maps') == 'uit') selected @endif value="Uit">Uit</option>
              <option @if(getSettingValue('planning_acties_maps') == 'Aan') selected @endif value="Aan">Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>SMS</label>
            <select name="planningActiesSms" class="form-select">
              <option @if(getSettingValue('planning_acties_sms') == 'Uit') selected @endif value="Uit">Uit</option>
              <option @if(getSettingValue('planning_acties_sms') == 'Aan') selected @endif value="Aan">Aan</option>
            </select>
          </div>
          <div class="my-2">
            <label>Email</label>
            <select name="planningActiesEmail" class="form-select">
              <option @if(getSettingValue('planning_acties_email') == 'Uit') selected @endif value="Uit">Uit</option>
              <option @if(getSettingValue('planning_acties_email') == 'Aan') selected @endif value="Aan">Aan</option>
            </select>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >App planning weergave</div>
        <div class="col-12 my-2">
          <label>Standaard open tabbladen</label>
          <div class="my-2 row mx-3">
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Acties</label>
              <select  class="form-select" name="settings[planning_view_buttons]">
                <option @if(getSettingValue('planning_view_buttons') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_buttons') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Activiteit</label>
              <select  class="form-select" name="settings[planning_view_activiteit]">
                <option @if(getSettingValue('planning_view_activiteit') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_activiteit') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Activiteit</label>
              <select  class="form-select" name="settings[planning_view_aantal_personen]">
                <option @if(getSettingValue('planning_view_aantal_personen') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_aantal_personen') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Klant</label>
              <select  class="form-select" name="settings[planning_view_klant]">
                <option @if(getSettingValue('planning_view_klant') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_klant') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Project</label>
              <select  class="form-select" name="settings[planning_view_project]">
                <option @if(getSettingValue('planning_view_project') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_project') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Aanvraag</label>
              <select  class="form-select" name="settings[planning_view_aanvraag]">
                <option @if(getSettingValue('planning_view_aanvraag') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_aanvraag') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Offerte</label>
              <select  class="form-select" name="settings[planning_view_offerte]">
                <option @if(getSettingValue('planning_view_offerte') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_offerte') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Opmerking</label>
              <select  class="form-select" name="settings[planning_view_opmerking]">
                <option @if(getSettingValue('planning_view_opmerking') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_opmerking') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
            <div class="col-xl-3 col-md-4 col-6 my-1">
              <label class="cursor-pointer" >Bestanden</label>
              <select  class="form-select" name="settings[planning_view_files]">
                <option @if(getSettingValue('planning_view_files') == "nee") selected @endif value="nee">Nee</option>
                <option @if(getSettingValue('planning_view_files') == "ja") selected @endif value="ja">Ja</option>
              </select>
            </div>
          </div>
        </div>
        <div class="col-12 my-2 row">
          <label class="col-12">Project info verbergen</label>
          @php
            $data = getSettingJson('planning_project_info_hide');
          @endphp
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['nummer'])) checked @endif type="checkbox" name="json[planning_project_info_hide][nummer]">&nbsp;Projectnummer</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['naam'])) checked @endif type="checkbox" name="json[planning_project_info_hide][naam]">&nbsp;Projectnaam</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['opdrachtnummer'])) checked @endif type="checkbox" name="json[planning_project_info_hide][opdrachtnummer]">&nbsp;Opdrachtnummer</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['taaknummer'])) checked @endif type="checkbox" name="json[planning_project_info_hide][taaknummer]">&nbsp;Taaknummer</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['opdrachtgever'])) checked @endif type="checkbox" name="json[planning_project_info_hide][opdrachtgever]">&nbsp;Opdrachtgever</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['code'])) checked @endif type="checkbox" name="json[planning_project_info_hide][code]">&nbsp;Code</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['contactpersoon'])) checked @endif type="checkbox" name="json[planning_project_info_hide][contactpersoon]">&nbsp;Contactpersoon</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['adres'])) checked @endif type="checkbox" name="json[planning_project_info_hide][adres]">&nbsp;Adres</label>
          </div>
          <div class="col-xl-3 col-md-4 col-6 my-1">
            <label class="cursor-pointer" ><input class="form-switch-custom" @if(isset($data['omschrijving'])) checked @endif type="checkbox" name="json[planning_project_info_hide][omschrijving]">&nbsp;Omschrijving</label>
          </div>
        </div>
      </div>
    </div>
  </div>
@endif
