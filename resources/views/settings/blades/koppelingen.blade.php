  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="bi bi-hdd-network" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Koppelingen</h4>
      </div>
      <div class="row m-3 d-none settings-content" id="koppelingenDiv" >
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Business Central</div>
        @php $data = json_decode((getSettingValue('business_central_credentials') ?? '{}')) @endphp
        <div class="col-12 my-2" >
          <div class="text-center my-2">
            <a class="btn {{businessCentralLocal()->connected ? 'btn-success' : 'btn-primary'}} text-white" {{businessCentralLocal()->connected ? "data-business-central-disconnect" : 'data-business-central-connect'}} >Inloggen </a>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Exact</div>
        <div class="col-12 my-2">
          <label>Exact XML export</label>
          <select name="settings[exact_xml_export]" class="form-select">
            <option @if(getSettingValue('exact_xml_export') == 'Uit') selected @endif value="Uit">Uit</option>
            <option @if(getSettingValue('exact_xml_export') == 'Aan') selected @endif value="Aan">Aan</option>
          </select>
        </div>
        @if(hasModule('Exact Globe'))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Exact Globe</div>
          @php $data = json_decode((getSettingValue('exact-globe-api-credentials') ?? '{}')) @endphp
          <div class="col-12 my-2" >
            <div class="text-center my-2">
              <a class="btn {{exactGlobe()->connected ? 'btn-success' : 'btn-primary'}} text-white" {{exactGlobe()->connected ? "data-exact-globe-disconnect" : 'data-exact-globe-connect'}} data-exact-globe-btn >Inloggen </a>
            </div>
            <div class="my-2">
              <label>Automatische synchronisatie</label>
              <select name="settings[exact_globe_auto_sync]" class="form-select" >
                <option value="nee" @if(getSettingValue('exact_globe_auto_sync') == 'nee') selected @endif >Nee</option>
                <option value="ja" @if(getSettingValue('exact_globe_auto_sync') == 'ja') selected @endif >Ja</option>
              </select>
            </div>
            <div class="my-2">
              <label>Factuurnummers langer dan 8 karakters afkorten</label>
              <select name="settings[exact_globe_auto_sync]" class="form-select" >
                <option value="nee" @if(getSettingValue('exact_globe_shorten_invoice_numbers') == 'nee') selected @endif >Nee</option>
                <option value="ja" @if(getSettingValue('exact_globe_shorten_invoice_numbers') == 'ja') selected @endif >Ja</option>
              </select>
            </div>
          </div>
        @endif
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Exact Online</div>
        <div class="col-12 my-2">
          <div class="text-center">
            <a class="btn {{exactOnlineLocal()->connected ? 'btn-success' : 'btn-primary'}} text-white" {{exactOnlineLocal()->connected ? "data-exact-online-disconnect" : 'data-exact-online-connect'}} >Inloggen </a>
          </div>
        </div>

        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Snelstart</div>
        @foreach(getBvs() as $bv)
          @php $snelstart_connected = getSettingValue("snelstart_subscription_key_{$bv->id}") !== null @endphp

          <div class="col-12 my-2">
            <div class="rounded-7 p-2 shadow-md flex-between"  >

              <div class="flex-align " >

                @if($snelstart_connected)
                  <div class="dot-glow dot-glow-success mx-2" ></div>
                @else
                  <div class="dot-glow dot-glow-danger mx-2" ></div>
                @endif

                <div class="line-h-12" >
                  <div class="font-size-125" >{{$bv->name}}</div>
                  <div class="font-size-075 text-secondary" >KvK {{$bv->kvk}}</div>
                </div>

              </div>

              <div class="flex-align" >

                @if(!$snelstart_connected)
                  <a class="btn btn-success text-white btn-sm mx-1 rounded-5 " href="https://web.snelstart.nl/couplings/activate/tessaProductie?referenceKey={{getSubdomein()}}_{{$bv->id}}&successUrl={{urlencode(url('home'))}}" >Inloggen</a>
                @else
                  <a class="btn btn-danger text-white btn-sm mx-1 rounded-5 " onclick="disconnectSnelstart({{$bv->id}})" >Uitloggen</a>
                @endif

                <a class="btn btn-inverse-primary btn-sm mx-1 rounded-5 @if(!$snelstart_connected) opacity-25 pointer-event-none @endif" href="{{url("/snelstart/show/klanten/{$bv->id}")}}" target="_blank" >Klanten vergelijken</a>

              </div>

              <div class="d-none" >
                <textarea name="settings[snelstart_subscription_key_{{$bv->id}}]" rows="5">{{getSettingValue("snelstart_subscription_key_{$bv->id}")}}</textarea>
              </div>

            </div>
          </div>

        @endforeach


        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >E-Boekhouden</div>
        <div class="col-12 my-2">
          @foreach (getBvs() as $bv)
            @php $data = json_decode(getSettingValue("eboekhouden_api_credentials") ?? '[]', true)[$bv->id] ?? null; @endphp
            <div class="checkbox-container p-2 border bg-inverse-secondary my-2" >
              <label class="cursor-pointer"><input @if(isset($data)) checked @endif type="checkbox" class="form-check-custom state-checkbox" name="json[eboekhouden_api_credentials][{{$bv->id}}][state]" > {{$bv->name}}</label>
              <div class="row">
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Gebruikersnaam</label>
                  <input class="form-control" name="json[eboekhouden_api_credentials][{{$bv->id}}][username]" value="{{$data['username'] ?? ''}}" placeholder="Gebruikersnaam">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Beveiligingscode 1</label>
                  <input type="text" class="form-control" name="json[eboekhouden_api_credentials][{{$bv->id}}][securitycode1]" value="{{$data['securitycode1'] ?? ''}}" placeholder="Beveiligingscode 1">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Beveiligingscode 2</label>
                  <input type="text" class="form-control" name="json[eboekhouden_api_credentials][{{$bv->id}}][securitycode2]" value="{{$data['securitycode2'] ?? ''}}" placeholder="Beveiligingscode 2">
                </div>
              </div>
            </div>
          @endforeach
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >King (Bjorn Lunden)</div>
        <div class="col-12 my-2">
          @foreach (getBvs() as $bv)
            @php $data = json_decode(getSettingValue("king_api_credentials") ?? '[]', true)[$bv->id] ?? null; @endphp
            <div class="checkbox-container p-2 border bg-inverse-secondary my-2" >
              <label class="cursor-pointer"><input @if(isset($data)) checked @endif type="checkbox" class="form-check-custom state-checkbox" name="json[king_api_credentials][{{$bv->id}}][state]" > {{$bv->name}}</label>
              <div class="row">
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Administratie</label>
                  <input class="form-control" name="json[king_api_credentials][{{$bv->id}}][administratie]" value="{{$data['administratie'] ?? ''}}" placeholder="Administratie">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Application key</label>
                  <input type="text" class="form-control" name="json[king_api_credentials][{{$bv->id}}][appkey]" value="{{$data['appkey'] ?? ''}}" placeholder="Application key">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Bearer token</label>
                  <input type="text" class="form-control" name="json[king_api_credentials][{{$bv->id}}][bearer]" value="{{$data['bearer'] ?? ''}}" placeholder="Bearer token">
                </div>
              </div>
            </div>
          @endforeach
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >WordPress WooCommerce</div>
        <div class="col-12 my-2">
          <label>Secret key</label>
          <div class="flex-between">
            <input readonly type="text" class="form-control-custom" name="settings[woocommerce_secret]" placeholder="WooCommerce Secret" value="{{getSettingValue('woocommerce_secret')}}">
            <a class="btn btn-inverse-primary mx-1" onclick="wooCommerceKey()">@icon_shuffle</a>
            <a class="btn btn-inverse-success" onclick="copyText('{{getSettingValue('woocommerce_secret')}}')">@icon_copy</a>
          </div>
          @php $data = json_decode(getSettingValue('woocommerce_metadata') ?? '[]'); @endphp
          <div class="woocommerce-metadata my-2">
            @foreach($data ?? [] as $row)
              @php $string = randomStringLetters(); @endphp
              <div class="flex-between my-2 {{$string}}">
                <input type="text" class="form-control-custom" name="json[woocommerce_metadata][]" placeholder="Meta data" value="{{$row}}">
                <a class="btn btn-inverse-danger ml-1" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="text-center">
            <a class="btn btn-inverse-primary" onclick="addWoocommerceMetadata()" >Meta data toevoegen @icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >2ba</div>
        @php $data = json_decode((getSettingValue('2ba_credentials') ?? '{}')) @endphp
        <div class="col-12 my-2" >
          <div class="text-center my-2">
            <a class="btn {{tweebaLocal()->connected ? 'btn-success' : 'btn-primary'}} text-white" {{tweebaLocal()->connected ? "data-2ba-disconnect" : 'data-2ba-connect'}} >Inloggen </a>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Zenvoices</div>
        <div class="col-12 my-2" >
        @foreach(getBvs() as $bv)
          @php $data = json_decode(getSettingValue('zenvoices_credentials') ?? '[]', true)[$bv->id] ?? null @endphp
            <div class="checkbox-container p-2 border bg-inverse-secondary my-2" >
              <label class="cursor-pointer"><input @if(isset($data)) checked @endif type="checkbox" class="form-check-custom state-checkbox" name="json[zenvoices_credentials][{{$bv->id}}][state]" > {{$bv->name}}</label>
              <div class="row">
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Omgevingsnaam</label>
                  <input class="form-control" name="json[zenvoices_credentials][{{$bv->id}}][tenancyname]" value="{{$data['tenancyname'] ?? ''}}" placeholder="Omgevingsnaam">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Gebruikersnaam</label>
                  <input type="text" class="form-control" name="json[zenvoices_credentials][{{$bv->id}}][username]" value="{{$data['username'] ?? ''}}" placeholder="Gebruikersnaam">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Wachtwoord</label>
                  <input type="text" class="form-control" name="json[zenvoices_credentials][{{$bv->id}}][password]" value="{{$data['password'] ?? ''}}" placeholder="Wachtwoord">
                </div>
                <div class="col-md-4 col-12 my-2">
                  <label class="cursor-pointer m-0">Administratie</label>
                  <input type="text" class="form-control" name="json[zenvoices_credentials][{{$bv->id}}][administration]" value="{{$data['administration'] ?? ''}}" placeholder="Administratie">
                </div>
              </div>
            </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>

  <script>
    $('[data-exact-globe-connect]').click(() => {
      confirmModal({
        text:`<form data-exact-globe-form >
                <div class="my-2">
                    <label>URL</label>
                    <input class="form-control-custom" placeholder="URL" name="eg_url" >
                </div>
                <div class="my-2">
                    <label>Username</label>
                    <input class="form-control-custom" placeholder="Username" name="eg_username" >
                </div>
                <div class="my-2">
                    <label>Password</label>
                    <input type="password" class="form-control-custom" placeholder="Password" name="eg_password" >
                </div>
                <div class="my-2">
                    <label>Server</label>
                    <input class="form-control-custom" placeholder="Server" name="eg_server" >
                </div>
                <div class="my-2">
                    <label>Database</label>
                    <input class="form-control-custom" placeholder="Database" name="eg_database" >
                </div>
            </form>`,
        btnText: 'Verbinden',
      })
        .then(response => {
          if(!response.status){ return; }

          loader()
          const data = $('[data-exact-globe-form]').serializeArray();
          ajax('api/exact/globe/connect', data)
            .then(() => {
              successLoader();
              location.reload();
            })
            .catch(err => {
              if(err.status == 501){
                clearLoader();
                notification('Ongeldige Exact Globe Credentials!');
                return;
              }

              errorLoader();
            });


        })
    });
    $('[data-exact-globe-disconnect]').click(() => {
      confirmModal({
        text: 'Weet je zeker dat je de koppeling met Exact Globe ongedaan wilt maken?'
      })
        .then(response => {
          if(!response.status){ return; }
          loader();
          ajax('api/settings/store', {
            name: 'exact-globe-api-credentials',
            value: null,
          })
            .then(() => {
              location.reload();
            })
            .catch(err => errorLoader());
        })
    })

    $('[data-exact-online-connect]').click(() => {
      loader();
      window.location.href = `${url}/exact/online/connect`;

    });
    $('[data-exact-online-disconnect]').click(() => {
      confirmModal({
        text: 'Weet je zeker dat je de koppeling met Exact Online ongedaan wilt maken?'
      })
        .then(response => {
          if(!response.status){ return; }
          loader();
          ajax('api/settings/store', {
            name: 'exact_online_refresh_token',
            value: null,
          })
            .then(() => {
              location.reload();
            })
            .catch(err => errorLoader());
        })
    })

    $('[data-business-central-connect]').click(() => {
      confirmModal({
        text:`<form data-business-central-form >
                <div class="my-2">
                    <label>Tenant</label>
                    <input class="form-control-custom" placeholder="Tenant" name="tenant" >
                </div>
                <div class="my-2">
                    <label>Client ID</label>
                    <input type="password" class="form-control-custom" placeholder="Client ID" name="client_id">
                </div>
                <div class="my-2">
                    <label>Client secret</label>
                    <input class="form-control-custom" placeholder="Client secret" name="client_secret" >
                </div>
            </form>`,
        btnText: 'Verbinden',
      })
        .then(response => {
          if(!response.status){ return; }

          loader()
          const data = $('[data-business-central-form]').serializeArray();
          ajax('api/business-central/connect', data)
            .then(() => {
              location.reload();
            })
            .catch(handleCatchError);


        })
    });
    $('[data-business-central-disconnect]').click (() => {
      confirmModal({
        text: 'Weet je zeker dat je de koppeling met Business Central ongedaan wilt maken?'
      })
        .then(response => {
          if(!response.status){ return; }
          loader();
          ajax('api/settings/store', {
            name: 'business_central_credentials',
            value: null,
          })
            .then(() => {
              location.reload();
            })
            .catch(handleCatchError);
        })
    });

    $('[data-2ba-connect]').click(async () => {
      try{
        const confirm = await confirmModal({
          text:`<form data-2ba-form >
                <div class="my-2">
                    <label>Gebruikersnaam</label>
                    <input class="form-control-custom" placeholder="Gebruikersnaam" name="username">
                </div>
                <div class="my-2">
                    <label>wachtwoord</label>
                    <input class="form-control-custom" placeholder="wachtwoord" name="password" >
                </div>
                <div class="my-2">
                    <label>Client ID</label>
                    <input class="form-control-custom" placeholder="Client ID" name="client_id">
                </div>
                <div class="my-2">
                    <label>Client wachtwoord</label>
                    <input class="form-control-custom" placeholder="Client wachtwoord" name="client_password" >
                </div>
            </form>`,
          btnText: 'Verbinden',
        });
        if(!confirm.status){ return; }


        loader()
        const data = $('[data-2ba-form]').serializeArray();
        const response = await ajax('api/tweeba/connect', data);
        location.reload();
      }
      catch(err){ handleCatchError(err); }

    });
    $('[data-2ba-disconnect]').click (async () => {
      try{
        const confirm = await confirmModal({
          text: 'Weet je zeker dat je de koppeling met 2ba ongedaan wilt maken?'
        })
        if(!confirm.status){ return; }

        loader();
        const response = await ajax('api/settings/store', {
          name: '2ba_credentials',
          value: null,
        })
        location.reload();
      }
      catch(err){ handleCatchError(err); }
    });
  </script>
