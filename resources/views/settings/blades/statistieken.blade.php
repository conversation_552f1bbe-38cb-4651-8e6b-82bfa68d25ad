@if(Auth::user()->hasPermissionTo('Statistieken bekijken') && hasModule("Statistieken"))
  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="fa fa-chart-pie py-2" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Statistieken</h4>
      </div>
      <div class="row m-3 d-none settings-content">
        @if(hasModule("Urenregistratie"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Urenregistratie statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_per_medewerker]" @if(isset($data['urenregistratie']['uren_per_medewerker'])) checked @endif > Uren per medewerker</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][verlof_per_medewerker]" @if(isset($data['urenregistratie']['verlof_per_medewerker'])) checked @endif > Verlof per medewerker</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_verhouding_uren]" @if(isset($data['urenregistratie']['uren_verhouding_uren'])) checked @endif > Verhouding uren</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_gemiddeld_aantal_uren_per_week]" @if(isset($data['urenregistratie']['uren_gemiddeld_aantal_uren_per_week'])) checked @endif > Gemiddeld aantal uren per week</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_top_projecten]" @if(isset($data['urenregistratie']['uren_top_projecten'])) checked @endif > Top 5 projecten</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_gemiddeld_aantal_uren_per_project]" @if(isset($data['urenregistratie']['uren_gemiddeld_aantal_uren_per_project'])) checked @endif > Gemiddeld aantal uren per project</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_op_andere_bv]" @if(isset($data['urenregistratie']['uren_op_andere_bv'])) checked @endif > Uren op andere bv</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][uren_zonder_project]" @if(isset($data['urenregistratie']['uren_zonder_project'])) checked @endif > Uren zonder project</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][urenregistratie][meeste_verzuim]" @if(isset($data['urenregistratie']['meeste_verzuim'])) checked @endif > Meeste verzuim</label>
              </div>
              <input class="d-none" type="checkbox" name="json[statistieken_show][urenregistratie][placeholder]" checked>
            </div>
          </div>
        @endif
        @if(hasModule("Offertes"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Offerte statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][total_revenue]" @if(isset($data['offertes']['total_revenue'])) checked @endif > Totale omzet</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][offertes_status]" @if(isset($data['offertes']['offertes_status'])) checked @endif > Afgeronde offertes</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][offertes_value]" @if(isset($data['offertes']['offertes_value'])) checked @endif > Verhouding offertewaarde</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][expired_offertes]" @if(isset($data['offertes']['expired_offertes'])) checked @endif > Vervallen offertes</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][average_decision]" @if(isset($data['offertes']['average_decision'])) checked @endif > Gemiddelde beslissingstermijn</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][sent_offertes]" @if(isset($data['offertes']['sent_offertes'])) checked @endif > Verzendstatus</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][opened_offertes]" @if(isset($data['offertes']['opened_offertes'])) checked @endif > Geopende offertes</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][average_opening]" @if(isset($data['offertes']['average_opening'])) checked @endif > Gemiddelde duur tot inzage</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][offertes][btw]" @if(isset($data['offertes']['btw'])) checked @endif > Btw</label>
              </div>
              <input class="d-none" type="checkbox" name="json[statistieken_show][offertes][placeholder]" checked>
            </div>
          </div>
        @endif
        @if(hasModule("Facturatie"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Facturatie statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][total_revenue]" @if(isset($data['facturatie']['total_revenue'])) checked @endif > Totale omzet</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][paid_invoices]" @if(isset($data['facturatie']['paid_invoices'])) checked @endif > Verhouding betaalde facturen</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][average_payment]" @if(isset($data['facturatie']['average_payment'])) checked @endif > Gemiddelde betalingstermijn</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][revenue_per_branch]" @if(isset($data['facturatie']['revenue_per_branch'])) checked @endif > Omzet per vestiging</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][paid_ratio]" @if(isset($data['facturatie']['paid_ratio'])) checked @endif > Verhouding betaalstatus totale factuurwaarde</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][sold_items]" @if(isset($data['facturatie']['sold_items'])) checked @endif > Aantal verkochte producten / diensten</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][sold_items_revenue]" @if(isset($data['facturatie']['sold_items_revenue'])) checked @endif > Totale omzet per producten / diensten</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][sold_items_group_revenue]" @if(isset($data['facturatie']['sold_items_group_revenue'])) checked @endif > Totale omzet per groep</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][facturatie][btw]" @if(isset($data['facturatie']['btw'])) checked @endif > Btw</label>
              </div>
              <input class="d-none" type="checkbox" name="json[statistieken_show][facturatie][placeholder]" checked>
            </div>
          </div>
          @if (someoneHasPermission('Proforma facturen beheren'))
            <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Pro forma statistieken tonen</div>
            <div class="col-12 my-2">
              @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
              <div class="row">
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][total_revenue]" @if(isset($data['proformas']['total_revenue'])) checked @endif > Totale Pro Forma omzet</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][approved_proformas]" @if(isset($data['proformas']['approved_proformas'])) checked @endif > Verhouding geaccordeerde pro forma's</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][average_approval]" @if(isset($data['proformas']['average_approval'])) checked @endif > Gemiddelde beslissingstermijn</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][revenue_per_branch]" @if(isset($data['proformas']['revenue_per_branch'])) checked @endif > Pro Forma omzet per vestiging</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][sold_items]" @if(isset($data['proformas']['sold_items'])) checked @endif > Top 10 verkochte producten / diensten</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][sold_items_revenue]" @if(isset($data['proformas']['sold_items_revenue'])) checked @endif > Top 10 omzet per product / dienst</label>
                </div>
                <div class="col-lg-4 col-md-6 col-12 my-2">
                  <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][proformas][btw]" @if(isset($data['proformas']['btw'])) checked @endif > Btw</label>
                </div>
                <input class="d-none" type="checkbox" name="json[statistieken_show][proformas][placeholder]" checked>
              </div>
            </div>
          @endif
        @endif
        @if(hasModule("Inkoopbonnen"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Inkoopbonnen statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][total_revenue]" @if(isset($data['inkoopbonnen']['total_revenue'])) checked @endif > Totale omzet</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][completed_receipts]" @if(isset($data['inkoopbonnen']['completed_receipts'])) checked @endif > Verhouding afgeronde inkoopbonnen</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][btw]" @if(isset($data['inkoopbonnen']['btw'])) checked @endif > Btw</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][zenvoice_kostendrager]" @if(isset($data['inkoopbonnen']['zenvoice_kostendrager'])) checked @endif > Zenvoice kostendrager</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][grootste_inkoopbonnen]" @if(isset($data['inkoopbonnen']['grootste_inkoopbonnen'])) checked @endif > Grootste inkoopbon</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][top_leveranciers_inkoopbonnen]" @if(isset($data['inkoopbonnen']['top_leveranciers_inkoopbonnen'])) checked @endif > Top leveranciers</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopbonnen][top_medewerkers_inkoopbonnen]" @if(isset($data['inkoopbonnen']['top_medewerkers_inkoopbonnen'])) checked @endif > Top medewerkers</label>
              </div>
              <input class="d-none" type="checkbox" name="json[statistieken_show][inkoopfacturen][placeholder]" checked>
            </div>
          </div>
        @endif
        @if(hasModule("Inkoopfacturen"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Inkoopfactuur statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][total_revenue]" @if(isset($data['inkoopfacturen']['total_revenue'])) checked @endif > Totale inkoopfactuur waarde</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][inkoopfacturen_status]" @if(isset($data['inkoopfacturen']['inkoopfacturen_status'])) checked @endif > Afgeronde inkoopfacturen</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][average_payment]" @if(isset($data['inkoopfacturen']['average_payment'])) checked @endif > Gemiddelde betalingstermijn</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][paid_invoices]" @if(isset($data['inkoopfacturen']['paid_invoices'])) checked @endif > Factuurwaarde verhouding</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][revenue_per_branch]" @if(isset($data['inkoopfacturen']['revenue_per_branch'])) checked @endif > Kosten per vestiging</label>
              </div>

              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][inkoopfacturen][btw]" @if(isset($data['inkoopfacturen']['btw'])) checked @endif > Btw</label>
              </div>
              <input class="d-none" type="checkbox" name="json[statistieken_show][inkoopfacturen][placeholder]" checked>
            </div>
          </div>
        @endif
        @if(hasModule("Projecten"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Projecten statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][top_invoiced_projectes]" @if(isset($data['projecten']['top_invoiced_projectes'])) checked @endif > Top 10 projecten (gefactureerde prijs)</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][top_quoted_projectes]" @if(isset($data['projecten']['top_quoted_projectes'])) checked @endif > Top 10 projecten (offerte prijs)</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][status_ratio]" @if(isset($data['projecten']['status_ratio'])) checked @endif > Verhouding project status</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][average_duration]" @if(isset($data['projecten']['average_duration'])) checked @endif > Gemiddelde projectduur</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][gemiddelde_doorlooptijd_opbouw]" @if(isset($data['projecten']['gemiddelde_doorlooptijd_opbouw'])) checked @endif > Gemiddelde doorlooptijd opbouw</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][projecten][gemiddelde_doorlooptijd_project]" @if(isset($data['projecten']['gemiddelde_doorlooptijd_project'])) checked @endif > Gemiddelde doorlooptijd project</label>
              </div>
            </div>
          </div>
        @endif
        @if(hasModule("Werkbonnen"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Werkbonnen statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">

              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][werkbonnen][amount_per_user]" @if(isset($data['werkbonnen']['amount_per_user'])) checked @endif > Aantal per medewerker</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][werkbonnen][status_ratio]" @if(isset($data['werkbonnen']['status_ratio'])) checked @endif > Status verhouding</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][werkbonnen][total_amount_to_order]" @if(isset($data['werkbonnen']['total_amount_to_order'])) checked @endif > Totale aantal te bestellen</label>
              </div>

            </div>
          </div>
        @endif
        @if(hasModule("Checklists"))
          <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Checklisten statistieken tonen</div>
          <div class="col-12 my-2">
            @php $data = json_decode((getSettingValue('statistieken_show') ?? '{}'), true) @endphp
            <div class="row">
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][checklists][total_amount]" @if(isset($data['checklists']['total_amount'])) checked @endif > Totaal aantal</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][checklists][amount_per_user]" @if(isset($data['checklists']['amount_per_user'])) checked @endif > Aantal per medewerker</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][checklists][status_ratio]" @if(isset($data['checklists']['status_ratio'])) checked @endif > Status verhouding</label>
              </div>
              <div class="col-lg-4 col-md-6 col-12 my-2">
                <label class="cursor-pointer" > <input class="form-check-custom" type="checkbox" name="json[statistieken_show][checklists][aantal_afwijkingen_per_groep]" @if(isset($data['checklists']['aantal_afwijkingen_per_groep'])) checked @endif > Aantal afwijkingen per groep</label>
              </div>
            </div>
          </div>
        @endif
      </div>
    </div>
  </div>
@endif
