@if((Auth::user()->hasPermissionTo('Werkbonnen beheren') || Auth::user()->hasPermissionTo('Eigen werkbonnen beheren')) && hasModule("Werkbonnen"))
  <div class="settings-container my-2 col-lg-3 col-md-6 col-12">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="bi bi-file-earmark-text-fill" style="font-size: 35px"></i></span>
        <h4 data-settings-header >Werkbonnen</h4>
      </div>
      <div class="row m-3 d-none settings-content" id="werkbonnenDiv">
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Instellingen</div>
        <div class="col-12 my-2">
          <label>Werkbon bevestiging</label>
          <input type="text" class="form-control" name="werkbonEmailNew" placeholder="<EMAIL>" value="{{getSettingValue('werkbon_email_new')}}" >
        </div>
        <div class="col-12 my-2">
          <label>Laatste werkbon van project mail</label>
          <input type="text" class="form-control" name="settings[last_proj_bon_mail]" placeholder="<EMAIL>" value="{{getSettingValue('last_proj_bon_mail')}}" >
        </div>
        <div class="col-12 my-2">
          <label>Alleen werkbonnen kunnen aanmaken bij project in opdracht</label>
          <select type="text" class="form-select" name="settings[werkbon_projecten_alleen_opdracht]">
            <option @if(getSettingValue('werkbon_projecten_alleen_opdracht') == 'nee') selected @endif value="nee">Nee</option>
            <option @if(getSettingValue('werkbon_projecten_alleen_opdracht') == 'ja') selected @endif value="ja">Ja</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Meerdere werkbonnen per project</label>
          <select type="text" class="form-select" name="settings[werkbon_multiple_per_project]">
            <option @if(getSettingValue('werkbon_multiple_per_project') == 'nee') selected @endif value="nee">Nee</option>
            <option @if(getSettingValue('werkbon_multiple_per_project') == 'ja') selected @endif value="ja">Ja</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Project afronden</label>
          <select type="text" class="form-select" name="settings[werkbon_project_complete]">
            <option @if(getSettingValue('werkbon_project_complete') == 'ja') selected @endif value="ja">Ja</option>
            <option @if(getSettingValue('werkbon_project_complete') == 'nee') selected @endif value="nee">Nee</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Project of klant verplicht</label>
          <select type="text" class="form-select" name="settings[werkbon_project_or_klant_required]">
            <option @if(getSettingValue('werkbon_project_or_klant_required', 'nee') == 'ja') selected @endif value="ja">Ja</option>
            <option @if(getSettingValue('werkbon_project_or_klant_required', 'nee') == 'nee') selected @endif value="nee">Nee</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Projecttaken afronden</label>
          <select type="text" class="form-select" name="settings[werkbon_projecttaken_complete]">
            <option @if(getSettingValue('werkbon_projecttaken_complete') == 'ja') selected @endif value="ja">Ja</option>
            <option @if(getSettingValue('werkbon_projecttaken_complete') == 'nee') selected @endif value="nee">Nee</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Leeg werkbon aanmaken bij vrijgeving ( Planning )</label>
          <select name="settings[werkbonnen_planning_empty_on_release]" class="form-select">
            <option @if(getSettingValue('werkbonnen_planning_empty_on_release') == 'nee') selected @endif value="nee">Nee</option>
            @foreach($werkbonnenTemplates as $template)
              <option @if(getSettingValue('werkbonnen_planning_empty_on_release') == $template->id) selected @endif value="{{$template->id}}">{{$template->naam}}</option>
            @endforeach
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Werkbon planning manier van aanmaken</label>
          <select name="settings[werkbon_planning_manier_aanmaken]" class="form-select">
            <option @if(getSettingValue('werkbon_planning_manier_aanmaken') == 'single') selected @endif value="single">Enkel</option>
            <option @if(getSettingValue('werkbon_planning_manier_aanmaken') == 'multiple') selected @endif value="multiple">Meerdere</option>
            <option @if(getSettingValue('werkbon_planning_manier_aanmaken') == 'released_werkbon') selected @endif value="released_werkbon">Vrijgegeven werkbon</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Aanmaker in CC</label>
          <select type="text" class="form-select" name="settings[werkbon_aanmaker_cc]">
            <option @if(getSettingValue('werkbon_aanmaker_cc') == 'nee') selected @endif value="nee">Nee</option>
            <option @if(getSettingValue('werkbon_aanmaker_cc') == 'ja') selected @endif value="ja">Ja</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Contactpersoon aanpassen in app</label>
          <select type="text" class="form-select" name="settings[werkbon_contactpersoon_aanpassen_app]">
            <option @if(getSettingValue('werkbon_contactpersoon_aanpassen_app') == 'ja') selected @endif value="ja">Ja</option>
            <option @if(getSettingValue('werkbon_contactpersoon_aanpassen_app') == 'nee') selected @endif value="nee">Nee</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Werkbon templates weergeven o.b.v. geplande activiteiten</label>
          <select type="text" class="form-select" name="settings[werkbon_templates_weergeven_obv_geplande_activiteiten]">
            <option @if(getSettingValue('werkbon_templates_weergeven_obv_geplande_activiteiten') == 'nee') selected @endif value="nee">Nee</option>
            <option @if(getSettingValue('werkbon_templates_weergeven_obv_geplande_activiteiten') == 'ja') selected @endif value="ja">Ja</option>
          </select>
        </div>
        <div class="my-2">
          <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" @if(getSettingCheckbox("werkbonnen_show_all")) checked @endif name="checkbox[werkbonnen_show_all]" value="true">&nbsp;Alle werkbonnen weergeven in de app</label>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Custom opvolgstappen</div>
        <div class="col-12 my-2 werkbon_opvolgstappen">
          @php
            $opvolgstappen = getSettingJson("werkbon_opvolgstappen");
          @endphp
          @foreach ($opvolgstappen as $key => $stap)
          <div id="{{$key}}">
            <div class="flex-between my-1">
              <div class="mx-1 w-100">
                <label>Naam</label>
                <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][naam]" value="{{$stap['naam'] ?? ''}}" placeholder="Naam">
              </div>
              <div class="mx-1 w-100">
                <label>Status</label>
                <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][status]" value="{{$stap['status'] ?? ''}}" placeholder="Status">
              </div>
              <div class="mx-1 w-100">
                <label>Status gereed</label>
                <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][statusgereed]" value="{{$stap['statusgereed'] ?? ''}}" placeholder="Status">
              </div>
            </div>
            <div class="flex-between my-1">
              <div class="mx-1 w-100">
                <label>Stadium</label>
                <select name="json[werkbon_opvolgstappen][{{$key}}][stadium]" class="form-select">
                  <option @if(!isset($stap['stadium']) || $stap['stadium'] == 'Opvolgen') selected @endif value="Opvolgen">Opvolgen</option>
                  <option @if( isset($stap['stadium']) && $stap['stadium'] == 'Afgerond') selected @endif value="Afgerond">Afgerond</option>
                  <option @if( isset($stap['stadium']) && $stap['stadium'] == 'Verwijderd') selected @endif value="Verwijderd">Verwijderd</option>
                  <option @if( isset($stap['stadium']) && $stap['stadium'] == 'Open') selected @endif value="Open">Open</option>
                </select>
              </div>
              <div class="mx-1 w-100">
                <label>Doorlooptijd (in dagen)</label>
                <input type="number" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][doorlooptijd]" value="{{$stap['doorlooptijd'] ?? ''}}" placeholder="Doorlooptijd">
              </div>
              <div class="mx-1 w-100">
                <label>Reminder na ... dagen</label>
                <input type="number" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][reminder]" value="{{$stap['reminder'] ?? ''}}" placeholder="Reminder">
              </div>
              <a class="btn btn-inverse-danger mt-4" onclick="deleteDiv('#{{$key}}')">@icon_close</a>
            </div>
            <div class="opvolgvelden_{{$key}} my-1">
              @foreach ($stap['velden'] ?? [] as $veldkey => $veld)
                <div class="flex-between" id="{{$key.'_'.$veldkey}}">
                  <div class="mx-1 w-100">
                    <label>Keyword</label>
                    <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][velden][{{$veldkey}}][keyword]" value="{{$veld['keyword'] ?? ''}}" placeholder="Keyword">
                  </div>
                  <div class="mx-1 w-100">
                    <label>Veldnaam</label>
                    <input type="text" class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][velden][{{$veldkey}}][naam]" value="{{$veld['naam'] ?? ''}}" placeholder="Veldnaam">
                  </div>
                  <div class="mx-1 w-100">
                    <label>Type</label>
                    <select class="form-control-custom" name="json[werkbon_opvolgstappen][{{$key}}][velden][{{$veldkey}}][type]">
                      <option @if($veld['type'] == 'text') selected @endif value="text">Tekst</option>
                      <option @if($veld['type'] == 'number') selected @endif value="number">Getal</option>
                      <option @if($veld['type'] == 'date') selected @endif value="date">Datum</option>
                      <option @if($veld['type'] == 'datedone') selected @endif value="datedone">Datum gereed</option>
                      <option @if($veld['type'] == 'time') selected @endif value="time">Tijd</option>
                      <option @if($veld['type'] == 'select') selected @endif value="select">Select</option>
                      <option @if($veld['type'] == 'selectedit') selected @endif value="selectedit">Select edit</option>
                      <option @if($veld['type'] == 'checkbox') selected @endif value="checkbox">Checkbox</option>
                      <option @if($veld['type'] == 'user') selected @endif value="user">Medewerker</option>
                      <option @if($veld['type'] == 'textarea') selected @endif value="textarea">Tekstveld</option>
                    </select>
                  </div>
                  <div class="mx-1 w-100">
                    <label>Data</label>
                    <input data-sjv class="form-control-custom mx-1 w-100" name="json[werkbon_opvolgstappen][{{$key}}][velden][{{$veldkey}}][data]" placeholder="Data" value="{{$veld['data'] ?? ''}}" >
                  </div>
                  <a class="btn btn-inverse-danger mt-4" onclick="deleteDiv('#{{$key}}_{{$veldkey}}')">@icon_close</a>
                </div>
              @endforeach
            </div>
            <div class="center my-1">
              <a class="btn btn-inverse-primary" onclick="addOpvolgVeld('{{$key}}')">Velden @icon_plus</a>
            </div>
          </div>
          @endforeach
        </div>
        <div class="col-12 my-1 center"><a class="btn btn-inverse-primary" onclick="addOpvolgStap()">Stap @icon_plus</a></div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Opvolgmail extra velden</div>
        <div class="col-12 my-2 werkbon_opvolgmail_extra_velden_tonen">
          <div class="my-2">
            <label>Opvolgmail extra velden</label>
            @php
              $data = getSettingJson('opvolgmail_extra_velden');
            @endphp
            @foreach($werkbonnenTemplates as $template)
              <div>
                <label class="mt-3">{{$template->naam}}</label>
                <infor-select-multiple class="form-control-custom" name="json[opvolgmail_extra_velden][{{$template->id}}]" placeholder="Selecteer extra velden" >
                  @foreach($template->keywords as $keyword)
                    <infor-select-option data-value="{{$keyword['keyword']}}" @if(@isset($data[$keyword->template_id]) && in_array($keyword->keyword, $data[$keyword->template_id])) data-selected @endif >{{$keyword->naam}}</infor-select-option>
                  @endforeach
                </infor-select-multiple>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Werkbon mail extra velden</div>
        <div class="col-12 my-2 werkbon_mail_extra_velden_tonen">
          <div class="my-2">
            <label>werkbon mail extra velden</label>
            @php
              $data = getSettingJson('werkbon_mail_extra_velden');
            @endphp
            @foreach($werkbonnenTemplates as $template)
              <div>
                <label class="mt-3">{{$template->naam}}</label>
                <infor-select-multiple class="form-control-custom" name="json[werkbon_mail_extra_velden][{{$template->id}}]" placeholder="Selecteer extra velden" >
                  @foreach($template->keywords as $keyword)
                    <infor-select-option data-value="{{$keyword['keyword']}}" @if(isset($data[$keyword->template_id]) && in_array($keyword->keyword, $data[$keyword->template_id])) data-selected @endif >{{$keyword->naam}}</infor-select-option>
                  @endforeach
                </infor-select-multiple>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Werkbon reminder mail</div>
        <div class="col-12 my-2 werkbon_mail_extra_velden_tonen">
          <div class="my-2">
            <div class="col-12 my-2">
              <label>werkbon reminder mail</label>
              <label class="flex-align cursor-pointer" >
                <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[werkbonnen_reminder_mail]" @if(getSettingCheckbox('werkbonnen_reminder_mail')) checked @endif >
                <span>Reminder sturen</span>
              </label>
            </div>
            <div class="col-12 my-2">
              <label>Termijn</label>
              <select type="text" class="form-select" name="settings[werkbonnen_reminder_mail_termijn]">
                <option @if(getSettingValue('werkbonnen_reminder_mail_termijn') == '7') selected @endif value="7">Week</option>
                <option @if(getSettingValue('werkbonnen_reminder_mail_termijn') == '30') selected @endif value="30">Maand</option>
                <option @if(getSettingValue('werkbonnen_reminder_mail_termijn') == '90') selected @endif value="90">3 Maanden</option>
              </select>
            </div>
            <div class="col-12 my-2">
              <label>Afzender</label>
              <input class="form-control" name="settings[werkbonnen_reminder_mail_afzender]" value="{{getSettingValue("werkbonnen_reminder_mail_afzender")}}" placeholder="Afzender">
            </div>
            <div class="col-12 my-2">
              <label>Werkbon reminder mail inhoud</label>
              <textarea id="werkbonSignatureContent" name="settings[werkbon_mail_reminder_inhoud]" >{!! getSettingValue('werkbon_mail_reminder_inhoud') !!}</textarea>
            </div>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Projectnummers</div>
        <div class="col-12 my-2">
          <div class="row">
            <div class="col-md-6 col-12 my-2">
              <label>Werkbonnummer prefix</label>
              <input type="text" name="settings[werkbonnummer_prefix]" id="projnummerprefix" class="form-control-custom" placeholder="PR" value="{{getSettingValue('werkbonnummer_prefix')}}">
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Werkbonnummer afterfix lengte ( Met project ) </label>
              <select name="settings[werkbonnummer_afterfix_length_project]" class="form-select" >
                @for($i = 0; $i < 10; $i++)
                  <option @if((getSettingValue('settings[werkbonnummer_afterfix_length_project]') ?? 3) == $i) selected @endif value="{{$i}}"  >{{$i}}</option>
                @endfor
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Werkbonnummer afterfix lengte ( Zonder project )</label>
              <select name="settings[werkbonnummer_afterfix_length_non_project]" class="form-select" >
                @for($i = 0; $i < 10; $i++)
                  <option @if((getSettingValue('settings[werkbonnummer_afterfix_length_non_project]') ?? 5) == $i) selected @endif value="{{$i}}"  >{{$i}}</option>
                @endfor
              </select>
            </div>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >App</div>
        <div class="col-12 my-2">
          <div class="row">
            <label class="flex-align cursor-pointer" >
              <input type="checkbox" class="form-switch-custom mr-2" name="checkbox[werkbonnen_app_blok_uren_incompleet]" @if(getSettingCheckbox('werkbonnen_app_blok_uren_incompleet')) checked @endif >
              <span>Blokkeer Werkbonnen bij niet ingevulde uren</span>
            </label>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Werkbon velden weergeven in app</div>
        <div class="col-12 my-2">
          <div class="row">
            @php $data = getSettingJson('werkbonnen_show_keywords_app'); @endphp
            @foreach (getWerkbonTemplates() as $temp)
              <div class="col-md-4 col-6 my-2">
                <label>{{$temp->naam}}</label>
                <infor-select-multiple id="templatekeywords_{{$temp->id}}" name="json[werkbonnen_show_keywords_app][{{$temp->id}}]" class="form-control" placeholder="Selecteer velden">
                  @foreach ($temp->keywords as $kw)
                    <infor-select-option data-name="{{$kw->naam}}" data-value="{{$kw->keyword}}" @if(in_array($kw->keyword, $data[$temp->id] ?? [])) data-selected @endif>{{$kw->naam}}</infor-select-option>
                  @endforeach
                </infor-select-multiple>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Email instellingen</div>
        <div class="col-12 my-2">
          <label>Afzender</label>
          <input class="form-control" name="settings[werkbon_email_sender]" value="{{getSettingValue("werkbon_email_sender")}}" placeholder="Afzender">
        </div>
        <div class="col-12 my-2">
          <label>Werkbon opdrachtgever ondertekening</label>
          <textarea id="werkbonSignatureContent" name="settings[werkbon_signature_content]" >{!! getSettingValue('werkbon_signature_content') !!}</textarea>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header >Offerte info</div>
        <div class="col-12 my-2">
          @foreach($keywords as $templateId => $keyword)
            <div class="my-2" >{{$templates[$templateId]->naam}}</div>
            <div class="my-2 row mx-3 border-right border-bottom">
              @foreach($keyword as $row)
                <div class="col-sm-4 col-6 my-1">
                  <label class="cursor-pointer"><input @if(isset($offerteInfo[$templateId][$row['keyword']])) checked @endif type="checkbox" value="{{$row["naam"]}}" name="offerteInfo[{{$templateId}}][{{$row['keyword']}}]">&nbsp;{{$row["naam"]}}</label>
                </div>
              @endforeach
            </div>
          @endforeach
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white" data-settings-sub-header>Standaard velden verbergen in werkbonnenoverzicht</div>
        @php
          $standaardvelden = \App\Werkbonnen::standaardVelden();
          $dataStandaardveld = getSettingJson("werkbon_overzicht_verbergen");
        @endphp
        @foreach($standaardvelden as $key=>$val)
          <div class="col-md-3 col-sm-4 col-6 mt-2">
            <label class="cursor-pointer"><input type="checkbox" name="json[werkbon_overzicht_verbergen][{{$key}}]" class="cursor-pointer form-switch-custom mr-2" @if(isset($dataStandaardveld) && isset($dataStandaardveld[$key])) checked @endif >&nbsp; {{$val}}</label>
          </div>
        @endforeach
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white" data-settings-sub-header>Velden weergeven in werkbonnenoverzicht</div>
        @php
          $kopjes = \App\Werkbonnen::templateKeywords();
          $data = getSettingJson("werkbon_overzicht_tonen");
        @endphp
        @foreach($kopjes as $kopKey=>$velden)
          <span class="pt-4"><b>{{$kopKey}}</b></span>
          @foreach($velden as $key=>$val)
            <div class="col-md-3 col-sm-4 col-6 mt-2">
              <label class="cursor-pointer"><input type="checkbox" name="json[werkbon_overzicht_tonen][{{$kopKey}}][{{$val}}]" class="cursor-pointer form-switch-custom mr-2" @if(isset($data) && isset($data[$kopKey][$val])) checked @endif >&nbsp; {{$val}}</label>
            </div>
          @endforeach
        @endforeach
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white" data-settings-sub-header>Opvolg velden weergeven in werkbonnenoverzicht</div>
        @foreach(@getSettingJson("werkbon_opvolgstappen") as $stap)
          @php
            $data = getSettingJson("werkbonnen_opvolgvelden_weergeven");
          @endphp
          <div>
            <label class="mt-3">{{$stap['naam']}}</label>
            <infor-select-multiple class="form-control-custom" name="json[werkbonnen_opvolgvelden_weergeven]" placeholder="Selecteer extra velden" >
              @foreach($stap['velden'] as $veld)
                <infor-select-option data-value="{{$veld['naam']}}" @if(in_array($veld['naam'], $data)) data-selected @endif >{{$veld['naam']}}</infor-select-option>
              @endforeach
            </infor-select-multiple>
          </div>
        @endforeach
      </div>
    </div>
  </div>
@endif
