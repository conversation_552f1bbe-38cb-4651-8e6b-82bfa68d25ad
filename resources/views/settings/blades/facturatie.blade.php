@if(Auth::user()->hasPermissionTo('Facturatie dashboard') || Auth::user()->hasPermissionTo('Facturatie dashboard (InforDB)'))
  @php $machtiging = json_decode(getSettingValue("doorlopende_machtiging")); @endphp
  <div class="settings-container my-2 col-lg-3 col-md-6">
    <div class="card cursor-pointer hover-shadow m-0" >
      <div class="text-center" >
        <span class="d-block my-3" ><i class="fa-solid fa-wallet py-2" style="font-size: 35px" ></i></span>
        <h4 data-settings-header >Facturatie</h4>
      </div>
      <div class="row m-3 d-none settings-content" id="facturatieDiv" >
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Facturatie overzicht</div>
        <div class="col-12 mt-2">
          <label>Ander woord voor betaald gebruiken:</label>
          <input type="text" name="settings[facturatie_alt_betaald_text]" class="form-control-custom" value="{{getSettingValue('facturatie_alt_betaald_text', 'Betaald')}}">
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Facturatie overzicht koppen verbergen</div>
        <div class="col-12 mt-2">
          @php
            $factuuroverzichtkoppen = ['datum', 'betalingstermijn', 'betaald_op', 'factuurbedrag', 'klant', 'debiteurnummer', 'referentie', 'adres', 'factuuradres', 'door', 'percentage'];
            $data = getSettingJson('facturatie_factuuroverzicht_hide');
          @endphp
          <div class="row">
            @foreach($factuuroverzichtkoppen as $kop)
              <div class="col-md-3 col-6 my-2">
                <label class="cursor-pointer" > <input class="form-switch-custom" type="checkbox" name="json[facturatie_factuuroverzicht_hide][{{$kop}}]" @if(isset($data[$kop])) checked @endif > {{$kop}}</label>
              </div>
            @endforeach
            <input type="hidden" name="json[facturatie_factuuroverzicht_hide][leeg]" value="0">
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Facturatie instellingen</div>
        <div class="col-12 mt-2">
          <div class="row">
            <div class="col-12 my-2">
              <label class="cursor-pointer mt-2" > <input class="form-switch-custom" type="checkbox" name="checkbox[standalone_factuur_uit]" @if(getSettingCheckbox('standalone_factuur_uit')) checked @endif > Nieuwe factuur aanmaken uit, enkel vanuit proforma. </label>
            </div>
            <div class="col-12 my-2">
              <label class="cursor-pointer mt-2" > <input class="form-switch-custom" type="checkbox" name="checkbox[facturatie_proforma_regels_samenvoegen]" @if(getSettingCheckbox('facturatie_proforma_regels_samenvoegen')) checked @endif > Regels van proforma samenvoegen bij factureren. </label>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Rapportagedatum</label>
              <select name="settings[facturatie_factuur_reporting_date]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_reporting_date') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('facturatie_factuur_reporting_date') == 'aan') selected @endif value="aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Standaard rapportagedatum <span class="text-muted">7, -7</span> </label>
              <input type="number" step="1" name="settings[facturatie_factuur_reporting_date_default]" class="form-control-custom" placeholder="Standaard rapportagedatum" value="{{getSettingValue('facturatie_factuur_reporting_date_default') ?? 0}}" >
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Boekingsomschrijving</label>
              <select name="settings[facturatie_posting_description]" class="form-select">
                <option @if(getSettingValue('facturatie_posting_description') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('facturatie_posting_description') == 'year_month') selected @endif value="year_month" >Jaar/Maand formaat</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Rapportagedatum overschrijfbaar bij versturen</label>
              <select name="settings[facturatie_factuur_reporting_date_overwrite]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_reporting_date_overwrite') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('facturatie_factuur_reporting_date_overwrite') == 'aan') selected @endif value="aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Eenheden</label>
              <select name="settings[facturatie_factuurregels_eenheden]" class="form-select">
                <option @if(getSettingValue('facturatie_factuurregels_eenheden') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('facturatie_factuurregels_eenheden') == 'aan') selected @endif value="aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Periode</label>
              <select name="settings[facturatie_factuur_date_periode]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_date_periode') == 'uit') selected @endif value="uit" >Uit</option>
                <option @if(getSettingValue('facturatie_factuur_date_periode') == 'aan') selected @endif value="aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Factuur verzenden als bijlage</label>
              <select name="settings[facturatie_factuur_send_as_attachment]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_send_as_attachment') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_send_as_attachment') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Werkbonnen als bijlage verbergen</label>
              <select name="settings[facturatie_factuur_send_hide_werkbonnen]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_send_hide_werkbonnen') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_send_hide_werkbonnen') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Werkbonnen standaard meesturen</label>
              <select name="settings[facturatie_factuur_send_always_werkbonnen]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_send_always_werkbonnen') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_send_always_werkbonnen') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            @if(hasModule('Offertes'))
            <div class="col-md-6 col-12 my-2">
              <label>Offerte als bijlage verbergen</label>
              <select name="settings[facturatie_factuur_send_hide_offerte]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_send_hide_offerte') == 'ja') selected @endif value="ja" >Ja</option>
                <option @if(getSettingValue('facturatie_factuur_send_hide_offerte') == 'nee') selected @endif value="nee" >Nee</option>
              </select>
            </div>
            @endif
            <div class="col-md-6 col-12 my-2">
              <label>Factuuradres niet aanpasbaar</label>
              <select name="settings[facturatie_factuur_lock_factuuradres]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_lock_factuuradres') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_lock_factuuradres') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Factuuradres verbergen in overzicht</label>
              <select name="settings[facturatie_overzicht_hide_factuuradres]" class="form-select">
                <option @if(getSettingValue('facturatie_overzicht_hide_factuuradres') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_overzicht_hide_factuuradres') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Project weergeven in overzicht</label>
              <select name="settings[facturatie_project_weergave]" class="form-select">
                <option @if(getSettingValue('facturatie_project_weergave') == 'Uit') selected @endif value="Uit" >Uit</option>
                <option @if(getSettingValue('facturatie_project_weergave') == 'Aan') selected @endif value="Aan" >Aan</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Losse regels uitzetten</label>
              <select name="settings[facturatie_factuur_disable_single_rows]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_disable_single_rows') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_disable_single_rows') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Projecturen Toevoegen als regel</label>
              <select name="settings[projecturen_overnemen]" class="form-select">
                <option @if(getSettingValue('projecturen_overnemen') == 'ja') selected @endif value="ja" >Ja</option>
                <option @if(getSettingValue('projecturen_overnemen') == 'nee') selected @endif value="nee" >Nee</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Geen projecturen bij geselecteerde offerte</label>
              <select name="settings[geen_projecturen_select_offerte]" class="form-select">
                <option @if(getSettingValue('geen_projecturen_select_offerte') == 'ja') selected @endif value="ja" >Ja</option>
                <option @if(getSettingValue('geen_projecturen_select_offerte') == 'nee') selected @endif value="nee" >Nee</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Uursoorten per week factureren</label>
              <select name="settings[factuur_uursoorten_per_week]" class="form-select">
                <option @if(getSettingValue('factuur_uursoorten_per_week') == 'ja') selected @endif value="ja" >Ja</option>
                <option @if(getSettingValue('factuur_uursoorten_per_week') == 'nee') selected @endif value="nee" >Nee</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Regels prefillen vanuit datasetitems in werkbon</label>
              <select name="settings[datasetitems_from_werkbon]" class="form-select">
                <option @if(getSettingValue('datasetitems_from_werkbon') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('datasetitems_from_werkbon') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Regel aanmaken vanuit begin en eindtijd in werkbon</label>
              <select name="settings[uren_from_werkbon]" class="form-select">
                <option @if(getSettingValue('uren_from_werkbon') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('uren_from_werkbon') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Regel aanmaken vanuit kilometers op werkbon</label>
              <select name="settings[kms_from_werkbon]" class="form-select">
                <option @if(getSettingValue('kms_from_werkbon') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('kms_from_werkbon') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Regel aanmaken vanuit reistijd op werkbon</label>
              <select name="settings[reistijd_from_werkbon]" class="form-select">
                <option @if(getSettingValue('reistijd_from_werkbon') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('reistijd_from_werkbon') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Werkbon toevoegen zonder factuurregel</label>
              <select name="settings[werkbon_zonder_factuurregel]" class="form-select">
                <option @if(getSettingValue('werkbon_zonder_factuurregel') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('werkbon_zonder_factuurregel') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
            <label>Inleiding verbergen</label>
            <select name="settings[facturatie_factuur_disable_inleiding]" class="form-select">
              <option @if(getSettingValue('facturatie_factuur_disable_inleiding') == 'nee') selected @endif value="nee" >Nee</option>
              <option @if(getSettingValue('facturatie_factuur_disable_inleiding') == 'ja') selected @endif value="ja" >Ja</option>
            </select>
          </div>
            <div class="col-md-6 col-12 my-2">
              <label>Slot verbergen</label>
              <select name="settings[facturatie_factuur_disable_slot]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_disable_slot') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_disable_slot') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Pro forma's standaard meesturen</label>
              <select name="settings[facturatie_send_default_attach_proforma]" class="form-select">
                <option @if(getSettingValue('facturatie_send_default_attach_proforma') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_send_default_attach_proforma') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Betalingstermijn prefillen vanuit klant</label>
              <select name="settings[factuur_termijn_prefill]" class="form-select">
                <option @if(getSettingValue('factuur_termijn_prefill') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_termijn_prefill') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Projecttaken factureren</label>
              <select name="settings[factuur_projecttaken_factureren]" class="form-select">
                <option @if(getSettingValue('factuur_projecttaken_factureren') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_projecttaken_factureren') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Per factuur btw verlegd aangeven</label>
              <select name="settings[factuur_btw_verlegd_per_factuur]" class="form-select">
                <option @if(getSettingValue('factuur_btw_verlegd_per_factuur') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_btw_verlegd_per_factuur') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Project uren afronden naar eerst volgende uur</label>
              <select name="settings[factuur_project_uren_afronden_naar_eerst_volgende_uur]" class="form-select">
                <option @if(getSettingValue('factuur_project_uren_afronden_naar_eerst_volgende_uur') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_project_uren_afronden_naar_eerst_volgende_uur') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Mandagen bij factuur aanmaken</label>
              <select name="settings[facturatie_factuur_mandagen]" class="form-select">
                <option @if(getSettingValue('facturatie_factuur_mandagen') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('facturatie_factuur_mandagen') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Verplichte velden</div>
        <div class="col-12 my-2">
          @php $data = json_decode((getSettingValue('facturatie_required') ?? '[]'), true); @endphp
          <div class="my-2">
            <label>Facturen</label>
            <div class="my-2 row">
              <label class="cursor-pointer col-lg-3 col-md-4 col-sm-6 col-12 my-2" > <input class="form-check-custom" @if(isset($data['factuur']['project'])) checked @endif  type="checkbox" name="json[facturatie_required][factuur][project]"> Project</label>
              <label class="cursor-pointer col-lg-3 col-md-4 col-sm-6 col-12 my-2" > <input class="form-check-custom" @if(isset($data['factuur']['offerte'])) checked @endif  type="checkbox" name="json[facturatie_required][factuur][offerte]"> Offerte</label>
            </div>
          </div>
          <div class="my-2">
            <label>Pro forma's</label>
            <div class="my-2 row">
              <label class="cursor-pointer col-lg-3 col-md-4 col-sm-6 col-12 my-2" > <input class="form-check-custom" @if(isset($data['proforma']['project'])) checked @endif  type="checkbox" name="json[facturatie_required][proforma][project]"> Project</label>
              <label class="cursor-pointer col-lg-3 col-md-4 col-sm-6 col-12 my-2" > <input class="form-check-custom" @if(isset($data['proforma']['offerte'])) checked @endif  type="checkbox" name="json[facturatie_required][proforma][offerte]"> Offerte</label>
            </div>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Custom functie</div>
        <div class="col-12 my-1">
          <div class="flex-between mx--1 my-2 custom-row-container">
            <input class="form-control-custom mx-1 w-100" name="settings[facturatie_custom_functie]" placeholder="Blade" value="{{getSettingValue('facturatie_custom_functie')}}" >
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Standaard factuurregels</div>
        <div class="col-12 my-1">
          @php $data = json_decode((getSettingValue('factuur_custom_rows') ?? '[]')); @endphp
          <div class="factuur-custom-rows">
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[factuur_custom_rows][{{$string}}][naam]" placeholder="Naam" value="{{$row->naam}}" >
                <input type="number" class="form-control-custom mx-1 w-100" min="0" name="json[factuur_custom_rows][{{$string}}][aantal]" placeholder="Aantal" value="{{$row->aantal}}" >
                <input type="number" class="form-control-custom mx-1 w-100" min="0" step="0.1" name="json[factuur_custom_rows][{{$string}}][bedrag]" placeholder="Bedrag" value="{{$row->bedrag}}" >
                <input type="number" class="form-control-custom mx-1 w-100" min="0" max="100" step="0.1" name="json[factuur_custom_rows][{{$string}}][btw]" placeholder="BTW %" value="{{$row->btw}}" >
                <div class="col-md-3 col-sm-4 col-6 mt-2"><label class="cursor-pointer"><input type="checkbox" name="json[factuur_custom_rows][{{$string}}][inclBtw]" @if(isset($row->inclBtw)) checked @endif > incl. BTW</label></div>
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="my-2 text-center" >
            <a class="btn btn-inverse-primary" onclick="addFactuurCustomRow()" >@icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Factuurregels custom velden</div>
        <div class="col-12 my-1">
          @php $data = getSettingJson('factuur_rows_custom_fields'); @endphp
          <div class="factuur-rows-custom-fields">
            @foreach($data as $string => $row)
              <div class="flex-between mx--1 my-2" id="{{$string}}" >
                <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][{{$string}}][keyword]" placeholder="Keyword" value="{{$row['keyword']}}" >
                <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][{{$string}}][label]" placeholder="Label" value="{{$row['label']}}" >
                <input class="form-control-custom mx-1 w-100" name="json[factuur_rows_custom_fields][{{$string}}][default]" placeholder="Standaardwaarde" value="{{$row['default']}}" >
                <a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#{{$string}}')" >@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="my-2 text-center" >
            <a class="btn btn-inverse-primary" onclick="addFactuurRowsCustomField()" >@icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >Factuur opsplitsen</div>
        <div class="col-12 mt-2 my-2">
          <div class="row">
            <div class="col-md-6 col-12 my-2">
              <label>Factuur opsplitsen naam</label>
              <select name="settings[factuur_opsplitsen_naam]" class="form-select">
                <option @if(getSettingValue('factuur_opsplitsen_naam') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_opsplitsen_naam') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Factuur opsplitsen datum</label>
              <select name="settings[factuur_opsplitsen_datum]" class="form-select">
                <option @if(getSettingValue('factuur_opsplitsen_datum') == 'nee') selected @endif value="nee" >Nee</option>
                <option @if(getSettingValue('factuur_opsplitsen_datum') == 'ja') selected @endif value="ja" >Ja</option>
              </select>
            </div>
          </div>
        </div>
        <div class="col-12 my-1 bg-secondary rounded p-2 text-white " data-settings-sub-header >G rekening</div>
        <div class="my-2">
          <div class="project-grekening-custom-rows">
            @php
              $grekeningNr = getSettingValue('factuur_grekeningNr_custom_row');
              $grekening = getSettingJson('factuur_grekening_custom_row');
              $customOverboeken = getSettingJson('factuur_overboeken_custom_row');
            @endphp
            <label>G rekening nummer</label>
            <div class="flex-between mx--1 my-2" >
              <input class="form-control-custom mx-1 w-100" name="settings[factuur_grekeningNr_custom_row]" placeholder="G rekening nummer" value="{{$grekeningNr?? ''}}" >
            </div>

            <label>G rekening</label>
            <div class="flex-between mx--1 my-2" >
              <input class="form-control-custom mx-1 w-100" name="json[factuur_grekening_custom_row][name]" placeholder="Naam" value="{{$grekening['name'] ?? ''}}" >
              <input type="number" class="form-control-custom mx-1 w-100" name="json[factuur_grekening_custom_row][value]" placeholder="Waarde" value="{{$grekening['value'] ?? ''}}" >
              <select class="form-select mx-1 w-100" name="json[factuur_grekening_custom_row][type]">
                <option @if(isset($grekening['type']) && $grekening['type'] == 'percentage') selected @endif value="percentage">Percentage</option>
                <option @if(isset($grekening['type']) && $grekening['type'] == 'bedrag') selected @endif value="bedrag">Bedrag</option>
              </select>
            </div>

            <label>Custom overboeking voorwaarde</label>
            <div class="flex-between mx--1 my-2" >
              <input class="form-control-custom mx-1 w-100" name="json[factuur_overboeken_custom_row][name]" placeholder="Naam" value="{{$customOverboeken['name'] ?? ''}}" >
              <input type="number" class="form-control-custom mx-1 w-100" name="json[factuur_overboeken_custom_row][value]" placeholder="Waarde" value="{{$customOverboeken['value'] ?? ''}}" >
              <select class="form-select mx-1 w-100" name="json[factuur_overboeken_custom_row][type]">
                <option @if(isset($customOverboeken['type']) && $customOverboeken['type'] == 'percentage') selected @endif value="percentage">Percentage</option>
                <option @if(isset($customOverboeken['type']) && $customOverboeken['type'] == 'bedrag') selected @endif value="bedrag">Bedrag</option>
              </select>
            </div>
          </div>
        </div>

        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Factuurnummer</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label>Wanneer is de factuur definitief?</label>
            <select name="settings[factuur_definitief]" class="form-select" {{getSettingValue("factuur_definitief")}} >
              <option value="Verzonden" @if(getSettingValue("factuur_definitief") == "Verzonden") selected @endif >Bij verzenden</option>
              <option value="Uitgebracht" @if(getSettingValue("factuur_definitief") == "Uitgebracht") selected @endif >Bij aanmaak</option>
            </select>
          </div>
          <div class="my-2">
            <label>Factuurnummer prefix</label>
            <input type="text" name="factuurnummerPrefix" class="form-control-custom" value="{{getSettingValue("factuur_factuurnummer_prefix")}}" placeholder="Prefix" >
          </div>
          <div class="my-2">
            <label>Factuurnummer datum format</label>
            <select name="factuurnummerDateFormat" class="form-select" {{getSettingValue("factuur_factuurnummer_datum_format")}} >
              <option value="long" @if(getSettingValue("factuur_factuurnummer_datum_format") == "long") selected @endif>{{date("Y")}}</option>
              <option value="long-" @if(getSettingValue("factuur_factuurnummer_datum_format") == "long-") selected @endif>{{date("Y")}}-</option>
              <option value="short" @if(getSettingValue("factuur_factuurnummer_datum_format") == "short") selected @endif>{{date("y")}}</option>
              <option value="short-" @if(getSettingValue("factuur_factuurnummer_datum_format") == "short-") selected @endif>{{date("y")}}-</option>
              <option value="none" @if(getSettingValue("factuur_factuurnummer_datum_format") == "none") selected @endif>Leeg</option>
            </select>
          </div>
          <div class="my-2">
            <label>Afterfix lengte</label>
            <select name="factuurnummerAfterfixLength" class="form-select" {{getSettingValue("factuur_factuurnummer_afterfix_length")}} >
              <option value="1" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "1") selected @endif>1</option>
              <option value="2" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "2") selected @endif>2</option>
              <option value="3" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "3") selected @endif>3</option>
              <option value="4" @if((getSettingValue("factuur_factuurnummer_afterfix_length") ?? "4") == "4") selected @endif>4</option>
              <option value="5" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "5") selected @endif>5</option>
              <option value="6" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "6") selected @endif>6</option>
              <option value="7" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "7") selected @endif>7</option>
              <option value="8" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "8") selected @endif>8</option>
              <option value="9" @if(getSettingValue("factuur_factuurnummer_afterfix_length") == "9") selected @endif>9</option>
            </select>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Proforma ondertekenen bijlagen</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label class="cursor-pointer" > <input type="checkbox" class="form-switch-custom" name="checkbox[facturatie_append_proforma_sign_attachments]" @if(getSettingCheckbox('facturatie_append_proforma_sign_attachments')) checked @endif  > Toevoegen als bijlage bij factuur</label>
          </div>
          @php $data = json_decode(getSettingValue('facturatie_proforma_sign_attachments') ?? '[]') @endphp
          <div data-proforma-sign-attach >
            @foreach($data as $string => $row)
              <div class="my-1 d-flex align-items-center" data-proforma-sign-attach-node="{{$string}}" >
                <input type="text" class="form-control-custom w-100" placeholder="Label" name="json[facturatie_proforma_sign_attachments][{{$string}}][name]" value="{{$row->name ?? ''}}" >
                <input type="checkbox" class="form-check-custom mx-2 tippy" data-tippy-content="Verplicht" name="json[facturatie_proforma_sign_attachments][{{$string}}][required]" @isset($row->required) checked @endisset >
                <a class="btn text-danger" onclick="deleteDiv('[data-proforma-sign-attach-node={{$string}}]')" >@icon_close</a>
              </div>
            @endforeach
          </div>
          <div class="my-2 text-center">
            <a class="btn btn-inverse-primary" onclick="addProformaSignAttach()" >@icon_plus</a>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Email instellingen</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label class="cursor-pointer mt-2" > <input class="form-switch-custom" type="checkbox" name="checkbox[facturatie_mails_hide_button]" @if(getSettingCheckbox('facturatie_mails_hide_button')) checked @endif > "Bekijk factuur" button verbergen </label>
          </div>
          <div class="my-2">
            <label>Factuur afzender</label>
            <input type="text" class="form-control" value="{{getSettingValue('factuur_afzender')}}" placeholder="<EMAIL>" name="factuurAfzender">
          </div>
          @if (someoneHasPermission('Proforma facturen beheren'))
            <div class="my-2">
              <label>Proforma afzender</label>
              <input type="text" class="form-control" value="{{getSettingValue('proforma_afzender')}}" placeholder="<EMAIL>" name="settings[proforma_afzender]">
            </div>
          @endif
          <div class="my-2">
            <label>Factuur bevestiging email</label>
            <input type="text" class="form-control" name="factuurBevestigingEmail" value="{{getSettingValue("factuur_bevestiging_email")}}" placeholder="<EMAIL>">
          </div>
          <div class="my-2">
            <label>Factuur template</label>
            <textarea name="factuurContent" id="factuurEditor" >@if(getSettingValue('factuur_email_content')) {!! $settings["factuur_email_content"]->value !!} @endif</textarea>
          </div>
          <div class="my-2">
            <label>Pro forma template</label>
            <textarea name="settings[proforma_email_content]" id="proformaEditor" >@if(getSettingValue('proforma_email_content')) {!! $settings["proforma_email_content"]->value !!} @endif</textarea>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Reminder</div>
        <div class="col-12 my-2">
          <div class="my-2 row">
            <div class="col-md-6 col-12 my-2">
              <label>Afzender</label>
              <input type="text" class="form-control-custom" value="{{getSettingValue('facturatie_reminder_afzender')}}" placeholder="<EMAIL>" name="settings[facturatie_reminder_afzender]">
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Afzender Proforma</label>
              <input type="text" class="form-control-custom" value="{{getSettingValue('proforma_reminder_afzender')}}" placeholder="<EMAIL>" name="settings[proforma_reminder_afzender]">
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Correspondentie toevoegen</label>
              <select class="form-select" name="settings[facturatie_reminder_correspondence]">
                <option value="off" @if(getSettingValue('facturatie_reminder_correspondence') == 'off') selected @endif >Uit</option>
                <option value="email" @if(getSettingValue('facturatie_reminder_correspondence') == 'email') selected @endif >Mails</option>
                <option value="reminders" @if(getSettingValue('facturatie_reminder_correspondence') == 'reminders') selected @endif >Reminders</option>
                <option value="all" @if(getSettingValue('facturatie_reminder_correspondence') == 'all') selected @endif >Alles</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Automatisch reminder pro forma's</label>
              <select class="form-select" name="settings[facturatie_reminder_proforma_interval]">
                <option value="off" @if(getSettingValue('facturatie_reminder_proforma_interval') == 'off') selected @endif >Uit</option>
                @for($i = 1; $i <= 30; $i++)
                  <option value="{{$i}}" @if(getSettingValue('facturatie_reminder_proforma_interval') == $i) selected @endif >{{$i === 1 ? 'Elke dag' : "Om de $i dagen"}}</option>
                @endfor
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Automatisch reminder facturen</label>
              <select disabled class="form-select" name="settings[facturatie_reminder_facturen_interval]">
                <option value="off" @if(getSettingValue('facturatie_reminder_facturen_interval') == 'off') selected @endif >Uit</option>
                @for($i = 1; $i <= 30; $i++)
                  <option value="{{$i}}" @if(getSettingValue('facturatie_reminder_facturen_interval') == $i) selected @endif >{{$i === 1 ? 'Elke dag' : "Om de $i dagen"}}</option>
                @endfor
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Default user</label>
              <select class="form-select" name="settings[facturatie_reminder_facturen_user]">
                <option value="off" @if(getSettingValue('facturatie_reminder_facturen_user') == 'off') selected @endif >Geen</option>
                @foreach(getUsers() as $user)
                  <option value="{{$user->id}}" @if(getSettingValue('facturatie_reminder_facturen_user') == $user->id) selected @endif >{{$user->name}} {{$user->lastname}}</option>
                @endforeach
              </select>
            </div>
          </div>
          <div class="my-2">
            <label>Pro forma reminder</label>
            <textarea name="settings[facturatie_reminder_template_proforma]" id="facturatie-reminder-proforma" >@if(getSettingValue('facturatie_reminder_template_proforma')) {!! getSettingValue('facturatie_reminder_template_proforma') !!} @endif</textarea>
          </div>
          <div class="my-2">
            <label>Factuur reminder</label>
            <textarea name="settings[facturatie_reminder_template_factuur]" id="facturatie-reminder-factuur" >@if(getSettingValue('facturatie_reminder_template_factuur')) {!! getSettingValue('facturatie_reminder_template_factuur') !!} @endif</textarea>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Snelstart Standaard Grootboeken</div>
        @foreach(getBvs() as $bv)
          @php
            $data = [
              9 => getSettingJson("facturatie_snelstart_grootboek_bv{$bv->id}_perc9"),
              21 => getSettingJson("facturatie_snelstart_grootboek_bv{$bv->id}_perc21"),
            ];
          @endphp

          <div class="col-12 my-2">

            <div class="flex-between p-2 rounded-7 shadow-md">

              <div>
                <div class="font-size-125" >{{$bv->name}}</div>
                <div class="font-size-075 text-secondary" >KvK: {{$bv->kvk}}</div>
              </div>

              <div class="flex-align">
                @foreach([9, 21] as $perc)
                  @php $data = getSettingJson("facturatie_snelstart_grootboek_bv{$bv->id}_perc{$perc}") @endphp
                  <a onclick="selectStandaardGrootboek({{$perc}}, {{$bv->id}})" data-snelstart-grootboek-perc="{{$perc}}" data-bv="{{$bv->id}}" class="btn btn-sm font-size-07 rounded-3 btn-inverse-primary mx-1">
                    <div>{{$perc}}%</div>
                    <div data-label >{{$data['name'] ?? 'Geen optie geselecteerd'}}</div>
                  </a>
                  <input type="hidden" name="settings[facturatie_snelstart_grootboek_bv{{$bv->id}}_perc{{$perc}}]" value="{{getSettingValue("facturatie_snelstart_grootboek_bv{$bv->id}_perc{$perc}", '{}')}}">

                @endforeach
              </div>

            </div>

          </div>

        @endforeach

        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Proforma status verandering</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue('facturatie_proforma_status_change_notifications'), true) @endphp
          <div class="facturatie-proforma-status-opties row">
            @foreach($data ?? [] as $type => $nots)
              @foreach($nots as $row)
                @php $string = randomString(); @endphp
                @if($type == 'email')
                  <div class="col-md-6 col-12 my-2 {{$string}}" >
                    <label>E-mail</label>
                    <div class="flex-between">
                      <input name="json[facturatie_proforma_status_change_notifications][email][]" class="form-control-custom" placeholder="E-mail" value="{{$row}}" >
                      <a class="btn text-danger" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
                    </div>
                  </div>
                @elseif($type == 'role')
                  <div class="col-md-6 col-12 my-2 {{$string}}" >
                    <label>Rol</label>
                    <div class="flex-between">
                      <select name="json[facturatie_proforma_status_change_notifications][role][]" class="form-select" >
                        @foreach(getRoles() ?? [] as $role)
                          <option @if($role->id == $row) selected @endif value="{{$role->id}}">{{$role->name}}</option>
                        @endforeach
                      </select>
                      <a class="btn text-danger" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
                    </div>
                  </div>
                @elseif($type == 'user')
                  <div class="col-md-6 col-12 my-2 {{$string}}" >
                    <label>User</label>
                    <div class="flex-between">
                      <select name="json[facturatie_proforma_status_change_notifications][user][]" class="form-select" >
                        @foreach(getUsers() ?? [] as $user)
                          <option @if($user->id == $row) selected @endif value="{{$user->id}}">{{$user->name}} {{$user->lastname}}</option>
                        @endforeach
                      </select>
                      <a class="btn text-danger" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
                    </div>
                  </div>
                @elseif($type == 'anders')
                  <div class="col-md-6 col-12 my-2 {{$string}}" >
                    <label>Anders</label>
                    <div class="flex-between">
                      <select name="json[facturatie_proforma_status_change_notifications][anders][]" class="form-select" >
                      </select>
                      <a class="btn text-danger" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
                    </div>
                  </div>
                @endif
              @endforeach
            @endforeach
          </div>
          <div class="text-center">
            <a class="btn btn-inverse-primary" onclick="addFactuurProformaStatusOptie()" >Optie toevoegen</a>
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Datasets</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue('facturen_datasets') ?? '[]', true)@endphp
          <label>Datasets koppelen</label>
          <div class="row">
            @foreach($datasets as $dataset)
              <div class="col-ld-3 col-md-4 col-6" >
                <div class="bg-inverse-secondary border rounded my-2 p-2 checkbox-container" >
                  <label class="cursor-pointer"><input @if(isset($data[$dataset->id])) checked @endif type="checkbox" name="json[facturen_datasets][{{$dataset->id}}][state]" class="form-check-custom state-checkbox">  {{$dataset->naam}}</label>
                  <select @if(!isset($data[$dataset->id])) disabled @endif name="json[facturen_datasets][{{$dataset->id}}][key]" class="form-select">
                    <option value="">Dataset key</option>
                    @foreach(json_decode(($dataset->items[0]->value ?? '[]'), true) as $name => $value)
                      <option @if(($data[$dataset->id]['key'] ?? null) == $name) selected @endif value="{{$name}}" >{{$name}}</option>
                    @endforeach
                  </select>
                </div>
              </div>
            @endforeach
          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>XML Export</div>
        <div class="col-12 my-2">
          @php $data = json_decode(getSettingValue("facturen_export_xml_data"), true); @endphp
          <div class="row">
            <div class="col-md-6 col-12 my-2">
              <label class="cursor-pointer m-0">Facturen exporteren</label>
              <select class="form-select" name="settings[facturen_export_xml_method]">
                <option @if(getSettingValue('facturen_export_xml_method') == 'open') selected @endif value="open">Open facturen</option>
                <option @if(getSettingValue('facturen_export_xml_method') == 'paid') selected @endif value="paid">Afgeronde facturen</option>
                <option @if(getSettingValue('facturen_export_xml_method') == 'all') selected @endif value="all">Alle facturen</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label class="cursor-pointer m-0">Klant Key</label>
              <select class="form-select" name="settings[klanten_export_xml_key]">
                <option @if(getSettingValue('facturen_export_xml_key') == 'id') selected @endif value="id">ID</option>
                <option @if(getSettingValue('facturen_export_xml_key') == 'debiteur') selected @endif value="debiteur">Debiteurnummer</option>
              </select>
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Wrapper</label>
              <input name="settings[facturen_export_xml_wrapper]" value="{{getSettingValue('facturen_export_xml_wrapper')}}" class="form-control" placeholder="Wrapper" >
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Regels</label>
              <input name="settings[facturen_export_xml_regels]" value="{{getSettingValue('facturen_export_xml_regels')}}" class="form-control" placeholder="Regels" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Factuurnummer</label>
              <input name="json[facturen_export_xml_data][factuurnummer]" value="{{$data['factuurnummer'] ?? ''}}" class="form-control" placeholder="Factuurnummer" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Status</label>
              <input name="json[facturen_export_xml_data][status]" value="{{$data['status'] ?? ''}}" class="form-control" placeholder="Status" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Referentie</label>
              <input name="json[facturen_export_xml_data][referentie]" value="{{$data['referentie'] ?? ''}}" class="form-control" placeholder="Referentie" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Datum</label>
              <input name="json[facturen_export_xml_data][datum]" value="{{$data['datum'] ?? ''}}" class="form-control" placeholder="Datum" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Betalingstermijn</label>
              <input name="json[facturen_export_xml_data][betalingstermijn]" value="{{$data['betalingstermijn'] ?? ''}}" class="form-control" placeholder="Betalingstermijn" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Betaald op</label>
              <input name="json[facturen_export_xml_data][betaald_op]" value="{{$data['betaald_op'] ?? ''}}" class="form-control" placeholder="Betaald op" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Valuta</label>
              <input name="json[facturen_export_xml_data][valuta]" value="{{$data['valuta'] ?? ''}}" class="form-control" placeholder="Valuta" >
            </div>
          </div>
          <div class="row border-top">
            <div class="col-md-6 col-12 my-2">
              <label>Klant wrapper</label>
              <input name="settings[facturen_export_xml_klant]" value="{{getSettingValue('facturen_export_xml_klant')}}" class="form-control" placeholder="Wrapper" >
            </div>
            <div class="col-md-6 col-12 my-2"></div>
            <div class="col-md-3 col-6 my-2">
              <label>ID</label>
              <input name="json[facturen_export_xml_data][klant][id]" value="{{$data['klant']['id'] ?? ''}}" class="form-control" placeholder="ID" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Datum</label>
              <input name="json[facturen_export_xml_data][klant][datum]" value="{{$data['klant']['datum'] ?? ''}}" class="form-control" placeholder="Datum" >
            </div>
          </div>
          <div class="row border-top">
            <div class="col-md-6 col-12 my-2">
              <label>Factuurregels wrapper</label>
              <input name="settings[facturen_export_xml_factuurregels]" value="{{getSettingValue('facturen_export_xml_factuurregels')}}" class="form-control" placeholder="Factuurregels" >
            </div>
            <div class="col-md-6 col-12 my-2">
              <label>Prijs wrapper</label>
              <input name="settings[facturen_export_xml_prijs]" value="{{getSettingValue('facturen_export_xml_prijs')}}" class="form-control" placeholder="Prijs" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Aantal</label>
              <input name="json[facturen_export_xml_data][regels][aantal]" value="{{$data['regels']['aantal'] ?? ''}}" class="form-control" placeholder="Aantal" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Valuta</label>
              <input name="json[facturen_export_xml_data][regels][valuta]" value="{{$data['regels']['valuta'] ?? ''}}" class="form-control" placeholder="Valuta" >
            </div>
            <div class="col-md-3 col-6 my-2">
              <label>Prijs</label>
              <input name="json[facturen_export_xml_data][regels][prijs]" value="{{$data['regels']['prijs'] ?? ''}}" class="form-control" placeholder="Prijs" >
            </div>
          </div>
          <div class="row border-top">
            @php $data = json_decode(getSettingValue('facturen_export_xml_datasets') ?? '[]', true)@endphp
            @foreach($datasets as $dataset)
              <div class="col-ld-3 col-md-4 col-6" >
                <div class="bg-inverse-secondary border rounded my-2 p-2 checkbox-container" >
                  <label class="cursor-pointer"><input @if(isset($data[$dataset->id])) checked @endif type="checkbox" name="json[facturen_datasets][{{$dataset->id}}][state]" class="form-check-custom facturen-state-checkbox">  {{$dataset->naam}}</label>
                  <input  @if(!isset($data[$dataset->id])) disabled @endif type="text" class="form-control-custom mb-1" placeholder="Naam" name="json[facturen_export_xml_datasets][{{$dataset->id}}][name]" value="{{$data[$dataset->id]['name'] ?? ''}}" >
                  <select @if(!isset($data[$dataset->id])) disabled @endif name="json[facturen_export_xml_datasets][{{$dataset->id}}][key]" class="form-select">
                    <option value="">Dataset key</option>
                    @foreach(json_decode(($dataset->items[0]->value ?? '[]'), true) as $name => $value)
                      <option @if(($data[$dataset->id]['key'] ?? null) == $name) selected @endif value="{{$name}}" >{{$name}}</option>
                    @endforeach
                  </select>
                </div>
              </div>
            @endforeach

          </div>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Factuur instellingen</div>
        <div class="col-12 mt-3">
          <label>Mollie API Key</label>
          <input type="text" class="form-control" value="{{getSettingValue('mollie_key')}}" placeholder="Mollie API Key" name="mollieKey">
        </div>
        <div class="col-12 mt-3">
          <label>Standaard betalingstermijn in dagen</label>
          <input type="number" class="form-control" value="{{getSettingValue('factuur_betalingstermijn')}}" placeholder="Betalingstermijn" name="factuurBetalingstermijn">
        </div>
        <div class="col-12 mt-3">
          <label>Standaard beslissingstermijn in dagen</label>
          <input type="number" class="form-control" value="{{getSettingValue('factuur_beslissingstermijn')}}" placeholder="Betalingstermijn" name="settings[factuur_beslissingstermijn]">
        </div>
        <div class="col-12 my-2">
          <label>BTW percentages</label>
          <div class="row facturatie-btw-percs">
            @foreach(json_decode(getSettingValue('factuur_btw_percs')) ?? [] as $per)
              <div class="col-lg-2 col-md-3 col-4">
                <input type="number" value="{{$per}}" class="form-control" name="factuurBtwPercs[]" placeholder="Percentage">
              </div>
            @endforeach
          </div>
          <div class="text-center my-2">
            <a class="btn btn-inverse-primary" onclick="addFacturatieBtwPerc()">BTW Percentage @icon_plus</a>
          </div>
        </div>

        <div class="col-12 my-2">
          <label>Standaard spiltsing</label>
          <div class="row facturatie-standaard-splitsing">
            @foreach(json_decode(getSettingValue('factuur_standaard_splitsing')) ?? [] as $split)
              @php $randomstring = randomString() @endphp
              <div class="col-lg-2 col-md-3 col-4 d-flex align-items-center" id="splitsing_{{$randomstring}}">
                <input type="number" value="{{$split}}" class="form-control" name="json[factuur_standaard_splitsing][]" placeholder="Percentage">
                <a onclick="deleteDiv('#splitsing_{{$randomstring}}')" class="btn btn-danger text-white ml-2" style="line-height: 23px;">@icon_trash</a>
              </div>
            @endforeach
          </div>
          <div class="text-center my-2">
            <a class="btn btn-inverse-primary" onclick="addFacturatieSplitsing()">Splitsing @icon_plus</a>
          </div>
        </div>

        <div class="col-12 my-2">
          <label>Standaard BTW percentage</label>
          <select class="form-select" name="factuurBtwPerc" >
            @foreach(json_decode(getSettingValue('factuur_btw_percs')) ?? [] as $per)
              <option @if(getSettingValue('factuur_btw_perc') == $per) selected @endif  value="{{$per}}" >{{$per}}%</option>
            @endforeach
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Standaard prijs incl. BTW</label>
          <select class="form-select" name="factuurStandaardInclBtw">
            <option value="1" @if(getSettingValue("factuur_standaard_incl_btw") == 1) selected @endif >Ja</option>
            <option value="0" @if(getSettingValue("factuur_standaard_incl_btw") == 0) selected @endif >Nee</option>
          </select>
        </div>
        <div class="col-12 my-2">
          <label>Standaard factuur inleiding</label>
          <textarea id="factuurInleiding" name="factuurInleiding">{{getSettingValue('factuur_inleiding')}}</textarea>
        </div>
        <div class="col-12 my-2">
          <label>Standaard factuur slot</label>
          <textarea id="factuurSlot" name="factuurSlot">{{getSettingValue('factuur_slot')}}</textarea>
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Automatische incasso</div>
        <div class="col-12 mt-2">
          <label>Naam incassant</label>
          <input type="text" name="naamIncassant" class="form-control" value="{{$machtiging->naamIncassant ?? ''}}">
        </div>
        <div class="col-12 mt-2">
          <label>Adres incassant</label>
          <input type="text" name="adresIncassant" class="form-control" value="{{$machtiging->adresIncassant ?? ''}}">
        </div>
        <div class="col-md-6 col-12 mt-2">
          <label>Postcode incassant</label>
          <input type="text" name="postcodeIncassant" class="form-control" value="{{$machtiging->postcodeIncassant ?? ''}}">
        </div>
        <div class="col-md-6 col-12 mt-2">
          <label>Woonplaats incassant</label>
          <input type="text" name="woonplaatsIncassant" class="form-control" value="{{$machtiging->woonplaatsIncassant ?? ''}}">
        </div>
        <div class="col-md-6 col-12 mt-2">
          <label>Land incassant</label>
          <input type="text" name="landIncassant" class="form-control" value="{{$machtiging->landIncassant ?? ''}}">
        </div>
        <div class="col-md-6 col-12 mt-2">
          <label>Incassant ID</label>
          <input type="text" name="incassantId" class="form-control" value="{{$machtiging->incassantId ?? ''}}">
        </div>
        <div class="col-12 mt-2">
          <label>Kenmerk machtiging</label>
          <input type="text" name="kenmerkMachtiging" class="form-control" value="{{$machtiging->kenmerkMachtiging ?? ''}}">
        </div>
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Factuur reminders</div>
        <div class="col-12 my-2 text-right ">
          <div id="factuurReminders">
            @foreach(json_decode(getSettingValue("factuur_reminders")) ?? [] as $i =>$reminder)
              <div id="factuurReminder{{$i}}" class="d-flex justify-content-between my-2" >
                <input type="text" placeholder="Reminder" class="form-control mx-2" name="factuurReminders[]" value="{{$reminder}}">
                <a onclick="deleteDiv('#factuurReminder{{$i}}')" class="btn btn-danger text-white align-self-center" >@icon_trash</a>
              </div>
            @endforeach
          </div>
          <a class="btn btn-primary text-white" onclick="addFactuurReminder()">@icon_plus</a>
        </div>
        @if(exactGlobe(['no_curl' => true])->connected)
          <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Exact Globe</div>
          <div class="col-12 my-2">
            <label class="cursor-pointer mt-2" > <input class="form-switch-custom" type="checkbox" name="checkbox[facturatie_exact_globe_only_this_year]" @if(getSettingCheckbox('facturatie_exact_globe_only_this_year')) checked @endif > Alleen facturen van het huidige jaar kunnen uploaden </label>
          </div>
          <div class="col-12 my-2">
            <div class="my-2 select_search-container">
              <label>Standaard dagboek</label>
              <input type="hidden" name="settings[facturatie_exact_globe_default_journal]" class="select_search-hidden-input" data-placeholder="Dagboeknummer">
              <div class="select_search-values" >
                <div class="select_search-box" >
                  @foreach(exactGlobe(['no_curl' => true])->getStoredJournals() as $journal)
                    <span class="select_search-value" data-value="{{$journal->journal_number}}" data-name="{{$journal->journal_number}} | {{$journal->description}}" @if(getSettingValue('facturatie_exact_globe_default_journal') == $journal->journal_number) data-selected="true" @endif >
                      <span class="badge badge-primary" >{{$journal->journal_number}}</span> {{$journal->description}}
                    </span>
                  @endforeach
                </div>
              </div>
            </div>
            <div class="my-2 select_search-container">
              <label>Standaard BTW</label>
              <input type="hidden" name="settings[facturatie_exact_globe_default_vat]" class="select_search-hidden-input" data-placeholder="BTW">
              <div class="select_search-values" >
                <div class="select_search-box" >
                  @foreach(exactGlobe(['no_curl' => true])->getStoredVat() as $vat)
                    <span class="select_search-value" data-value="{{$vat->code}}" data-name="{{$vat->description}}" @if(getSettingValue('facturatie_exact_globe_default_vat') == $vat->code) data-selected="true" @endif >
                     <span class="badge badge-primary" >{{$vat->percentage}}%</span> {{$vat->description}}
                    </span>
                  @endforeach
                </div>
              </div>
            </div>
            <div class="my-2">
              <label class="cursor-pointer mt-2" > <input class="form-switch-custom" type="checkbox" name="checkbox[facturatie_exact_globe_only_selected_ledgers]" @if(getSettingCheckbox('facturatie_exact_globe_only_selected_ledgers')) checked @endif > Alleen geselecteerde grootboeken </label>
              <div class="select_multiple-container my-2" data-placeholder="Grootboeken" >
                @php $data = json_decode((getSettingValue('facturatie_exact_globe_ledgers') ?? '[]'), true); @endphp
                @foreach(exactGlobe(['no_curl' => true])->getStoredGLByCode(8) as $ledger)
                  <label class="select_multiple-value" > <span class="badge badge-primary mr-2 " >{{$ledger->code}}</span> {{$ledger->description}} <input type="checkbox" class="d-none" name="json[facturatie_exact_globe_ledgers][{{$ledger->code}}]" data-name="{{$ledger->code}}" value="{{$ledger->code}}" @if(isset($data[$ledger->code])) checked @endif ></label>
                @endforeach
              </div>
            </div>
          </div>
        @endif
        @if (isEboekhouden())
          <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Eboekhouden</div>
          <div class="my-2 select_search-container">
            <label>Standaard BTW</label>
            <input type="hidden" name="settings[facturatie_eboekhouden_default_vat]" class="select_search-hidden-input" data-placeholder="BTW">
            <div class="select_search-values" >
              <div class="select_search-box" >
                @foreach(eboekBtwCodes() as $vat)
                  <span class="select_search-value" data-value="{{$vat->code}}" data-name="{{$vat->omschrijving}}" @if(getSettingValue('facturatie_eboekhouden_default_vat') == $vat->code) data-selected="true" @endif >
                   <span class="badge badge-primary" >{{$vat->percentage}}%</span> {{$vat->omschrijving}}
                  </span>
                @endforeach
              </div>
            </div>
          </div>
          <div class="my-2 select_search-container">
            <label>Standaard grootboek</label>
            <input type="hidden" name="settings[facturatie_eboekhouden_default_gb]" class="select_search-hidden-input" data-placeholder="Grootboek">
            <div class="select_search-values" >
              <div class="select_search-box" >
                @foreach(eboekGrootboeken() as $gb)
                  <span class="select_search-value" data-value="{{$gb->code}}" data-name="{{$gb->omschrijving}}" @if(getSettingValue('facturatie_eboekhouden_default_gb') == $gb->code) data-selected="true" @endif >
                   <span class="badge badge-primary" >{{$gb->code}}</span> {{$gb->omschrijving}}
                  </span>
                @endforeach
              </div>
            </div>
          </div>
        @endif
        @if (someoneHasPermission('Proforma facturen beheren'))
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Accorderen</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label class="cursor-pointer" > <input type="checkbox" class="form-switch-custom" name="checkbox[proforma_accorderen_auto_factuur]" @if(getSettingCheckbox('proforma_accorderen_auto_factuur')) checked @endif  > Bij proforma accorderen automatisch factuur aanmaken</label>
          </div>
        </div>
        @endif
        <div class="col-12 my-2 bg-secondary rounded p-2 text-white " data-settings-sub-header>Proforma details</div>
        <div class="col-12 my-2">
          <div class="my-2">
            <label class="cursor-pointer" > <input type="checkbox" class="form-switch-custom" name="checkbox[proforma_details_prefil_most_recent]" @if(getSettingCheckbox('proforma_details_prefil_most_recent')) checked @endif  > Automatisch Prefillen vanuit Project bij nieuwe pro forma</label>
          </div>
        </div>
      </div>
    </div>
  </div>
@endif
