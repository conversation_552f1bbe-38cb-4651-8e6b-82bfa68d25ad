@extends('layouts.app')

@section('title', $titel)

@section('content')
  <form id="newWachtwoord" method="POST" enctype="multipart/form-data" class="append-loader wachtwoord-form">
      <section class="my-2">
        <div class="card col-3 my-4 p-4 m-0">
          <div class="row">
            <div class="col-12 my-2"><b>{{$titel}}</b></div>
                <div class="col-md-12 col-12 my-2 select_edit-container">
                  <label>Categorie</label>
                  <input type="text" autocomplete="off" name="categorie" class="select_edit-input form-select" value="{{$kluisRegel->categorie ?? ''}}" placeholder="Categorie" required>

                  <div class="select_edit-values">  <!-- class 'show' to display the dropdown-->
                    <div class="select_edit-box">
                      @foreach($categorien as $categorie)
                        <span class="select_edit-value form-control-custom" data-value="{{$categorie['categorie']}}">{{$categorie['categorie']}}</span>
                      @endforeach
                    </div>
                  </div>
                </div>

            <div class="col-md-12 col-12 my-2">
              <label>Naam</label>
              <input name="naam" class="form-control-custom"  placeholder="Naam" value="{{$kluisRegel->naam ?? ''}}"/>
            </div>
            @if($titel == 'Nieuw wachtwoord' && Auth::user()->hasPermissionTo('Wachtwoorden beheren'))
            <infor-select-multiple id="gebruikers" name="gebruikers" class="form-control" placeholder="Selecteer Rechthebbende">
              <label>Rechthebbende</label>
              @foreach($gebruikers as $gebruiker)
                <infor-select-option data-name="{{$gebruiker->name}}" data-value="{{$gebruiker->id}}">{{$gebruiker->name}}</infor-select-option>
              @endforeach
            </infor-select-multiple>
            @endif
              <div class="col-md-12 col-12 my-2">
                <label>Link</label>
                <input name="link" class="form-control-custom"  placeholder="Link" value="{{$kluisRegel->link ?? ''}}"/>
              </div>
              <div class="col-md-12 col-12 my-2">
                <label>Gebruikersnaam</label>
                <input name="gebruikersnaam" class="form-control-custom"  placeholder="Gebruikersnaam" value="{{$kluisRegel->gebruikersnaam ?? ''}}"/>
              </div>
              <div class="col-md-12 col-12 my-2">
                <label>Wachtwoord</label>
                <input name="wachtwoord" class="form-control-custom"  placeholder="Wachtwoord" required value="{{$kluisRegel->wachtwoord ?? ''}}"/>
              </div>
              <div class="col-md-12 col-12 my-2">
                <label>Opmerking</label>
                <input name="opmerking" class="form-control-custom" placeholder="Opmerking"  value="{{$kluisRegel->opmerking ?? ''}}"/>
              </div>
          </div>
        </div>
      </section>
    <div>
      <button class="btn btn-success" type="submit">Opslaan</button>
      @csrf
    </div>

  </form>
@endsection
@section("script")
  <script>

  </script>
@endsection
