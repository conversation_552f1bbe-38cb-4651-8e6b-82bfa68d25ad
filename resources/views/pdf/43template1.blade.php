<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$offerte->offertenummer}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 80%;
      margin: 170px 10% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      font-family: sans-serif;
      font-size: 15px;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      top: -135px;
      left: -0%;
      z-index: -1;
      width: 100%;
      text-align: center;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: -100px;
      left: -12.5%;
      right: 0cm;
      z-index: -1;
      width: 100.1%;

    }

  </style>
</head>
<header class="">
  <table>
    <tr>
      <td class="w80">
      </td>
      <td class="w100" >
        <div class="d-inline" >
          <img style="width: 120%; display: inline-block;" src="{{url("client/public/img/pdf/43template1/headerimgWeerts.png")}}" >
        </div>
      </td>
      <td class="w100">
        <div class="d-inline text-left nobr" style="color: rgb(128, 95, 47); font-size: 0.8em; margin-left: 20%">
          <label>{{$offerte->template->_bv->straat}} {{$offerte->template->_bv->huisnummer}}</label><br>
          <label>{{$offerte->template->_bv->postcode}} {{$offerte->template->_bv->plaats}}</label><br>
          <label>T {{$offerte->template->_bv->telefoon}}</label><br>
          <br>
          <label>Kvk: {{$offerte->template->_bv->kvk}}</label><br>
          <label>Btw nr: {{$offerte->template->_bv->btw}}</label><br>
          <label>Banknr: {{$offerte->template->_bv->iban}}</label><br>
        </div>
      </td>
    </tr>
  </table>
</header>
<footer>
  <div class="w100 " >
    <img style="width: 125%; margin-bottom:-11.5%; z-index: 1; display: inline-block; border-top: none" src="{{url("client/public/img/pdf/43template1/footerimgWeerts1.jpeg")}}" >
  </div>
  <div class="w90 nobr" style="color: rgb(89, 57, 27); font-size: 1.4rem; margin-left: 12%; margin-bottom: 3%">
    <label><EMAIL></label>
    <label> | </label>
    <label><EMAIL></label>
  </div>
</footer>

<main>

  <!-- Page 1 -->
  <section>
    <div class="w20 d-inline text-left pt-2 pl-2 pb-3 pr-3" style="margin-top: -20%">
      <label>
        {{$contactpersoon}}
        <br>
        {{$offerte->klant->straat}} {{$offerte->klant->huisnummer}} {{$offerte->klant->toevoeging}}
        <br>
        {{$offerte->klant->postcode}} {{$offerte->klant->plaats}}
      </label>
    </div>
    <div class="text-left" style="font-size: 0.9rem; margin-top: 5%">
      <label><b>Offerte nummer: {{$offerte->offertenummer}}</b></label>
    </div>
    <div class="text-left" style="font-size: 0.9rem; margin-top: 2%">
      <label>Offerte datum: {{CarbonDmy($offerte->offerte_datum)}}</label>
    </div>
    <div class="text-left" style="font-size: 0.9rem; margin-top: 2%">
      <label>Verval datum offerte: {{CarbonDmy($offerte->geldig_tot)}}</label>
    </div>

    <p style="margin-top: 30px">{!!$offerteteksten["voorwoord"]!!}</p>

    @foreach ($offerte->allRows() as $row)
      @if ($row->isDetail)
        <div style="page-break-inside: avoid">
          <div class="text-left" style="font-size: 0.9rem; margin-top: 40px;">
            <b>{{$row->naam}}:</b>
          </div>
          <div class="text-left my-2" style="font-size: 0.9rem;">
            <label>{!!$row->description!!}</label>
          </div>
          <div class="text-right my-2" style="font-size: 0.9rem;">
            <label><span style="margin-left: 10%">excl btw: {{number_format($row->price, 2, ",", ".")}}</span></label>
          </div>
        </div>
      @endif
    @endforeach

    <div class="text-left priceTable" style="font-size: 0.9rem; padding-top: 40px; page-break-inside: avoid;">
      <table>
        <th>
          <tr>
            <td colspan="3"><label><b>Werkzaamheden</b></label></td>
            <td colspan="1"><b>Aantal</b></td>
            <td colspan="1" style="text-align: right"><b>Prijs</b></td>
            <td colspan="1" style="text-align: right"><b>btw</b></td>
            <td colspan="1" style="text-align: right"><b>Subtotaal excl btw</b></td>
          </tr>
        </th>
        @php $totaalBtw = 0; $deelbareregels = []; @endphp
        @foreach ($offerte->allRows() as $row)
          @if ($row->deelbaar)
            @php
            $deelbareregels[] = $row;
            @endphp
            @continue
          @endif
          <tr>
            <td colspan="3"><label>{{$row->naam}}</label></td>
            <td colspan="1">1</td>
            <td colspan="1" style="text-align: right">€{{number_format($row->price, 2, ",", ".")}}</td>
            @php $totaalBtw += ($row->price/100) * $row->btw; @endphp
            <td colspan="1" style="text-align: right">{{number_format($row->btw, 1, ",", ".")}}</td>
            <td colspan="1" style="text-align: right"><label>€{{number_format($row->total, 2, ",", ".")}}</label></td>
          </tr>
        @endforeach
        @if (count($deelbareregels))
          <tr style="margin-top: 10px;">
            <td colspan="3"><label><b>Optionele werkzaamheden</b></label></td>
            <td colspan="1"><b>Aantal</b></td>
            <td colspan="1" style="text-align: right"><b>Prijs</b></td>
            <td colspan="1" style="text-align: right"><b>btw</b></td>
            <td colspan="1" style="text-align: right"><b>Subtotaal excl btw</b></td>
          </tr>
          @foreach($deelbareregels ?? [] as $row)
            @php
            $streep = '';
              if(!$row->active){
                $streep = 'text-decoration: line-through;';
              }
            @endphp
            <tr>
              <td colspan="3"><label>{{$row->naam}}</label></td>
              <td colspan="1">{{$row->aantal}}</td>
              <td colspan="1" style="text-align: right">€{{number_format($row->price, 2, ",", ".")}}</td>
              @php $totaalBtw += $row->active ? (($row->price/100) * $row->btw) : 0 @endphp
              <td colspan="1" style="text-align: right">{{number_format($row->btw, 1, ",", ".")}}</td>
              <td colspan="1" style="text-align: right"><label>€{{number_format($row->active ? $row->total : 0, 2, ",", ".")}}</label></td>
            </tr>
          @endforeach
        @endif
        <tr>
          <td colspan="4"></td>
          <td colspan="3"><hr></td>
        </tr>
        <tr>
          <td colspan="5"></td>
          <td colspan="1"><label>Subtotaal excl btw: </label></td>
          <td colspan="1" style="text-align: right"><label>€{{number_format($offerteteksten["totaalEx"], 2, ",", ".")}}</label></td>
        </tr>
        <tr>
          <td colspan="5"></td>
          <td colspan="1"><label>Btw: </label><br></td>
          <td colspan="1" style="text-align: right"><label>€{{number_format($totaalBtw, 2, ",", ".")}}</label></td>
        </tr>
        <tr>
          <td colspan="5"></td>
          <td colspan="1"><label>Totaal: </label></td>
          @php $totaalInclBtw = $offerteteksten["totaalEx"] + $totaalBtw @endphp
          <td colspan="1" style="text-align: right"><label>€{{number_format($totaalInclBtw, 2, ",", ".")}}</label></td>
        </tr>
      </table>

      @if(($opmerkingen != "[]"))
        <div class="text-left" style="font-size: 0.9rem; margin-top: 5%">
          <label><b>Opmerkingen:</b></label>
        </div>
      @endif
      @foreach($opmerkingen as $opmerking)
        <div class="text-left" style="font-size: 0.9rem; margin-top: 2%">
          <label>{{$opmerking->tekst}}</label>
        </div>
      @endforeach
    </div>

    @if(isset($offerteteksten['Niet_in_prijs_inbegrepen']))
      <div class="text-left" style="font-size: 0.9rem; margin-top: 15%">
        <label><b>Niet in prijs inbegrepen:</b></label>
      </div>

      <ul>
        @foreach(json_decode($offerteteksten['Niet_in_prijs_inbegrepen']) as $item)
          <li>
            <div class="text-left" style="font-size: 0.9rem; margin-top: 2%">
              <label>{!!$item->value!!}</label>
            </div>
          </li>
        @endforeach
      </ul>
    @endif

    <p style="margin-top: 30px">{!!$offerteteksten["slot"]!!}</p>

    <div class="d-inline" style="page-break-inside: avoid">
      <table style="border: none; vertical-align: bottom" >
        <tr style="background-color: transparent" >
          <td style="width: 40%; border-bottom: 1px solid" >
            @if(isset($handtekening))
              <img style="width: 100%" src="http://infordb.ikbentessa.nl/client/public/img/handtekeningen/{{$handtekening}}">
            @endif
          </td>
          <td style="width: 10%;" ></td>
          <td style="width: 40%; border-bottom: 1px solid" >
            @if(isset($offerte->handtekening))
              <img style="width: 100%" src="http://infordb.ikbentessa.nl/client/public/img/handtekeningen/{{$offerte->handtekening}}">
            @endif
          </td>
        </tr>
        <tr style="background-color: transparent" >
          <td style="width: 40%" ><span class="w100">{{$user}}<br>{{$offerte->template->_bv->name}}</span></td>
          <td style="width: 10%;" ></td>
          <td style="width: 40%" ><span class="w100">{{$contactpersoon}}<br>{{$klant->soort_klant == 'Zakelijk' ? $klant->naam : ''}}</span></td>
        </tr>
      </table>
    </div>
  </section>

</main>

</body>
</html>
