<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$offerte->offertenummer}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 85%;
      margin: 120px 7.5% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      font-family: sans-serif;
      font-size: 15px;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      top: -100px;
      left: -0%;
      z-index: -1;
      width: 100%;
      text-align: center;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: -90px;
      left: 0;
      z-index: -1;
      width: 100%;
      margin: 0 -10%
    }
    footer b{
      margin-right: 8%;
      padding: 50px 0;
    }
    footer .bg{
      height: 20px;
      background-color: #6EAF51;
    }


    img{width: 110px;}
    table{
      border-collapse: collapse;
      width: 100%;
    }
    .table{
      margin: 0!important;
      padding: 0 0px!important;
      width: 100%;

    }
    .inhoudsopgave table{
      border: 0;
    }
    .inhoudsopgave table tr{
      background-color: inherit!important;
    }
    .inhoudsopgave table tr td{
      border-bottom: 1px dotted rgba(0,0,0,0.4);
      padding-bottom: 5px;
    }
    #pageCounter {
      position: fixed;
      float: right;
      right: 0;
      bottom: -1.3cm;
      z-index: -1;
    }
    .begroting tbody{
      border: 1px solid black;
    }
    .begroting tbody tr td{
      border: 1px solid #666666;
      text-align: center;
    }
    .begroting tbody tr td:first-child{
      text-align: left;
    }
    .begroting tbody tr:first-child{
      background-color: #CCCCCC;
    }
    #pageCounter .counter:after { content: counter(page, decimal); }
    .border{border: 1px solid black;}
    .text-right{text-align: right}
    .text-left{text-align: left}
    small{font-size: 10px;}
    .page-break{page-break-after: always;}
    .border-bottom{
      border-bottom: 1px solid black;
    }

    #overzicht-table td{
      padding: 5px;
      border: 1px solid;
    }
    #overzicht-table tr td:first-of-type{
      width: 180px;
      background-color: rgba(213, 143, 156, 0.3);
    }

    #asbestbronnen td{
      padding: 10px 5px;
      border: 1px solid;
    }

    #asbestbronnen td{}

    .sloop-table th{
      text-align: left;
      border: 1px solid;
    }
    .sloop-table td{
      border: 1px solid;
    }
    .sloop-table .post-row{
       background-color: rgba(213, 143, 156, 0.2);
       font-weight: bolder;
     }
    .sloop-table .post-row-nd{
      background-color: rgba(213, 143, 156, 0.1);
      font-weight: bold;
    }

    .sloop-table th p{
      margin: 0;
      padding: 0;
      font-weight: bold;
    }
    .open-begroting-table td{
      border: 1px solid;
    }
    .open-begroting-table th{
      text-align: left;
    }
    .info-table th, .info-table td{
      padding: 0;
    }


  </style>
</head>
<header>
    <div class="text-right opacity-50">
      <img style="width: 300px" src="{{url("/client/public/img/logos/bv19.png")}}" >
    </div>
</header>
<div id="pageCounter"><p class="counter d-inline"></p></div>
<body>
<main>
  <section class="w100">
    <div>
      <table>
        <tbody>
          <tr>
            <td>
              @if(isset($klant->naam))<b class="d-block" >{{$klant->naam}}</b>@endif
              <span class="d-block">{{$contactpersoon}}</span>
              <span class="d-block">{{$klant->straat}} {{$klant->huisnummer}}{{$klant->toevoeging}}</span>
              <span class="d-block">{{$klant->postcode}} {{$klant->plaats}}</span>
            </td>
            <td style="width: 325px" >
              <table class="info-table">
                <tbody>
                    <tr>
                      <td>Uw contactpersoon</td>
                      <td>:</td>
                      <td class="text-right" >{{$user}}</td>
                    </tr>
                    <tr>
                      <td>Telefoon</td>
                      <td class="text-center" >:</td>
                      <td class="text-right" >{{$offerte->user->phone}}</td>
                    </tr>
                    <tr>
                      <td>E-Mail</td>
                      <td>:</td>
                      <td class="text-right" >{{$offerte->user->email}}</td>
                    </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>

    </div>
    <div class="my-40">
      <table style="width: auto;" >
        <tbody>
        <tr>
          <td>Project</td>
          <td>:</td>
          <td>{{$offerte->offertenummer}}</td>
        </tr>
        <tr>
          <td>Betreft</td>
          <td>:</td>
          <td>{{$offerteteksten['betreft'] ?? ''}}</td>
        </tr>
        <tr>
          <td>Uw kenmerk</td>
          <td>:</td>
          <td>{{$offerteteksten['kenmerk'] ?? ''}}</td>
        </tr>
        <tr>
          <td>Werkadres</td>
          <td>:</td>
          <td>{{$offerte->adres->straat ?? ''}} {{$offerte->adres->huisnummer ?? ""}}{{$offerte->adres->toevoeging ?? ""}}</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td>{{$offerte->adres->postcode ?? ''}} {{$offerte->adres->plaats ?? ''}}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </section>

  <section class="w100">
{{--    <div class="my-20">--}}
{{--      <table>--}}
{{--        <tr>--}}
{{--          <td style="width: 50px; " class="opacity-50">--}}
{{--            <img src="{{url('client/public/img/pdf/24template1/image001.png')}}" style="width: 50px;">--}}
{{--          </td>--}}
{{--          <td class="opacity-50" style="vertical-align: center" >--}}
{{--            <div style="margin-top: 15px;" >--}}
{{--              <small class="d-block" >Laarakkers Sloop en Asbest B.V.</small>--}}
{{--              <small class="d-block" >Karel Doormanstraat 19 | 5831 LT Boxmeer</small>--}}
{{--            </div>--}}
{{--          </td>--}}
{{--        </tr>--}}
{{--      </table>--}}
{{--    </div>--}}
    <div class="my-40" >
      {!! $offerteteksten["inleiding"] ?? "" !!}
    </div>

    @php $asbestExists = false @endphp
    @foreach($offerte->details as $detail)
      @if($detail->regel->active == "0") @continue @endif
      @if($detail->keyword == "binnensanering" || $detail->keyword == "buitensanering" || $detail->keyword == "bodemsanering")
        @php $asbestExists = true @endphp
      @endif
    @endforeach
    @if($asbestExists)
      <div class="my-40">
        <b>Te saneren asbestbronnen:</b>
        <table class="my-20" id="asbestbronnen" >
          <thead>
          <tr>
            <th class="text-left">Nr.</th>
            <th class="text-left">Aantal</th>
            <th class="text-left">Omschrijving</th>
            <th class="text-left">Locatie</th>
            <th class="text-left">Risico kl.</th>
            <th class="text-left">Saneringstechniek</th>
          </tr>
          </thead>
          <tbody>
          @foreach($offerte->details as $detail)
            @if($detail->regel->active == "0") @continue @endif
            @if($detail->keyword == "binnensanering" || $detail->keyword == "buitensanering" || $detail->keyword == "bodemsanering")
              @php $bronnen = json_decode($detail->values['asbestbronnen']->value ?? '[]'); @endphp
              @foreach($bronnen as $bron)
                <tr>
                  <td>{{$bron->nr->value ?? ''}}</td>
                  <td>{{$bron->aantal->value ?? 0}} {{$bron->eenheid->value ?? ''}}</td>
                  <td>{{$bron->omschrijving->value ?? ''}}</td>
                  <td>{{$bron->locatie->value ?? ''}}</td>
                  <td>{{$bron->risico->value ?? ''}}</td>
                  <td>{{$detail->name ?? ''}}</td>
                </tr>
              @endforeach
            @endif
          @endforeach
          </tbody>
        </table>
      </div>
    @endif

    @if(($offerteteksten["openBegroting"] ?? "") == "ja")
      {{-- <div class="my-20">
        <table class="my-20 open-begroting-table" >
          <thead>
            <tr>
              <th colspan="3" style="padding: 20px 0" >Open begroting</th>
            </tr>
            <tr>
              <th>Omschrijving</th>
              <th>Aantal</th>
              <th>Totaal excl. BTW</th>
            </tr>
          </thead>
          <tbody>
            @foreach($offerte->details as $detail)
              @if($detail->regel->active == "0") @continue @endif
              @if($detail->keyword == "binnensanering" || $detail->keyword == "buitensanering" || $detail->keyword == "bodemsanering")
                @php $tarieven = $detail->values['tarief']->datasets @endphp
                  @foreach($tarieven as $tarief)
                  @php
                    $setItem = json_decode($tarief->value);
                    $total = 0;
                    if($offerte->inclBtw == "1"){
                        $total = (($setItem->Prijs ?? 0) * $tarief->aantal) / (100 + $detail->regel->btw) * 100;
                    }
                    else{
                        $total = ($setItem->Prijs ?? 0) * $tarief->aantal;
                    }
                  @endphp
                  <tr>
                    <td>{{$setItem->Omschrijving ?? ''}}</td>
                    <td class="center" >{{$tarief->aantal}} {{$setItem->Eenheid ?? ''}}</td>
                    <td>
                      <table class="content-border-0" >
                        <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >{{number_format($total, 2, ",", ".")}}</td>
                        </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                @endforeach
              @elseif($detail->keyword == "sloop" || $detail->keyword == "renovatiesloop")
                @php
                  $posten = json_decode($detail->values['posten']->value ?? '[]');
                  $tarieven = $detail->values['tarief']->datasets ?? [];
                 @endphp
                @foreach($posten as $post)
                  @php
                    $prijs = $post->prijs->value ?? 0;
                    $aantal = $post->aantal->value ?? 0;
                    $eenheid = $post->eenheid->value ?? '';
                  @endphp
                  <tr>
                    <td>{{$post->omschrijving->value ?? ''}}</td>
                    <td class="center" >{{$aantal}} {{$eenheid}}</td>
                    <td>
                      <table class="content-border-0" >
                        <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >
                            @if($offerte->inclBtw == "1")
                              {{number_format(($prijs / (100 + $detail->regel->btw) * 100), 2, ",", ".")}}
                            @else
                              {{number_format(($prijs * $aantal), 2, ",", ".")}}
                            @endif
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                @endforeach
                @foreach($tarieven as $tarief)
                  @php
                    $setItem = json_decode($tarief->value);
                    $total = 0;
                    if($offerte->inclBtw == "1"){
                        $total = (($setItem->Prijs ?? 0) * $tarief->aantal) / (100 + $detail->regel->btw) * 100;
                    }
                    else{
                        $total = ($setItem->Prijs ?? 0) * $tarief->aantal;
                    }
                  @endphp
                  <tr>
                    <td>{{$setItem->Omschrijving ?? ''}}</td>
                    <td class="center" >{{$tarief->aantal}} {{$setItem->Eenheid ?? ''}}</td>
                    <td>
                      <table class="content-border-0" >
                        <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >{{number_format($total, 2, ",", ".")}}</td>
                        </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                @endforeach
              @endif
            @endforeach
            @foreach($offerte->regels as $row)
              @if($row->active == "0") @continue @endif
              @php
                if($offerte->inclBtw == "1"){
                    $total = $row->totaalprijs / (100 + $row->btw) * 100;
                }
                else{
                    $total = $row->totaalprijs;
                }
              @endphp
              <tr>
                <td>{{$row->tekst ?? ''}}</td>
                <td class="center" >{{$row->aantal}}</td>
                <td>
                  <table class="content-border-0" >
                    <tbody>
                    <tr>
                      <td>€</td>
                      <td class="text-right" >{{number_format($total, 2, ",", ".")}}</td>
                    </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            @endforeach
          </tbody>
        </table>
      </div> --}}
    @endif

    @if(isset($offerteteksten["werkzaamheden"]))
      @php $list = json_decode($offerteteksten["werkzaamheden"]); @endphp
      <div class="my-40" style="page-break-inside: auto" >
        <b>Uit te voeren werkzaamheden:</b>
        <div>
          <ul>
            @foreach($list as $row)
              <li>{{$row->value}}</li>
            @endforeach
          </ul>
        </div>
      </div>
    @endif

    <div class="my-40">
      <table>
        <tr>
          <td>Bovengenoemde werkzaamheden kunnen wij voor u uitvoeren voor een bedrag van:</td>
          <td class="text-right" ><nobr><b>€ {{number_format(($offerteteksten["totaalEx"] ?? 0), 2, ",", ".")}} excl. BTW.</b></nobr></td>
        </tr>
      </table>
    </div>

    <div>
      @foreach($opmerkingen as $opmerking)
        <p>{{$opmerking->tekst}}</p>
      @endforeach
    </div>

{{--  </section>--}}
{{--  <div class="page-break"></div>--}}
{{--  <section>--}}

    @if(isset($offerteteksten["prijsvorming"]))
      @php $list = json_decode($offerteteksten["prijsvorming"]); @endphp
      <div class="my-40">
        <b>Bij onze prijsvorming zijn wij ervan uitgegaan dat:</b>
        <ul>
          @foreach($list as $row)
            <li>{{$row->value}}</li>
          @endforeach
        </ul>
      </div>
    @endif

    @if(isset($offerteteksten["nietInbegrepen"]))
      @php $list = json_decode($offerteteksten["nietInbegrepen"]); @endphp
      <div class="my-40">
        <b>Niet in onze prijs inbegrepen:</b>
        <ul>
          @foreach($list as $row)
            <li>{{$row->value}}</li>
          @endforeach
        </ul>
      </div>
    @endif

    <div class="my-40" id="overzicht-table">
      @php
        $diff = \Carbon\Carbon::parse(\Carbon\Carbon::parse($offerte->offerte_datum)->format("Y-m-d"))->diffInDays($offerte->geldig_tot);

        $overzicht = $offerteteksten["overzicht"] ?? "";
        $overzicht = str_replace("{geldigheidsduur}", $diff, $overzicht);
      @endphp
      {!! $overzicht !!}
    </div>
  </section>

  <div class="page-break"></div>

  <section>
    <div class="my-40">
      {!! $offerteteksten["slot"] ?? "" !!}
    </div>

    <table style="table-layout: fixed" >
        <tbody>
        <tr>
          <td>Met vriendelijke groet,</td>
          <td></td>
          <td>
            voor akkoord opdracht:
            <div style="padding-right: 20px;" >
              <div style="margin-top: 10px; " class="border-bottom" >
                Datum:
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div  style="height: 100px;">
              @if(isset($handtekening))
                <img style="width: 100%" src="http://infordb.ikbentessa.nl/client/public/img/handtekeningen/{{$handtekening}}">
              @endif
            </div>
            <div  style="border-bottom: 1px solid black;"></div>
            <span >{{$user}}<br>Laarakkers Sloop en Asbest B.V.</span>
          </td>
          <td></td>
          <td>
            <div  style="height: 100px;">
              @if(isset($offerte->handtekening))
                <img style="width: 100%" src="{{url('client/public/img/handtekeningen/'.$offerte->handtekening)}}">
              @endif
            </div>
            <div  style="border-bottom: 1px solid black;"></div>
            <span >{{$contactpersoon}}<br>{!!$klant->naam ?? '&nbsp;' !!}</span>
          </td>
        </tr>
        </tbody>
      </table>

    @if(isset($offerteteksten["openBegroting"]) && $offerteteksten["openBegroting"] != "alleen_totaalprijs")

      @php
        $sloopExists = false;
        $saneringExists = false;

        foreach($offerte->details as $detail){
          if($detail->regel->active == "0"){continue;}

          if($detail->keyword == "sloop" || $detail->keyword == "renovatiesloop"){
            $sloopExists = true;
          }
          elseif($detail->keyword == "binnensanering" || $detail->keyword == "buitensanering" || $detail->keyword == "bodemsanering"){
            $saneringExists = true;
          }
        }
      @endphp




      @if(isset($posten['posten']) && count($posten['posten']))
          <table class="sloop-table my-20" >
            <thead>
            <tr>
              <th>POST</th>
              <th>OMSCHRIJVING</th>
              <th>EH</th>
              <th>HVH</th>
              <th>TOTAAL</th>
            </tr>
            </thead>
            <tbody>
            @php
              function loop($posten, $begroting){
                foreach($posten as $post){
                  $data = json_decode($post->value);
                  $cls = !isset($post->parent_id) ? 'post-row' : (count($post->subposten) ? 'post-row-nd' : '');

                  if($begroting == "prijzen_per_post"){
                      if(isset($post->parent_id)){continue;}
                  }

                  echo "<tr>
                        <td class='$cls' >".($data->post ?? '')."</td>
                        <td class='$cls' >".($data->omschrijving ?? '')."</td>
                        <td class='$cls' >".($data->eenheid ?? '')."</td>
                        <td class='$cls' >".($data->aantal ?? '')."</td>
                        <td class='$cls' >".number_format(($data->totaal ?? 0), 2, ',', '.')."</td>
                      </tr>";

                  loop($post->subposten, $begroting);
                }
              }
              loop($posten["posten"] ?? [], ($offerteteksten["openBegroting"] ?? ''));
            @endphp
            </tbody>
          </table>
      @endif

      @if($saneringExists)
        <div class="my-40">
          <table class="sloop-table" >
            <thead>
            <tr>
              <th>Titel</th>
              <th>Aantal</th>
              <th>Prijs</th>
              <th>Totaal</th>
            </tr>
            </thead>
            <tbody>
            @foreach($offerte->details as $detail)
              @if($detail->regel->active == "0") @continue @endif
              @if($detail->keyword == "binnensanering" || $detail->keyword == "buitensanering" || $detail->keyword == "bodemsanering")
                @php $tarieven = json_decode($detail->values['tarief']->datasets ?? '[]'); @endphp

                <tr class="post-row" >
                  <td><b>{{$detail->title}}</b></td>
                  <td class="center"><b>{{number_format($detail->regel->aantal, 2, ",", ".")}}</b></td>
                  <td>
                    <table class="content-border-0" >
                      <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >@if($offerte->inclBtw == "1")<b>{{number_format(($detail->regel->prijs / (100 + $detail->regel->btw) * 100), 2, ",", ".")}}</b>@else<b>{{number_format($detail->regel->prijs, 2, ",", ".")}}</b>@endif</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                  <td>
                    <table class="content-border-0" >
                      <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >@if($offerte->inclBtw == "1")<b>{{number_format(($detail->regel->prijs / (100 + $detail->regel->btw) * 100), 2, ",", ".")}}</b>@else<b>{{number_format($detail->regel->prijs, 2, ",", ".")}}</b>@endif</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>

                @foreach($tarieven as $tarief)
                  @php
                    $setItem = json_decode($tarief->value);
                    if($offerte->inclBtw == "1"){
                          $total = (($setItem->Prijs ?? 0) * $tarief->aantal) / (100 + $detail->regel->btw) * 100;
                      }
                      else{
                          $total = ($setItem->Prijs ?? 0) * $tarief->aantal;
                      }
                    $prijs = $tarief->prijs->value ?? 0;
                    $aantal = $tarief->aantal->value ?? 0;
                  @endphp
                  <tr>
                    <td>{{$setItem->Omschrijving ?? ''}}</td>
                    <td class="center" >{{$tarief->aantal}}</td>
                    <td>
                      <table class="content-border-0" >
                        <tbody>
                          <tr>
                            <td>€</td>
                            <td class="text-right" >{{number_format($setItem->Prijs, 2, ",", ".")}}</td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                    <td>
                      <table class="content-border-0" >
                        <tbody>
                          <tr>
                            <td>€</td>
                            <td class="text-right" >{{number_format($total, 2, ",", ".")}}</td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                @endforeach
              @endif
            @endforeach
            </tbody>
          </table>
        </div>
      @endif

      @if(count($offerte->regels))
        <div class="my-40">
          <table class="sloop-table">
            <thead>
            <tr>
              <th>Titel</th>
              <th>Aantal</th>
              <th>Prijs</th>
              <th>Totaal</th>
            </tr>
            </thead>
            <tbody>
              @foreach($offerte->regels as $regel)
                @if($regel->active == 0) @continue @endif
                <tr>
                  <td><b>{{$regel->tekst}}</b></td>
                  <td class="center"><b>{{number_format($regel->aantal, 2, ",", ".")}}</b></td>
                  <td>
                    <table class="content-border-0" >
                      <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >@if($offerte->inclBtw == "1")<b>{{number_format(($regel->stukprijs / (100 + $regel->btw) * 100), 2, ",", ".")}}</b>@else<b>{{number_format($regel->stukprijs, 2, ",", ".")}}</b>@endif</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                  <td>
                    <table class="content-border-0" >
                      <tbody>
                        <tr>
                          <td>€</td>
                          <td class="text-right" >@if($offerte->inclBtw == "1")<b>{{number_format(($regel->totaalprijs / (100 + $regel->btw) * 100), 2, ",", ".")}}</b>@else<b>{{number_format($regel->totaalprijs, 2, ",", ".")}}</b>@endif</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      @endif
    @endif
  </section>
</main>
</body>
</html>
