<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{{$offerte->offertenummer}}</title>
    <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&display=swap" rel="stylesheet">

    <style>
        @page {
            size: A4;
            width: 21cm;
            height: 29.7cm;
            margin: 250px 80px 50px;
            /*padding: 0 200px;*/
        }

        /*Global*/
        * {
            font-family: Quicksand;
            color: #333333;
            font-size: 16px;
            line-height: .9;
            font-weight: 500;
        }


        figure.table{
            margin: 0;
        }
        ul{
            padding: 0 0 0 1.25rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }
        p{
            margin-top: 0;
            margin-bottom: 1rem;
        }
        b{
            font-weight: 700;
            font-size: 1.05rem;
        }

        /*Header*/
        header{
            /*width: 21cm;*/
            height: 225px;
            width: 100%;
            background-color: black;
            margin: -250px -80px 0;
            padding: 0 80px;
            position: fixed
        }
        header td{
            padding: 10px 0;
        }
        header *{
            color: white!important;
            line-height: .8;
        }

        /*Pages*/
        .px{
            padding-left: 1rem!important;
            padding-right: 1rem!important;
        }
        .py{
            padding-top: 1rem!important;
            padding-bottom: 1rem!important;
        }

        .mx{
            margin-left: 2rem!important;
            margin-right: 2rem!important;
        }
        .my{
            margin-top: 2rem!important;
            margin-bottom: 2rem!important;
        }

        /*Details Table*/
        .details-table{
            margin: 2rem 0;
        }
        .details-table tbody:first-of-type{
            border-top: 2px solid #0B6380;
            border-bottom: 2px solid #0B6380;
        }
        .details-table th{
            text-align: left;
            font-weight: 700;
            padding: 7px 10px;
        }
        .details-table td{
            padding: 5px 10px;
        }
        .details-table .subtext *{
            font-size: .75rem;
            color: rgba(0, 0, 0, .5);
            margin: 0;
        }
        .details-table .euro{
            margin-right: .5rem;
        }

        /*Algemeene voorwaarden*/
        .algemene-voorwaarden{
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .algemene-voorwaarden *{
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
        }

        /*Footer*/
        .footer{
            position: absolute;
            bottom: -50px;
            left: -80px;
            width: 100%;
        }

        .footer .footer-circle-container{
          width: 100%;
          padding: 0 80px;
          position: relative;
          z-index: 1;
        }
        .footer .footer-circle{
          background-color: black;
          position: absolute;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
          border-radius: 100rem;

          padding: .5rem;

        }

        .footer .footer-img-container{
          position: relative;
          z-index: 0;
        }
        .footer .footer-img-container .red-cover{
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          background-color: rgba(255, 0, 0, 0.4);
          padding: 0 80px;
        }
        .footer .footer-img-container img{
            width: 21.05cm;
            height: auto;
        }

        /*Ondertekening*/
        .ondertekening-header-table .text-title{
            font-size: 4rem;
            font-weight: 400;
        }
        .ondertekening-table{
            table-layout: fixed;
            margin-top: 4rem;
        }
        .ondertekening-table .handtekening-td{
            height: 200px;
            border-bottom: 2px solid #0B6380;
            vertical-align: bottom;
        }
        .ondertekening-table .handtekening-td img{
            width: 100%;
        }

    </style>
</head>
<body>
<header>
    <table>
        <tr>
            <td></td>
            <td class="text-right" >
                <img src="{{url('client/public/img/pdf/82template1/logo_dwm_w500.png')}}" height="100" >
            </td>
        </tr>
        <tr>
            <td>
                <div><b>Drone with a Mission</b></div>
                <div>{{$offerte->template->_bv->straat}} {{$offerte->template->_bv->huisnummer}}</div>
                <div>{{$offerte->template->_bv->postcode}} {{$offerte->template->_bv->plaats}}</div>
                <div>{{$offerte->template->_bv->website}}</div>
            </td>
            <td class="text-right" >
                <div><b>Datum:</b> {{Carbon($offerte->datum)->format('d/m/Y')}}</div>
                <div><b>Geldig tot:</b> {{Carbon($offerte->geldig_tot)->format('d/m/Y')}}</div>
                <div><b>Klantnummer:</b> {{$klant->debiteurnummer}}</div>
            </td>
        </tr>
    </table>
</header>
<main>

    <section>

        {{--Text--}}
        <table>
            <tr>
                <td>
                    <div class="nobr" ><b>T.A.V. {{$offerte->_contactpersoonNaam()}},</b></div>
                    <div class="nobr" >{{$klant->naam}}</div>
                    <div class="nobr" >{{$offerte->_locatie()->straat}} {{$offerte->_locatie()->huisnummer}}{{$offerte->_locatie()->toevoeging}},</div>
                    <div class="nobr" >{{$offerte->_locatie()->postcode}} {{$offerte->_locatie()->plaats}}</div>
                </td>
                <td class="text-right" >
                    <div>Onderwerp: {{$offerteteksten['onderwerp'] ?? ''}}</div>
                    <div>Offertenummer: {{$offerte->offertenummer}}</div>
                </td>
            </tr>
        </table>
        {{--Inleiding--}}
        @if($offerteteksten['inleiding'] ?? null)
            <div class="my" >
                <div><p><b>Geachte {{$offerte->_contactpersoonNaam()}}</b></p></div>
                <div>{!! $offerteteksten['inleiding']  !!}</div>
            </div>
        @endif

        {{--Diensten--}}
        @if($offerteteksten['onze_diensten'] ?? null)
            <div class="my" >
                <div><b>Onze diensten</b></div>
                <div>{!! $offerteteksten['onze_diensten']  !!}</div>
            </div>
        @endif

        {{--Beperkingen--}}
        @if($offerteteksten['beperkingen_en_omstandigheden'] ?? null)
            <div class="my" >
                <div><b>Beperkingen en omstandigheden</b></div>
                <div>{!! $offerteteksten['beperkingen_en_omstandigheden']  !!}</div>
            </div>
        @endif

        {{--Beperkingen--}}
        @if($offerteteksten['gecertificeerd_dronebedrijf'] ?? null)
            <div class="my" >
                <div><b>Gecertificeerd dronebedrijf</b></div>
                <div>{!! $offerteteksten['gecertificeerd_dronebedrijf']  !!}</div>
            </div>
        @endif

        {{--Conclusie--}}
        @if($offerteteksten['conclusie'] ?? null)
            <div class="my" >
                <div><b>Conclusie</b></div>
                <div>{!! $offerteteksten['conclusie']  !!}</div>
            </div>
        @endif
        <div>
            <div>Met vriendelijke groet,</div>
            <div><b>{{$user}}</b></div>
        </div>
        <div class="page-break"></div>

        {{--Details--}}
        <table>
            <tr>
                <td>
                    <div><b>T.A.V. {{$offerte->_contactpersoonNaam()}},</b></div>
                    <div>{{$klant->naam}}</div>
                    <div>{{$offerte->_locatie()->straat}} {{$offerte->_locatie()->huisnummer}}{{$offerte->_locatie()->toevoeging}},</div>
                    <div>{{$offerte->_locatie()->postcode}} {{$offerte->_locatie()->plaats}}</div>
                </td>
                <td class="text-right" >
                    <div>Onderwerp: {{$offerteteksten['onderwerp'] ?? ''}}</div>
                    <div>Offertenummer: {{$offerte->offertenummer}}</div>
                </td>
            </tr>
        </table>
        <table class="details-table" >
            <tbody>
                <tr>
                    <th>Omschrijving</th>
                    <th>Aantal</th>
                    <th>P.P.E.</th>
                    <th>Totaal</th>
                </tr>
                @foreach($offerte->details->where('keyword', '3d_mapping') as $detail)
                    @foreach($detail->values['woningen']->datasets as $dataset_index => $dataset)
                        @php
                            $ppe = floatval($dataset->jsonValue('ppe', 0));
                            $total = floatval($dataset->jsonValue('prijs', 0));

                            if($dataset->marge){
                                $ppe += $ppe * (floatval($dataset->marge) / 100);
                                $total += $total * (floatval($dataset->marge) / 100);
                            }

                            //Apply the adjustment only to the first dataset in case multiple were added
                            if(!$dataset_index && $detail->adjustment){
                                $ppe += floatval($detail->adjustment) / intval($dataset->jsonValue('aantal'));
                                $total += floatval($detail->adjustment);
                            }

                        @endphp
                        <tr>
                            <td>
                                <div>{{$detail->title}}</div>
                                <div class="subtext" >{!! $detail->description !!}</div>
                            </td>
                            <td>{{$dataset->jsonValue('aantal')}}</td>
                            <td>
                                <table class="content-p-0" >
                                    <tr>
                                        <td><span class="euro">&euro;</span></td>
                                        <td class="text-right" >{{number_format($ppe, 2, ',', '.')}}</td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table class="content-p-0" >
                                    <tr>
                                        <td><span class="euro">&euro;</span></td>
                                        <td class="text-right" >{{number_format($total, 2, ',', '.')}}</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    @endforeach
                @endforeach
                @foreach($offerteregels as $regel)
                    @if(!$regel->aantal || !$regel->stukprijs) @continue @endif

                    <tr>
                        <td>{{$regel->tekst}}</td>
                        <td>{{$regel->aantal}}</td>
                        <td>
                            <table class="content-p-0" >
                                <tr>
                                    <td><span class="euro">&euro;</span></td>
                                    <td class="text-right" >{{number_format($regel->stukprijs, 2, ',', '.')}}</td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table class="content-p-0" >
                                <tr>
                                    <td><span class="euro">&euro;</span></td>
                                    <td class="text-right" >{{number_format($regel->totaalprijs, 2, ',', '.')}}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                @endforeach
            </tbody>
            <tbody>

                {{--Spacer--}}
                <tr><td colspan="4" class="py" ></td></tr>

                <tr>
                    <td colspan="3" class="text-right" >SUBTOTAAL</td>
                    <td>
                        <table class="content-p-0" >
                            <tr>
                                <td><span class="euro">&euro;</span></td>
                                <td class="text-right" >{{number_format($offerte->totaal()->excl, 2, ',', '.')}}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" class="text-right" >BTW</td>
                    <td>
                        <table class="content-p-0" >
                            <tr>
                                <td><span class="euro">&euro;</span></td>
                                <td class="text-right" >{{number_format($offerte->totaal()->btw, 2, ',', '.')}}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" class="text-right" ><b>TOTAAL</b></td>
                    <td>
                        <table class="content-p-0" >
                            <tr>
                                <td><b><span class="euro">&euro;</span></b></td>
                                <td class="text-right" ><b>{{number_format($offerte->totaal()->incl, 2, ',', '.')}}</b></td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </tbody>

        </table>
        @if($opmerkingen->count())
            <div class="my">
                @foreach($opmerkingen as $opmerking)
                    <div>{{$opmerking->tekst}}</div>
                @endforeach
            </div>
        @endif

        <div class="algemene-voorwaarden" >
            <a href="{{url('api/file/explorer/files/jemu26fs4vlqvxdtr4i7r5a8zamlr66v808.pdf')}}" target="_blank" >Algemene voorwaarden</a> zijn van toepassing
        </div>

        <div class="page-break"></div>

        {{--Handtekening--}}

        <div class="my center">Voor akkoord:</div>
        <table class="ondertekening-table" >

            {{--Signatues--}}
            <tr>
                <td colspan="2" class="handtekening-td" >
                    @if($offerte->user->handtekening)
                        <img class="signature-img" src="{{url('client/public/img/handtekeningen/'.$offerte->user->handtekening)}}">
                    @endif
                </td>
                <td></td>
                <td colspan="2" class="handtekening-td" >
                    @if($offerte->handtekening)<img class="signature-img" src="{{url('client/public/img/handtekeningen/'.$offerte->handtekening)}}">@endif
                </td>
            </tr>

            {{--Names--}}
            <tr>
                <td colspan="2">
                    <div>{{$user}}</div>
                    <div><b>Drone with a mission</b></div>
                </td>
                <td></td>
                <td colspan="2">
                    <div>{{$contactpersoon}}</div>
                    <div><b>{{$klant->naam}}</b></div>
                </td>
            </tr>
        </table>
        <div class="footer" >

            <div class="footer-circle-container">

              <div class="footer-circle" >
                <img src="{{url('client/public/img/pdf/82template1/logo_dwm_w250_square.png')}}" height="100" >
              </div>

            </div>

            <div class="footer-img-container">
              <img src="{{url('client/public/img/pdf/82template1/footer_2_comp.png')}}" height="80" >
              <div class="red-cover"></div>
            </div>

        </div>

    </section>

</main>
</body>
</html>
