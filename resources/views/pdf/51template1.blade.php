<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$offerte->offertenummer ?? ''}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 80%;
      margin: 170px 10% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      margin-left: 47.8px;
      font-family: sans-serif;
      font-size: 13px;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      width: 800px;
      top: -170px;
      left: -80px;
      right: 0px;
      height: 150px;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: -130px;
      z-index: -1;
      width: 80%;
      font-size: 10px;
    }

    #pageCounter {
      position: fixed;
      right: 0;
      top: -118px;
      z-index: -1;
      font-size: 13px;
      font-weight: bold;
    }

    #pageCounter .counter:after {
      content: counter(page, decimal);
    }

    .sidebarLeft {
      position: fixed;
      top: -190px;
      left: -80px;
      width: 120px;
    }

    table {
      page-break-inside: auto;
    }

    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

    .info{
      margin-top: -50px;
      width: 100%;
      font-size: 12px;
    }

    .slot table td, .slot table th {
      padding: 0;
    }

    .slot figure{
      margin: 0;
    }

    .detail-table tr{
      line-height: 0.6;
    }

    ul {
      margin-left: 10px;
      padding-left: 10px;
    }

    li {
      margin-left: 0;
      padding-left: 5px;
    }

  </style>
</head>
<header>
  <img src="{{url("/client/public/img/pdf/51template1/headerHaltronic.png")}}" width="100%" height="100%"/>
</header>
<div id="pageCounter">{{$offerte->offertenummer}}, Blad <span class="counter"></span></div>
<footer class="center">
  {!! $offerteteksten['footer'] !!}
</footer>
<body>
<main>
  <div class="sidebarLeft">
    <img src="{{url("/client/public/img/pdf/51template1/sidebar.png")}}" width="100%" style="margin-top: 7.5cm;"/>
  </div>
  <section class="w100 ">
    <table class="table-nopad info mt-2">
      <tr><td colspan="2">{{$offerte->template->_bv->straat ?? ''}} {{$offerte->template->_bv->huisnummer ?? ''}} {{$offerte->template->_bv->toevoeging ?? ''}}</td></tr>
      <tr><td colspan="2">{{$offerte->template->_bv->postcode ?? ''}} {{$offerte->template->_bv->plaats ?? ''}}</td></tr>
      <tr><td colspan="2">Tel: {{$offerte->template->_bv->telefoon ?? ''}}</td></tr>
      <tr><td colspan="2">{{$offerte->template->_bv->website ?? ''}}</td></tr>
      <tr><td colspan="2">Rabobank 1377.58.995</td></tr>
      <tr>
        <td class="w55">SWIFT-adres: {{$offerte->template->_bv->bic ?? ''}}</td>
        <td>{{$klant->title()}}</td>
      </tr>
      <tr>
        <td>IBAN: {{$offerte->template->_bv->iban ?? ''}}</td>
        <td>
          T.a.v.
          @if(isset($offerte->contactpersoon))
            {{$offerte->contactpersoon->titel}} {{$offerte->contactpersoon->voornaam}} {{$offerte->contactpersoon->achternaam}}
          @else
            {{$klant->contactpersoon_titel}} {{$klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam}}
          @endif
        </td>
      </tr>
      <tr>
        <td>KVK: {{$offerte->template->_bv->kvk ?? ''}}</td>
        <td>{{$offerte->_locatie()->straat}} {{$offerte->_locatie()->huisnummer}}{{$offerte->_locatie()->toevoeging}}</td>
      </tr>
      <tr>
        <td>BTW: {{$offerte->template->_bv->btw ?? ''}}</td>
        <td>{{$offerte->_locatie()->postcode}} {{$offerte->_locatie()->plaats}}</td>
      </tr>
    </table>
    <table class="my-20">
      <tr>
        <th colspan="4" class="font-size-12 text-left pl-0">Offerte</th>
      </tr>
      <tr>
        <th class="border-bottom text-left pl-0">Project</th>
        <th class="border-bottom text-left pl-0">Referentie</th>
        <th class="border-bottom text-left pl-0">Offertenummer</th>
        <th class="border-bottom text-left pl-0">Datum</th>
      </tr>
      <tr>
        <td class="pl-0">{{$offerte->projectTemp->projectnr ?? ''}}</td>
        <td class="pl-0">{{$referentie = $offerte->projectTemp->custom->where('keyword', 'referentie')->first()->value ?? ''}}</td>
        <td class="pl-0">{{$offerte->offertenummer ?? ''}}</td>
        <td class="pl-0">{{Carbon($offerte->datum)->format('d-m-Y') ?? ''}}</td>
      </tr>
    </table>
    <div class="my-20">
      {!! $offerteteksten['inleiding'] !!}
    </div>
    @if (countObject(json_decode($offerteteksten["uitgesloten"] ?? '[]')))
    <div class="my-20">
      <label>Uitgesloten:</label>
        @foreach(json_decode($offerteteksten["uitgesloten"] ?? '[]') as $listItem)
          <p class="my-0">- {{$listItem->value}}</p>
        @endforeach
    </div>
    @endif

    <div class="my-20">
      {!! $offerteteksten['algemeen'] !!}
    </div>

    @foreach ($offerte->details as $d)
      @php
        $sumMontageTijd = [];
        $products = [];
        $datasets = [];
      @endphp

      @foreach ($d->values ?? [] as $val)
        @if($val->type == '2ba_sum_montage_tijd')
          @php
            $tweebaSumValues = json_decode($val->value);
            $sumMontageTijd[] = [
                'amount' => $tweebaSumValues->tijd + $tweebaSumValues->extraTijd,
                'unit' => 'Uur',
                'description' => $tweebaSumValues->name
            ];
          @endphp
        @elseif($val->type == '2ba_products')
          @foreach ($val->tweeba_products as $product)
            @php
              $products[] = [
                  'amount' => $product->amount,
                  'unit' => $product->unit ?? 'st',
                  'description' => $product->description
              ];
            @endphp
          @endforeach
        @elseif($val->type == 'dataset')
          @foreach ($val->datasets as $ds)
            @php
              $dsval = json_decode($ds->value);
              $datasets[] = [
                  'amount' => $ds->aantal,
                  'unit' => $dsval->eenheid ?? 'st',
                  'description' => $dsval->omschrijving ?? $dsval->naam
              ];
            @endphp
          @endforeach
        @endif
      @endforeach
      <table class="detail-table avoid-break">
        <thead>
          <tr>
            <th colspan="4" class="text-left">{{$d->title}}</th>
          </tr>
        </thead>
        <tbody>
        @foreach ($sumMontageTijd as $item)
          <tr>
            <td>{{$item['amount']}}</td>
            <td>{{$item['unit']}}</td>
            <td>{{$item['description']}}</td>
            <td></td>
          </tr>
        @endforeach

        @foreach ($products as $item)
          <tr>
            <td>{{$item['amount']}}</td>
            <td>{{$item['unit']}}</td>
            <td>{{$item['description']}}</td>
            <td></td>
          </tr>
        @endforeach

        @foreach ($datasets as $item)
          <tr>
            <td>{{$item['amount']}}</td>
            <td>{{$item['unit']}}</td>
            <td>{{$item['description']}}</td>
            <td></td>
          </tr>
        @endforeach
        </tbody>
        <tfoot>
          <tr>
            <th colspan="3" class="text-left border-bottom">Totaal {{$d->title}}</th>
            <th class="center nobr border-bottom">€ {{number_format($offerte->inclBtw ? ($d->regel->totaal * (1 + $d->regel->btw/100)) : $d->regel->totaal, 2, ',', '.')}}</th>
          </tr>
        </tfoot>
      </table>
    @endforeach

    <div class="page-break"></div>
    @if (countObject(json_decode($offerteteksten["niet_opgenomen"] ?? '[]')))
    <div class="my-20">
      <label>Niet in de offerte opgenomen:</label>
        @foreach(json_decode($offerteteksten["niet_opgenomen"] ?? '[]') as $listItem)
        <p class="my-0">- {{$listItem->value}}</p>
        @endforeach
    </div>
    @endif

    @php
      $offerteteksten['slot'] = str_replace('&lt;{geldigheid}&gt;', Carbon($offerte->datum)->diffInDays(Carbon($offerte->geldig_tot)->addDay()), $offerteteksten['slot']);
    @endphp
    <div class="my-20 slot avoid-break">
      {!! $offerteteksten['slot'] !!}
    </div>

    <table class="my-2" style="border: none; vertical-align: bottom" >
      <tr style="background-color: transparent" >
        <td style="width: 40%; border-bottom: 1px solid" >
            @if(isset($handtekening))
              <img style="width: 150px" src="http://infordb.ikbentessa.nl/client/public/img/handtekeningen/{{$handtekening}}">
            @endif
        </td>
        <td style="width: 10%;" ></td>
        <td style="width: 40%; border-bottom: 1px solid" >
          @if(isset($offerte->handtekening))
            <img style="width: 150px" src="http://infordb.ikbentessa.nl/client/public/img/handtekeningen/{{$offerte->handtekening}}">
          @endif
        </td>
      </tr>
      <tr style="background-color: transparent" >
        <td style="width: 40%" ><span class="w100">{{$offerte->template->_bv->name}}<br>{{$user}}</span></td>
        <td style="width: 10%;" ></td>
        <td style="width: 40%" ><span class="w100">{{$klant->title()}}</span></td>
      </tr>
    </table>

    <table class="my-2">
      <tr>
        <td class="border-top text-left">Totaal excl</td>
        <td class="border-top text-right">€ {{number_format($offerte->totaal()->excl, 2, ',', '.' )}}</td>
      </tr>
      <tr>
        <td class="tezt-left">BTW 21,0%</td>
        <td class="text-right">€ {{number_format($offerte->totaal()->btw, 2, ',', '.')}}</td>
      </tr>
      <tr>
        <th class="border-bottom text-left">Totaal incl</th>
        <th class="border-bottom text-right">€ {{number_format($offerte->totaal()->incl, 2, ',', '.')}}</th>
      </tr>
    </table>
  </section>
</main>
</body>
