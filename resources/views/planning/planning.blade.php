@extends('layouts.app')

@section('title', 'Inplannen')

@section('content')
    <div class="card my-3 @if(isset($_GET['hide_navbar'])) d-none @endif ">
        <section class="append-loader" data-no-margin >
            <div class="d-flex flex-wrap justify-content-between m-2">

                <div class="overflow-auto " >

                    <div class="d-flex flex-md-nowrap flex-wrap">

                      <div class="mr-2">
                        <div class="flex-align form-control-custom my-1 p-05 rounded-10 w-auto">

                          @if($type != 'periode')
                            <a class="btn btn-light rounded-pill w-100 tippy" data-tippy-content="Vorige periode" onclick="movePeriod('previous')">@icon_arrow_left</a>
                            <div class="form-control-divider mx-2" ></div>
                          @endif

                            <select class="form-control-plaintext p-0 mx-2 w-px-100" data-period-type >
                                <option @if($type == "dag") selected @endif value="dag">Dag</option>
                                <option @if($type == "week") selected @endif value="week">Week</option>
                                <option @if($type == "maand") selected @endif value="maand">Maand</option>
                                <option @if($type == "kwartaal") selected @endif value="kwartaal">Kwartaal</option>
                                <option @if($type == "jaar") selected @endif value="jaar">Jaar</option>
                                <option @if($type == "periode") selected @endif value="periode">Periode</option>
                                <option @if($type == "komende30") selected @endif value="komende30">Komende 30 dagen</option>
                                <option @if($type == "60dagen") selected @endif value="60dagen">30 dagen terug en vooruit</option>
                            </select>

                            <div class="form-control-divider mx-2" ></div>

                            {{--Dag--}}
                            <input class="form-control-plaintext w-px-125 p-0 mx-2 d-none" data-period-start="dag" type="date"  value="{{$period->start->format('Y-m-d')}}" >

                            {{--week--}}
                            <select class="form-control-plaintext w-px-75 p-0 mx-2 d-none" data-period-start="week" >
                                @for($i = 1; $i <= 52; $i++)
                                    <option @if($period->start->week == $i) selected @endif @if(Carbon()->now()->week == $i) class="font-weight-bold" @endif value="{{$i}}">{{$i}}</option>
                                @endfor
                            </select>

                            {{--Maand--}}
                            <select class="form-control-plaintext w-px-125 p-0 mx-2 d-none" data-period-start="maand" >
                                @foreach(getMaanden() as $i => $maand)
                                    <option @if($period->start->month == $i) selected @endif @if(Carbon()->now()->month == $i) class="font-weight-bold" @endif value="{{$i}}">{{$maand}}</option>
                                @endforeach
                            </select>

                            {{--Kwartaal--}}
                            <select class="form-control-plaintext w-px-100 p-0 mx-2 d-none" data-period-start="kwartaal" >
                                <option @if($period->start->month == 1) selected @endif @if(Carbon()->now()->quarter == 1) class="font-weight-bold" @endif value="1">Eerste</option>
                                <option @if($period->start->month == 4) selected @endif @if(Carbon()->now()->quarter == 2) class="font-weight-bold" @endif value="4">Tweede</option>
                                <option @if($period->start->month == 7) selected @endif @if(Carbon()->now()->quarter == 3) class="font-weight-bold" @endif value="7">Derde</option>
                                <option @if($period->start->month == 10) selected @endif @if(Carbon()->now()->quarter == 4) class="font-weight-bold" @endif value="10">Vierde</option>
                            </select>

                            {{--Jaar--}}
                            <select class="form-control-plaintext w-px-75 p-0 mx-2 d-none" data-period-start="jaar" >
                                @for($i = Carbon()->now()->year - 1; $i <= Carbon()->now()->year + 1; $i++)
                                    <option @if($period->start->year == $i) selected @endif @if(Carbon()->now()->year == $i) class="font-weight-bold" @endif value="{{$i}}">{{$i}}</option>
                                @endfor
                            </select>

                            {{--periode--}}
                            <input class="form-control-plaintext w-px-125 p-0 mx-2 d-none" data-period-start="periode" type="date"  value="{{$period->start->format('Y-m-d')}}" >
                            <span data-period-divider class="d-none" >-</span>
                            <input class="form-control-plaintext w-px-125 p-0 mx-2 d-none" data-period-end type="date"  value="{{$period->end->format('Y-m-d')}}" >

                            <div class="form-control-divider mx-2" ></div>

                            <a data-period-search class="text-primary cursor-pointer ml-2 mr-3 p-1" >@icon_zoom</a>

                            @if($type != 'periode')
                              <div class="form-control-divider mx-2" ></div>
                              <a class="btn btn-light rounded-pill w-100 tippy" data-tippy-content="Volgende periode" onclick="movePeriod('next')">@icon_arrow_right</a>
                            @endif

                        </div>
                      </div>

                        <div class="select_multiple-container min-w-150 my-1" data-rounded data-placeholder="Weergave"  >
                            <label class="select_multiple-value" >Tijd <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Tijd" value="tijd" @isset($_COOKIE['planning_info_display_tijd']) checked @endif ></label>
                            <label class="select_multiple-value" >Activiteit <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Activiteit" value="activiteit" @isset($_COOKIE['planning_info_display_activiteit']) checked @endif ></label>
                            <label class="select_multiple-value" >Klant <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Klant" value="klant" @isset($_COOKIE['planning_info_display_klant']) checked @endif ></label>
                            @if(getSettingValue("planning_adres") == "Aan")
                                <label class="select_multiple-value" >Adres <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Adres" value="adres" @isset($_COOKIE['planning_info_display_adres']) checked @endif ></label>
                            @endif
                            @if(getSettingValue('planning_machines') == 'Aan' || getSettingValue('planning_machines') == 'pp')
                                <label class="select_multiple-value" >Machines <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Machines" value="machines" @isset($_COOKIE['planning_info_display_machines']) checked @endif ></label>
                            @endif
                            <label class="select_multiple-value" >Taken <input type="checkbox" class="d-none" name="planning_display_info[]" data-name="Taken" value="taken" @isset($_COOKIE['planning_info_display_taken']) checked @endif ></label>
                        </div>
                    </div>

                </div>
                <div class="overflow-auto" >
                    <div>
                        <div class="overflow-auto my-1">
                            <nobr class="d-flex ">
                                @if($projectId)
                                    <a data-tippy-content="Excel export" class="ml-1 btn btn-primary rounded-10 v-center tippy" href="{{url('planning/project/export/' . $projectId)}}" > <i class="fa-solid fa-table"></i> </a>
                                @endif
                                @if(Carbon()->now()->isBetween($period->start, $period->end) && !$full && $type != 'dag')
                                    <a data-tippy-content="Volledige planning" class="ml-1 btn btn-primary rounded-10 v-center tippy" href="{{url()->current()}}?full=true" > <i class="fa-solid fa-calendar-week mx-0"></i> </a>
                                @endif
                                @if($type == 'dag')
                                  <a data-tippy-content="Dagplanning export" class="ml-1 btn btn-primary rounded-10 v-center tippy" data-toggle-nodes-direction href="{{url('planning/dagplanning/pdf')}}/{{$period->start->format('Y-m-d')}}" target="_blank"> <i class="fa-solid fa-file-export mx-0 d-inline-block "></i></a>
                                @endif
                                @if($type != 'dag')
                                    <a data-tippy-content="Blokken weergave" class="ml-1 btn btn-primary rounded-10 v-center tippy" data-toggle-nodes-direction > <i class="fa-solid fa-right-left mx-0 d-inline-block rotate-315"></i></a>
                                @endif
                                @if(hasModule('Beschikbaarheid') && Auth::user()->hasPermissionTo("Planning bewerken"))
                                    <a data-tippy-content="Beschikbaarheid weergeven" id="showBeschikbaarheid" class="ml-1 btn btn-primary rounded-10 v-center tippy" onclick="showBeschikbaarheid(true)" ><i class="fa-solid fa-calendar-day mx-0"></i></a>
                                    <a data-tippy-content="Beschikbaarheid verbergen" id="hideBeschikbaarheid" class="ml-1 btn btn-danger d-none rounded-10 v-center tippy" onclick="showBeschikbaarheid(false)" ><i class="fa-solid fa-calendar-day mx-0"></i></a>
                                @endif
                                @if(!isset($preview))
                                    <a data-tippy-content="Beschikbaarheid" class="ml-1 btn btn-primary rounded-10 v-center tippy" data-toggle="modal" data-target="#routeModal"><i class="fa-solid fa-location-dot mx-0"></i></a>
                                    <a data-tippy-content="Legenda" class="ml-1 btn btn-primary tippy rounded-10 v-center tippy" data-toggle="modal" data-target="#legendaModal"> <i class="fa-solid fa-list mx-0"></i></a>
                                @endif
                            </nobr>
                        </div>
                    </div>

                  @if(!isset($preview))
                    @if(getSettingValue('planning_google_hide') != 'ja')
                      <div class="my-2">
                        @if(isset(Auth::user()->google_info) && isset(Auth::user()->google_token))
                          @php $google_user = json_decode(Auth::user()->google_info); @endphp
                          <div class="border rounded overflow-hidden" >
                            <div class="p-2 d-flex justify-content-between align-items-center">
                              <div class="mr-5 d-flex justify-content-between align-items-center" >
                                <div>
                                  <img height="40" class="rounded mr-2" src="{{$google_user->picture}}">
                                </div>
                                <div>
                                  <small class="d-block">{{$google_user->name}}</small> <small class="d-block">{{$google_user->email}}</small>
                                </div>
                              </div>
                              <div class="align-self-center" >
                                <a href="https://calendar.google.com/calendar" target="_blank" ><img height="40" alt="Google_Agenda" class="hoverSelect" src="{{url('client/public/img/google/google_agenda.png')}}" ></a>
                                <a href="{{url('/unlink')}}" class="btn btn-outline-danger"><i class="fas fa-sign-out-alt m-0"></i></a>
                              </div>
                            </div>
                            <div class="d-flex" style="height: 5px">
                              <div class="w-100" style="background-color: #E3271D" ></div>
                              <div class="w-100" style="background-color: #F9B002" ></div>
                              <div class="w-100" style="background-color: #1C9F39" ></div>
                              <div class="w-100" style="background-color: #2779F2" ></div>
                            </div>
                          </div>
                        @else
                          <div class="cursor-pointer flex-align form-control google_sync hover-mark px-2 py-1 rounded-10 w100" >
                            <img height="20" alt="Google_SignIn" class="mr-2" src="{{url('client/public/img/google/google_logo.png')}}" >
                            <span class="m-0 text-muted" >Sign in with Google</span>
                          </div>
                        @endif
                      </div>
                    @endif
                  @endif
                </div>
            </div>
        </section>
    </div>

    <div class="card my-3">
        {{--    Google & buttons--}}


        {{--    Table--}}
        <section>
            <div class="flex-between m-2 @if(isset($_GET['hide_date_select'])) d-none @endif" >
                @if($type == 'dag')
                    <div class="d-flex">
                        <h3 class="mr-2" >{{$period->start->format("d")}} {{getMaanden()[$period->start->format("n")]}}</h3>
                        <h6>{{$period->start->format("Y")}}</h6>
                    </div>
                @else
                    <div class="d-flex align-items-center my-2">
                        <div class="d-flex">
                            <h3 class="mr-2" >{{$period->start->format("d")}} {{getMaanden()[$period->start->format("n")]}}</h3>
                            <h6>{{$period->start->format("Y")}}</h6>
                        </div>
                        <div class="mx-2 opacity-50" ><h3>-</h3></div>
                        <div class="d-flex">
                            <h3 class="mr-2" >{{$period->end->format("d")}} {{getMaanden()[$period->end->format("n")]}}</h3>
                            <h6>{{$period->end->format("Y")}}</h6>
                        </div>
                    </div>
                @endif

                <div class="my-2">

                    <div class="mx--1 d-flex" >
                        @if(getSettingValue('planning_group_users_by') == 'vestiging')
                            <select class="form-select min-w-150 mx-1 rounded-pill" data-table-users-vestiging >
                                <option value="">Vestiging</option>
                                @foreach(getVestigingen() as $vestiging)
                                    <option value="{{$vestiging->id}}" @if(($_COOKIE['planning_table_vestiging'] ?? null) == $vestiging->id) selected @endif>{{$vestiging->naam ?? $vestiging->plaats}}</option>
                                @endforeach
                            </select>
                            <div class="form-control-divider mx-2" ></div>
                        @endif
                        <input class="form-control-custom mx-1 rounded-pill min-w-250" id="search" placeholder="Zoeken" >
                        <a class="btn btn-light mx-1 tt rounded-pill" onclick="reInitPlanning()" data-tippy-content="Planning vernieuwen" >@icon_redo</a>
                    </div>
                </div>
            </div>

            <div class="position-relative overflow-auto pb-4 planning-section d-none" id="main_table">
                <div class="align-items-center bg-inverse-dark d-flex h-100 justify-content-center planning-loader position-absolute w-100 z-index-99">
                    <div class="progress w-50" style="height: 12px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                </div>
                <table data-no-dt-customize="true" class="table w-100 @if($type == 'dag') table-fixed @endif" id="planningTable">
                    <thead>
                    <tr>
                        <th style="position: -webkit-sticky; position: sticky; left: 0; width: 100px" class="bg-white max-w-100" >Personeel</th>
                        @php $woy = $period->start->format("w"); @endphp
                        @foreach($period as $day)
                            @if($type == 'dag')
                                <th class="p-0" >
                                    <div class="d-flex p-2">
                                        @for($h=0;$h<24;$h++)
                                            @if(isset(json_decode(getSettingValue('dagoverzicht_active_uren'), true)[$h]))
                                              <div class="w-100 min-w-50" >
                                                  <span class="rotate-315 d-inline-block" >{{{str_pad($h, 2,0, STR_PAD_LEFT).":00"}}}</span>
                                              </div>
                                            @endif
                                        @endfor
                                    </div>
                                </th>
                            @else
                                <th style="vertical-align: bottom;" >
                                    <span class="d-block text-muted my-1" >@if($woy != $day->weekOfYear)Week: {{$day->weekOfYear}} @endif</span>
                                    <a
                                            @if(Auth::user()->hasPermissionTo("Dagplanning bekijken"))
                                                @if(routePrefix() == "iframe") href="{{url('iframe/planning/dagplanning/'.$day->format("Y-m-d"))}}" @else href="{{url('planning/dagplanning/index/'.$day->format("Y-m-d"))}}" @endif>
                                        @endif
                                        <span>{{substr($weekdagen[$day->dayOfWeek], 0, 2)}}</span> <span>{{$day->format("d-m")}}</span>
                                    </a>
                                  @if($type != 'dag')
                                  <div>
                                    @if(getSettingValue('vooruit_planning') == 'released' && Auth::user()->hasPermissionTo('Planning bewerken'))
                                    <span data-tippy-content="Dag planning vrijgeven" class="mt-2 mb--1 btn-primary rounded-2 p-2 d-inline-flex tippy" onclick="confirmVrijgeven('{{$day->format("Y-m-d")}}')"><i class="fa-solid fa-eye"></i></span>
                                    <span data-tippy-content="Taken afronden" class="mt-2 mb--1 btn-primary rounded-2 p-2 d-inline-flex tippy" onclick="confirmAfronden('{{$day->format("Y-m-d")}}')"><i class="fa-solid fa-check"></i></span>
                                    @endif
                                    @if(bladeExists("planning.pdf.C{client}dagplanning"))
                                    <a data-tippy-content="Dagplanning export" class="mt-2 mb--1 btn-primary rounded-2 p-2 d-inline-flex tippy" href="{{url('planning/dagplanning/pdf')}}/{{$day->format("Y-m-d")}}" target="_blank"> <i class="fa-solid fa-file-export mx-0 d-inline-block "></i></a>
                                    @endif
                                  </div>
                                  @endif
                                </th>
                            @endif
                            @php $woy = $day->weekOfYear; @endphp
                        @endforeach
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="planning-spinner text-center py-5 my-4">@spinner_large</div>
        </section>
    </div>
    @if(!isset($preview))
        @if(isset(json_decode(getSettingValue('planning_elementen'), true)['aanvragen']))
            <div class="card my-3">
                <div class="d-inline mx-3 pb-2 rounded"  style="background-image: linear-gradient(to bottom right, #ff660080, #ff6600)">
                    <span class="h4 bg-white">Aanvragen</span>
                </div>

                <div class="overflow-auto max-h-500" >
                    <table class="append-loader w-100" id="aanvragenTable">
                        <thead>
                        <th>Datum</th>
                        <th>Aanvraagnummer</th>
                        <th>Klant</th>
                        <th>BV</th>
                        <th>Methode</th>
                        <th>Status</th>
                        <th class="text-right">Acties</th>
                        </thead>
                        <tbody>
                        @foreach($aanvragen ?? [] as $row)
                            <tr class="trHover">
                                <td>{{\Carbon\Carbon::parse($row->datum)->format("d-m-Y")}}</td>
                                <td><a target="_blank" href="{{url("aanvragen/show/".$row->id)}}" >{{$row->aanvraagnummer}}</a></td>
                                <td>@if(isset($row->klant)){{$row->klant->naam ?? $row->klant->contactpersoon_voornaam." ".$row->klant->contactpersoon_achternaam}}@endif</td>
                                <td>{{$bvs[$row->bv]->name ?? ""}}</td>
                                <td>{{$row->methode}}</td>
                                <td>{{$row->status}}</td>
                                <td class="text-right" >
                                    @if(isset($row->planning))
                                        <a data-toggle="modal" data-target="#aanvragenInplannen" onclick="aanvraagInplannen({{$row->id}})" class="btn btn-success text-white tippy" data-tippy-content="
                      <div class='text-left' ><b class='mr-2' >{{Carbon()->parse($row->planning->datum)->format('d-m-Y')}}</b> {{Carbon()->parse($row->planning->begin)->format("H:i")}} - {{Carbon()->parse($row->planning->eind)->format("H:i")}}</div>
                    ">Ingepland</a>
                                    @else
                                        <a data-toggle="modal" data-target="#aanvragenInplannen" onclick="aanvraagInplannen({{$row->id}})" class="btn btn-primary text-white">Inplannen</a>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
        @if(isset(json_decode(getSettingValue('planning_elementen'), true)['projecten']))
            <div class="card my-3">
                <div class="flex-between m-2">
                    <h3 class="my-2 mx-2" >Projecten</h3>
                </div>

              <div class="overflow-auto max-h-500">
                <table class="append-loader w-100" id="projectenTable">
                  <thead>
                  <th>BV</th>
                  <th>Projectnummer</th>
                  <th>Opdrachtgever</th>
                  <th>Projectnaam</th>
                  @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen")) <th>Werkbon opvolgen</th> @endif
                  <th></th>
                  <th class="text-right">Acties</th>
                  </thead>
                  <tbody>
                  @foreach($projecten ?? [] as $row)
                    <tr class="trHover">
                      <td>{{$bvs[$row->bv]->name ?? ""}}</td>
                      <td>{{$row->projectnr}}</td>
                      <td>{{$row->opdrachtgever}}</td>
                      <td>{{$row->projectnaam}}</td>
                      @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen"))
                        <td>
                          @foreach($row->werkbonnen as $werkbon)
                            @if($werkbon->opvolgen)
                              <div>{{$werkbon->werkbonnummer}}</div>
                            @endif
                          @endforeach
                        </td>
                      @endif
                      <td class="center">
                        <div class="p-4 rounded d-inline-block cursor-pointer" onclick="projectColor({{$row->id}})" style="background-image: linear-gradient(to bottom right, {{$row->planning_color ?? "#169BD7"}}80, {{$row->planning_color ?? "#169BD7"}});"></div>
                      </td>
                      <td class="text-right">
                        @if(isset($planningByProject[$row->id]))
                          @php $pl = $planningByProject[$row->id]; @endphp
                          <a onclick="projectInplannen({{$row->id}})" class="btn btn-success text-white tippy" data-tippy-content="
                          <div class='text-left'><b class='mr-2'>{{Carbon()->parse($pl->datum)->format('d-m-Y')}}</b> {{Carbon()->parse($pl->begin)->format("H:i")}} - {{Carbon()->parse($pl->eind)->format("H:i")}}</div>
                        ">Ingepland</a>
                        @else
                          <a onclick="projectInplannen({{$row->id}})" class="btn btn-primary text-white">Inplannen</a>
                        @endif
                      </td>
                    </tr>
                  @endforeach
                  </tbody>
                </table>
              </div>

            </div>
        @endif
        @if(isset(json_decode(getSettingValue('planning_elementen'), true)['projecttaken']))
            <section class="card my-3" data-section-taken >
                <div class="flex-between m-2">
                    <h3 class="my-2 mx-2" >Projecten</h3>
                    <div class="flex-between">

                        <select name="sorteren" id="projecten-sorteren" class="taken-filter font-size-09 form-select max-w-150 mx-1 rounded-pill">
                            <option value="created_at" >Sorteren op</option>
                            <option value="created_at" >Datum</option>
                            <option value="projectnr">Projectnummer</option>
                            <option value="projectnaam">Projectnaam</option>
                            <option value="woonplaats">Locatie</option>
                        </select>
                        <a data-dynamic-order-button data-input-class="taken-filter" data-name="ascdesc" data-prefill="DESC" class="btn btn-light rounded-pill mx-1"></a>

                        <div class="form-control-divider mx-1" ></div>

                        <select name="vestiging" id="vestiging" class="taken-filter font-size-09 form-select max-w-150 mx-1 rounded-pill">
                            <option value="" disabled selected hidden>Vestiging</option>
                            <option value="">Alle vestigingen</option>
                            @foreach (getVestigingen() as $vestiging)
                                <option value="{{$vestiging->id}}">{{$vestiging->naam ?? $vestiging->plaats}}</option>
                            @endforeach
                        </select>
                        <select name="bv" id="bv" class="taken-filter font-size-09 form-select max-w-150 mx-1 rounded-pill">
                            <option value="" disabled selected hidden>BV</option>
                            @foreach ($bvs as $bv)
                                <option value="{{$bv->id}}">{{$bv->name}}</option>
                            @endforeach
                        </select>
                        <select name="taken_planned" id="taken_planned" class="taken-filter font-size-09 form-select max-w-150 mx-1 rounded-pill">
                            <option value="" disabled hidden>Taken</option>
                            <option value="alle_taken">Alle taken</option>
                            <option value="ingepland">Ingepland</option>
                            <option value="niet_ingepland" selected >Niet ingepland</option>
                        </select>

                        <div class="form-control-divider mx-1" ></div>

                        <infor-search
                          id="taken_search"
                          name="taken_search"
                          placeholder="Zoeken..."
                          class="form-control-custom max-w-150 mx-1 rounded-pill"
                          data-content="projectnr"
                          data-sub-content="projectnaam"
                          data-api="api/projecten/search"
                          data-errors="handleCatchError"
                        ></infor-search>`
                        <a class="btn btn-light rounded-pill mx-1 tippy" onclick="fillTaken()" data-tippy-content="Projecten vernieuwen" >@icon_redo</a>
                    </div>
                </div>
                <div class="project-taken-container overflow-auto max-h-500"></div>
            </section>
        @endif
        @if(isset(json_decode(getSettingValue('planning_elementen'), true)['offerteActiviteiten']))
            <div class="card my-3">

                <div class="flex-between m-2">
                    <h3 class="my-2 mx-2" >Offerte activiteiten</h3>
                </div>

                <div class="overflow-auto max-h-500" >
                    <table class="append-loader w-100" id="offertesTable">
                        <thead>
                        <th>Klant</th>
                        <th>Activiteit</th>
                        <th>Begintijd</th>
                        <th>Eindtijd</th>
                        <th>Offerte status</th>
                        <th class="text-right" >Acties</th>
                        </thead>
                        <tbody>
                        @foreach($offertePlanning ?? [] as $row)
                            <tr class="trHover">
                                <td>{{$row->offerte->klanten->naam ?? $row->offerte->klanten->contactpersoon_voornaam." ".$row->offerte->klanten->contactpersoon_achternaam}}</td>
                                <td>{{$row->naam}}</td>
                                <td>{{date("d-m-Y H:i", strtotime($row->begin))}}</td>
                                <td>{{date("d-m-Y H:i", strtotime($row->eind))}}</td>
                                <td>
                                    @if($allOffertes[$row->offerte_id]->status == "Akkoord") <span class="text-success">{{$allOffertes[$row->offerte_id]->status}}</span>
                                    @else<span class="text-warning">{{$allOffertes[$row->offerte_id]->status}}</span>
                                    @endif
                                </td>
                                <td class="text-right" >
                                    @if($row->status == null)
                                        <a data-toggle="modal" data-target="#rowInplannenModal" onclick="rowInplannen({{$row->id}})" class="btn btn-primary text-white">Inplannen</a>
                                    @elseif(isset($planningById[$row->status]) && $planningById[$row->status]->user_id  == 0)
                                        <div class="alert alert-warning text-center" >Activiteit ingepland als optie</div>
                                    @else
                                        <div class="alert alert-success text-center" >Activiteit ingepland</div>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    @endif

    {{--  Modals--}}
    <section>
        <!-- legenda modal -->
        <div class="modal fade overflow-auto" id="legendaModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Legenda</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-12 center mb-3">
                                <a id="optieToevoegen" class="btn btn-primary text-white">Activiteit toevoegen</a>
                            </div>
                            <div class="col-12">
                                <form method="post" action="{{url('planning/legenda')}}" id="legendaAddForm" class="form mt-3 mb-3 d-none">
                                    <div class="row">
                                        <div class="col-md-12 my-2">
                                            <label>Naam</label>
                                            <input required type="text" name="legendaNaam" placeholder="Naam" class="form-control">
                                        </div>
                                        <div class="col-12 my-2">
                                            <label>Omschrijving</label>
                                            <textarea rows="5" name="legendaOmschrijving" class="form-control" placeholder="Omschrijving"></textarea>
                                        </div>
                                        <div class="col-md-12 my-2">
                                            <div class="wheel center" id="colorWheel"></div>
                                            <div class="text-center">
                                                <a onclick="colorWheel.color.hexString = randomHex()" class="btn btn-inverse-primary">@icon_random</a>
                                            </div>
                                            <input type="hidden" name="legendaHex" id="legendaHex">
                                        </div>

                                    </div>
                                    <input type="hidden" value="{{currentRoute()}}" name="url">
                                    <input type="submit" id="submitButton" class="d-none">
                                    <input type="hidden" name="legendaOfferteId">
                                    <input type="hidden" name="legendaAanvraagId">
                                    @csrf
                                </form>
                                <div id="legenda">
                                    @foreach($legenda as $row)
                                        <div onclick="showButtons('legendaButtons{{$row->id}}')" class="row p-3 trHover  border-top cursor-pointer">
                                            <div class="col-md-10 col-9">
                                                <b class="d-block">{{$row->naam}}</b>
                                                {{$row->omschrijving}}
                                            </div>
                                            <div class="col-md-2 col-3">
                                                <div class="rounded" style="height: 100%;width:40px;min-height: 40px;background-image: linear-gradient(to bottom right, {{$row->kleur}}80, {{$row->kleur}});box-shadow: inset 0 0 10px 3px rgba(0,0,0,0.13);"></div>
                                            </div>
                                        </div>
                                        <div id="legendaButtons{{$row->id}}" class="col-12 d-none">
                                            <div class="row m-3">
                                                <div class="col-12 center">
                                                    <small class="d-none" id="lText{{$row->id}}" >Klik nogmaals om te bevestigen.</small>
                                                </div>
                                                <div class="col-6">
                                                    <a onclick="fillLegendaUpdateModal({{$row->id}})" class="btn btn-primary text-white w-100" data-toggle='modal' data-target='#legendaUpdateModal' data-dismiss='modal'>Wijzig</a>

                                                </div>
                                                <div class="col-6">
                                                    <a onclick="confirmLegendaDelete({{$row->id}})" id="lButton{{$row->id}}" class="btn btn-danger text-white w-100">Verwijderen</a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a id="opslaanButton" class="btn btn-success text-white d-none">Opslaan</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- offerte planning -->
        <div class="modal fade overflow-auto inplannen-modal" id="rowInplannenModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Inplannen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form method="post" enctype="multipart/form-data" class="inplannen-form" action="{{url('/planning/rowInplannen')}}">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12">
                                    <div class="my-2" id="offerteVerlofAlert" style="max-height: 150px;overflow-y: auto;"></div>
                                    <div id="rowAlert" class="alert alert-warning cursor-pointer" data-dismiss="modal" data-toggle="modal" data-target="#legendaModal">Geen bijbehorend activiteit gevonden, selecteer een activiteit handmatig of voeg een activiteit toe!</div>
                                </div>
                                <div class="col-12">
                                    <div class="d-inline-block form-check form-switch p-0">
                                        <span>Pushmelding</span>
                                        <input @if(getSettingValue('planning_standaard_pushmelding') == "Aan") checked @endif class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="pushmelding">
                                    </div>
                                </div>
                                <div class="col-12 my-2">
                                    <label class="d-block" >Activiteit</label>
                                    <select required name="legendaId" id="rowLegenda" class="form-select">
                                        @foreach($legenda as $row)
                                            <option value="{{$row->id}}" >{{$row->naam}}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-12 my-2">
                                    <label>Medewerker</label>
                                    <select data-alert="#offerteVerlofAlert" required name="rowUsers[]" id="offerteUser" class="form-select userSelect">
                                        <option value="" disabled selected >Selecteer de medewerker</option>
                                        @foreach($users as $user)
                                            <option value="{{$user->id}}" >{{$user->name." ".$user->lastname}}</option>
                                        @endforeach
                                        <option value="0" >Optie</option>
                                    </select>
                                </div>
                                <div class="col-12" id="offerteUsers" ></div>
                                <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Medewerker toevoegen" onclick="addOfferteUsers()" >Medewerker <i class="fas fa-plus m-0"></i></a>
                                </div>
                                @if(getSettingValue('planning_machines') == 'Aan')
                                    <div class="col-12 mt-2">
                                        <label>Machines</label>
                                        <div id="offertes-machines" class="machines" ></div>
                                        <div class="text-right" >
                                            <a class="btn btn-inverse-primary tippy" data-tippy-content="Machine toevoegen" onclick="addMachines(`#offertes-machines`)" >Machine @icon_plus</a>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-12 my-2" data-google-calendar-container >
                                    @if(isset(Auth::user()->google_token) && count($agendas))
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Toevoegen aan Google Agenda</span>
                                            <input checked class="form-check-input position-relative mx-1 cursor-pointer google_calendar_switch" type="checkbox" value="on" name="google_agenda">
                                        </div>
                                        <select required name="google_agenda_id" class="form-select google_calendar">
                                            <option value="" selected>Selecteer een agenda</option>
                                            @foreach($agendas as $agenda)
                                                <option value="{{$agenda["id"]}}" >{{$agenda["summary"]}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span class="text-muted" >Toevoegen aan Google Agenda</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" disabled>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-12 my-2">
                                    <label>Klant</label>
                                    <input type="text" class="form-control" id="rowKlantNaam" disabled>
                                    <input type="hidden" name="klantId" id="rowKlantId">
                                </div>
                              @php $string = randomString(); @endphp
                              <div id="{{ $string }}" class="row">
                                <div class="col-6 mt-3">
                                    <label>Begintijd</label>
                                    <input required class="form-control" type="datetime-local" name="rowBegin" value="" id="rowBegin">
                                </div>
                                <div class="col-6 mt-3">
                                    <label>Eindtijd</label>
                                    <input required class="form-control" type="datetime-local" name="rowEind" value="" id="rowEind">
                                </div>
                                <div class="col-2 mt-5">
                                  <a class="btn btn-inverse-danger cursor-pointer ml-3 pb-3" onclick="deleteDiv('#{{ $string }}')" ><span class="vertical-align-center">@icon_close</span></a>
                                </div>
                              </div>
                                <div class="col-12 mt-3">
                                    <label>Opmerking</label>
                                    <textarea placeholder="opmerking" class="form-control" name="rowOpmerking" id="rowOpmerking"></textarea>
                                </div>
                                @if (getSettingValue('planning_klant_mail') == 'Aan')
                                    <div class="col-12 my-2">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Klant mailen</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="klantMail">
                                        </div>
                                    </div>
                                @endif
                                @if(hasModule('Werkbonnen'))
                                    @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen"))
                                        <div class="mt-4 werkbonPrefillToggleContainer"><label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="werkbonPrefillToggle" value="true">&nbsp;Opvolg bon?</label></div>
                                        <div class="col-12 werkbon-nummers d-none"></div>
                                    @endif
                                    <div class="col-12 werkbon-templates-select"></div>
                                    <div class="col-12 werkbon-templates"></div>
                                @endif
                                @if(getSettingValue("planning_adres") == "Aan")
                                    <div class="col-12 my-2">
                                        <label>Straat</label>
                                        <input type="text" name="straat" class="form-control" placeholder="Straat" id="offerteStraat" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Huisnummer</label>
                                        <input type="text" name="huisnummer" class="form-control" placeholder="Huisnummer" id="offerteHuisnummer" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Postcode</label>
                                        <input type="text" name="postcode" class="form-control" placeholder="Postcode" id="offertePostcode" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Plaats</label>
                                        <input type="text" name="plaats" class="form-control" placeholder="Plaats" id="offertePlaats" >
                                    </div>
                                @endif
                                @if(getSettingValue("planning_bijlagen") == "Aan" && hasModule('Bestanden'))
                                    <div class="col-12 my-2">
                                        <div id="offerteFiles" class="planning-files"></div>
                                        <div class="text-right my-2">
                                            <a onclick="openExplorer('#offerteFiles')" class="btn btn-dark text-white ml-1" ><i class="fas fa-folder-open m-0"></i></a>
                                        </div>
                                    </div>
                                @endif
                                @foreach (json_decode(getSettingValue('planning_custom_rows') ?? '[]') as $customRow)
                                    @php $data = json_decode($customRow->data ?? '{}') @endphp
                                    <div class="col-12 my-2">
                                        <label>{{$customRow->name}}</label>
                                        @if($customRow->type == 'select')
                                            <select class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]">
                                                @foreach($data->options ?? [] as $option)
                                                    <option value="{{$option->value}}">{{$option->name}}</option>
                                                @endforeach
                                            </select>
                                        @elseif($customRow->type == 'tekstveld')
                                            <textarea class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}" rows="8"></textarea>
                                        @else
                                            <input type="{{$customRow->type}}" class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}">
                                        @endif
                                    </div>
                                @endforeach
                                @if (count($planningDatasets))
                                  @foreach ($planningDatasets as $set)
                                  @php
                                    $dataset = $set['dataset'];
                                  @endphp
                                    <div class="col-12 my-2 itemContainer">
                                      <span class="btn btn-primary text-white w-100 addItemBtn" data-dataset-id="{{$dataset->id}}">{{$dataset->naam}} @icon_plus</span>
                                      <div class="row datasetItems"></div>
                                    </div>
                                  @endforeach
                                @endif
                                {{-- planning herhalen --}}
                                <div class="col-12 my-2">
                                  <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="herhalen" value="on"> Herhalen?</label>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <select class="form-select" name="herhalen_freq">
                                    <option value="" selected>Selecteer frequentie</option>
                                    <option value="1 day">Dagelijks</option>
                                    <option value="1 week">Wekelijks</option>
                                    <option value="1 month weekday">Maandelijks (zelfde weekdag)</option>
                                    <option value="1 month">Maandelijks (zelfde datum)</option>
                                    <option value="1 year weekday">Jaarlijks (zelfde weekdag)</option>
                                    <option value="1 year">Jaarlijks (zelfde datum)</option>
                                    <option value="4 months weekday">4 Maandelijks (zelfde weekdag)</option>
                                    <option value="4 months">4 Maandelijks (zelfde datum)</option>
                                    <option value="8 months weekday">8 Maandelijks (zelfde weekdag)</option>
                                    <option value="8 months">8 Maandelijks (zelfde datum)</option>
                                  </select>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <label>Tot</label>
                                  <input type="date" class="form-control-custom" name="herhalen_eind" id="herhalen_eind">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success text-white">Opslaan</button>
                            <input type="hidden" value="{{currentRoute()}}" name="url">
                            <input type="hidden" id="rowId" name="rowId">
                            @csrf
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Aanvragen -->
        <div class="modal fade overflow-auto inplannen-modal" id="aanvragenInplannen" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Aanvraag Inplannen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form method="post" enctype="multipart/form-data" action="{{url('/planning/aanvraagInplannen')}}" class="inplannen-form">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12 my-2" id="aanvragenVerlofAlert" style="max-height: 150px;overflow-y: auto;"></div>
                                <div class="col-12">
                                    <div class="d-inline-block form-check form-switch p-0">
                                        <span>Pushmelding</span>
                                        <input @if(getSettingValue('planning_standaard_pushmelding') == "Aan") checked @endif class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="pushmelding">
                                    </div>
                                </div>
                                <div class="col-12 my-2">
                                    <label>Medewerker</label>
                                    <select data-alert="#aanvragenVerlofAlert" required name="users[]" class="form-select userSelect">
                                        <option value="" selected disabled>Selecteer de medewerker</option>
                                        @foreach($users as $user)
                                            <option value="{{$user->id}}" >{{$user->name." ".$user->lastname}}</option>
                                        @endforeach
                                        <option value="0" >Optie</option>
                                    </select>
                                </div>
                                <div class="col-12" id="aanvragenUsers" ></div>
                                <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Medewerker toevoegen" onclick="addAanvragenUsers()" >Medewerker <i class="fas fa-plus m-0"></i></a>
                                </div>
                                @if(getSettingValue('planning_machines') == 'Aan')
                                    <div class="col-12 mt-2">
                                        <label>Machines</label>
                                        <div id="aanvragen-machines" class="machines" ></div>
                                        <div class="text-right" >
                                            <a class="btn btn-inverse-primary tippy" data-tippy-content="Machine toevoegen" onclick="addMachines(`#aanvragen-machines`)" >Machine @icon_plus</a>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-12 my-2" data-google-calendar-container >
                                    @if(isset(Auth::user()->google_token) && count($agendas))
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Toevoegen aan Google Agenda</span>
                                            <input checked class="form-check-input position-relative mx-1 cursor-pointer google_calendar_switch" type="checkbox" value="on" name="google_agenda">
                                        </div>
                                        <select required name="google_agenda_id" class="form-select google_calendar">
                                            <option value="" selected>Selecteer een agenda</option>
                                            @foreach($agendas as $agenda)
                                                <option value="{{$agenda["id"]}}" >{{$agenda["summary"]}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span class="text-muted" >Toevoegen aan Google Agenda</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" disabled>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-12 my-2">
                                    <label>Klant</label>
                                    <input type="text" class="form-control" id="aanvraagKlant" disabled>
                                    <input type="hidden" name="klantId" id="aanvraagKlantId">
                                </div>
                              @php $string = randomString(); @endphp
                              <div id="{{ $string }}" class="row">
                                <div class="col-6 mt-3">
                                    <label>Begintijd</label>
                                    <input required class="form-control aanvragenBegintijd" type="datetime-local" name="begin">
                                </div>
                                <div class="col-6 mt-3">
                                    <label>Eindtijd</label>
                                    <input required class="form-control aanvragenEindtijd" type="datetime-local" name="eind">
                                </div>
                                <div class="col-2 mt-5">
                                  <a class="btn btn-inverse-danger cursor-pointer ml-3 pb-3" onclick="deleteDiv('#{{ $string }}')" ><span class="vertical-align-center">@icon_close</span></a>
                                </div>
                              </div>
                                <div class="col-12 mt-3">
                                    <label>Opmerking</label>
                                    <textarea placeholder="opmerking" class="form-control" name="opmerking"></textarea>
                                </div>
                                @if (getSettingValue('planning_klant_mail') == 'Aan')
                                    <div class="col-12 my-2">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Klant mailen</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="klantMail">
                                        </div>
                                    </div>
                                @endif
                                @if(hasModule('Werkbonnen'))
                                    @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen"))
                                        <div class="mt-4 werkbonPrefillToggleContainer"><label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="werkbonPrefillToggle" value="true">&nbsp;Opvolg bon?</label></div>
                                        <div class="col-12 werkbon-nummers d-none"></div>
                                    @endif
                                    <div class="col-12 werkbon-templates-select"></div>
                                    <div class="col-12 werkbon-templates"></div>
                                @endif
                                @if(getSettingValue("planning_adres") == "Aan")
                                    <div class="col-12 my-2">
                                        <label>Straat</label>
                                        <input type="text" name="straat" class="form-control" placeholder="Straat" id="aanvraagStraat" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Huisnummer</label>
                                        <input type="text" name="huisnummer" class="form-control" placeholder="Huisnummer" id="aanvraagHuisnummer" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Postcode</label>
                                        <input type="text" name="postcode" class="form-control" placeholder="Postcode" id="aanvraagPostcode" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Plaats</label>
                                        <input type="text" name="plaats" class="form-control" placeholder="Plaats" id="aanvraagPlaats" >
                                    </div>
                                @endif
                                @if(getSettingValue("planning_bijlagen") == "Aan" && hasModule('Bestanden'))
                                    <div class="col-12 my-2">
                                        <div id="aanvragenFiles" class="planning-files"></div>
                                        <div class="text-right my-2">
                                            <a onclick="openExplorer('#aanvragenFiles')" class="btn btn-dark text-white ml-1" ><i class="fas fa-folder-open m-0"></i></a>
                                        </div>
                                    </div>
                                @endif
                                @foreach (json_decode(getSettingValue('planning_custom_rows') ?? '[]') as $customRow)
                                    @php $data = json_decode($customRow->data ?? '{}') @endphp
                                    <div class="col-12 my-2">
                                        <label>{{$customRow->name}}</label>
                                        @if($customRow->type == 'select')
                                            <select class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]">
                                                @foreach($data->options ?? [] as $option)
                                                    <option value="{{$option->value}}">{{$option->name}}</option>
                                                @endforeach
                                            </select>
                                        @elseif($customRow->type == 'tekstveld')
                                            <textarea class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}" rows="8"></textarea>
                                        @else
                                            <input type="{{$customRow->type}}" class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}">
                                        @endif
                                    </div>
                                @endforeach
                                @if (count($planningDatasets))
                                  @foreach ($planningDatasets as $set)
                                  @php
                                    $dataset = $set['dataset'];
                                  @endphp
                                    <div class="col-12 my-2 itemContainer">
                                      <span class="btn btn-primary text-white w-100 addItemBtn" data-dataset-id="{{$dataset->id}}">{{$dataset->naam}} @icon_plus</span>
                                      <div class="row datasetItems"></div>
                                    </div>
                                  @endforeach
                                @endif
                                {{-- planning herhalen --}}
                                <div class="col-12 my-2">
                                  <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="herhalen" value="on"> Herhalen?</label>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <select class="form-select" name="herhalen_freq">
                                    <option value="" selected>Selecteer frequentie</option>
                                    <option value="1 day">Dagelijks</option>
                                    <option value="1 week">Wekelijks</option>
                                    <option value="1 month weekday">Maandelijks (zelfde weekdag)</option>
                                    <option value="1 month">Maandelijks (zelfde datum)</option>
                                    <option value="1 year weekday">Jaarlijks (zelfde weekdag)</option>
                                    <option value="1 year">Jaarlijks (zelfde datum)</option>
                                    <option value="4 months weekday">4 Maandelijks (zelfde weekdag)</option>
                                    <option value="4 months">4 Maandelijks (zelfde datum)</option>
                                    <option value="8 months weekday">8 Maandelijks (zelfde weekdag)</option>
                                    <option value="8 months">8 Maandelijks (zelfde datum)</option>
                                  </select>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <label>Tot</label>
                                  <input type="date" class="form-control-custom" name="herhalen_eind" id="herhalen_eind">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success text-white">Opslaan</button>
                            <input type="hidden" value="{{currentRoute()}}" name="url">
                            <input type="hidden" id="aanvraagId" name="aanvraagId">
                            @csrf
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Projecten-->
        <div class="modal fade overflow-auto inplannen-modal" id="projectenInplannen" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Project inplannen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form method="post" enctype="multipart/form-data" action="{{url('/planning/projectInplannen')}}" class="inplannen-form">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12 my-2" id="projectVerlofAlert" style="max-height: 150px;overflow-y: auto;"></div>
                                <div class="col-12 my-2">
                                  <label>Project</label>
                                  <input type="text" class="form-control-custom" id="projectNummer" disabled>
                                </div>
                                <div class="col-12">
                                    <div class="d-inline-block form-check form-switch p-0">
                                        <span>Pushmelding</span>
                                        <input @if(getSettingValue('planning_standaard_pushmelding') == "Aan") checked @endif class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="pushmelding">
                                    </div>
                                </div>
                                @if (getSettingValue('planning_aantal_medewerkers') == 'aan')
                                  <div class="col-12 mt-3">
                                    <label>Benodigd personeel</label>
                                    <input type="number" step="1" min="0" class="form-control" id="benodigdPersoneelProjectInplannen" name="projectInplannenAantalMedewerkers" placeholder="0" value="">
                                  </div>
                                @endif
                                <div class="col-12 my-2">
                                    <div class="flex-between mb-2">
                                        <label class="m-0" >Medewerkers</label>
                                        @if(getSettingValue('planning_group_users_by') == 'vestiging')
                                            <select class="font-size-08 form-select max-w-150 mx-1 rounded-pill" data-users-filter="vestiging" data-remember-value >
                                                <option value="">Vestiging</option>
                                                @foreach(getVestigingen() as $vestiging)
                                                    <option value="{{$vestiging->id}}">{{$vestiging->naam ?? $vestiging->plaats}}</option>
                                                @endforeach
                                            </select>
                                        @endif
                                    </div>
                                    @php $rString = randomString() @endphp
                                    <div class="flex-between" id="div{{$rString}}">
                                          <select required name="rowUsers[]" class="form-select userSelect mb-2" id="#div{{$rString}}">
                                              <option value="" selected disabled>Selecteer de medewerker</option>
                                              @foreach($users as $user)
                                                  <option data-user-vestiging="{{$user->vestiging_id}}" value="{{$user->id}}" >{{$user->name." ".$user->lastname}}</option>
                                              @endforeach
                                              <option value="0" >Opties</option>
                                          </select>
                                    </div>
                                        @if(getSettingValue('planning_machines') == 'pp')
                                            <a class="btn btn-primary text-white btn-block mb-2" id="machinefirstuser" onclick="addUserMachine('firstuser')">Machine bij medewerker inplannen</a>
                                        @endif

                                </div>
                                <div class="col-12" id="projectUsers" ></div>
                                <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Medewerker toevoegen" onclick="addProjectUsers()" >Medewerker <i class="fas fa-plus m-0"></i></a>
                                </div>
                                <div class="taken-container">
                                    <label>Taken</label>
                                    <div id="inplannen-taken"></div>
                                    <div class="my-2 text-right">
                                        <a class="btn btn-inverse-primary text-white" onclick="addInplannenTaken()" >Taken @icon_plus</a>
                                    </div>
                                </div>
                                @if(getSettingValue('planning_machines') == 'Aan')
                                    <div class="col-12 mt-2">
                                        <label>Machines</label>
                                        <div id="projecten-machines" class="machines"></div>
                                        <div class="text-right" >
                                            <a class="btn btn-inverse-primary tippy" data-tippy-content="Machine toevoegen" onclick="addMachines(`#projecten-machines`)" >Machine @icon_plus</a>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-12 my-2" data-google-calendar-container >
                                    @if(isset(Auth::user()->google_token) && count($agendas))
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Toevoegen aan Google Agenda</span>
                                            <input checked class="form-check-input position-relative mx-1 cursor-pointer google_calendar_switch" type="checkbox" value="on" name="google_agenda">
                                        </div>
                                        <select required name="google_agenda_id" class="form-select google_calendar">
                                            <option value="" selected>Selecteer een agenda</option>
                                            @foreach($agendas as $agenda)
                                                <option value="{{$agenda["id"]}}" >{{$agenda["summary"]}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span class="text-muted" >Toevoegen aan Google Agenda</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" disabled>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-12 my-2">
                                    <label>Klant</label>
                                    <input type="text" class="form-control-custom" id="projectKlant" disabled>
                                    <input type="hidden" name="klantId" id="projectKlantId">
                                </div>
                              @php $string = randomString(); @endphp
                              <div class="flex-between mx--1">
                                <div class="w-100 mx-1">
                                  <label>Begintijd</label>
                                </div>
                                <div class="w-100 mx-1">
                                  <label>Eindtijd</label>
                                </div>
                                <div class="mx-1">
                                  <a class="btn btn-inverse-danger opacity-0"><span class="vertical-align-center">@icon_close</span></a>
                                </div>
                              </div>

                              <div class="flex-between mx--1">
                                <div id="{{ $string }}" class="flex-between">
                                  <div class="w-100 mx-1">
                                    <input required class="form-control projectBegintijd" id="inplannenBegin" type="datetime-local" name="begin[]">
                                  </div>
                                  <div class="w-100 mx-1">
                                    <input required class="form-control projectEindtijd" id="inplannenEind" type="datetime-local"  name="eind[]">
                                  </div>
                                  <div class="mx-1">
                                    <a class="btn btn-inverse-danger" onclick="deleteDiv('#{{ $string }}')" ><span class="vertical-align-center">@icon_close</span></a>
                                  </div>
                                </div>
                              </div>

                              <div class="mx--1" id="projectDatums"></div>

                                <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Datum toevoegen" onclick="addProjectDatum()" >Datum <i class="fas fa-plus m-0"></i></a>
                                </div>
                                <div class="col-12 mt-3">
                                    <label>Opmerking</label>
                                    <textarea placeholder="opmerking" class="form-control" name="opmerking"></textarea>
                                </div>
                                @if (getSettingValue('planning_klant_mail') == 'Aan')
                                    <div class="col-12 my-2">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Klant mailen</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="klantMail">
                                        </div>
                                    </div>
                                @endif
                                @if(hasModule('Werkbonnen'))
                                    @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen"))
                                        <div class="mt-4 werkbonPrefillToggleContainer"><label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="werkbonPrefillToggle" value="true">&nbsp;Opvolg bon?</label></div>
                                        <div class="col-12 werkbon-nummers d-none"></div>
                                    @endif
                                    <div class="col-12 werkbon-templates-select"></div>
                                    <div class="col-12 werkbon-templates"></div>
                                @endif
                                @if(getSettingValue("planning_prijs") == "Aan")
                                  <div class="col-12 my-2">
                                    <div class="row">
                                      <div class="col-12 my-2">
                                        <label>Aantal personen</label>
                                        <input type="number" class="form-control" placeholder="Aantal personen" name="actAantal" id="actAantal">
                                      </div>
                                      <div class="col-12 my-2">
                                        <label>Prijs p.p.</label>
                                        <input type="number" class="form-control" step="0.01" placeholder="Prijs" name="actPrijs" id="actPrijs">
                                      </div>
                                      <div class="col-12 my-2">
                                        <label>BTW</label>
                                        <input type="text" class="form-control" placeholder="BTW" name="actBtw" id="actBtw">
                                      </div>
                                    </div>
                                  </div>
                                @endif
                                @if(getSettingValue("planning_adres") == "Aan")
                                    <div class="col-12 my-2">
                                        <label>Straat</label>
                                        <input type="text" name="straat" class="form-control" placeholder="Straat" id="projectStraat" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Huisnummer</label>
                                        <input type="text" name="huisnummer" class="form-control" placeholder="Huisnummer" id="projectHuisnummer" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Postcode</label>
                                        <input type="text" name="postcode" class="form-control" placeholder="Postcode" id="projectPostcode" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Plaats</label>
                                        <input type="text" name="plaats" class="form-control" placeholder="Plaats" id="projectPlaats" >
                                    </div>
                                @endif
                                @if(getSettingValue("planning_bijlagen") == "Aan" && hasModule('Bestanden'))
                                    <div class="col-12 my-2">
                                        <div id="projectenFiles" class="planning-files" ></div>
                                        <div class="text-right my-2">
                                            <a onclick="openExplorer('#projectenFiles')" class="btn btn-dark text-white ml-1" ><i class="fas fa-folder-open m-0"></i></a>
                                        </div>
                                    </div>
                                @endif
                                @foreach (json_decode(getSettingValue('planning_custom_rows') ?? '[]') as $customRow)
                                    @php $data = json_decode($customRow->data ?? '{}') @endphp
                                    <div class="col-12 my-2">
                                        <label>{{$customRow->name}}</label>
                                        @if($customRow->type == 'select')
                                            <select class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]">
                                                @foreach($data->options ?? [] as $option)
                                                    <option value="{{$option->value}}">{{$option->name}}</option>
                                                @endforeach
                                            </select>
                                        @elseif($customRow->type == 'tekstveld')
                                            <textarea class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}" rows="8"></textarea>
                                        @else
                                            <input type="{{$customRow->type}}" class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}">
                                        @endif
                                    </div>
                                @endforeach
                                @if (count($planningDatasets))
                                  @foreach ($planningDatasets as $set)
                                  @php
                                    $dataset = $set['dataset'];
                                  @endphp
                                    <div class="col-12 my-2 itemContainer">
                                      <span class="btn btn-primary text-white w-100 addItemBtn" data-dataset-id="{{$dataset->id}}">{{$dataset->naam}} @icon_plus</span>
                                      <div class="row datasetItems"></div>
                                    </div>
                                  @endforeach
                                @endif
                                {{-- planning herhalen --}}
                                <div class="col-12 my-2">
                                  <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="herhalen" value="on"> Herhalen?</label>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <select class="form-select" name="herhalen_freq">
                                    <option value="" selected>Selecteer frequentie</option>
                                    <option value="1 day">Dagelijks</option>
                                    <option value="1 week">Wekelijks</option>
                                    <option value="1 month weekday">Maandelijks (zelfde weekdag)</option>
                                    <option value="1 month">Maandelijks (zelfde datum)</option>
                                    <option value="1 year weekday">Jaarlijks (zelfde weekdag)</option>
                                    <option value="1 year">Jaarlijks (zelfde datum)</option>
                                    <option value="4 months weekday">4 Maandelijks (zelfde weekdag)</option>
                                    <option value="4 months">4 Maandelijks (zelfde datum)</option>
                                    <option value="8 months weekday">8 Maandelijks (zelfde weekdag)</option>
                                    <option value="8 months">8 Maandelijks (zelfde datum)</option>
                                  </select>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <label>Tot</label>
                                  <input type="date" class="form-control-custom" name="herhalen_eind" id="herhalen_eind">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success text-white">Opslaan</button>
                            <input type="hidden" value="{{currentRoute()}}" name="url">
                            <input type="hidden" id="projectId" name="projectId">
                            @csrf
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--  afronden modal ---->
        <div class="modal fade overflow-auto afronden-modal" id="afrondenModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document">
            <div class="modal-content bg-light">
              <div class="modal-header p-3">
                <h5 class="modal-title" id="exampleModalLabel">Afronden</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <div class="ml-3"><h3>Afronden</h3></div>
                <div id="afrondenModalContainer">

                </div>
                <div class="modal-footer mt-2">
                  <span class="btn btn-primary pt-2 mb--2 px-4 rounded-2 cursor-pointer" onclick="hideModal('afrondenModal')">Close</span>
                  <span class="btn btn-success pt-2 mb--2 px-4 rounded-2 cursor-pointer" onclick="hideModal('afrondenModal'); afrondenDagTaken()">Opslaan</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Inplannen -->
        <div class="modal fade overflow-auto inplannen-modal" id="inplannenModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Inplannen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <a id="inplannenModalToggle" class="btn btn-primary text-white m-3">Activiteit inplannen</a>
                    <a class="btn btn-primary text-white m-3" onclick="searchProject()">Project inplannen</a>
                    <div class="row m-2" id="inplannenAlert"></div>
                    <div id="inplannenModalInplannen" class="d-none">
                        <div class="row m-2" id="inplannenInfo"></div>
                        <form method="post" action="{{url('/planning/inplannen')}}" enctype="multipart/form-data" class="inplannen-form">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-12 my-2" id="inplannen-beschikbaarheid" >

                                    </div>
                                    <div class="col-12 my-2">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Pushmelding</span>
                                            <input @if(getSettingValue('planning_standaard_pushmelding') == "Aan") checked @endif class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="pushmelding">
                                        </div>
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Medewerker</label>
                                        <input id="userIdInplannenName" class="form-control-custom" disabled>
                                        <input type="hidden" id="userIdInplannen" name="users[]" class="form-select">
                                    </div>
                                    <div class="col-12" id="inplannenUsers"></div>
                                    <div class="col-12 my-2 text-right">
                                        <a class="btn btn-inverse-primary tippy" data-tippy-content="Medewerker toevoegen" onclick="addInplannenUsers()" >Medewerker <i class="fas fa-plus m-0"></i></a>
                                    </div>
                                    @if(getSettingValue('planning_machines') == 'Aan')
                                        <div class="col-12 mt-2">
                                            <label>Machines</label>
                                            <div id="inplannen-machines" class="machines"></div>
                                            <div class="text-right" >
                                                <a class="btn btn-inverse-primary tippy" data-tippy-content="Machine toevoegen" onclick="addMachines(`#inplannen-machines`)" >Machine @icon_plus</a>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="col-12 my-2" data-google-calendar-container >
                                        @if(isset(Auth::user()->google_token) && count($agendas))
                                            <div class="d-inline-block form-check form-switch p-0">
                                                <span>Toevoegen aan Google Agenda</span>
                                                <input checked class="form-check-input position-relative mx-1 cursor-pointer google_calendar_switch" type="checkbox" value="on" name="google_agenda">
                                            </div>
                                            <select required name="google_agenda_id" class="form-select google_calendar">
                                                <option value="" selected>Selecteer een agenda</option>
                                                @foreach($agendas as $agenda)
                                                    <option value="{{$agenda["id"]}}" >{{$agenda["summary"]}}</option>
                                                @endforeach
                                            </select>
                                        @else
                                            <div class="d-inline-block form-check form-switch p-0">
                                                <span class="text-muted" >Toevoegen aan Google Agenda</span>
                                                <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" disabled>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Activiteit</label>
                                        <select required name="legendaId" id="inplannenActiviteitSelect" class="form-select">
                                            <option value="" selected disabled>Selecteer een activiteit</option>
                                            @foreach($legenda as $row)
                                                <option value="{{$row->id}}">{{$row->naam}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-12 my-2">
                                        <div class="select_search-container">
                                            <label> Klant </label>
                                            <input type="hidden" name="klantId" class="select_search-hidden-input" data-placeholder="Select" data-required="required" >
                                            <div class="select_search-values" >
                                                <div class="select_search-box" id="refreshableKlanten">
                                                    <span class="select_search-value" data-value="" data-name="Selecteer een klant">Selecteer een klant</span>
                                                    @foreach($klanten->where('status', 1) as $row)
                                                        <span class="select_search-value" data-value="{{$row->id}}" data-name="@if(isset($row->naam)) {{$row->naam}}, @endif {{$row->contactpersoon_voornaam}} {{$row->contactpersoon_achternaam}}">@if(isset($row->naam)) {{$row->naam}}, @endif {{$row->contactpersoon_voornaam}} {{$row->contactpersoon_achternaam}}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <a class="btn btn-primary my-1 text-white w-100" id="refreshKlantBtn" onclick="addKlantBtn()" >Klant toevoegen</a>
                                        <div class="text-right klanten-refresh" ></div>
                                    </div>
                                    <div id="inplannenKlantToevoegen" class="col-12 d-none my-2">
                                        <div class="row">
                                            <div class="col-12 my-2 text-right">
                                                <a class="p-2 cursor-pointer" onclick="showButtons('inplannenKlantToevoegen');showButtons('inplannenKlant');toggleProp('#inplannenKlantSelect', 'disabled');toggleProp('#inplannenKlantnaam', 'required');toggleProp('#inplannenEmail', 'requierd')"><i class=" h4 mdi mdi-close"></i></a>
                                            </div>
                                            <div class="col-6 my-2">
                                                <label>Klantnaam</label>
                                                <input id="inplannenKlantnaam" class="form-control" type="text" name="klantnaam" placeholder="Klantnaam">
                                            </div>
                                            <div class="col-6 my-2">
                                                <label>Email</label>
                                                <input id="inplannenKlantemail" class="form-control" type="text" name="klantemail" placeholder="Klant Email">
                                            </div>
                                            <div class="col-12 my-2">
                                                <label>BV</label>
                                                <select name="bv" class="form-control">
                                                    @foreach($bvs as $bv)
                                                        <option value="{{$bv->id}}">{{$bv->name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                  @php $string = randomString(); @endphp
                                  <div class="flex-between mx--1">
                                    <div class="w-100 mx-1">
                                      <label>Begintijd</label>
                                    </div>
                                    <div class="w-100 mx-1">
                                      <label>Eindtijd</label>
                                    </div>
                                    <div class="mx-1">
                                      <a class="btn btn-inverse-danger opacity-0"><span class="vertical-align-center">@icon_close</span></a>
                                    </div>
                                  </div>

                                  <div class="flex-between mx--1">
                                    <div id="{{ $string }}" class="flex-between">
                                      <div class="w-100 mx-1">
                                        <input required class="form-control activiteitBegintijd" type="datetime-local" name="begin[]">
                                      </div>
                                      <div class="w-100 mx-1">
                                        <input required class="form-control activiteitEindtijd" type="datetime-local"  name="eind[]">
                                      </div>
                                      <div class="mx-1">
                                        <a class="btn btn-inverse-danger" onclick="deleteDiv('#{{ $string }}')" ><span class="vertical-align-center">@icon_close</span></a>
                                      </div>
                                    </div>
                                  </div>

                                  <div class="mx--1" id="activiteitDatums"></div>

                                  <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Datum toevoegen" onclick="addProjectDatum('#activiteitDatums')" >Datum <i class="fas fa-plus m-0"></i></a>
                                  </div>

                                    <div class="col-12 my-2">
                                        <label>Opmerking</label>
                                        <textarea  name="inplannenOpmerking" class="form-control" placeholder="Opmerking"></textarea>
                                    </div>
                                    <div class="col-12 my-2">
                                        <label class="cursor-pointer" >
                                            <input checked class="form-check-custom" type="checkbox" value="on" name="project">&nbsp;Project aanmaken @if(hasModule("Werkbonnen")) <small class="text-muted" >Verplicht voor werkbonnen</small> @endif
                                        </label>
                                    </div>
                                    @if(getSettingValue('planning_klant_mail') == 'Aan')
                                        <div class="col-12 my-2">
                                            <div class="d-inline-block form-check form-switch p-0">
                                                <span>Klant mailen</span>
                                                <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="klantMail">
                                            </div>
                                        </div>
                                    @endif

                                    @if(hasModule('Werkbonnen'))
                                        @if(getSettingCheckbox("planning_tonen_werkbon_opvolgen"))
                                            <div class="mt-4 werkbonPrefillToggleContainer"><label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="werkbonPrefillToggle" value="true">&nbsp;Opvolg bon?</label></div>
                                            <div class="col-12 werkbon-nummers d-none"></div>
                                        @endif
                                        <div class="col-12 werkbon-templates-select"></div>
                                        <div class="col-12 werkbon-templates"></div>
                                    @endif
                                    @if(getSettingValue("planning_prijs") == "Aan")
                                        <div class="col-12 my-2">
                                            <div class="row">
                                                <div class="col-12 my-2">
                                                    <label>Aantal personen</label>
                                                    <input type="number" class="form-control" placeholder="Aantal personen" name="actAantal">
                                                </div>
                                                <div class="col-12 my-2">
                                                    <label>Prijs p.p.</label>
                                                    <input type="number" class="form-control" step="0.01" placeholder="Prijs" name="actPrijs">
                                                </div>
                                                <div class="col-12 my-2">
                                                    <label>BTW</label>
                                                    <input type="text" class="form-control" placeholder="BTW" name="actBtw">
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if(getSettingValue("planning_adres") == "Aan")
                                        <div class="col-12 my-2">
                                            <label>Straat</label>
                                            <input type="text" name="straat" class="form-control" placeholder="Straat" >
                                        </div>
                                        <div class="col-12 my-2">
                                            <label>Huisnummer</label>
                                            <input type="text" name="huisnummer" class="form-control" placeholder="Huisnummer" >
                                        </div>
                                        <div class="col-12 my-2">
                                            <label>Postcode</label>
                                            <input type="text" name="postcode" class="form-control" placeholder="Postcode" >
                                        </div>
                                        <div class="col-12 my-2">
                                            <label>Plaats</label>
                                            <input type="text" name="plaats" class="form-control" placeholder="Plaats" >
                                        </div>
                                    @endif
                                    @if(getSettingValue("planning_bijlagen") == "Aan" && hasModule('Bestanden'))
                                        <div class="col-12 my-2">
                                            <div id="planningFiles" class="planning-files" ></div>
                                            <div class="text-right my-2">
                                                <a onclick="openExplorer('#planningFiles')" class="btn btn-dark text-white ml-1" ><i class="fas fa-folder-open m-0"></i></a>
                                            </div>
                                        </div>
                                    @endif
                                    @foreach (json_decode(getSettingValue('planning_custom_rows') ?? '[]') as $customRow)
                                        @php $data = json_decode($customRow->data ?? '{}') @endphp
                                        <div class="col-12 my-2">
                                            <label>{{$customRow->name}}</label>
                                            @if($customRow->type == 'select')
                                                <select class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]">
                                                    @foreach($data->options ?? [] as $option)
                                                        <option value="{{$option->value}}">{{$option->name}}</option>
                                                    @endforeach
                                                </select>
                                            @elseif($customRow->type == 'tekstveld')
                                                <textarea class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}" rows="8"></textarea>
                                            @else
                                                <input type="{{$customRow->type}}" class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}">
                                            @endif
                                        </div>
                                    @endforeach
                                    @if (count($planningDatasets))
                                  @foreach ($planningDatasets as $set)
                                  @php
                                    $dataset = $set['dataset'];
                                  @endphp
                                    <div class="col-12 my-2 itemContainer">
                                      <span class="btn btn-primary text-white w-100 addItemBtn" data-dataset-id="{{$dataset->id}}">{{$dataset->naam}} @icon_plus</span>
                                      <div class="row datasetItems"></div>
                                    </div>
                                  @endforeach
                                @endif
                                  {{-- planning herhalen --}}
                                  <div class="col-12 my-2">
                                    <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="herhalen" value="on"> Herhalen?</label>
                                  </div>
                                  <div class="col-12 my-2 herhaaldiv d-none">
                                    <select class="form-select" name="herhalen_freq">
                                      <option value="" selected>Selecteer frequentie</option>
                                      <option value="1 day">Dagelijks</option>
                                      <option value="1 week">Wekelijks</option>
                                      <option value="1 month weekday">Maandelijks (zelfde weekdag)</option>
                                      <option value="1 month">Maandelijks (zelfde datum)</option>
                                      <option value="1 year weekday">Jaarlijks (zelfde weekdag)</option>
                                      <option value="1 year">Jaarlijks (zelfde datum)</option>
                                      <option value="4 months weekday">4 Maandelijks (zelfde weekdag)</option>
                                      <option value="4 months">4 Maandelijks (zelfde datum)</option>
                                      <option value="8 months weekday">8 Maandelijks (zelfde weekdag)</option>
                                      <option value="8 months">8 Maandelijks (zelfde datum)</option>
                                    </select>
                                  </div>
                                  <div class="col-12 my-2 herhaaldiv d-none">
                                    <label>Tot</label>
                                    <input type="date" class="form-control-custom" name="herhalen_eind" id="herhalen_eind">
                                  </div>
                                @if(getSettingCheckbox('planning_legenda_urenregistratie'))
                                    <div class="col-12 my-2">
                                        <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="opslaanUrenreg" value="on"> Wil je deze uren in de urenregistratie opslaan?</label>
                                    </div>
                                @endif
                                </div>
                                <div class="modal-footer">
                                    <button type="submit" class="btn btn-success text-white">Opslaan</button>
                                    <input type="hidden" id="dateInplannen" name="date">
                                    <input type="hidden" name="url" value="{{currentRoute()}}">
                                    @csrf
                                </div>
                            </div>
                        </form>
                    </div>
                    <div id="inplannenModalAanpassen">
                        <div id="inplannenModalAanpassenBody" class="modal-body"></div>
                        <div class="modal-footer"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- update Modal -->
        <div class="modal fade overflow-auto" id="updateModal" data-planning-id="0" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Planning wijzigen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form method="post" enctype="multipart/form-data" action="{{url('planning/update')}}" class="update-form">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-8 col-6" id="updateNaam" ></div>
                                <div class="col-md-4 col-6"><input type="date" class="form-control" id="updateDatum" name="datum"></div>
                                <div class="col-12" id="updateKlant" ><b>Klant: </b></div>
                                <div class="col-12 border-bottom pb-3" id="updateOmsch" >

                                </div>
                                <div class="col-12">
                                    <div class="d-inline-block form-check form-switch p-0 col-6">
                                        <span>Pushmelding</span>
                                        <input @if(getSettingValue('planning_standaard_pushmelding') == "Aan") checked @endif class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="pushmelding">
                                    </div>
                                </div>
                              @if (getSettingValue('planning_aantal_medewerkers') == 'aan')
                                <div class="col-12 mt-3">
                                  <label>Benodigd personeel</label>
                                  <input type="number" step="1" min="0" class="form-control" id="benodigdPersoneelUpdate" name="updateAantalMedewerkers" placeholder="0">
                                </div>
                              @endif
                              @php $rString = randomString() @endphp
                                <div class="col-12 mt-3">
                                  <label>Medewerker</label>
                                  <div class="flex-between" id="div{{$rString}}">
                                    <a></a>
                                    <select name="updateUser[]" class="form-select mb-2" >
                                        @foreach($users as $user)
                                            <option value="{{$user->id}}" >{{$user->name}} {{$user->lastname}}</option>
                                        @endforeach
                                        <option value="0" >Optie</option>
                                    </select>
                                  </div>
                                </div>
                                <div class="col-12" id="projectUsersWijzig" data-planning-wijzig-id="0"></div>
                                <div class="col-12 my-2 text-right">
                                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Medewerker toevoegen" onclick="addProjectUsers(true)" >Medewerker <i class="fas fa-plus m-0"></i></a>
                                </div>
                                <div id="updateTaken" class="col-12 my-2 d-none">

                                </div>
                                @if(getSettingValue('planning_machines') == 'Aan')
                                    <div class="col-12 mt-2">
                                        <label>Machines</label>
                                        <div id="edit-machines" class="machines" ></div>
                                        <div class="text-right" >
                                            <a class="btn btn-inverse-primary tippy" data-tippy-content="Machine toevoegen" onclick="addMachines(`#edit-machines`)" >Machine @icon_plus</a>
                                        </div>
                                    </div>
                                @endif
                              @php $string = randomString(); @endphp
                              <div class="row">
                                <div class="col-6">
                                  <label>Begintijd</label>
                                  <input required class="form-control updateProjectBegintijd" type="time" name="begin" id="updateBegin">
                                </div>
                                <div class="col-6">
                                  <label>Eindtijd</label>
                                  <input required class="form-control updateProjectEindtijd" type="time"  name="eind" id="updateEind">
                                </div>
                              </div>

                                <div class="col-12 mt-3">
                                    <label>Opmerking</label>
                                    <textarea placeholder="Opmerking" name="opmerking" id="updateOpmerking" class="form-control"></textarea>
                                </div>
                                @if (getSettingValue('planning_klant_mail') == 'Aan')
                                    <div class="col-12 my-2">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Klant mailen</span>
                                            <input class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="klantMail">
                                        </div>
                                    </div>
                                @endif
                                @if(getSettingValue("planning_prijs") == "Aan")
                                  <div>
                                      <div class="row">
                                          <div class="col-12 mt-3">
                                              <label>Aantal personen</label>
                                              <input type="number" step="1" class="form-control" placeholder="Aantal personen" name="updateAantal">
                                          </div>
                                          <div class="col-12 mt-3">
                                              <label>Prijs p.p.</label>
                                              <input type="number" class="form-control" step="0.01" placeholder="Prijs" name="updatePrijs">
                                          </div>
                                          <div class="col-12 mt-3">
                                              <label>BTW</label>
                                              <input type="text" class="form-control" placeholder="BTW" name="updateBtw">
                                          </div>
                                      </div>
                                  </div>
                                @endif
                                @if(getSettingValue("planning_adres") == "Aan")
                                    <div class="col-12 my-2">
                                        <label>Straat</label>
                                        <input type="text" name="straat" class="form-control" placeholder="Straat" id="updateStraat" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Huisnummer</label>
                                        <input type="text" name="huisnummer" class="form-control" placeholder="Huisnummer" id="updateHuisnummer" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Postcode</label>
                                        <input type="text" name="postcode" class="form-control" placeholder="Postcode" id="updatePostcode" >
                                    </div>
                                    <div class="col-12 my-2">
                                        <label>Plaats</label>
                                        <input type="text" name="plaats" class="form-control" placeholder="Plaats" id="updatePlaats" >
                                    </div>
                                @endif
                                @foreach (json_decode(getSettingValue('planning_custom_rows') ?? '[]') as $customRow)
                                    @php $data = json_decode($customRow->data ?? '{}') @endphp
                                    <div class="col-12 my-2">
                                        <label>{{$customRow->name}}</label>
                                        @if($customRow->type == 'select')
                                            <select class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]">
                                                @foreach($data->options ?? [] as $option)
                                                    <option value="{{$option->value}}">{{$option->name}}</option>
                                                @endforeach
                                            </select>
                                        @elseif($customRow->type == 'tekstveld')
                                            <textarea class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}" rows="8"></textarea>
                                        @else
                                            <input type="{{$customRow->type}}" class="form-control-custom custom-{{$customRow->keyword}}" name="custom[{{$customRow->keyword}}]" placeholder="{{$customRow->name}}">
                                        @endif
                                    </div>
                                @endforeach
                                @if (count($planningDatasets))
                                  @foreach ($planningDatasets as $set)
                                  @php
                                    $dataset = $set['dataset'];
                                  @endphp
                                    <div class="col-12 my-2 itemContainer">
                                      <span class="btn btn-primary text-white w-100 addItemBtn" data-dataset-id="{{$dataset->id}}">{{$dataset->naam}} @icon_plus</span>
                                      <div class="row datasetItems" id="dataset_items_update_{{$dataset->id}}"></div>
                                    </div>
                                  @endforeach
                                @endif
                                <div class="col-12">
                                    <div class="d-inline-block form-check form-switch p-0">
                                        <span>Afgerond</span>
                                        <input  class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="completed">
                                    </div>
                                </div>
                                <div id="sameprojectdiv" class="col-12 d-none">
                                  <div class="d-inline-block form-check form-switch p-0">
                                    <div id="sameprojectusers"></div>
                                  </div>
                                </div>
                                @if(getSettingValue('vooruit_planning') == 'released')
                                    <div class="col-12">
                                        <div class="d-inline-block form-check form-switch p-0">
                                            <span>Vrijgegeven</span>
                                            <input  class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="on" name="released">
                                        </div>
                                    </div>
                                @endif
                                @if(getSettingValue("planning_bijlagen") == "Aan" && hasModule('Bestanden'))
                                    <div class="col-12 my-2">
                                        <div id="updateFiles" class="planning-files" ></div>
                                        <div class="text-right my-2">
                                            <a onclick="openExplorer('#updateFiles')" class="btn btn-dark text-white ml-1" ><i class="fas fa-folder-open m-0"></i></a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success text-white">Opslaan</button>
                            <input type="hidden" name="id" id="updateId">
                            <input type="hidden" name="url" value="{{currentRoute()}}">

                            @csrf
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- legendaUpdate modal -->
        <div class="modal fade overflow-auto" id="legendaUpdateModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content bg-light">
                    <div class="modal-header p-3">
                        <h5 class="modal-title" id="exampleModalLabel">Planning wijzigen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form onsubmit="return fillHex('legendaUpdateHex', legendaColorWheel.color.hexString)" method="post" action="{{url('planning/legenda/update')}}" class="form mt-3 mb-3">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-12 mt-2">
                                    <label>Naam</label>
                                    <input id="legendaUpdateNaam" required type="text" name="legendaNaam" placeholder="Naam" class="form-control">
                                </div>
                                <div class="col-12 mt-2">
                                    <label>Omschrijving</label>
                                    <textarea rows="5" id="legendaUpdateOmschrijving" name="legendaOmschrijving" class="form-control" placeholder="Omschrijving"></textarea>
                                </div>
                                <div class="col-md-12 mt-3">
                                    <div class="wheel center" id="legendaColorWheel"></div>
                                    <div class="text-center">
                                        <a onclick="legendaColorWheel.color.hexString = randomHex()" class="btn btn-inverse-primary">@icon_random</a>
                                    </div>
                                    <input type="hidden" name="legendaHex" id="legendaUpdateHex">
                                </div>
                                {{-- planning herhalen --}}
                                <div class="col-12 my-2">
                                  <label class="cursor-pointer" ><input class="form-switch-custom" type="checkbox" name="herhalen" value="on"> Herhalen?</label>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <select class="form-select" name="herhalen_freq">
                                    <option value="" selected>Selecteer frequentie</option>
                                    <option value="1 day">Dagelijks</option>
                                    <option value="1 week">Wekelijks</option>
                                    <option value="1 month weekday">Maandelijks (zelfde weekdag)</option>
                                    <option value="1 month">Maandelijks (zelfde datum)</option>
                                    <option value="1 year weekday">Jaarlijks (zelfde weekdag)</option>
                                    <option value="1 year">Jaarlijks (zelfde datum)</option>
                                    <option value="4 months weekday">4 Maandelijks (zelfde weekdag)</option>
                                    <option value="4 months">4 Maandelijks (zelfde datum)</option>
                                    <option value="8 months weekday">8 Maandelijks (zelfde weekdag)</option>
                                    <option value="8 months">8 Maandelijks (zelfde datum)</option>
                                  </select>
                                </div>
                                <div class="col-12 my-2 herhaaldiv d-none">
                                  <label>Tot</label>
                                  <input type="date" class="form-control-custom" name="herhalen_eind" id="herhalen_eind">
                                </div>
                            </div>
                            @csrf
                            <input type="hidden" name="legendaAanvraagIdUpdate">
                            <input type="hidden" name="legendaOfferteIdUpdate">
                        </div>
                        <div class="modal-footer">
                            <a class="btn btn-secondary" data-dismiss="modal">Close</a>
                            <button type="submit" class="btn btn-success text-white">Opslaan</button>
                            <input type="hidden" name="id" id="legendaUpdateId">
                            <input type="hidden" name="url" value="{{currentRoute()}}">
                            @csrf
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- route modal-->
        <div class="modal fade bd-example-modal-lg" id="routeModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Route berekenen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-3 col-6 my-2">
                                <input type="date" value="{{date("Y-m-d")}}" id="routeDatum" class="form-control" >
                            </div>
                            <div class="col-lg-3 col-6 my-2">
                                <input type="time" value="{{date("H:i")}}" id="routeTijd" class="form-control" >
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 my-2">
                                <label class="d-block text-center my-2" >Klant selecteren</label>
                                <select class="form-select" id="routeKlantId">
                                    <option value="" selected disabled >Klant selecteren</option>
                                    @foreach($klanten as $klant)
                                        <option value="{{$klant->id}}" >{{$klant->naam}}, {{$klant->contactpersoon_voornaam}} {{$klant->contactpersoon_achternaam}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-12 my-2">
                                <span class="d-block text-center">Of</span>
                            </div>
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-md-6 col-12 my-2"><label>Straat</label><input type="text" id="routeStraat" placeholder="Straat" class="form-control"> </div>
                                    <div class="col-md-6 col-12 my-2"><label>Huisnummer</label><input type="text" id="routeHuisnummer" placeholder="Huisnummer" class="form-control"> </div>
                                    <div class="col-md-6 col-12 my-2"><label>Postcode</label><input type="text" id="routePostcode" placeholder="Straat" class="form-control"> </div>
                                    <div class="col-md-6 col-12 my-2"><label>Plaats</label><input type="text" id="routePlaats" placeholder="Plaats" class="form-control"> </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div id="calcRouteSpinner" class="d-none spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <button id="calcRouteBtn" type="button" class="btn btn-primary" onclick="startPost(true);">Route berekenen</button>
                        <button id="showReistijdModal" class="d-none" data-dismiss="modal" data-toggle="modal" data-target="#reistijdModal" ></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- reistijd modal-->
        <div class="modal fade bd-example-modal-lg" style="overflow-y: auto;" id="reistijdModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Route berekenen</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body overflow-auto" id="tableAppend" >

                    </div>
                </div>
            </div>
        </div>
        <!--  Reserveerplein modal-->
        <div class="modal" id="rpModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="row" >
                            <div class="col-12">
                                <img class="w-100 d-inline-blcok" src="{{url("client/public/img/rpTekst.png")}}" >
                            </div>
                            <div class="col-12 my-2">
                                <h4 class="text-center"><span id="rpNumber"></span> nieuwe reserveringen gevonden!</h4>
                            </div>
                            <div class="col-12 my-2 text-center">
                                <a class="btn btn-reserveerplein text-white" href="{{url(url()->current())}}">Refresh</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- Files modal --}}
        <div class="modal fade" id="filesModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div id="files">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{--  Project color--}}
        <div class="modal fade" id="color-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="my-3">
                            <div class="wheel center" id="projectColorWheel"></div>
                            <input type="hidden" name="projectHexColor">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div id="color-modal-btn" ></div>
                        <input type="hidden" name="colorModalId">
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('script')
    <script>

      var planningItemKleuren = @json(getSettingValue('planning_item_kleuren'));
      var planningPrijs = @json(getSettingValue('planning_prijs'));
      var preview = @json(isset($preview));
      var isIframe = @json(routePrefix() == 'iframe');
      var type = @json($type);
      var period = @json($period->toArray());
      var bvs = @json($bvs);
      var legenda = @json($legenda);
      var offertes = @json($offertes);
      var allOffertes = @json($allOffertes);
      var aanvragen = @json($aanvragen);
      var klanten = @json($klanten);
      var planning = @json($planning);
      var offertePlanning = @json($offertePlanning);
      var projecten = @json($projecten);
      var projectId = @json($projectId ?? null);
      var verlof = @json($verlof);
      var verlofRedenen = @json($verlofRedenen);
      var feestdag = @json($feestdag);
      var users = @json($users);
      var beschikbaarheid = @json($beschikbaarheid);
      var werkbonTemplates = @json($werkbonTemplates);
      var routeTable;
      var auth = @json(Auth::user());
      var reserveerpleinId = @json(getSettingValue("reserveerpleinId"));
      var subdomein = "{{getSubdomein()}}"
      var showBeschikbaarheidState = false;
      var machines = @json($machines);
      var planningAantalMedewerkers = @json(getSettingValue('planning_aantal_medewerkers'));
      var intervals = {
        newKlant: null,
      }
      var settings = {
        pushmelding: '{{getSettingValue('planning_standaard_pushmelding')}}',
        googleAgenda: '{{getSettingValue('planning_standaard_google_agenda')}}',
        project: '{{getSettingValue('planning_standaard_project')}}',
        alleenInOpdracht: @json(getSettingValue('planning_projecten_alleen_in_opdracht')),
        adres: '{{getSettingValue("planning_adres")}}',
        klantMail: '{{getSettingValue("planning_standaard_klant_mail")}}',
        werkbonPrefill: @json(json_decode(getSettingValue('planning_werkbon_keywords_prefill') ?? '[]')),
        takenCustomRows: @json(json_decode(getSettingValue('projecten_taken_custom_rows'))),
        adresSetting: @json(getSettingValue("planning_adres")),
        machineSetting: @json(getSettingValue('planning_machines')),
        werkbonnenSetting: @json(getSettingValue('planning_werkbonnen')),
        afgerondenProjectenTonen: @json(getSettingValue('planning_afgeronden_projecten_tonen')),
        prefillBegin: @json(Carbon()->parse(getSettingValue('planning_prefill_begin') ?? '9:00')->format('H:i')),
        prefillEind: @json(Carbon()->parse(getSettingValue('planning_prefill_eind') ?? '10:00')->format('H:i')),
        timeDiffBeginEind: @json(Carbon()->parse(getSettingValue('planning_prefill_eind') ?? '10:00')->diffInHours(Carbon()->parse(getSettingValue('planning_prefill_begin') ?? '9:00'))),
        planning_datasets: @json($planningDatasets),
        planning_tonenOpvolgBonnen: @json(getSettingCheckbox("planning_tonen_werkbon_opvolgen")),
        planning_elementen: @json(getSettingJson('planning_elementen')),
        planning_in_verleden: @json(getSettingCheckbox('planning_in_verleden')),
      }


      const _this = {
        main_table: null,
        planning: null,
        transition: 200
      }
      const _projectInplannen = {
        project: null,
        user: null,
        modal: $('#projectenInplannen'),
      }


      pageComplete(() => {
        initPlanning();
      })

      $(document).ready( function () {
        initRightClick();
        initIframe();
        initTakenSearch();
        $("#projectenTable, #aanvragenTable, #offertesTable, #takenTable").dataTable({
          "ordering": false,
          "paging": false,
        });

        if (reserveerpleinId){
          $.ajax({
            type: "GET",
            url: "https://reserveerplein.nl/api/reserveringen/"+reserveerpleinId,
            crossDomain: true,
            cache: false,
            async: true,
            success: function(result){
              if(result){
                $.ajax({
                  type: "POST",
                  data: {data: JSON.stringify(result)},
                  url: '{{url("planning/reserveerplein")}}',
                  crossDomain: true,
                  cache: false,
                  async: true,
                  success: function(result){
                    if(result){
                      if (Number(result) >= 1){
                        $('#rpModal').modal('show');
                        $("#rpNumber").html(result);
                      }
                    }
                  }
                });
              }
            }
          });
        }

        if(_settings.planning_google_hide == 'ja'){
          $('[data-google-calendar-container]').remove();
        }

        $('[data-period-type]').trigger('change');

        tippy('.tt', {
          allowHTML: true,
        });
        tippyInit();
      });

      const colorWheel = new iro.ColorPicker("#colorWheel", {
        display: "inline-block"
      });
      const legendaColorWheel = new iro.ColorPicker("#legendaColorWheel", {
        display: "inline-block",
      });
      const projectColorWheel = new iro.ColorPicker("#projectColorWheel", {
        display: "inline-block",
      });
      projectColorWheel.on('color:change', function(color) {
        $("input[name=projectHexColor]").val(color.hexString);
      });

      $("#inplannenKlantButton").click(function(){
        $("#inplannenKLantSelect").prop("disabled")
      });
      $("#inplannenModalToggle").click(function(){
        $("#inplannenModalAanpassen").toggleClass("d-none");
        $("#inplannenModalInplannen").toggleClass("d-none");
        if($("#inplannenModalAanpassen").hasClass("d-none")){
          $(this).html("Planning aanpassen");
              $('.werkbon-nummers').addClass('d-none');
              $('.werkbonPrefillToggleContainer').addClass('d-none');
              $('.werkbon-templates-select').removeClass('d-none');
        }
        else{
          $(this).html("Activiteit inplannen");
        }

      });
      $("#optieToevoegen").click(function(){
        $("#legendaAddForm").toggleClass("d-none");
        $("#opslaanButton").toggleClass("d-none");
        $("#legenda").toggleClass("d-none");
      });
      $("#opslaanButton").click(function(){
        $("#legendaHex").val(colorWheel.color.hexString);
        $("#submitButton").trigger("click");
      });
      $(".userSelect").change(function(){
        let user = users[$(this).val()];
        let append = $($(this).attr("data-alert"));
        append.html("")
        // for(let ym in verlof){
        //   if(ym.slice(0,8) == yearMonth){
        //     if(verlof[ym][user.id]){
        //       for (let v of verlof[ym][user.id]){
        //         if(v.van !== null && v.tot !== null){
        //           append.append('<div class="alert alert-warning" >Verlof: '+format(new Date(v.datum))+' van '+v.van.slice(0,5)+' tot '+v.tot.slice(0,5)+'</div>')
        //         }
        //         else{
        //           append.append('<div class="alert alert-warning" >Verlof: '+format(new Date(v.datum))+' hele dag!</div>')
        //         }
        //       }
        //     }
        //   }
        // }
      })
      $('.inplannen-form').submit(function(e){
        e.preventDefault();

        var postData = $(this).serializeArray();
        var formURL = $(this).attr("action");

        for(const input of postData){
          if(input.name != 'begin[]'){ continue; }

          const timestamp_today = new Date(now().date.us).getTime();
          const timestamp_begin = new Date(input.value).getTime();

          if(timestamp_begin < timestamp_today && !settings.planning_in_verleden){
            notification(`Datum uit het verleden is niet toegestaan: <b>${convert(timestamp_begin)}</b>`)
            return;
          }
        }

        loader();
        $.ajax({
          url : formURL,
          type: "POST",
          data : postData,
          success:function(response){
            $('#inplannenModal, #projectenInplannen, #aanvragenInplannen, #rowInplannenModal').hideModal();
            fillTaken();

            if(type == 'dag'){
              reInitPlanning();
              return
            }
            if(getParameter('taakinplannen')){
              window.close();
            }

            appendPlanning(response.planning);
            successLoader();
          },
          error: function(err){
            actError(err)
            displayCatchError(err);
          }
        });
      })
      $("#projecten-sorteren").change(function(){
        setCookie('planningProjectSort',  $("#projecten-sorteren").val());
      })
      $("#vestiging").change(function(){
        setCookie('planningVestiging',  $("#vestiging").val());
      })
      $("#bv").change(function(){
        setCookie('planningBv',  $("#bv").val());
      })
      $("#taken_planned").change(function(){
        setCookie('planningTaken_planned',  $("#taken_planned").val());
      })
      $('.update-form').submit(function(e){
        e.preventDefault();

        var postData = $(this).serializeArray();
        var formURL = $(this).attr("action");

        loader();
        $.ajax({
          url : formURL,
          type: "POST",
          data : postData,
          success:function(response){
            $('#updateModal').hideModal();
            if(type == 'dag'){
              reInitPlanning();
              return
            }
            if(getParameter('edit')){
              window.close();
            }
            const {planning, updPlanning} = response;

            for(const p of updPlanning){
                deletePlanningObject(p.id);
            }
            appendPlanning(updPlanning);
            successLoader();
          },
          error: function(){
            errorLoader();
          }
        });
      })
      $('[name=werkbonPrefillToggle]').change(function(){
        $('.werkbon-templates-select').toggleClass('d-none');
        $('.werkbon-nummers').toggleClass('d-none');
        $('.werkbon-keywords-prefill').empty();
      })

      $('[data-toggle-nodes-direction]').click(function(){
        $('.act-div').toggleClass('flex-column');
        setCookie('planning_nodes_direction', $('.act-div').hasClass('flex-column') ? 'vertical' : 'horizontal');
      });
      $(".google_sync").click(function(){
        window.location.href = 'https://infordb.ikbentessa.nl/google/calendar/'+subdomein+'/'+auth.id;
      })


      $(document).on("keyup", "#search", function(){
        let search = $(this).val().toLowerCase().split(' ');
        $(".searchRow").each(function(){
          let attr = $(this).attr("data-search").toLowerCase().replace(' ', '');
          attr = attr.replace(new RegExp(' ', 'g'), '');
          let includes = true;
          for(let s of search){
            if(!attr.includes(s)){
              includes = false;
            }
          }
          if(includes){
            $(this).addClass("d-inline-block").removeClass("d-none")
          }
          else{
            $(this).removeClass("d-inline-block").addClass("d-none");
          }
        });
      });

      $(document).on('change', '[name="planning_display_info[]"]', function(){
        const value = this.value;
        const name = `planning_info_display_${value}`;
        const checked = $(this).is(':checked');

        checked ? setCookie(name, true) : removeCookie(name);
        fillPlanningInfoContent()
      })

      $(document).on("change", "#inplannenBegin", function(){
        let val = $(this).val();
        let date = new Date("2000-01-01 "+val);
        date.setHours(date.getHours() + settings.timeDiffBeginEind ?? 1);
        let string = ("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2)
        $("#inplannenEind").val(string);
      });
      $(document).on("change", ".projectBegintijd", function(){
        let val = $(this).val();
        let date = new Date(val);
        date.setHours(date.getHours() + settings.timeDiffBeginEind ?? 1);
        let string = date.getFullYear()+"-"+("0"+(date.getMonth()+1)).slice(-2)+"-"+("0"+date.getDate()).slice(-2)+"T"+("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2);

        //kijkt of begintijd groter is of gelijk aan eind tijd zo ja zet de eindtijd een uur vooruit zo nee doe niks
        let index = $(".projectBegintijd").index(this);
        let eindTijd = new Date($(".projectEindtijd:eq("+index+")").val());
        if (eindTijd == "Invalid Date" || date >= eindTijd){
          $(".projectEindtijd:eq("+index+")").val(string);
        }
      });
      $(document).on("change", ".activiteitBegintijd", function(){
        let val = $(this).val();
        let date = new Date(val);
        date.setHours(date.getHours() + settings.timeDiffBeginEind ?? 1);
        let string = date.getFullYear()+"-"+("0"+(date.getMonth()+1)).slice(-2)+"-"+("0"+date.getDate()).slice(-2)+"T"+("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2);

        //kijkt of begintijd groter is of gelijk aan eind tijd zo ja zet de eindtijd een uur vooruit zo nee doe niks
        let index = $(".activiteitBegintijd").index(this);
        let eindTijd = new Date($(".activiteitEindtijd:eq("+index+")").val());
        if (eindTijd == "Invalid Date" || date >= eindTijd){
          $(".activiteitEindtijd:eq("+index+")").val(string);
        }
      });
      $(document).on("change", ".updateProjectBegintijd", function(){
        let val = $(this).val();
        let timeArray = val.split(':');

        // Create a new date (today's date) and set the input time
        let date = new Date();
        date.setHours(parseInt(timeArray[0]));
        date.setMinutes(parseInt(timeArray[1]));

        // Increment the time by one hour
        date.setHours(date.getHours() + settings.timeDiffBeginEind ?? 1);

        // Format the time into HH:MM format
        let string = ("0" + date.getHours()).slice(-2) + ":" + ("0" + date.getMinutes()).slice(-2);

        // Retrieve the index of the current input element
        let index = $(".updateProjectBegintijd").index(this);

        // Get the end time value at the same index
        let endTimeInput = $(".updateProjectEindtijd:eq("+index+")");
        let endTime = endTimeInput.val();
        if (endTime !== "") {
          // Assuming time is in HH:MM format
          let endTimeArray = endTime.split(':');
          let endDateTime = new Date();
          endDateTime.setHours(parseInt(endTimeArray[0]));
          endDateTime.setMinutes(parseInt(endTimeArray[1]));

          // Compare the start time with the end time
          if (date >= endDateTime) {
            // Increment the end time by one hour
            endDateTime.setHours(endDateTime.getHours() + 1);
            let endString = ("0" + endDateTime.getHours()).slice(-2) + ":" + ("0" + endDateTime.getMinutes()).slice(-2);

            // Update the end time input with the new calculated time
            endTimeInput.val(endString);
          }
        }
      });
      $(document).on("change", ".aanvragenBegintijd", function(){
        let val = $(this).val();
        let date = new Date(val);
        date.setHours(date.getHours() + settings.timeDiffBeginEind ?? 1);
        let string = date.getFullYear()+"-"+("0"+(date.getMonth()+1)).slice(-2)+"-"+("0"+date.getDate()).slice(-2)+"T"+("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2);

        let index = $(".projectBegintijd").index(this);
        $(".aanvragenEindtijd:eq("+index+")").val(string);
      });
      $(document).on("change", ".google_calendar_switch", function(){
        $(".google_calendar").prop('disabled', true);
        if($(this).prop('checked')){
          $(".google_calendar").prop('disabled', false);
        }
      });

      $(document).on('change', '#routeKlantId', function(){
        let klant = klanten[$(this).val()];
        if(klant.straat){$("#routeStraat").val(klant.straat);}else{$("#routeStraat").val("")}
        if(klant.huisnummer){$("#routeHuisnummer").val(klant.huisnummer);}else{$("#routeHuisnummer").val("")}
        if(klant.huisnummer && klant.toevoeging){$("#routeHuisnummer").val(klant.huisnummer+klant.toevoeging);}
        if(klant.postcode){$("#routePostcode").val(klant.postcode);}else{$("#routePostcode").val("")}
        if(klant.plaats){$("#routePlaats").val(klant.plaats);}else{$("#routePlaats").val("")}
      })
      $(document).on('change', '[name=werkbonPrefill]', function(){
          let container = $('.werkbon-templates');
          const id = this.value;

          container.find('.werkbon-keywords-prefill').empty();
          if (!id) { return }

          const template = werkbonTemplates.find(row => row.id == id);
          const keys = settings.werkbonPrefill[id];

        container.find('.werkbon-keywords-prefill').html(`<div class='bg-light-grey p-2 rounded border ${randomString()}'></div>`)
        container = $(`.${lastString()}`)


        for(const key in keys){
          const keyword = template.keywords.find(row => row.keyword == key);
          if(!keyword){continue}
          const data = JSON.parse(keyword.data || '[]');

          if(keyword.type == 'time'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <input type="time" class="form-control-custom" name="werkbonPrefillKeywords[${id}][${key}]" >
            </div>`
            )
          }
          else if(keyword.type == 'select'){
            let options = `<option value='' >Selecteer optie</option>`
            for(const option of data.options){
              options += `<option value='${option.value}' >${option.name}</option>`
            }
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <select class="form-select" name="werkbonPrefillKeywords[${id}][${key}]" >${options}</select>
            </div>`
            )
          }
          else if(keyword.type == 'input_slot' || keyword.type == 'text'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <input type="text" class="form-control-custom" name="werkbonPrefillKeywords[${id}][${key}]" placeholder="${keyword.naam}" >
            </div>`
            )
          }
          else if(keyword.type == 'textarea'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <textarea class="form-control-custom" rows="3" name="werkbonPrefillKeywords[${id}][${key}]" placeholder="${keyword.naam}"></textarea>
            </div>`
            )
          }
          else if(keyword.type == 'input_slot'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <input type="text" class="form-control-custom" name="werkbonPrefillKeywords[${id}][${key}]" placeholder="${keyword.naam}" >
            </div>`
            )
          }
          else if(keyword.type == 'textarea'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.naam}</label>
              <textarea placeholder="${keyword.naam}" class="form-control-custom keyword-input" name="werkbonPrefillKeywords[${id}][${key}]"></textarea>
            </div>`
            )
          }

        }

      })

      $(document).on('change', '[name=werkbonId]', function() {
        let container = $('.werkbon-templates');
        const id = this.value;

        container.find('.werkbon-keywords-prefill').empty();
        if (!id) { return }

        const werkbon = _projectInplannen?.project?.werkbonnen.find(row => row.id == id);
        const keys = settings.werkbonPrefill[werkbon.template.id];

        container.find('.werkbon-keywords-prefill').html(`<div class='bg-light-grey p-2 rounded border ${randomString()}'></div>`)
        container = $(`.${lastString()}`)

        for(const key in keys){
          const keyword = werkbon.keywords.find(row => row.keyword == key);
          if(!keyword){continue}
          const data = JSON.parse(keyword.input.data || '[]');

          if(keyword.type == 'time'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.input.naam}</label>
              <input type="time" class="form-control-custom" name="werkbonPrefillKeywords[${werkbon.template.id}][${key}]" >
            </div>`
            )
          }
          else if (keyword.type == 'select') {
              container.append(
                  `<div class='my-2'>
                    <label>${keyword.input.naam}</label>
                    <select class="form-select" name="werkbonPrefillKeywords[${werkbon.template.id}][${key}]">
                      <option value=''>Selecteer optie</option>
                      ${data.options.map(option => `
                        <option value='${option.value}' ${option.value === keyword.value ? 'selected' : ''}>
                          ${option.name}
                        </option>`).join('')}
                    </select>
                  </div>`
              );
          }
          else if(keyword.type == 'input_slot' || keyword.type == 'text'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.input.naam}</label>
              <input type="text" class="form-control-custom" name="werkbonPrefillKeywords[${werkbon.template.id}][${key}]" placeholder="${keyword.input.naam}" value="${HTMLToText(keyword.value || '')}">
            </div>`
            )
          }
          else if(keyword.type == 'textarea'){
            container.append(
              `<div class='my-2'>
                <label>${keyword.input.naam}</label>
                <textarea class="form-control-custom" rows="3" name="werkbonPrefillKeywords[${werkbon.template.id}][${key}]" placeholder="${keyword.input.naam}">${HTMLToText(keyword.value || '')}</textarea>
              </div>`
            );
          }
          else if(keyword.type == 'input_slot'){
            container.append(
              `<div class='my-2' >
              <label>${keyword.input.naam}</label>
              <input type="text" class="form-control-custom" name="werkbonPrefillKeywords[${werkbon.template.id}][${key}]" placeholder="${keyword.input.naam}" >
            </div>`
            )
          }
        }
      });

      $(document).on('change', '.taken-filter', fillTaken);

      $(document).on('click', 'html', (e) => {
        if(e.which != 1){ return }

        removePlanningActions();
      })

      $(document).on('change', '[data-users-filter=vestiging]', function(){
        $('[data-user-vestiging]').removeClass('d-none');
        if(!this.value){ return; }

        $('[data-user-vestiging]').not(`[data-user-vestiging=${this.value}]`).addClass('d-none');
      });
      $(document).on('change', '[ data-table-users-vestiging]', function(){
        setCookie('planning_table_vestiging', this.value);

        $('[data-user-vestiging-tr]').removeClass('d-none');
        if(!this.value){ return; }

        $('[data-user-vestiging-tr]').not(`[data-user-vestiging-tr=${this.value}]`).addClass('d-none');
      });

      $(document).on('change', '[data-period-type]', function(){
        const type = this.value;
        $('[data-period-start], [data-period-divider], [data-period-end]').addClass('d-none');
        $(`[data-period-start=${type}]`).removeClass('d-none');

        if(type == 'week' || type == 'maand' || type == 'kwartaal'){
          $(`[data-period-start=jaar]`).removeClass('d-none');
        }

        if(type == 'periode'){
          $('[data-period-divider], [data-period-end]').removeClass('d-none');
        }

        if(type == 'komende30'){
          let startDate = now(new Date());
          let dateEnd = startDate.addDays(30);

          $('[data-period-start=periode]').val(startDate.date.us);
          $('[data-period-end]').val(dateEnd.date.us);
          $('[data-period-start=periode], [data-period-divider], [data-period-end]').removeClass('d-none');
        }

        if(type == '60dagen'){
          let startDate = now(new Date()).subDays(30);
          let dateEnd = startDate.addDays(60);

          $('[data-period-start=periode]').val(startDate.date.us);
          $('[data-period-end]').val(dateEnd.date.us);
          $('[data-period-start=periode], [data-period-divider], [data-period-end]').removeClass('d-none');
        }
      })
      $(document).on('click', '[data-period-search]', function(){
        const type = $('[data-period-type]').val();
        const year = $('[data-period-start=jaar]').val();
        const start = $(`[data-period-start=${type}]`).val();
        const end = $('[data-period-end]').val();

        let urltype = type;

        let afterfix = '';
        switch (type) {
          case 'week' :
          case 'maand' :
          case 'kwartaal' :
            afterfix = `${year}/${start}`;
            break;
          case 'jaar':
            afterfix = `${year}/0`
            break;
          case 'periode':
            afterfix = `${now(start).date.us}/${now(end).date.us}`
            break;
          case 'komende30':
            afterfix = `${now().date.us}/${now().addDays(30).date.us}`
            urltype = 'periode';
            break;
          case '60dagen':
            afterfix = `${now().subDays(30).date.us}/${now().addDays(30).date.us}`
            urltype = 'periode';
            break;
          default:
            afterfix = `${now(start).year}/${now(start).month}-${now(start).day}`
        }

        loader();
        const planurl = `${url}${isIframe ? '/iframe' : ''}/planning/${preview ? 'overzicht' : 'inplannen'}/${urltype}/${afterfix}`;
        window.location.href = planurl;
      })
      $(document).on('change', '[name=herhalen]', function(){
        $('.herhaaldiv').addClass('d-none');

        if(this.checked){
          $('.herhaaldiv').removeClass('d-none');
        }
      })
      $(document).on('input', '.search-taken', function () {

        const searchTerm = $(this).val().toLowerCase();
        const table = $(this).closest('.project-row').find('.table-custom');
        table.find('tbody tr').each(function () {
          const dataSearch = $(this).data('search') || '';
          $(this).toggle(dataSearch.toLowerCase().includes(searchTerm));
        });
      });

      $('.addItemBtn').click(function(){
        const id = $(this).data('dataset-id');
        const container = findContainer('itemContainer', $(this));
        const div = container.find('.datasetItems');

        const dataset = settings.planning_datasets[id].dataset;
        let options = '';
        for(let itemid in dataset.items){
          const item = dataset.items[itemid];
          options += `<span class="select_search-value" data-value="${item.id}" data-name="${item.name}">${item.name}</span>`
        }
        confirmModal({
          text: `<div class="select_search-container">
                  <input type="hidden" name="dataset_item_select_${id}" class="select_search-hidden-input" data-placeholder="Select" data-required="required" >
                  <div class="select_search-values" >
                    <div class="select_search-box" >
                      ${options}
                    </div>
                  </div>
                </div>
              `,
              execute: initSelectSearch
        }).then((response) => {
          if(!response.status){ return }
          const datasetItem = dataset.items.find(row => row.id == $(`[name=dataset_item_select_${id}]`).val());

          div.append(
            `<div class="my-2" id="${randomString()}">
              <div class="d-flex justify-content-between" >
                <input class="form-control-custom mr-2" readonly value="${datasetItem.name}">
                <input type="number" name="datasetItemAantal[${id}][${datasetItem.id}]" class="form-control-custom w20 mr-2" placeholder="Aantal">
                <a class="btn btn-inverse-danger" onclick="deleteDiv(${lastString()})" >@icon_close</a>
              </div>
            </div>`
          )
        })
      })

      function initIframe(){
        if(!isIframe){return;}

        $('.container-fluid, .content-wrapper').removeClass('px-md-2').addClass(['pb-0', 'h-100']);
        $('.container-fluid .card').addClass('m-0');
        $('footer').remove();
      }

      function appInit(){
        if(!isIframe){return;}
        if(getParameter('edit')){
          fillUpdateModal(getParameter('edit'));
          return;
        }
        if(getParameter('taakinplannen') && getParameter('project')){
          inplannenTaken(getParameter('project'), getParameter('taakinplannen'));
        }
      }

      function initPlanning(){
        ajax('api/planning/portal', {
          start: convert(period[0], true),
          end: convert(period[lastKey(period)], true),
          project: projectId,
          relations_lite: {
            project: 'taken',
            klanten: null,
            offerte: null,
            user: 'role',
          }
        })
          .then(response => {
            planning = response.planning
            initTable();
            initPlanningFill();
            initNodesDirection();
            appInit();
          })
          .catch(handleCatchError);
      }
      function initPlanningFill(i = 0){
        if(!planning[i]){
          setTimeout(() => {$('.planning-loader').remove();}, 200)
          return;
        }

        const perc = (i / planning.length * 100).toFixed(0);
        $('.planning-loader').find('.progress-bar').css({width: `${perc}%`}).html(`${perc}%`);

        appendPlanning(planning.slice(i, i + 1000));
        setTimeout(initPlanningFill, 1, i + 1000);
      }
      function initTable(){
        try{
          var userIdsInPlanning = planning.map(p => parseInt(p.user_id)).filter((v, i, a) => a.indexOf(v) === i);

          var userList = users;
          if(projectId){
              userList = {};
              userIdsInPlanning.forEach(id => {
                  if (users[id]) {
                      userList[id] = users[id];
                  }
              });
          }

          const temp = clone(userList);
          temp[0] = {id: 0, name: 'Opties', lastname: '', role: {id: 0, name: "Opties"}};

          //PHP ordered useres are reorederd while parsing to JS
          const permission = @json(Auth::user()->hasPermissionTo("Volledige planning bekijken"));
          const orderSetting = @json(json_decode((getSettingValue('planning_roles_order') ?? '[]'), true));

          const order = [];
          for(const r in orderSetting){
            if(!orderSetting[r]){continue;}

            order.push({role: Number(r), order: Number(orderSetting[r])});
          }
          order.sort((a,b) => a.order - b.order);

          let lastRole = '';

          //Append users by role;
          for(const ord of order){
            for(const u in temp){
              const user = temp[u];

              if(user.role_id != ord.role){continue;}
              if(!permission && user.id != auth.id){continue;}

              appendUser(user);
            }

          }
          appendUser(temp[0]);

          function appendUser(user){
            if(lastRole !== user.role.id){appendRole(user.role)}

            let tr = '';
            let bsh = {
              div: '',
              tippy: '',
              times: '',
            };

            for(const day of period){
              const date = convert(day, true);

              bsh = {
                div: '',
                tippy: '',
                times: '',
              };

              if(beschikbaarheid[user.id] && beschikbaarheid[user.id][date]){
                let span = '';
                for(const row of beschikbaarheid[user.id][date]){
                  span += `<span class='${Number(row.type) ? 'text-success' : 'text-danger'}' >${Number(row.day) ? 'Hele dag' : `${row.start.slice(0, 5)} - ${row.end.slice(0, 5)}`}</span> <small>${row.opmerking || ''}</small><br>`
                }
                bsh.tippy = `data-tippy-content="
                          <div class='text-left' >
                              ${span}
                          </div>"`

                for(let b = 0; b <24; b++){
                  bsh.times += `<div class="w-100 bg-inverse-secondary" id="beschikbaarheid-u${user.id}-d${date}-h${b}" ></div>`
                }

              }

              bsh.div = `<div class="beschikbaarheid-div h-100 rounded overflow-hidden opacity-75 d-none ${bsh.tippy ? 'tippy' : ''}" ${bsh.tippy}>
                      <div class="d-flex h-100">
                        ${bsh.times ? bsh.times : `<div class="w-100 bg-inverse-secondary" ></div>`}
                      </div>
                    </div>`

              let bg = '';
              if(date == now().date.us){
                bg = 'background-color: rgba(25, 216, 149, 0.125);'
              }
              if(verlof[date] && verlof[date][user.id]){
                bg = 'background-color: #dee2e6B3;'
              }
              if(feestdag[date]){
                bg = 'background-color: #CBC3E3;'
              }
              if(now(date).isWeekend){
                bg = 'background-color: #f8d7daB3;'
              }

              let nonPreview = '';
              if(!preview){
                nonPreview = `data-toggle="modal" data-target="#inplannenModal"  onclick="inplannen('${user.id}','${user.name || ''} ${user.lastname || ''}','${date}')"`
              }

              let actDivContent = `<td data-day="${date}" data-user="${user.id}" style="${bg}" ${nonPreview} class="p-0 day-td border cursor-pointer transition-05 hover-bg-inverse-secondary" >
                                <div class="beschikbaarheid-planning h-100" >
                                   <div class="act-div rounded h-100 w-100 p-2 overflow-hidden d-flex"></div>
                                </div>
                                ${bsh.div}
                              </td>`;
              tr += actDivContent;
            }

            if(type == 'dag'){
              tr = `<td class="p-0 border" data-day-container="${user.id}">
                  <div class="beschikbaarheid-planning h-100" >
                     <div class="act-div h-100 p-2 cursor-pointer hover-bg-inverse-secondary" ${!preview ? `data-toggle="modal" data-target="#inplannenModal" onclick="inplannen(${user.id},'${user.name} ${user.lastname}','${convert(period[0], true)}')"` : '' }  ></div>
                  </div>
                  ${bsh.div}
                </td>`
            }

            const userTd = `<td style="position: -webkit-sticky; position: sticky; left: 0;" data-tippy-content="${user.name || ''} ${user.lastname || ''}<br>${user.email || ''}" class="border-left-0 w-0 bg-white tippy border-right h6 border ${user.id ? 'text-primary' : 'text-muted'}" ><small>${user.name || ''}<br>${user.lastname || ''}</small></td>`

            $('#planningTable').find('tbody').append(`<tr ${ Number(user.id) ? `data-user-vestiging-tr="${user.vestiging_id || ''}"` : '' } data-user-tr="${user.id}" >${userTd}${tr}</tr>`)
          }
          function appendRole(role){
            lastRole = role.id;

            let tds = '';
            for(const day of period){
              tds += `<td class="border-0 py-0"></td>`
            }

            if(type == 'dag'){
              tds = '<td class="p-0 border" > <div class="d-flex" ></div> </td>'
            }

            $('#planningTable').find('tbody').append(
              `<tr style="background: rgba(192, 194, 195, 0.2)!important;" >
              <td style="position: -webkit-sticky; position: sticky; left: 0;" class="py-0 text-uppercase border-0" ><small><b>${role.name}</b></small></td>
              ${tds}
          </tr>`
            )
          }

          $('.planning-spinner').remove();
          $('.planning-section').removeClass('d-none');

          _this.main_table = $('#planningTable').DataTable({
            paging: false,
            ordering: false,
            searching: false,
            info: false,
          });

          $('[data-table-users-vestiging]').trigger('change');

          if(isIframe){scrl('#main_table') }
        }
        catch (e) { handleCatchError(e); }
      }
      function initNodesDirection(){
        if(type == 'dag'){ return }
        if(getCookie('planning_nodes_direction') == 'horizontal'){ return }

        $('.act-div').addClass('flex-column');
      }
      function confirmVrijgeven(dag){
        confirmModal({
          text: 'Weet je zeker dat je deze dag planning vrij wil geven?',
          btnColor: 'btn-primary'
        }).then((response) => {
          if(!response.status){ return }
          dagVrijgeven(dag);
        })
      }
      function dagVrijgeven(dag){
        let data = $('#main_table').find(`[data-day=${dag}]`);
        let planningIdList = '';
        let userList = [];
        //loopt door alle tds van die dag
        for (const planningItem of data){
          //loopt door alle gevonden planning elementen van die dag heen op bassis van attr data-id (dit zijn de planning ids)
          for (let planningElement of $(planningItem).find(`[data-id]`)){
            //zet alle gevonden planning ids in een string komen zodat deze in de ajax call geüpdatet kunnen worden
            planningIdList +=  $(planningElement).data('id')+",";

            if(!userList.includes($(planningItem).data('user')))
            {
              userList.push($(planningItem).data('user'))
            }
          }
        }
        //ajax call om de planning items te up daten en een pushmelding naar de gebruikers te sturen
        ajax('api/planning/vrijgevendag', {
          planningIdList: planningIdList,
          userIds: userList,
        }).then(response => {
          successLoader()
          location.reload();
        })
      }
      function confirmAfronden(dag){
        let planningItems = planning.filter(row => row.datum == dag)
        let takenIds = [];
        $('#afrondenModalContainer').html('');

        for (const planningItem of planningItems){
          if (planningItem.taken.length == 1){
            $('#afrondenModalContainer').append(`
               <div class="col-md-12 col-12 m1-1 d-inline-flex antwoord">
                <input type="checkbox" name="takenAfronden" value="${planningItem.taken[0].id}" class="cursor-pointer d-inline" checked>
                <input name="antwoord" class="form-control-custom ml-2 d-inline singleAntwoordInputMeerkeuze" value="${planningItem.taken[0].name}" disabled/>
              </div>
            `);
          }
          else if(planningItem.taken.length > 1)
          {
            for (const taak of planningItem.taken){
              //voorkomt dubbelle taken in de modal
              if(takenIds.includes(taak.id)){continue}
              takenIds.push(taak.id);

              $('#afrondenModalContainer').append(`
               <div class="col-md-12 col-12 mt-1 d-inline-flex antwoord">
                <input type="checkbox" name="takenAfronden" value="${taak.id}" class="cursor-pointer d-inline" checked>
                <input name="antwoord" class="form-control-custom ml-2 d-inline singleAntwoordInputMeerkeuze" value="${taak.name}" disabled/>
              </div>
            `);
            }
          }
        }
        showModal('afrondenModal')
      }
      function afrondenDagTaken(){
        let taakIds = [];

        $('[name=takenAfronden]').each(function(){
          if($(this).prop('checked')){
            taakIds.push($(this).val())
          }
        })

        ajax('api/planning/afrondenDag', {
          taakIds: taakIds,
        }).then(response => {
          successLoader()
          location.reload();
        })
      }
      function initRightClick(){
        if(preview || isIframe){ return; }

        if (document.addEventListener) {
          document.addEventListener('contextmenu', function(e) {
            _this.planning = null;
            const path = event.path || (event.composedPath && event.composedPath());
            for(const element of path){
              if(!$(element).hasClass('planning-element')){ continue }
              const id = $(element).attr('data-id');
              const plan = planning.find(row => row.id == id);
              _this.planning = plan;
              e.preventDefault();
              rightClickPlanning($(element))
            }

          }, false);
          return;
        }

        document.attachEvent('oncontextmenu', function(e) {
          _this.planning = null;

          for(const element of e.path){
            if(!$(element).hasClass('planning-element')){ continue }
            const id = $(element).data('id');
            const plan = planning.find(row => row.id == id);

            _this.planning = plan;
            rightClickPlanning($(element))
            window.event.returnValue = false;
          }
        });
      }

      function reInitPlanning(){
        //Dagplanning needs to re-appeend all elements
        //Ajax required to resort elements by begintijd

        loader()
        ajax('api/planning/portal', {
          start: convert(period[0], true),
          end: convert(period[lastKey(period)], true),
          relations_lite: {
            project: 'taken',
            klanten: null,
            offerte: null,
          }
        })
          .then((response) => {
            planning = response.planning
            $('[data-day-row-container]').remove();
            $('.act-div').empty();
            appendPlanning(planning);
            successLoader();
          })
          .catch(handleCatchError);
      }

      function rightClickPlanning(container){
        randomString();

        removePlanningActions(lastString());

        let x = container.offset().left + container.outerWidth() + 5;
        let y = container.offset().top;
        let dw = $(document).width();


        $('body').append(
          `<div class="bg-modal border border-dark planning-actions position-absolute rounded shadow ${lastString()}" style="display: none; ${x + 160 > dw ? `right: 10px` : `left: ${x}px`}; top: ${y}px" >
          <a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer" onclick="fillUpdateModal(${_this.planning.id})" >Wijzigen</a>
          ${(!Number(_this.planning.released) && _settings.vooruit_planning == 'released') ? `<a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer" onclick="releasePlanning(${_this.planning.id})" >Vrijgeven</a>` : ''}
          ${Number(_this.planning.completed) ? '' : `<a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer" onclick="completePlanning(${_this.planning.id})" >Afronden</a>`}
          ${Number(_this.planning.explorer.length) ? `<a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer" onclick="fillFilesModal(${_this.planning.id})" >Bestanden</a>` : ''}
          ${auth.google_token ? `<a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer text-dark text-decoration-none" href="${url}/planning/google/calendar/${_this.planning.id}" target="_blank" >Google Agenda</a>` : ''}
          <a class="px-3 py-2 hover-bg-inverse-primary d-block cursor-pointer text-danger" onclick="confirmDelete(${_this.planning.id})" >Verwijderen</a>
        </div>`
        );

        $(`.${lastString()}`).toggle(_this.transition);
      }
      function removePlanningActions(exception = 'ASD'){
        $('.planning-actions').not(`.${exception}`).toggle(_this.transition);
        setTimeout(() => {
          $('.planning-actions').not(`.${exception}`).remove();
        }, _this.transition);
      }

      function findVerlofById(verlof_id) {
        for (const dateKey in verlof) {
          const dayObj = verlof[dateKey];
          for (const key in dayObj) {
            const verlofArray = dayObj[key];
            for (const verlofObj of verlofArray) {
              if (verlofObj.id == verlof_id) {
                return verlofObj;
              }
            }
          }
        }
        return null;
      }

      function appendPlanning(arr){
        try{
          let userDayRows = {};

          for(const row of arr){
            const check = planning.find(pl => pl.id == row.id);
            if(!check){ planning.push(row); }

            if(!userDayRows[row.user_id]){ userDayRows[row.user_id] = []; }
            const dayRows = userDayRows[row.user_id];
            let adres = `${row.straat||''}${row.huisnummer||''}${row.postcode||''}${row.plaats||''}`

            let opmerking = row.opmerking || ''
            let klantSearch = ''
            let klantNaam = ''
            if(row.klanten){
              const {naam} = row.klanten

              klantSearch = `${naam || ''}${row.klanten.contactpersoon_voornaam || ''}${row.klanten.contactpersoon_achternaam || ''}`;
              klantNaam = naam ? naam : `${row.klanten.contactpersoon_voornaam || ''} ${row.klanten.contactpersoon_achternaam || ''}`
            }

            let infoActiviteit = ''
            let search = ''
            let color = ''

            if(row.type == 'legenda'){
              infoActiviteit = `<div>Activiteit: ${row.legenda.naam}</div>`;
              search = `legenda ${row.legenda.naam}`;
              color = row.legenda.kleur;
            }
            else if(row.type == 'aanvraag'){
              infoActiviteit = `<div>Aanvraag: ${row.aanvraag.aanvraagnummer}</div>`;
              search = `aanvraag ${row.aanvraag.aanvraagnummer}`;
              color = `#ff6600`;
            }
            else if(row.type == 'checklist'){
              infoActiviteit = `<div>${row.checklist.description}</div>`;
              search = `${row.checklist.description} ${row.checklist.checklistnummer}`;
              color = `#40E0D0`;
            }
            else if(row.type == 'project'){
              infoActiviteit = `<div>Project: ${row.project.projectnaam || ''} (${row.project.projectnr})</div>`;
              search = `project ${row.project.projectnr} ${row.project.projectnaam || ''}`;
              if(planningItemKleuren == "Taak"){
                color = '#B4B4B4';
              }else{
                color = row.project.planning_color || '#169BD7';
              }
            }
            else if(row.type == 'verlof'){
              infoActiviteit = `<div>Verlof</div>`;
              color = `#000000`;
              if (row.verlof_id){
                foundVerlof = findVerlofById(row.verlof_id);
                if (foundVerlof != null){
                  const reden = verlofRedenen.find(r => r.id == foundVerlof.reden_id);
                  color = reden.kleur;
                  infoActiviteit = `<div>${reden.reden}<br>${foundVerlof.opmerkingen}</div>`;
                }
              }
            }

            let taken = ''
            for(const taak of row.taken){
              taken +=  `<div class='p-1 m-1 rounded text-white' style='background-image: linear-gradient(to bottom right, ${taak.planning_color}80, ${taak.planning_color});'>${taak.name}</div>`
            }

            let machines = '';
            for(const machine of row.machines){
              machines += `<img width='40' class='m-2 tippy' data-tippy-content='<div>${machine.name || ''}</div><small>${machine.group ? machine.group.name : ''}</small>' src='${url}/client/public/img/machines/${machine.icon}.png'> `
            }

            if(klantNaam){klantNaam = klantNaam.replaceAll('"', '`').replaceAll("'", "`");}
            if(search){search = search.replaceAll('"', '`').replaceAll("'", "`");}
            if(infoActiviteit){infoActiviteit = infoActiviteit.replaceAll('"', '`').replaceAll("'", "`");}
            if(adres){adres = adres.replaceAll('"', '`').replaceAll("'", "`");}
            if(opmerking){opmerking = row.opmerking.replaceAll('"', '`').replaceAll("'", "`");}
            if(klantSearch){klantSearch = klantSearch.replaceAll('"', '`').replaceAll("'", "`");}


            const _completed = {
              badge: '',
              tippy: ''
            }
            const _released = {
              badge: '',
              tippy: ''
            }

            if(!Number(row.completed)){
              _completed.badge = `<span class='mx-1' ><i class='fa-regular fa-hourglass-half m-0'></i></span>`;
              _completed.tippy = `<div class='mx--1 mb-1 flex-between' > <span class='mx-1'>Niet afgerond</span> <span class='mx-1' ><i class='fa-regular fa-hourglass-half m-0'></i></span> </div>`
            }
            if(!Number(row.released) && _settings.vooruit_planning == 'released'){
              _released.badge += `<span class='mx-1' ><i class='far fa-eye-slash m-0'></i></span>`;
              _released.tippy = `<div class='mx--1 mb-1 flex-between' > <span class='mx-1'>Niet vrijgegeven</span> <span class='mx-1' ><i class='far fa-eye-slash m-0'></i></span> </div>`
            }

            let div = `<div
                      data-id="${row.id}"
                      data-info-activiteit="${infoActiviteit}"
                      data-info-klant="${klantNaam}"
                      data-info-tijd="<div class='flex-between mb-2 mx--2' ><span class='mx-2' >${row.begin.slice(0, 5)}-${row.eind.slice(0, 5)}</span> <div class='mx-1' >${_completed.badge} ${_released.badge}</div> </div>"
                      data-info-adres="<div>${row.straat || ''} ${row.huisnummer || ''}</div>"
                      data-info-taken="
                        <div class='${row.taken.length ? 'bg-white rounded p-1' : ''}'>
                          ${taken}
                        </div>"
                      data-info-machines="<div class='mx--2 show-machines show-element'>${machines}</div>"
                      data-search="${search} ${row.begin} ${row.eind} ${adres || ''} ${convert(row.datum)} ${opmerking} ${klantSearch || ''}"
                      data-tippy-content="
                      ${_completed.tippy}
                      ${_released.tippy}
                      ${(_released.tippy || _completed.tippy) ? `<div class='m-3 border-bottom h-1'></div>` : ''}
                      ${infoActiviteit}
                      <div>${row.begin.slice(0, 5)}-${row.eind.slice(0, 5)}</div>
                      <div>${opmerking}</div>
                      <div>${row.straat || ''} ${row.huisnummer || ''}</div>
                      <div>${row.postcode || ''} ${row.plaats || ''}</div>"
                      class="planning-${row.id} ${type == 'dag' ? 'font-size-08' : 'm-1'} p-2 w-100 h-100 rounded text-white tippy planning-element searchRow" style="background-image: linear-gradient(to bottom right, ${color}80, ${color})">
                      </div>`

            if(type == 'dag'){

              div = `<div class="h-100 overflow-hidden rounded" data-start="${row.begin}" data-end="${row.eind}" data-id="${row.id}" data-day-container="${row.id}">
                  ${div}
                </div>`

              rowLoop: for(let x = 0; x < 100; x++){
                //Create and push planning when craeting a new row
                if(!dayRows[x]){
                  dayRows[x] = [ {planning: row, div: div} ];
                  break
                }

                for(const element of dayRows[x]){
                  const {begin, eind} = element.planning;

                  //check for overlapping, if exists go to the next row
                  if(isTimeBetween(row.begin, begin, eind)){continue rowLoop}
                  if(isTimeBetweenEquals(row.eind, begin, eind)){continue rowLoop}
                  if(isTimeSmaller(row.begin, begin) && isTimeBigger(row.eind, eind)){continue rowLoop}
                }
                dayRows[x].push({planning: row, div: div});
                break rowLoop;
              }

            }
            else{
              $(`.day-td[data-user='${row.user_id}'][data-day='${row.datum}']`).find('.act-div').append(div);
            }
          }

          if(type == 'dag'){

            const activeUren = _settings.dagoverzicht_active_uren || []

            for(const u in userDayRows){
              for(const r in userDayRows[u]){

                //Append row container if doesnt exist
                if(!$(`[data-day-container=${u}]`).find(`[data-day-row-container=${r}]`).length){
                  $(`[data-day-container=${u}]`).find('.act-div').append(
                    `<div data-day-row-container="${r}" class="d-flex my-1" ></div>`
                  )
                }
                const container = $(`[data-day-container=${u}]`).find(`[data-day-row-container=${r}]`)

                let lastHour = null
                let maxEindHour = `${activeUren[activeUren.length - 1]}:59`;

                for(const element of userDayRows[u][r]){
                  const { planning, div } = element;
                  let { begin, eind } = planning;

                  //First element, check if planning element is earlier than active hours setting
                  if(!lastHour){
                    lastHour = `${('0'+activeUren[0]).slice(-2)}:00`;
                    if(isTimeSmaller(begin, lastHour)){
                      begin = lastHour;
                    }
                  }

                  let diff = diffInHours(lastHour, begin);
                  container.append(`<div data-day-filler style="width: ${diff / activeUren.length * 100}%" ></div>`)
                  lastHour = eind;

                  //Check if eind isn't bigger than displayed hours
                  if(isTimeBigger(eind, maxEindHour)){ eind = maxEindHour; }

                  diff = diffInHours(begin, eind);
                  container.append(`<div class="cursor-pointer" style="width: ${diff / activeUren.length * 100}%" >${div}</div>`)


                }
              }
            }
          }

          tippyInit();

          fillPlanningInfoContent();
        }
        catch (e) { handleCatchError(e); }
      }
      function deletePlanning(id){
        loader();

        ajax(`/planning/delete/${id}`, {id: id})
          .then(() => {
            successLoader();
            deletePlanningObject(id);

            $('#inplannenModal').hideModal();
          })
          .catch(handleCatchError)
      }
      function deletePlanningObject(id){
        try{
          $(`.planning-${id}`).remove();
          for(const [i, row] of planning.entries()){
            if(!row){ continue }
            if(row.id == id){
              delete planning[i];
              break;
            }
          }
          planning = resetIndex(planning);
        }
        catch (e) { handleCatchError(e); }
      }


      function projectColor(id){
        const project = getProject(id).then(project => {
          if(!project){ return }

          const legenda = project.legenda || [];

          let content = '';
          for(const i in legenda){
            const row = legenda[i];
            content += `<div class="my-1 py-1" onclick="_hexColor.instance.color.hexString = '${row.kleur}'" >
                        <div class="d-flex align-items-end rounded cursor-pointer p-1 mx-1 hover-shadow" >
                          <div class="p-3 rounded mr-2" style="background-image: linear-gradient(to bottom right, ${row.kleur}80, ${row.kleur});" ></div>
                          <span class="nobr overflow-hidden" >${row.naam}</span>
                        </div>
                      </div>`
          }
          content = content ? `<div class="max-h-300 overflow-auto" ><b>Legenda</b>${content}</div>` : '';

          getHexColor({
            color: project.planning_color,
            extra_content: content
          })
            .then((hex) => {
              loader();
              ajax('api/projecten/color', {id: id, color: hex})
                .then(() => {
                  successLoader();
                  project.planning_color = hex;

                  $(`[data-project-taak-color=${id}]`).attr('style', `background-image: linear-gradient(to bottom right, ${hex}80, ${hex})`);
                  $(`[onclick='projectColor(${id})']`).attr('style', `background-image: linear-gradient(to bottom right, ${hex}80, ${hex})`);

                  for(const row of planning){
                    if(row.project_id != id){ continue }
                    row.project.planning_color = hex;
                    $(`.planning-${row.id}`).attr('style', `background-image: linear-gradient(to bottom right, ${hex}80, ${hex})`);
                  }
                })
                .catch(err => errorLoader());
            })
            .catch(handleCatchError);
        });
      }
      function storeProjectColor(id){
        $("#color-modal-btn").html(`@spinner`)
        $.ajax({
          type: "POST",
          url: "{{url("api/projecten/color")}}",
          data: {
            id: id,
            color: projectColorWheel.color.hexString,
            _token: "{{csrf_token()}}",
          },
          success: function () {
            location.reload();
          },
          error: function () {
            $("#color-modal-btn").html(`<div class="alert alert-danger w-100 text-left my-2" >Er is iets foutgegaan</div>`)
          }
        });
      }

      function openExplorer(id){
        getExplorerFile()
          .then((res) => {
            const {status, file} = res;

            if(!status){return;}

            $(id).append(
              `<div id="${randomString()}" class="my-1 d-flex justify-content-between" >
               <div class="d-flex align-items-center" >
                 <img height="50" src="${url}/client/public/img/explorer/files/${file.icon}" >
                 <h4 class="my-0 mx-2" >${file.name}</h4>
               </div>
               <a class="btn btn-danger text-white align-self-center" onclick="deleteDiv('#${lastString()}')" >@icon_trash</a>
               <input type="hidden" value="${file.id}" name="explorer_files[]">
             </div>`
            );
          })
          .catch(() => {
            errorLoader();
          })
      }
      function deleteDiv(id) {
        $(id).remove();
      }
      function startPost(method){
        if(method === true){
          $("#calcRouteBtn").removeClass("btn-primary").addClass("btn-inverse-primary").attr("onclick", "");
          $("#calcRouteSpinner").removeClass("d-none").addClass("d-inline-block");
          setTimeout(calcRoute, 100);
        }
        else{
          $("#calcRouteBtn").addClass("btn-primary").removeClass("btn-inverse-primary").attr("onclick", "startPost(true)");
          $("#calcRouteSpinner").addClass("d-none").removeClass("d-inline-block");
        }
      }
      function calcRoute(){
        let sl = @json(getSettingValue("standaard_planning_adres"));
        let date = new Date($("#routeDatum").val() +" "+ $("#routeTijd").val());
        let dateString = $("#routeDatum").val();
        $("#tableAppend").html(
          '<table id="routeTable">'+
          '<thead>'+
          '<tr>'+
          '<th>Medewerker</th>'+
          '<th>Opmerking</th>'+
          '<th>Locatie</th>'+
          '<th>Beschikbaar om</th>'+
          ' <th>Afstand</th>'+
          '<th>Reistijd naar de nieuwe locatie</th>'+
          '<th>Bechikbaar op de nieuwe locatie om</th>'+
          '</tr>'+
          '</thead>'+
          '<tbody id="routeTBody" >'+
          '</tbody>'+
          '</table>'
        );
        for(let i in users){
          let append = '';
          let currentLocation = false;
          let prevAct = [];
          if(sl){
            $.ajax({
              type: "POST",
              async: false,
              url: '{{url("planning/calcroute")}}',
              data: {
                origin: sl.replaceAll(" ", "%20"),
                destination: ($("#routeStraat").val()+"%20"+$("#routeHuisnummer").val()+"%20"+$("#routePostcode").val()+"%20"+$("#routePlaats").val()).replace(" ", "%20"),
                _token: '{{csrf_token()}}'
              },
              success: function(response){
                let locatieTijd = new Date(date.getTime()+response.duration.value*1000)
                append =
                  "<tr>" +
                  "<td>"+users[i].name+" "+users[i].lastname+"</td>" +
                  "<td><div class='alert alert-secondary'>Reistijd obv standaard locatie</div></td>" +
                  "<td>"+sl+"</td>"+
                  "<td class='text-center' >"+date.getHours()+":"+("0"+date.getMinutes()).slice(-2)+"</td>" +
                  "<td class='text-center' >"+response.distance.text+"</td>" +
                  "<td class='text-center'>"+response.duration.text+"</td>" +
                  "<td><div class='alert alert-success text-center' >"+locatieTijd.getHours()+":"+("0"+locatieTijd.getMinutes()).slice(-2)+"</div></td>" +
                  "</tr>";
              },
            });
          }
          else{
            append =
              "<tr>" +
              "<td>"+users[i].name+" "+users[i].lastname+"</td>" +
              "<td><div class='alert alert-danger' >Locatie niet beschikbaar</div></td>" +
              "<td></td>"+
              "<td class='text-center' ></td>" +
              "<td class='text-center' ></td>" +
              "<td class='text-center'></td>" +
              "<td class='text-center text-white'>9999999</td>" +
              "</tr>";
          }
          for(const pl of planning.filter(row => (row.user_id == i && row.datum == dateString))){
            let begin = new Date(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate()+" "+pl.begin.slice(0, 5)).getTime();
            let eind = new Date(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate()+" "+pl.eind.slice(0, 5)).getTime();
            let locatie = "";
            let klant;
            if(eind < date.getTime()){
              prevAct.push(pl);
            }
            if(isBetween(date.getTime(), begin, eind)){
              if(pl.klant_id){
                currentLocation = true;
                klant = klanten[pl.klant_id];
                if(klant.straat){locatie += klant.straat+" ";}
                if(klant.huisnummer){locatie += klant.huisnummer+" ";}
                if(klant.huisnummer && klant.toevoeging){locatie += klant.toevoeging+" ";}
                if(klant.postcode){locatie += klant.postcode+" ";}
                if(klant.plaats){locatie += klant.plaats+" ";}
                $.ajax({
                  type: "POST",
                  async: false,
                  url: '{{url("planning/calcroute")}}',
                  data: {
                    origin: locatie.replaceAll(" ", "%20"),
                    destination: ($("#routeStraat").val()+"%20"+$("#routeHuisnummer").val()+"%20"+$("#routePostcode").val()+"%20"+$("#routePlaats").val()).replace(" ", "%20"),
                    _token: '{{csrf_token()}}'
                  },
                  success: function(response){
                    let locatieTijd = new Date(eind+response.duration.value*1000)
                    let opmerking = '';
                    append =
                      "<tr>" +
                      "<td>"+users[i].name+" "+users[i].lastname+"</td>" +
                      "<td>"+opmerking+"</td>" +
                      "<td>"+locatie+"</td>"+
                      "<td class='text-center' >"+pl.eind.slice(0, 5)+"</td>" +
                      "<td class='text-center' >"+response.distance.text+"</td>" +
                      "<td class='text-center'>"+response.duration.text+"</td>" +
                      "<td><div class='alert alert-success text-center' >"+("0"+locatieTijd.getHours()).slice(-2)+":"+("0"+locatieTijd.getMinutes()).slice(-2)+"</div></td>" +
                      "</tr>";
                  },
                });
              }
            }
          }
          if(currentLocation === false){
            for(let x = prevAct.length - 1; x >= 0; x = x-1) {
              if (prevAct[x].klant_id) {
                let act = prevAct[x];
                let klant = act.klanten
                if (klant.postcode || klant.plaats || klant.straat) {
                  let locatie = '';
                  if (klant.straat) {
                    locatie += klant.straat + " ";
                  }
                  if (klant.huisnummer) {
                    locatie += klant.huisnummer + " ";
                  }
                  if (klant.huisnummer && klant.toevoeging) {
                    locatie += klant.toevoeging + " ";
                  }
                  if (klant.postcode) {
                    locatie += klant.postcode + " ";
                  }
                  if (klant.plaats) {
                    locatie += klant.plaats + " ";
                  }
                  $.ajax({
                    type: "POST",
                    async: false,
                    url: '{{url("planning/calcroute")}}',
                    data: {
                      origin: locatie.replaceAll(" ", "%20"),
                      destination: ($("#routeStraat").val() + "%20" + $("#routeHuisnummer").val() + "%20" + $("#routePostcode").val() + "%20" + $("#routePlaats").val()).replace(" ", "%20"),
                      _token: '{{csrf_token()}}'
                    },
                    success: function (response) {
                      let eind = new Date(act.datum + " " + act.eind).getTime();
                      let locatieTijd = new Date(eind + response.duration.value * 1000)
                      let opmerking = '<div class="alert alert-warning">Reistijd obv laatst bekende locatie (' + act.begin.slice(0, 5) + ' - ' + act.eind.slice(0, 5) + ')</div>'
                      append =
                        "<tr>" +
                        "<td>" + users[i].name + " " + users[i].lastname + "</td>" +
                        "<td>" + opmerking + "</td>" +
                        "<td>" + locatie + "</td>" +
                        "<td class='text-center' >" + act.eind.slice(0, 5) + "</td>" +
                        "<td class='text-center' >" + response.distance.text + "</td>" +
                        "<td class='text-center'>" + response.duration.text + "</td>" +
                        "<td><div class='alert alert-success text-center' >" + ("0" + locatieTijd.getHours()).slice(-2) + ":" + ("0" + locatieTijd.getMinutes()).slice(-2) + "</div></td>" +
                        "</tr>";
                    },
                  });
                }
              }
            }
          }
          $("#routeTBody").append(append);
        }
        $("#routeTable").DataTable({
          "order": [[ 6, "asc" ]],
          "pageLength" : 100,
        });
        $("#showReistijdModal").click();
        startPost(false);
      }
      function isBetween(point, a, b){
        return point >= a && point <= b;

      }
      function format(date){
        return ("0"+date.getDate()).slice(-2)+"-"+("0"+date.getMonth()).slice(-2)+"-"+date.getFullYear();
      }
      function diff(dt2, dt1) {
        return dt2.getTime() - dt1.getTime()
      }

      function rowInplannen(id){
        clearInplannenModal();
        resetGoogleCalendar();
        resetMachines();
        resetFiles()

        addMachines('#offertes-machines');
        setPushmelding();
        setKlantMail();

        werkbonPrefillInit()

        $("#offertesUsers").empty();
        let act = offertePlanning[id];
        let offerte = offertes[act.offerte_id];
        let klant = klanten[offerte.klant_id];
        let alert = true;
        for(let index in legenda){
          if(legenda[index].naam === act.naam){
            $("#rowLegenda").val(index).attr("readonly", true)
            alert = false;
          }
        }
        if(alert){
          $("#rowAlert").removeClass("d-none")
          $("#rowLegenda").attr("readonly", false);
        }
        else{
          $("#rowAlert").addClass("d-none")
        }

        let adresSetting = @json(getSettingValue("planning_adres"));
        if(adresSetting == "Aan" && klant){
          let straat  =klant.straat || '';
          let huisnummer = (klant.huisnummer || '')+(klant.toevoeging || '');
          let postcode = klant.postcode || '';
          let plaats = klant.plaats || '';
          if(offerte.locatie){
            straat = offerte.locatie.straat || '';
            huisnummer = (offerte.locatie.huisnummer || '')+(offerte.locatie.toevoeging || '');
            postcode = offerte.locatie.postcode || '';
            plaats = offerte.locatie.plaats || '';
          }
          $("#offerteStraat").val(straat);
          $("#offerteHuisnummer").val(huisnummer);
          $("#offertePostcode").val(postcode);
          $("#offertePlaats").val(plaats);
        }


        $("#rowNaam").val(act.naam);
        $("#rowOmschrijving").val(act.omsch);
        $("#rowBegin").val(act.begin.replace(" ","T"));
        $("#rowEind").val(act.eind.replace(" ","T"));
        $("#rowEind").val(act.eind.replace(" ","T"));
        $("#rowKlantNaam").val(klant.naam);
        $("#rowKlantId").val(klant.id);
        $("#rowId").val(id);
      }
      function inplannen(userId, user, date){
        _projectInplannen.user = userId;
        _projectInplannen.date = date;
        clearInplannenModal();
        resetGoogleCalendar();
        resetMachines();
        resetFiles();

        addMachines('#inplannen-machines');
        setPushmelding();
        setProject()
        setKlantMail();

        werkbonPrefillInit();

        $("#inplannen-beschikbaarheid").html('');
        $("#inplannenModalToggle").html("Activiteit inplannen")
        $("#inplannenModalAanpassen").removeClass("d-none");
        $("#inplannenModalInplannen").addClass("d-none");
        $("#inplannenUsers").html("");
        $("#userIdInplannenName").val(user);
        $("#userIdInplannen").val(userId);
        $("#dateInplannen").val(date);
        $("#inplannenInfo").html(`<div class='col-12'><h4>${convertWithMonthName(date)}</h4></div>`);
        $("#inplannenModalAanpassenBody").html("");
        $(".activiteitBegintijd").val(date+`T${settings.prefillBegin}`);
        $(".activiteitEindtijd").val(date+`T${settings.prefillEind}`);

        if(verlof[date] && verlof[date][userId] && verlof[date][userId].van !== null && verlof[date][userId].tot !== null){
          const ver = verlof[date][userId];
          for(let v of ver){
            if(v.van && v.tot){
              $("#inplannenInfo").append("<div class='alert alert-warning'>Verlof: van "+v.van.slice(0,5)+" tot "+v.tot.slice(0,5)+"</div>");
            }
            else{
              $("#inplannenInfo").append("<div class='alert alert-warning'>Verlof: hele dag!</div>");
            }
          }
        }
        if(showBeschikbaarheidState && beschikbaarheid[userId] && beschikbaarheid[userId][date]){
          $("#inplannen-beschikbaarheid").append('<h4>Beschikbaarheid</h4>');
          for(let row of beschikbaarheid[userId] && beschikbaarheid[userId][date]){
            let opmerking = '';
            let alert = 'alert-danger';
            let period = row.start.slice(0, 5)+' - '+row.end.slice(0, 5);

            if(row.type === '1'){alert = 'alert-success'}
            if(row.day === '1'){period = 'Hele dag'}
            if(row.opmerking){opmerking = '<small class="d-block" >'+row.opmerking+'</small>'}

            $("#inplannen-beschikbaarheid").append('<div class="alert '+alert+'" ><span class="d-block" >'+period+'</span>'+opmerking+'</div>');
          }
        }

        const user_dag_planning = planning.filter(row => (row.user_id == userId && row.datum == date));

        if(user_dag_planning.length){
          $(user_dag_planning).each(function(i){
            var opmerking = '';
            let klant = '';
            let kleur = '';
            let naam = '';
            let offerteAlert = '';
            if(this.klanten){
              const titel = this.klanten.naam ? this.klanten.naam : this.klanten.contactpersoon_voornaam +" "+ this.klanten.contactpersoon_achternaam ;
              klant = "<div class='col-12 mt-2 mb-2'><b>Klant: </b>"+titel+"</div>"
            }
            if(this.opmerking){
              opmerking = "<div class='col-12 mt-2 mb-2'><b>Opmerking: </b><span id='opmerking"+this.id+"' >"+this.opmerking+"</span></div>";
            }
            if(this.type === "legenda"){
              kleur = this.legenda.kleur;
              naam = this.legenda.naam;
              if(this.offerte_id){
                if(allOffertes[this.offerte_id].status != "Akkoord"){
                  offerteAlert = '<div> <div class="alert alert-warning">Offerte is nog niet geaccordeerd</div> </div>'
                }
              }
            }
            else if(this.type === "aanvraag"){
              kleur = "#ff6600";
              naam = this.aanvraag.aanvraagnummer;
            }
            else if(this.type === "checklist"){
              kleur = "#40E0D0";
              naam = this.checklist.description;
            }
            else if(this.type === "project"){
              kleur = this.project.planning_color || "#169BD7";
              naam = this.project.projectnr;
            }

            let price = '';
            let machs = '';
            let taken = '';
            let werkbonKeywords = '';
            if(this.aantal_personen || this.prijs || this.btw){
              price =
                "<div class='col-12 mt-2 mb-2'>" +
                "<div class='row m-0 p-2 shadow-inset rounded border'>" +
                "<div class='col-6'><b>Aantal personen</b></div>"+
                "<div class='col-3'><b>Prijs</b></div>"+
                "<div class='col-3'><b>BTW</b></div>"+
                "<div class='col-6'>"+(this.aantal_personen || 0)+"</div>"+
                "<div class='col-3'>€ "+(this.prijs || 0)+"</div>"+
                "<div class='col-3'>"+(this.btw || 0)+"%</div>"+
                "</div>"+
                "</div>";
            }
            if(this.taken.length){
              taken = '<div class="col-12 mt-2" ><b>Taken</b><ul class="m-0 pl-4" >';
              for(const taak of this.taken){
                taken += `<li>${taak.name}</li>`
              }
              taken += '</ul></div>';
            }
            if(this.machines.length){
              machs = '<div class="col-12 mt-2" ><b>Machines</b><div class="mx--2" >';
              for(const machine of this.machines){
                machs += `<img width="40" class="mx-2 tippy" data-tippy-content="<div>${machine.name}</div><small>${machine.group.name}</small>" src="${url}/client/public/img/machines/${machine.icon}.png">`
              }
              machs += '</div></div>';
            }
            if(this.werkbon_keywords){
              let keywords = JSON.parse(this.werkbon_keywords);
              let tables = '';
              for(const id in keywords){
                let items = '';
                let template;
                for(const key in keywords[id]){
                  template = werkbonTemplates.find(row => row.id == id);
                  if(!template){continue;}
                  const keyword = template.keywords.find(row => row.keyword == key);
                  if(!keyword){continue;}
                  items += `<tr><th class='w-0 p-1' >${keyword.naam}:</th><td class='p-1' >${keywords[id][key]}</td></tr>`
                }
                tables = `<div class="bg-inverse-secondary border rounded p-2" ><label>Werkbon template: ${template.naam}</label><table class="w-100" >${items}</table></div>`;
              }
              werkbonKeywords = `<div class="col-12" >${tables}</div>`;
            }




            $("#inplannenModalAanpassenBody").append(
              `<div  class='pt-3  mb-3 rounded shadow cursor-pointer' style='border: 1px solid ${kleur}' >
              <div class='row m-0' onclick='showButtons("overzichtDiv${this.id}")' >
                ${offerteAlert}
                <div class='col-7'><h4><b>${naam}</b></h4></div>
                <div class='col-5 text-right'><b>${this.begin.slice(0,5)}-${this.eind.slice(0,5)}</b></div>
                ${klant}
                ${taken}
                ${opmerking}
                ${price}
                ${machs}
                ${werkbonKeywords}
              </div>
              <div class='m-2'>
                <div id='overzichtDiv${this.id}' class='row d-none'>
                  <div class='col-6'></div>
                  <div class='col-6'><small id='text${this.id}' class='d-none'>Klik nogmaals om te bevestigen</small></div>
                  <div class='col-6'><a onclick='fillUpdateModal(${this.id})' data-dismiss='modal' class='btn btn-primary w-100 text-white'>Wijzigen</a></div>
                  <div class='col-6'><a id='button${this.id}' onclick='confirmDelete(${this.id})' class='btn btn-danger w-100 text-white'>Verwijderen</a></div>
                </div>
              </div>
              <div style='height: 25px; background-image: linear-gradient(to right bottom, ${kleur}, ${kleur}BF, ${kleur})' ></div>
            </div>`
            );
          });
        }
        else{
          $("#inplannenModalAanpassenBody").html('<div><h4 class="text-center text-muted" >Geen planning gevonden</h4></div>')
        }
        tippyInit();
      }
      function aanvraagInplannen(id){
        let aanvraag = aanvragen[id];
        let klant = klanten[aanvraag.klant_id];

        clearInplannenModal();
        resetGoogleCalendar();
        resetMachines();
        resetFiles();

        addMachines('#aanvragen-machines');
        setPushmelding();
        setKlantMail();

        werkbonPrefillInit()

        $("#aanvragenUsers").html("")
        $("#aanvraagId").val(id);
        if(aanvraag.klant_id){
          $("#aanvraagKlant").val(klanten[aanvraag.klant_id].naam);
          $("#aanvraagKlantId").val(klanten[aanvraag.klant_id].id);
        }
        else{
          $("#aanvraagKlant").val("Geen bijbehorend klant!");
        }

        let adresSetting = @json(getSettingValue("planning_adres"));
        if(adresSetting == "Aan" && klant){
          $("#aanvraagStraat").val(klant.straat ? klant.straat : '');
          $("#aanvraagHuisnummer").val(klant.huisnummer ? klant.huisnummer : '' + klant.toevoeging ? klant.toevoeging : '');
          $("#aanvraagPostcode").val(klant.postcode ? klant.postcode : '');
          $("#aanvraagPlaats").val(klant.plaats ? klant.plaats : '');
        }
      }

      function releasePlanning(id) {
        const plan = planning.find(row => row.id == id);

        $('#inplannenModal').hideModal();

        confirmModal({
          text: 'Weet je zeker dat je deze activiteit wilt vrijgeven?',
        }).then((response) => {

          if (!response.status) {
            return
          }

          loader();
          ajax('api/planning/release', {id: plan.id})
            .then((response) => {
              const {werkbon} = response;
              successLoader();

              if (werkbon) {
                notification(`Werkbon <b>${werkbon.werkbonnummer}</b> aangemaakt!`, 'success', 7.5);
              }
              if (type == 'dag') {
                reInitPlanning();
                return
              }

              plan.released = 1;

              deletePlanningObject(plan.id);
              appendPlanning([plan]);
            })

            .catch(handleCatchError)
        })
      }
      function releaseDagPlanning(dag) {
        confirmModal({
          text: 'Weet je zeker dat je deze activiteiten wilt vrijgeven?',
        }).then((response) => {

          if (!response.status) {
            return
          }

          loader();
          ajax('api/planning/releasedag/', {dag: dag})
            .then((response) => {
              const {werkbonnen, planning} = response;
              successLoader();
              if (werkbonnen) {
                notification(`Werkbonnen aangemaakt!`, 'success', 7.5);
              }

              for (const plan of planning) {
                const planning_object = planning.find(row => row.id == plan.id);
                planning_object.released = 1;

                deletePlanningObject(plan.id);
                appendPlanning([planning_object]);
              }
            })

            .catch(handleCatchError)
        })
      }
      function completePlanning(id) {
        const plan = planning.find(row => row.id == id);

        $('#inplannenModal').hideModal();

        confirmModal({
          text: 'Weet je zeker dat je deze activiteit wilt afronden?',
        }).then((response) => {

          if (!response.status) {
            return
          }

          loader();
          ajax('api/planning/complete', {id: plan.id})
            .then(() => {
              if (type == 'dag') {
                reInitPlanning();
                return
              }

              plan.completed = 1;
              deletePlanningObject(plan.id);
              appendPlanning([plan]);
              successLoader();
            })
            .catch(handleCatchError)
        })
      }

      function initTakenSearch(){
        if(isIframe || preview){return;}
        if(!settings.planning_elementen['projecttaken']){return;}
        initInforSearch();
        _inforSearch.get('taken_search').onchange = fillTaken;
      }

      function fillTaken(id = null) {
        $('.project-taken-container').html(`<div class='my-4 text-center'>@spinner</div>`);

        const generateTakenHeader = () => {
          let header = resetIndex(settings.takenCustomRows).map(row => `<td>${row.name}</td>`).join('');
          if (settings.werkbonnenSetting === 'Aan') {
            header += `<td>werkbonnummer</td>`;
          }
          return `
            <tr class='font-weight-semibold border-bottom'>
              ${_settings.projecten_taken_hide_name !== 'ja' ? '<td>Naam</td>' : ''}
              ${header}
              <td></td>
            </tr>`;
        };

        PrefillFilters();

        const container = $('[data-section-taken]');
        let data = {
          sort_by: container.find('[name=sorteren]').val(),
          sort_type: container.find('[name=ascdesc]').val(),
          vestiging: container.find('[name=vestiging]').val(),
          bv: container.find('[name=bv]').val(),
          taken_planned: container.find('[name=taken_planned]').val(),
          relations: ['taken', 'planning', 'vestiging', 'werkbonnen'],
        };

        if (id && typeof id === 'string') {
          data = { ids: [id], relations: ['taken', 'planning', 'vestiging', 'werkbonnen'] };
        }

        if (settings.alleenInOpdracht === 'Aan') data.status = ['Opdracht'];
        if (settings.afgerondenProjectenTonen === 'Aan') {
          data.status = Array.isArray(data.status) ? data.status : [];
          data.status.push('Afgerond');
        }
        if (_settings.planning_project_taken_hide_project_btn === 'ja' || data.taken_planned === 'ingepland') {
          data.must_have_taken = true;
        }

        loader();
        ajax('api/projecten/get', data)
          .then(fillTaakResponse => {
            let divs = '';

            for (const project of fillTaakResponse.projecten) {
              let search = `${project.projectnr || ''}${project.projectnaam || ''}${project.opdrachtgever || ''}`;
              let taken = '';

              for (const taak of project.taken) {
                const completed = !!Number(taak.completed);
                search += taak.name || '';

                let btn = '';
                if (!completed) {
                  btn = `<a class="btn btn-inverse-primary" onclick="inplannenTaken(${project.id}, ${taak.id})">Inplannen</a>`;
                  if (taak.planning.length) {
                    let tippy = '';
                    for (const row of taak.planning) {
                      tippy += ` <div class='text-left'><b class='mr-2'>${convert(row.datum)}</b> ${row.begin.slice(0, 5)} - ${row.eind.slice(0, 5)}</div>`;
                    }
                    btn = `<a class="btn btn-inverse-success tippy" data-tippy-content="${tippy}" onclick="inplannenTaken(${project.id}, ${taak.id})">Ingepland</a>`;
                  }
                }

                let tds = '';
                for (const i in settings.takenCustomRows) {
                  const row = settings.takenCustomRows[i];
                  const value = taak.custom.find(item => item.keyword === row.keyword);
                  let v = value ? value.value : '';
                  if (v) {
                    if (value.type === 'date') v = convert(v);
                    else if (value.type === 'checkbox') {
                      v = Number(v)
                        ? `<span class="font-size-125 rounded text-success">@icon_confirm</span>`
                        : `<span class="font-size-125 rounded text-danger">@icon_close</span>`;
                    } else if (value.type === 'user' && value.user) {
                      const { name, lastname } = value.user;
                      v = `${name || ''} ${lastname || ''}`;
                    } else if (value.type === 'machine' && value.machine) {
                      v = `${value.machine.name || ''}`;
                    } else if (value.type === 'checklist') {
                      const list = JSON.parse(value.value || '');
                      v = list
                        .map(item => `<div class="checklist-taak d-flex font-size-075 hover-shadow px-2 py-1 rounded-pill tippy" data-tippy-content="${item.status || (item.checked ? 'Afgerond' : 'Geen status')}">
                            <span class="mr-2" style="color:${item.statuscolor || (item.checked ? '#5cb85c' : '#999999')}"><i class="fa-solid fa-circle m-0"></i></span>
                            <span>${item.name}</span>
                          </div>`)
                        .join('');
                    }
                  }
                  tds += `<td>${v || ''}</td>`;
                }

                taken += `<tr class="${completed ? 'text-success' : ''}" data-search="${[taak.name, ...Array.from(tds.matchAll(/<td>(.*?)<\/td>/g), m => m[1])].join(' ').replace(/<.*?>/g, '').trim()}">
                  ${_settings.projecten_taken_hide_name !== 'ja' ? `<td>${taak.name}</td>` : ''}
                  ${tds}
                  <td class='text-right'>${btn}</td>
                </tr>`;
              }

              let btn = ''
              if(_settings.planning_project_taken_hide_project_btn != 'ja'){
                btn = `<div><a class="btn btn-primary text-white" onclick="projectInplannen(${project.id})" >Inplannen</a></div>`
                if(project.planning.length){
                  let tippy = '';
                  for(const row of project.planning){
                    tippy += ` <div class='text-left' ><b class='mr-2' >${convert(row.datum)}</b> ${row.begin.slice(0, 5)} - ${row.eind.slice(0, 5)}</div>`;
                  }
                  btn = `<div><a class="btn btn-success text-white tippy" onclick="projectInplannen(${project.id})" data-tippy-content="${tippy}">Ingepland</a></div>`;
                }
              }
              let takenBtn = '';
              if (project.status != "Acquisitie" || @json((getSettingValue('projecten_taken_koppel_acquisitie') ?? 'ja')  == 'ja')) {
                takenBtn = `<span class="form-control-divider"></span><a class="btn btn-sm tippy" data-tippy-content="Taken beheren" onclick="editProjectTaken(${project.id})">@icon_edit</a>`;
              }
              divs += `<div class="border bg-light-grey rounded m-2 p-2 project-row" data-search="${search}">
                <div class="flex-between">
                  <div class="d-flex">
                        <div class="mr-2">
                            <b>${project.projectnr}</b>
                            <div class="opacity-75">${project.projectnaam || ''}</div>
                            <div class="rounded h-px-5 cursor-pointer" onclick="projectColor(${project.id})"
                                data-project-taak-color="${project.id}"
                                style="background-image: linear-gradient(to bottom right, ${project.planning_color || '#169BD7'}80, ${project.planning_color || '#169BD7'})">
                            </div>
                        </div>
                        <div class="ml-2" style="display: flex; align-items: center;">
                            ${project?.opdrachtgever ? `<div class="badge badge-primary tippy d-block mb-1 mx-1" data-tippy-content="Opdrachtgever">${project.opdrachtgever}</div>` : ''}
                            ${project?.vestiging ? `<div class="badge badge-primary tippy d-block mb-1 mx-1" data-tippy-content="Vestiging">${project.vestiging.plaats}</div>` : ''}
                            ${settings.planning_tonenOpvolgBonnen ? `
                            ${project.werkbonnen
                              .map(werkbon => werkbon.opvolgen
                                ? `<a target="_blank" href="${url}/werkbonnen/pdf/${werkbon.id}" class="badge badge-primary tippy d-block mb-1 ml-1 removeDecoration" data-tippy-content="Opvolgen">
                                    ${werkbon.werkbonnummer}
                                </a>`
                                : ''
                              )
                              .join('')
                            }
                            ` : ''}
                        </div>
                    </div>
                    <div class="flex-between">
                      <div class="search-bar-container form-control-custom rounded-pill py-0 mx-2 flex-align">
                        <input type="text" class="search-taken form-control-plaintext py-0" placeholder="Taak zoeken..." />
                        ${takenBtn}
                      </div>
                      ${btn}
                    </div>
                  </div>
                  <div class="overflow-auto">
                    <table class="table-custom my-2 font-size-08">
                      <thead>${generateTakenHeader()}</thead>
                      <tbody>${taken}</tbody>
                    </table>
                  </div>
                </div>`;
            }

            $('.project-taken-container').html(divs);

            tippyInit();
            successLoader();
          })
          .catch(handleCatchError);
      }
      function PrefillFilters(){
        const filterArr = {'projecten-sorteren':'planningProjectSort', 'vestiging':'planningVestiging', 'bv':'planningBv', 'taken_planned':'planningTaken_planned'};
        for(const key in filterArr){
          if(getCookie(filterArr[key])){
            $("#"+key).val(getCookie(filterArr[key]));
          }
        }
      }
      function werkbonPrefillInit(){
        $('.werkbon-templates-select').empty();
        $('.werkbon-templates').empty();

        if(!werkbonTemplates.length){return}

        const uniqueArray = resetIndex(settings.werkbonPrefill).filter((item, index, self) =>
          index === self.findIndex((obj) => obj.id === item.id)
        );

        if(uniqueArray.length < 1){return;}

        $('.werkbon-templates-select').html(
          `<div class="my-2" >
            <label>Werkbon prefillen</label>
            <select name='werkbonPrefill' class='form-select'>
              <option value=''>Selecteer template</option>
              ${werkbonTemplates
                    .filter(temp => settings.werkbonPrefill[temp.id])
                    .map(temp => `<option value='${temp.id}'>${temp.naam}</option>`)
                    .join('')}
            </select>
          </div>`
        );
        $('.werkbon-templates').html(`
            <div class="my-2 werkbon-keywords-prefill"></div>
        `);

        $('.werkbon-nummers').html(
          `<div class="my-2" >
            <label>Werkbon nummer</label>
            <select name='werkbonId' class='form-select'>
              <option value=''>Selecteer template</option>
              ${(_projectInplannen?.project?.werkbonnen || [])
                    .filter(werkbon => werkbon.opvolgen)
                    .map(werkbon => `<option value='${werkbon.id}'>${werkbon.werkbonnummer}</option>`)
                    .join('')}
            </select>
            <div class="my-2 werkbon-keywords-nummer"></div>
          </div>`
        );
      }

      function searchProject(){
        $('#inplannenModal').hideModal();
        html = `<label>Project zoeken</label>
          <infor-search
            id="projectSearch"
            name="project"
            placeholder="Zoeken..."
            class="form-control-custom"
            data-content="projectnr"
            data-sub-content="projectnaam"
            data-api="api/projecten/search"
            data-errors="handleCatchError"
          ></infor-search>`

          confirmModal({text: html, hideFooter: true, execute: projectSearchInit});

      }
      function projectSearchInit(){
        initInforSearch();
        _inforSearch.get('projectSearch').onchange = projectInplannen;
      }
      function projectInplannenBase(){
        try{
          clearInplannenModal();
          resetGoogleCalendar();
          resetMachines();
          resetFiles();

          addMachines('#projecten-machines');
          setPushmelding();
          setKlantMail();

          werkbonPrefillInit()

          $('.taken-container').addClass('d-none');
          $('#inplannen-taken').empty();

          $("#projectUsers").html('');
          $("#projectDatums").html('');

          $('[data-users-filter=vestiging]').trigger('change');

          _projectInplannen.modal.showModal();
        }
        catch (e) { handleCatchError(e); }
      }
      function projectInplannen(id){
        try{
            if(!$('.werkbon-nummers').hasClass('d-none')){
                $('.werkbon-nummers').toggleClass('d-none');
                $('.werkbon-templates-select').toggleClass('d-none');
            }
            $('.werkbonPrefillToggleContainer').removeClass('d-none');

          let project = getProject(id, {relations: ['klant', 'taken', 'werkbonnen']}).then(project => {


            if(!project){
              $('#dynamic-confirm-modal').hideModal();
              notification('Kies een planbaar project');
              return;
            }
            _projectInplannen.project = project;

            projectInplannenBase();
            $("#projectId").val(id);
            const projnaam = project.projectnaam ? project.projectnr + ' - ' + project.projectnaam : project.projectnr;
            $("#projectNummer").val(projnaam);


            if(project && project.klant){
              const klantNaam = project.klant.naam ? project.klant.naam : project.klant.contactpersoon_voornaam +" "+ project.klant.contactpersoon_achternaam;
              $("#projectKlant").val(klantNaam);
              $("#projectKlantId").val(project.klant_id ?? '');
            }
            else{
              $("#projectKlant").val("Geen bijbehorend klant!");
            }

            if(settings.adres == "Aan"){
              $("#projectStraat").val(project.adres || '');
              $("#projectHuisnummer").val(`${project.huisnummer || ''}${project.toevoeging || ''}`);
              $("#projectPostcode").val(project.postcode || '');
              $("#projectPlaats").val(project.woonplaats || '');
            }
            $(".projectBegintijd").val(`${now().date.us}T${settings.prefillBegin}`);
            $(".projectEindtijd").val(`${now().date.us}T${settings.prefillEind}`);
            if (_projectInplannen.user && _projectInplannen.date) {
              $('#dynamic-confirm-modal').hideModal();
              $('.userSelect').val(_projectInplannen.user);
              $('.projectBegintijd').val(_projectInplannen.date + 'T' + settings.prefillBegin);
              $('.projectEindtijd').val(_projectInplannen.date + 'T' + settings.prefillEind);
            }
          });
        }
        catch (e) { handleCatchError(e); }
      }
      function inplannenTaken(id, t){
        try{
          getProject(id, {relations: ['klant', 'taken', 'werkbonnen']}).then(project => {

            let taak = project.taken.find(row => row.id == t);

            _projectInplannen.project = project;

            projectInplannenBase();

            const projnaam = project.projectnaam ? project.projectnr + ' - ' + project.projectnaam : project.projectnr;
            $("#projectNummer").val(projnaam);

            if(taak.custom.find(subarray => subarray.keyword === 'user') !== undefined && taak.custom.find(subarray => subarray.keyword === 'user').type === 'user'){
              $("select[name='rowUsers[]']").val(taak.custom.find(subarray => subarray.keyword === 'user').user.id)
            };

            $('.taken-container').removeClass('d-none');

            addInplannenTaken(id);
            $(`[name='taken[]']`).eq(0).val(t);

            $("#projectId").val(id);
            if(project && project.klant){
              const klantNaam = project.klant.naam ? project.klant.naam : project.klant.contactpersoon_voornaam +" "+ project.klant.contactpersoon_achternaam;
              $("#projectKlant").val(klantNaam);
              $("#projectKlantId").val(project.klant_id ?? '');
            }
            else{
              $("#projectKlant").val("Geen bijbehorend klant!");
            }

            if(settings.adres == "Aan"){
              $("#projectStraat").val(project.adres || '');
              $("#projectHuisnummer").val(`${project.huisnummer || ''}${project.toevoeging || ''}`);
              $("#projectPostcode").val(project.postcode || '');
              $("#projectPlaats").val(project.woonplaats || '');
            }

            const {start, end, date, opmerking} = _settings.planning_taken_inplannen_prefill
            let d = now().date.us;
            let s = '00:00';
            let e = '00:00';
            if(date){
              const customDate = taak.custom.find(row => row.keyword == date);
              if(customDate && customDate.value){
                d = customDate.value;
              }
            }
            if(start){
              const customStart = taak.custom.find(row => row.keyword == start);
              if(customStart && customStart.value){
                s = customStart.value.slice(0, 5);
              }
            }
            if(end){
              const customEnd = taak.custom.find(row => row.keyword == end);
              if(customEnd && customEnd.value){
                e = customEnd.value.slice(0, 5);
              }
            }
            if(opmerking){
              const opmerkingValue = taak.custom.find(row => row.keyword == opmerking);
              if(opmerkingValue && opmerkingValue.value){
                $('[name=opmerking]').val(opmerkingValue.value);
              }
            }
            $('.projectBegintijd').val(`${d} ${s != '00:00' ? s : `${settings.prefillBegin}`}`);
            $('.projectEindtijd').val(`${d} ${e != '00:00' ? e : `${settings.prefillEind}`}`);
          });
        }
        catch (e) { handleCatchError(e); }
      }
      function addInplannenTaken(){
        let options = '';

        for(let taak of _projectInplannen.project.taken){
          if(Number(taak.completed)){continue;}
          options += `<option value="${taak.id}" >${taak.name}</option>`
        }

        $("#inplannen-taken").append(
          `<div class="my-2 d-flex justify-content-between align-items-center" id="${randomString()}" >
            <select name="taken[]" class="form-select">
              <option selected >Selecteer een taak</option>
              ${options}
            </select>
            <a class="btn btn-inverse-danger cursor-pointer ml-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
          </div>`
        );

      }

      function clearInplannenModal(){
        $('.inplannen-modal').find('.modal-body').find('input, select, textarea').not('[data-remember-value], [type=checkbox], [type=hidden]').val(null);
        $('.inplannen-modal').find('.modal-body').find('input[type=checkbox]').prop('checked', false);
        $('.herhaaldiv').addClass('d-none');
        $('.datasetItems').empty();
      }

      function toggleProp(id, prop){
        if($(id).prop(prop)){
          $(id).prop(prop, false);
        }
        else{
          $(id).prop(prop, true);
        }
      }
      function changeDate(date){
        let dt = new Date(date);
        return ("0" + dt.getDay()).slice(-2) + "-" + ("0" + (dt.getMonth() + 1)).slice(-2) + "-" + dt.getFullYear();
      }
      function hideParent(id){
        $(id).parent().addClass("d-none");
      }
      async function refreshKlanten(klant_id = null){
        loader();
        const klant = (await getKlanten({by_query: true, where: {id: klant_id}}))[klant_id];
        const container = $("#refreshableKlanten");

        const naam = klant.naam || `${klant.contactpersoon_voornaam || ''} ${klant.contactpersoon_achternaam || ''}`

        container.append(`<span class="select_search-value" data-value="${klant.id}" data-name="${naam}">${naam}</span>`);
        container.find(`[data-value=${klant.id}]`).click();

        clearLoader();
      }

      function fillUpdateModal(planningId){
        $('#projectUsersWijzig').html('');
        $('#updateTaken').addClass('d-none');
        $('#updateModal').data('planning-id', planningId)
        $('#updateModal').find('#projectUsersWijzig').attr('data-planning-wijzig-id', planningId)
        $('#updateModal').find('.modal-body').find('input, select, textarea').not('[data-remember-value], [type=checkbox], [type=hidden]').val(null).prop('checked', false);
        $('#updateModal').showModal();
        $('#sameprojectdiv').addClass('d-none');
        $('#sameprojectusers').html('');
        $('.datasetItems').empty();
        resetFiles();
        resetMachines();
        setPushmelding();
        setKlantMail();
        let pl = planning.find(row => row.id == planningId);


        let klant = '';
        if(pl.klanten){
          const titel = pl.klanten.naam ? pl.klanten.naam : pl.klanten.contactpersoon_voornaam +" "+ pl.klanten.contactpersoon_achternaam ;
          klant = '<b>Klant: </b>'+titel+"</div>"
        }


        if(pl.type === "legenda"){
          let omschrijving = "";
          if(pl.legenda.omschrijving){
            omschrijving = '<b>Omschrijving: </b>'+pl.legenda.omschrijving
          }
          if(pl.klanten){
            const titel = pl.klanten.naam ? pl.klanten.naam : pl.klanten.contactpersoon_voornaam +" "+ pl.klanten.contactpersoon_achternaam ;
            klant = '<b>Klant: </b>'+titel+"</div>"
          }
          $("#updateNaam").html("<b>Activiteit: </b>"+pl.legenda.naam)
          $("#updateOmsch").html(omschrijving);
        }
        if(pl.type === "aanvraag"){
          $("#updateNaam").html("<b>Aanvraagnummer: </b>"+pl.aanvraag.aanvraagnummer)
          $("#updateOmsch").html("<b>BV: </b>"+pl.aanvraag._bv.name)
        }
        if(pl.type === "checklist"){
          $("#updateNaam").html("<b>Checklist: </b>"+pl.checklist.description)
          $("#updateOmsch").html('');
        }
        if(pl.type === "project"){
          const projectnummer = pl.project.projectnr ? pl.project.projectnr : '';
          const projectnaam = pl.project.projectnaam ? pl.project.projectnaam : '';
          const projecttaken = pl.project.taken ? pl.project.taken : '';
          const sameProjectPlannen = pl.sameProjectPlannen ? pl.sameProjectPlannen : '';

          if(projecttaken.length){
            $('#updateTaken').html(`
              <label>Taken</label>
              <infor-select-multiple name="taken" id="updateTakenSelect" class="form-control-custom" placeholder="Selecteer Taken" >
                ${projecttaken.map(row => `<infor-select-option ${pl.taken.find(taak => taak.id == row.id) ? 'data-selected="true"' : ''} data-name="${row.name}" data-value="${row.id}" >${row.name}</infor-select-option>`).join('')}
              </infor-select-multiple>
            `);
            initInforSelectMultiple();
            $('#updateTaken').removeClass('d-none');
          }

          if(sameProjectPlannen.length){

            $('#sameprojectusers').html(`
              <label>Selecteer hieronder de gebruikers waarvan het zelfde plan gewijzigd dient te worden.</label>
              <infor-select-multiple name="sameProjectPlannen" id="sameprojectusersSelect" class="form-control-custom" placeholder="Selecteer Gebruikers" >
                ${sameProjectPlannen.map(row => `<infor-select-option data-name="${row.user.name} ${row.user.lastname}" data-value="${row.id}" >${row.user.name} ${row.user.lastname}</infor-select-option>`).join('')}
              </infor-select-multiple>
            `);
            initInforSelectMultiple();
            $('#sameprojectdiv').removeClass('d-none');
          }

          $("#updateNaam").html(
            "<span class='d-block'><b>Projectnummer: </b>"+projectnummer+"</span>" +
            "<span class='d-block'><b>Projectnaam: </b>"+projectnaam+"</span>"
          );
          $("#updateOmsch").html('');
        }


        // for (let i=0; i< (pl.aantal_personen - 1); i++){
        //   if($('select[name="updateUser[]"]').length <= (pl.aantal_personen - 1)){
        //     addProjectUsers(true)
        //   }
        // }

        $("select[name='updateUser[]']").first().val(pl.user_id);
        $("#benodigdPersoneelUpdate").val(pl.aantal_medewerkers);
        $("#updateId").val(planningId);
        $("#updateBegin").val(pl.begin);
        $("#updateEind").val(pl.eind)
        $("#updateKlant").html(klant)
        $("#updateDatum").val(pl.datum);
        $("#updateOpmerking").val(pl.opmerking || '');

        $("input[name=updateAantal]").val(pl.aantal_personen || '')
        $("input[name=updatePrijs]").val(pl.prijs || '')
        $("input[name=updateBtw]").val(pl.btw || '')

        $('input[name=completed]').prop('checked', !!(Number(pl.completed)))
        $('input[name=released]').prop('checked', !!(Number(pl.released)))

        if(settings.adresSetting == "Aan"){
          $("#updateStraat").val(pl.straat || '');
          $("#updateHuisnummer").val((pl.huisnummer || '') + (pl.toevoeging || ''));
          $("#updatePostcode").val(pl.postcode || '');
          $("#updatePlaats").val(pl.plaats || '');
        }
        for(file of pl.explorer){
          $("#updateFiles").append(
            `<div id="${randomString()}" class="my-1 d-flex justify-content-between" >
            <div class="d-flex align-items-center" >
              <img height="50" src="${url}/client/public/img/explorer/files/${file.icon}" >
              <h4 class="my-0 mx-2" >${file.name}</h4>
            </div>
            <a class="btn btn-danger text-white align-self-center" onclick="deleteDiv('#${lastString()}')" >@icon_trash</a>
            <input type="hidden" value="${file.id}" name="explorer_files[]">
          </div>`
          )
        }
        for(const i in pl.machines){
          const machine = pl.machines[i];

          addMachines('#edit-machines');
          $('#edit-machines').children().eq(i).find(`span[data-value="${machine.id}"]`).click();
        }
        for(const i in pl.custom){
          const custom = pl.custom[i];
          if(custom.type == 'dataset'){
            values = JSON.parse(custom.value);
            for(const value of values){
              $(`#dataset_items_update_${value.dataset_id}`).append(`
                <div class="my-2" id="${randomString()}">
                  <div class="d-flex justify-content-between" >
                    <input class="form-control-custom mr-2" readonly value="${value.name}"></input>
                    <input type="number" name="datasetItemAantal[${value.dataset_id}][${value.item_id}]" class="form-control-custom w20 mr-2" value="${value.aantal}">
                    <a class="btn btn-inverse-danger" onclick="deleteDiv(${lastString()})" >@icon_close</a>
                  </div>
                </div>
              `)
            }
          }

          $(`[name="custom[${custom.keyword}]"]`).val(custom.value);
        }
      }
      function fillHex(id, color){
        $("#"+id).val(color);
        return true;
      }
      function fillLegendaUpdateModal(id){
        var leg = legenda[id];
        $("#legendaUpdateNaam").val(leg.naam);
        $("#legendaUpdateOmschrijving").val(leg.omschrijving);
        $("#legendaUpdateId").val(leg.id);
        legendaColorWheel.color.hexString = leg.kleur
      }
      function fillFilesModal(id){
        $("#files").html('');
        $("#filesModal").modal("show");

        let plan = planning.find(row => row.id == id);
        for(let file of plan.explorer){
          $("#files").append(
            `<div class="my-1 d-flex justify-content-between">
            <div class="d-flex align-items-center">
              <img height="50" src="${url}/client/public/img/explorer/files/${file.icon}">
              <h4 class="my-0 mx-2">${file.name}</h4>
            </div>
            <a class="btn btn-primary text-white align-self-center" href="${url}/api/file/explorer/files/${file.src}" download >@icon_download</a>
          </div>`
          )
        }
      }
      function fillPlanningInfoContent(){
        const display = $('[name="planning_display_info[]"]:checked').map((index, element) => { return element.value }).get()
        $(".searchRow").each(function(){
          const row = $(this);
          let html = '';

          for(const display_element of display){
            html += row.attr(`data-info-${display_element}`);
          }

          row.html(html);
        })
      }

      function addMachines(id){
        let options = '';

        for(let machine of machines){
          options += `<span class="select_search-value" data-value="${machine.id}" data-name="${machine.name}">
                      <div class="d-flex">
                        <img width="50" height="50" class="mr-2" src="${url}/client/public/img/machines/${machine.icon}.png">
                        <div>
                          <div>${machine.name}</div>
                          <div class="text-muted" >${machine.group.name}</div>
                        </div>
                      </div>
                    </span>`
        }

        $(id).append(
          `<div class="flex-between my-2" id="${randomString()}" >
          <div class="select_search-container w-100">
            <input type="hidden" name="machines[]" class="select_search-hidden-input" data-placeholder="Selecteer een machine">
            <div class="select_search-values" >
              <div class="select_search-box" >${options}</div>
            </div>
          </div>
          <a class="btn btn-inverse-danger ml-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
        </div>`
        );
        initSelectSearch();
      }
      function addKlantBtn(){
        clearInterval(intervals.newKlant);
        window.open('{{url("iframe/klanten/create/window")}}','newwindow','width=500,height=800');

        intervals.newKlant = setInterval(function(){
          if(localStorage.newKlant){
            const klantId = localStorage.newKlant;
            localStorage.removeItem('newKlant');
            clearInterval(intervals.newKlant);
            refreshKlanten(klantId);
          }
        },750);
      }
      function addInplannenUsers(){
        let selectString = randomString(10);
        $("#inplannenUsers").append(
          '<select id="users'+selectString+'" name="users[]" class="form-select userSelect mb-2">'+
          '<option value="" selected disabled>Selecteer de medewerker</option>'+
          '</select>'
        );
        for(let i in users){
          const user = users[i];
          $("#users"+selectString).append(
            '<option value="'+user.id+'">'+user.name+" "+user.lastname+'</option>'
          )
        }
        $("#users"+selectString).append('<option value="0" >Opties</option>');
      }
      function addOfferteUsers(){
        let selectString = randomString(10);
        $("#offerteUsers").append(
          '<select id="users'+selectString+'" name="rowUsers[]" class="form-select mb-2">'+
          '<option value="" selected disabled>Selecteer de medewerker</option>'+
          '</select>'
        );
        for(let i in users){
          const user = users[i];
          $("#users"+selectString).append(
            '<option data-user-vestiging="${user.vestiging_id}" value="'+user.id+'">'+user.name+" "+user.lastname+'</option>'
          )
        }
        $("#users"+selectString).append('<option value="0" >Opties</option>');
      }
      function addProjectUsers(wijzig = false){
        let selectString = randomString(10);
        let machineBtn = '';
        let appendDiv = $("#projectUsers");
        let appendName = "rowUsers[]";

        if(settings.machineSetting == 'pp'){
          machineBtn = '<a class="btn btn-primary text-white btn-block mb-2" id="machine'+selectString+'" onclick="addUserMachine(`'+selectString+'`)">Machine bij medewerker inplannen</a>';
        }

        if(wijzig){
          appendDiv = $(`[data-planning-wijzig-id=${$("#updateModal").data('planning-id')}]`);
          appendName = "updateUser[]";
        }

        appendDiv.append(
          `<div id="div${selectString}" class="flex-between">
          <a class="btn btn-inverse-danger mb-2 mr-1 ml-1" onclick="deleteDiv('#div${selectString}')">@icon_close</a>
          <select id="users${selectString}" name="${appendName}" class="form-select userSelect mb-2" required>
          <option value="" selected disabled>Selecteer de medewerker</option>
          </select>
          </div>`
        );

        for(let i in users){
          const user = users[i];
          $("#users"+selectString).append(
            `<option data-user-vestiging="${user.vestiging_id || ''}" value="${user.id}">${user.name || ''} ${user.lastname || ''}</option>`
          )
        }
        $("#users"+selectString).append('<option value="0" >Opties</option>');
        $(`[data-users-filter=vestiging]`).trigger('change')
      }
      function addUserMachine(selectString){
        var userid = $('#users'+selectString).val();
        $('#machine'+selectString).remove();
        let options = '';

        for(let machine of machines){
          options += `<span class="select_search-value" data-value="${machine.id}" data-name="${machine.name}">
                      <div class="d-flex">
                        <img width="50" height="50" class="mr-2" src="${url}/client/public/img/machines/${machine.icon}.png">
                        <div>
                          <div>${machine.name}</div>
                          <div class="text-muted" >${machine.group.name}</div>
                        </div>
                      </div>
                    </span>`
        }

        $('#div'+selectString).append(
          `<div class="flex-between my-2" id="machine${selectString}" >
          <div class="select_search-container w-100">
            <input type="hidden" name="usermachines[${userid}]" class="select_search-hidden-input" data-placeholder="Selecteer een machine">
            <div class="select_search-values" >
              <div class="select_search-box" >${options}</div>
            </div>
          </div>
          <a class="btn btn-inverse-danger ml-1" onclick="deleteUserMachine('${selectString}')" >@icon_close</a>
        </div>`
        );
        initSelectSearch();
      }
      function deleteUserMachine(selectString){
        $('#machine'+selectString).remove();
        if(settings.machineSetting != 'pp'){
          return;
        }
        $('#div'+selectString).append(`
        <a class="btn btn-primary text-white btn-block mb-2" id="machine${selectString}" onclick="addUserMachine('${selectString}')">Machine bij medewerker inplannen</a>
        `);
      }
      function addProjectDatum(div = false){

        classBegin = 'projectBegintijd';
        classEind = 'projectEindtijd';
        if(!div){div = "#projectDatums"}
        if(div == "#activiteitDatums" )
        {
          classBegin = 'activiteitBegintijd';
          classEind = 'activiteitEindtijd';
        }

        let l = $("." + classBegin).length;
        let b = '';
        let e = '';

        if(l > 0){
          b = $("." + classBegin)[l - 1].value;
          e = $("." + classEind)[l - 1].value;
        }

        if(b){
          let date = new Date(b);
          date.setDate(date.getDate() + 1);
          b = date.getFullYear()+"-"+("0"+(date.getMonth()+1)).slice(-2)+"-"+("0"+date.getDate()).slice(-2)+"T"+("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2);
        }
        if(e){
          let date = new Date(e);
          date.setDate(date.getDate() + 1);
          e = date.getFullYear()+"-"+("0"+(date.getMonth()+1)).slice(-2)+"-"+("0"+date.getDate()).slice(-2)+"T"+("0"+date.getHours()).slice(-2)+":"+("0"+date.getMinutes()).slice(-2);
        }

        $(div).append(
          `<div id="${randomString()}" class="flex-between mt-1">
            <div class="w-100 mx-1">
            <input required value="${b}" class="form-control ${classBegin}" type="datetime-local" name="begin[]">
            </div>
            <div class="w-100 mx-1">
            <input required value="${e}" class="form-control ${classEind}" type="datetime-local" name="eind[]">
            </div>
            <div class="w-100 mx-1">
              <a class="btn btn-inverse-danger" onclick="deleteDiv('#${lastString()}')" ><span class="vertical-align-center">@icon_close</span></a>
            </div>
          </div>`

        )
      }
      function addAanvragenUsers(){
        let selectString = randomString(10);
        $("#aanvragenUsers").append(
          '<select id="users'+selectString+'" data-alert="#aanvragenVerlofAlert" name="users[]" class="form-select userSelect mb-2">'+
          '<option value="" selected disabled>Selecteer de medewerker</option>'+
          '</select>'
        );
        for(let i in users){
          const user = users[i];
          $("#users"+selectString).append(
            '<option value="'+user.id+'">'+user.name+" "+user.lastname+'</option>'
          )
        }
        $("#users"+selectString).append('<option value="0" >Opties</option>');
      }
      function addFiles(id){
        let string =  randomString(10);
        $(id).append(
          '<div id="file'+string+'" class="my-1 d-flex justify-content-between" >' +
          '<input type="file" name="bijlagen[]"> ' +
          '<a class="btn btn-danger text-white align-self-center" onclick="deleteDiv(\'#file'+string+'\')" >@icon_trash</a>' +
          '</div>'
        )
      }

      function showBeschikbaarheid(state){
        showBeschikbaarheidState = state;
        if(state === true){
          $("#showBeschikbaarheid").addClass('d-none')
          $("#hideBeschikbaarheid").removeClass('d-none')

          $('.beschikbaarheid-planning').addClass('d-none');
          $('.beschikbaarheid-div').removeClass('d-none');
          for(let user in beschikbaarheid){
            for(let date in beschikbaarheid[user]){
              for(let row of beschikbaarheid[user][date]){

                if(row.day === '1'){
                  for(let i = 0; i < 24; i++){
                    $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).removeClass('bg-inverse-secondary');
                    if(row.type === '1'){
                      $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).addClass('bg-success');
                    }
                    else{
                      $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).addClass('bg-danger');
                    }
                  }
                  continue
                }

                for(let i = 0; i  < 24; i++){
                  if(!isTimeBetween(`${i}:30`, row.start, row.end)){ continue }
                  $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).removeClass('bg-inverse-secondary');
                  if(row.type === '1'){
                    $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).addClass('bg-success');
                  }
                  else{
                    $('#beschikbaarheid-u'+user+'-d'+date+'-h'+i).addClass('bg-danger');
                  }
                }

              }
            }
          }
        }
        else{
          $("#showBeschikbaarheid").removeClass('d-none')
          $("#hideBeschikbaarheid").addClass('d-none')

          $('.beschikbaarheid-planning').removeClass('d-none');
          $('.beschikbaarheid-div').addClass('d-none');
        }
      }
      function showButtons(div){
        $("#"+div).toggleClass("d-none");
      }
      function showCalendar(){
        $("#calendar-modal").modal("show");
      }

      function confirmDelete(id){
        $('#inplannenModal').hideModal();

        confirmModal({
          text: 'Weet je zeker dat je deze activiteit wilt verwijderen?',
          btnColor: 'btn-danger'
        }).then((response) => {
          if(!response.status){ return }

          deletePlanning(id);

        })
      }
      function confirmLegendaDelete(id){
        $("#lText"+id).toggleClass("d-none");
        $("#lButton"+id).html("Bevestig");
        $("#lButton"+id).removeAttr("onclick");
        setTimeout(function(){
          //TODO Fix
          $("#lButton"+id).attr("href",`{{url('planning')}}`+`/legenda/delete/${id}/${type}/${year}/${target}`);
        }, 50);
      }

      function resetMachines(){
        $('.machines').empty();
      }
      function resetGoogleCalendar(){
        if(settings.googleAgenda == 'Aan'){
          $(".google_calendar").val('');
          $(".google_calendar_switch").prop('checked', true).trigger('change');
        }
        else{
          $(".google_calendar").val('');
          $(".google_calendar_switch").prop('checked', false).trigger('change');
        }

      }
      function resetFiles(){
        $('.planning-files').empty();

      }
      function setPushmelding(){
        if(settings.pushmelding == 'Aan'){
          $("input[name=pushmelding]").prop("checked", true)
        }
        else{
          $("input[name=pushmelding]").prop("checked", false)
        }
      }
      function setProject(){
        if(settings.project == 'Aan'){
          $("input[name=project]").prop("checked", true)
        }
        else{
          $("input[name=project]").prop("checked", false)
        }
      }
      function setKlantMail(){
        if(settings.klantMail == 'Aan'){
          $("input[name=klantMail]").prop("checked", true)
        }
        else{
          $("input[name=klantMail]").prop("checked", false)
        }
      }

      function dashboardScroll(){
        $("html").css("overflow", "hidden");
        $("body").addClass("dashboard-scroll-planning-animate");
        $(".day-td").addClass("h-auto")
      }

      function movePeriod(direction){
        const type = $('[data-period-type]').val();
        let year = parseInt($('[data-period-start=jaar]').val(), 10);
        let start = parseInt($(`[data-period-start=${type}]`).val(), 10);
        let day = $(`[data-period-start='dag']`).val();

        switch (type) {
          case 'week':
            if(start === 1 && direction === 'previous'){
              year--;
              start = 52;
              $('[data-period-start=jaar]').val(year);
            }
            else if(start === 52 && direction === 'next'){
              year++;
              start = 1;
              $('[data-period-start=jaar]').val(year);
            }
            else{
              start = (direction === 'next') ? start + 1 : start - 1;
            }
            break;
          case 'maand':
            if (start === 1 && direction === 'previous') {
              year--;
              start = 12;
              $('[data-period-start=jaar]').val(year);
            } else if (start === 12 && direction === 'next') {
              year++;
              start = 1;
              $('[data-period-start=jaar]').val(year);
            } else {
              start = (direction === 'next') ? start + 1 : start - 1;
            }
            break;
          case 'kwartaal':
            if (direction === 'next') {
              if (start === 10) {
                start = 1;
                year++;
                $('[data-period-start=jaar]').val(year);
              } else {
                start += 3;
              }
            } else if (direction === 'previous') {
              if (start === 1) {
                start = 10;
                year--;
                $('[data-period-start=jaar]').val(year);
              } else {
                start -= 3;
              }
            }
            break;
          case 'jaar':
            year = (direction === 'next') ? year + 1 : year - 1;
            break;
          default:
            const date = now(day);
            start = direction === 'next' ? date.addDays(1).date.us : date.subDays(1).date.us;
            break;
        }

        $('[data-period-start=' + type + ']').val(start);

        if (type === 'jaar') {
          $('[data-period-start=jaar]').val(year);
        }

        $('[data-period-search]').click();
      }

    </script>
@endsection
