@extends('layouts.app')

@section('title', 'Abonnementen')

@section('content')
    <section class="append-loader">
      <div class="card p-2 my-2">
        <div class="row">
          <div class="col-12 m-2">
            <a data-toggle="modal" data-target="#itemModal" class="btn btn-primary text-white" onclick="resetModal()"><i class="fas fa-plus m-0"></i></a>
          </div>
          <div class="col-12 my-2">
            <table id="table">
              <thead>
              <tr>
                <th>Naam</th>
                <th>Omschrijving</th>
                <th>Aangemaakt door</th>
                <th>Rol</th>
                <th>Preview</th>
                <th>Prijs</th>
                <th>Betalingsperiode</th>
                <th></th>
              </tr>
              </thead>
              <tbody>
              @foreach($items as $item)
                <tr>
                  <td>{{$item->naam}}</td>
                  <td><small>@if(strlen($item->omschrijving) > 40) {{substr($item->omschrijving, 0, 40)}}... @else {{$item->omschrijving}} @endif</small></td>
                  <td>{{substr($item->user->name, 0, 1)}}. {{$item->user->lastname}}</td>
                  <td>{{$item->role}}</td>
                  <td class="imgTd" ><img class="tdImg" src="{{asset("api/file/abonnementen/items/".$item->preview)}}"></td>
                  <td>€&nbsp;{{number_format($item->prijs, 2, ",",".")}}</td>
                  <td>
                    @if($item->geldigheid == 'Uitgaves')
                      {{$item->aantal_uitgaves}}&nbsp;{{$item->geldigheid}}
                    @else
                      1&nbsp;{{$item->geldigheid}}
                    @endif
                  </td>
                  <td class="text-right" >
                    <div class="dropdown">
                      <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Acties
                      </button>
                      <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        @if($item->geldigheid == "Uitgaves")
                          <a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#uitgaves-modal" onclick="fillUitgaves({{$item->id}})">Uitgaves</a>
                        @endif
                        <a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#itemModal" onclick="fillEditModal({{$item->id}})">Wijzigen</a>
                        <a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#deleteModal" onclick="fillDeleteModal({{$item->id}})">Verwijderen</a>
                      </div>
                    </div>
                  </td>
                </tr>
              @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>

{{--modals--}}
  <section>
    {{--Item modal--}}
    <div class="modal" id="itemModal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <form method="post" onsubmit="return checkConnection()" enctype="multipart/form-data" >
            <div class="modal-body">
              <div class="row">
                <div class="col-12 my-2">
                  <label>Preview*</label>
                  <input id="file" name="img" type="file" class="dropify mb-2" data-height="200" data-allowed-file-extensions="jpeg jpg png "/>
                  <input type="hidden" name="hiddenImg">
                </div>
                <div class="col-12 my-2">
                  <label>BV</label>
                  <select name="bv" class="form-select" required >
                    @foreach($bvs as $bv)
                      <option value="{{$bv->id}}">{{$bv->name}}</option>
                    @endforeach
                  </select>
                </div>
                <div class="col-12 my-2">
                  <label>Formulier type</label>
                  <select name="form_type" class="form-select" required >
                    <option value="digitaal">Digitaal</option>
                    <option value="papier">Papier</option>
                  </select>
                </div>
                <div class="col-12 my-2">
                  <label>Naam*</label>
                  <input type="text" name="naam" class="form-control" placeholder="Naam" required>
                </div>
                <div class="col-12 my-2">
                  <label>Omschrijving</label>
                  <textarea name="omschrijving" class="form-control" placeholder="Omschrijving"></textarea>
                </div>
                <div class="col-12 my-2">
                  <label>Typetekst*</label>
                  <input type="text" name="typetekst" class="form-control" placeholder="Naam" required>
                </div>
                <div class="col-12 my-2">
                  <label>Prijs particulieren*</label>
                  <input class="form-control" name="prijs" type="number" step="0.01" placeholder="Prijs" value="0.00" required>
                </div>
                <div class="col-12 my-2">
                  <label>Prijs zakelijk*</label>
                  <input class="form-control" name="prijs_zakelijk" type="number" step="0.01" placeholder="Prijs zakelijk" value="0.00" required>
                </div>
                <div class="col-12 my-2">
                  <label>Prijs buitenland binnen EU*</label>
                  <input class="form-control" name="prijs_binnen_eu" type="number" step="0.01" placeholder="Prijs binnen EU" value="0.00" required>
                </div>
                <div class="col-12 my-2">
                  <label>Prijs buitenland buiten EU*</label>
                  <input class="form-control" name="prijs_buiten_eu" type="number" step="0.01" placeholder="Prijs buiten EU" value="0.00" required>
                </div>
                <div class="col-12 my-2">
                  <label>Prijs ip*</label>
                  <input class="form-control" name="prijs_ip" type="number" step="0.01" placeholder="Prijs ip" value="0.00" required>
                </div>
                <div class="col-12 my-2">
                  <label>BTW*</label>
                  <input class="form-control" name="btw" type="number" step="0.01" placeholder="Btw" value="21" required>
                </div>
                <div class="col-12 my-2">
                  <div class="d-inline-block form-check form-switch p-0">
                    <span>Incl BTW</span>
                    <input checked class="form-check-input position-relative mx-1 cursor-pointer" type="checkbox" value="1" name="inclBtw">
                  </div>
                </div>
                <div class="col-12 mt-2">
                  <label>Betalingsperiode*</label>
                </div>
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="radioWeek" name="geldigheid" value="Week" checked>
                  <label class="btn btn-outline-primary d-block" for="radioWeek">Week</label>
                </div>
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="radioMaand" name="geldigheid" value="Maand">
                  <label class="btn btn-outline-primary d-block" for="radioMaand">Maand</label>
                </div>
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="radioJaar" name="geldigheid" value="Jaar">
                  <label class="btn btn-outline-primary d-block" for="radioJaar">Jaar</label>
                </div>
                <div class="col-6 mb-2">
                  <input class="btn-check" type="radio" id="radioUitgaves" name="geldigheid" value="Uitgaves">
                  <label class="btn btn-outline-primary d-block" for="radioUitgaves">Uitgaves</label>
                </div>
                <div class="my-2 d-none" id="aantal-uitgaves">
                  <label>Aantal uitgaves</label>
                  <input name="aantalUitgaves" class="form-control" placeholder="Aantal uitgaves" type="number" min="1" step="1" >
                </div>
              </div>
              <div id="form-database" class="row border-top my-2 d-none">
                <div class="col-12 my-2">
                  <label>Website url</label>
                  <input type="text" class="form-control" placeholder="URL" name="website">
                </div>
                <div class="col-12 my-2">
                  <label>IP</label>
                  <input type="text" class="form-control" placeholder="Database" name="db">
                </div>
                <div class="col-12 my-2">
                  <label>Database</label>
                  <input type="text" class="form-control" placeholder="Database" name="db_name">
                </div>
                <div class="col-12 my-2">
                  <label>Login</label>
                  <input type="text" class="form-control" placeholder="Login" name="db_login">
                </div>
                <div class="col-12 my-2">
                  <label>Wachtwoord</label>
                  <input type="text" class="form-control" placeholder="Wachtwoord" name="db_pass">
                </div>
                <div class="col-12 my-2">
                  <label>Database prefix</label>
                  <input type="text" class="form-control" placeholder="Database prefix" name="db_prefix">
                </div>
                <div class="col-12 my-2">
                  <label>Rol</label>
                  <select name="role" class="form-select" disabled required>

                  </select>
                  <div id="levelAlert">
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <input type="submit" class="btn btn-success text-white" value="Opslaan">
              <input type="hidden" name="itemId">
              @csrf
            </div>
          </form>
        </div>
      </div>
    </div>

    {{--  Delete modal--}}
    <div class="modal" id="deleteModal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body" >
            <h6>Weet je zeker dat je item <b id="deleteSpan"></b> wilt verwijderen!</h6>
          </div>
          <div class="modal-footer">
            <a class="btn btn-danger text-white" id="deleteBtn">Verwijderen</a>
          </div>
        </div>
      </div>
    </div>

    {{--    Uitgaves mdoal--}}
    <div class="modal fade" id="uitgaves-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div id="uitgaves"></div>
            <div class="text-center">
              <a class="btn btn-primary text-white" onclick="addUitgave()" >@icon_plus</a>
            </div>
            <div id="uitgaves-modal-alert" ></div>
          </div>
          <div class="modal-footer">
            <div id="uitgaves-modal-btn" ></div>
          </div>
        </div>
      </div>
    </div>
  </section>
@endsection
@section('script')
  <script>

    const items = @json($items);
    var dbSet = false;
    var dbConnection = null;

    $(document).ready(function(){
      dropify();
      $("#table").dataTable({
        "pageLength": 100,
      });
    })

    $(document).on("change", "input[name=db], input[name=db_name], input[name=db_login], input[name=db_pass], input[name=db_prefix]", function(){
      let db = $("input[name=db]").val();
      let db_name = $("input[name=db_name]").val();
      let db_login = $("input[name=db_login]").val();
      let db_pass = $("input[name=db_pass]").val();
      let db_prefix = $("input[name=db_prefix]").val();
      if(db && db_name && db_login && db_pass){
        dbSet = true;
        tryConnection(db, db_name, db_login, db_pass, db_prefix);
      }
      else{
        dbSet = false;
        $("#levelAlert").html("");
        $("select[name=rol]").prop("disabled", true).html("");
      }
    })

    $('input[name=geldigheid]').change(function(){
      const value = $(this).val()

      if(value == 'Uitgaves'){
        $("#aantal-uitgaves").removeClass('d-none').find('input').prop('disabled', false);
      }
      else{
        $("#aantal-uitgaves").addClass('d-none').find('input').prop('disabled', true);
      }
    })

    $('select[name=form_type]').change(function(){
      const value = $(this).val()
      if(value == 'digitaal'){
        $("#form-database").removeClass('d-none').find('input').prop('disabled', false);
      }
      else{
        $("#form-database").addClass('d-none').find('input').prop('disabled', true);
      }
    })


    function dropify(){
      $('.dropify').dropify();
    }
    function resetModal(){

      let itemModal = $('#itemModal').find('input, textarea, select').not('input[type=submit]').not(`[name='_token']`).not(`[name='geldigheid']`);
      itemModal.val("");
      $("#levelAlert").empty();

      $(".dropify-clear").click();

      $("input[name=prijs]").val("0.00")
      $("#radioWeek").click()
      $("select[name=role]").empty().attr('disabled', true);
    }
    async function fillEditModal(id){

      let item = items[id];
      setPreview(item.preview, "{{url("api/file/abonnementen/items/")}}/"+item.preview, item.preview);
      $("input[name=hiddenImg]").val(item.preview)
      $("input[name=naam]").val(item.naam)
      $("select[name=bv]").val(item.bv)
      $("textarea[name=omschrijving]").val(item.omschrijving)
      $("input[name=typetekst]").val(item.typetekst)
      $("input[name=prijs]").val(item.prijs)
      $("input[name=prijs_zakelijk]").val(item.prijs_zakelijk)
      $("input[name=prijs_binnen_eu]").val(item.prijs_binnen_eu)
      $("input[name=prijs_buiten_eu]").val(item.prijs_buiten_eu)
      $("input[name=prijs_ip]").val(item.prijs_ip)
      $("input[name=itemId]").val(id)
      $("input[name=btw]").val(item.btw);
      $("input[name=website]").val(item.website);
      $("select[name=form_type]").val(item.form_type);
      $("#radio"+item.geldigheid).click()
      if(item.aantal_uitgaves){
        $("input[name=aantalUitgaves]").val(item.aantal_uitgaves);
      }
      if(item.incl_btw == 1){
        $("input[name=inclBtw]").prop("checked", true)
      }
      else{
        $("input[name=inclBtw]").prop("checked", false)
      }

      if(item.form_type == 'papier'){
        $("#form-database").addClass('d-none').find('input').prop('disabled', true);
      }
      else{
        $("#form-database").removeClass('d-none').find('input').prop('disabled', false);
        $("select[name=form_type]").val("digitaal");
      }

      $("input[name=db]").val(item.db)
      $("input[name=db_name]").val(item.db_name)
      $("input[name=db_login]").val(item.db_login)
      $("input[name=db_pass]").val(item.db_pass)
      $("input[name=db_prefix]").val(item.db_prefix)

      if(item.db && item.db_name && item.db_login && item.db_pass){
        dbSet = true;
        await tryConnection(item.db, item.db_name, item.db_login, item.db_pass, item.db_prefix);
      }
      if(item.role){
        console.log(item.role);

        $("select[name=role]").val('subscriber');
      }
    }
    function setPreview(name, src, fname = '') {
      let input = $('input[name="img"]');
      let wrapper = input.closest('.dropify-wrapper');
      let preview = wrapper.find('.dropify-preview');
      let filename = wrapper.find('.dropify-filename-inner');
      let render = wrapper.find('.dropify-render').html('');

      input.val('').attr('title', fname);
      wrapper.removeClass('has-error').addClass('has-preview');
      filename.html(fname);

      render.append($('<img />').attr('src', src).css('max-height', input.data('height') || ''));
      preview.fadeIn();
    }
    function fillDeleteModal(id){
      let item = items[id];
      $("#deleteSpan").html(item.naam);
      $("#deleteBtn").attr("href", "{{url("abonnementen/items/delete")}}/"+item.id);
    }
    async function tryConnection(db, db_name, db_login, db_pass, db_prefix){
      $("#levelAlert").html("");

      loader();
      response = await ajax('abonnementen/wp/levels', {
        db: db,
        db_name: db_name,
        db_login: db_login,
        db_pass: db_pass,
        db_prefix: db_prefix,
      }).catch(err => {
          errorLoader();
          dbConnection = false;
          $("select[name=role]").prop("disabled", true).html("");
          $("#levelAlert").html("<small class='text-danger'>Verbinding maken met de database is niet gelukt!</small>")
        })
      if(response){
        successLoader();
        dbConnection = true;
        $("select[name=role]").prop("disabled", false).html("");
        $("#levelAlert").html("<small class='text-success'>Verbinding maken met de database is gelukt!</small>")

        for(let role in response.roles){
          $("select[name=role]").append(`<option value="${role}" >${role}</option>`)
        }
      }
    }
    function checkConnection(){
      if(dbSet == true && dbConnection == false){
        return false
      }
      return true;
    }

    function fillUitgaves(id){
      const item = items[id];

      $("#uitgaves").html('');
      $("#uitgaves-modal-alert").html('')
      $("#uitgaves-modal-btn").html('<a class="btn btn-success text-white" onclick="storeUitgaves('+id+')" >Opslaan</a>')

      for(const uitgave of item.uitgaves){
        const string = randomString(10);
        $("#uitgaves").append(
          '<div class="d-flex align-items-center my-2" id="'+string+'" >' +
          '<input type="date" class="form-control" value="'+uitgave.date+'" >' +
          '<a class="btn btn-danger text-white mx-2" onclick="deleteDiv(\'#'+string+'\')" >@icon_trash</a>' +
          '</div>'
        );
      }
    }
    function addUitgave(){
      let string = randomString(10);
      $("#uitgaves").prepend(
        '<div class="d-flex align-items-center my-2" id="'+string+'" >' +
          '<input type="date" class="form-control">' +
          '<a class="btn btn-danger text-white mx-2" onclick="deleteDiv(\'#'+string+'\')" >@icon_trash</a>' +
        '</div>'
      );
    }
    function storeUitgaves(id){
      $("#uitgaves-modal-alert").html('')
      $("#uitgaves-modal-btn").html('@spinner')

      const dates = [];
       $("#uitgaves").find('input[type=date]').each(function(){
         if($(this).val()){
           dates.push($(this).val());
         }
       });

      $.ajax({
        type: "POST",
        url: "{{url("abonnementen/items/uitgaves")}}",
        data: {
          item: id,
          dates: dates,
          _token: "{{csrf_token()}}",
        },
        success: function () {
          $("#uitgaves-modal").modal('hide');
        },
        error: function () {
          $("#uitgaves-modal-btn").html('<a class="btn btn-success text-white" onclick="storeUitgaves('+id+')" >Opslaan</a>')
          $("#uitgaves-modal-alert").html('<div class="alert alert-danger mb-0 mt-2" >Er is iets foutgegaan</div>')
        }
      });

    }

  </script>
@endsection
