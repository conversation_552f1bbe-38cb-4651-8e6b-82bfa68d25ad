@extends('layouts.app')

@section('title', 'Werkbonnen')

@section('content')
  <form class="main-form append-loader" onsubmit="return formPost()" action="" method="post" enctype="multipart/form-data">


    <div class="card p-2 my-2">

      {{--  Main content--}}
      <div class="text-right mx--1" data-project-relations>
        @isset($werkbon->project->offerte)
          <a class="btn btn-sm btn-inverse-primary m-1"
             href="{{url('offertes/edit/'.$werkbon->project->offerte->id.'/preview')}}" target="_blank">Offerte
            @icon_external_link</a>
        @endisset
        @isset($werkbon->klant)
          <a class="btn btn-sm btn-inverse-primary m-1" onclick="showKlant({{$werkbon->klant->id}})">Klant
            @icon_external_link</a>
        @endisset
      </div>
      <div class="row">
        @if($template->project)
          <div class="col-md-6 col-12 my-2">
            <label>Project</label>
            @if(isset($werkbon->project))
              <input type="hidden" name="project" value="{{$werkbon->project->id}}">
              <input type="text" class="form-control-custom" value="{{$werkbon->project->projectnr ?? ''}}" placeholder="Project" readonly>
            @else
              <div class="select_search-container">
                <input type="hidden" name="project" class="select_search-hidden-input" data-placeholder="Project" @if(getSettingValue('werkbon_project_or_klant_required', 'nee') == 'ja') required @endif>
                <div class="select_search-values">
                  <div class="select_search-box">
                    @foreach($projecten as $project)
                      <span class="select_search-value" data-value="{{$project->id}}"
                            data-name="{{$project->projectnr}}">{{$project->projectnr}} <small
                          class="text-muted">{{$project->projectnaam}}</small></span>
                    @endforeach
                  </div>
                </div>
              </div>
            @endisset
          </div>
        @else
          @php $klanten = getKlanten(['with' => ['contactpersonen']]); @endphp
          <div class="col-md-6 col-12 my-2">
            <label>Klant</label>
            @if(isset($werkbon->klant))
              <input type="text" class="form-control-custom" value="{{$werkbon->klant->naam ?? ''}}" placeholder="Klant" readonly>
            @else
              <div class="select_search-container">
                <input type="hidden" name="klant" class="select_search-hidden-input" data-placeholder="Klant" @if(getSettingValue('werkbon_project_or_klant_required', 'nee') == 'ja') required @endif>
                <div class="select_search-values">
                  <div class="select_search-box">
                    @foreach($klanten as $klant)
                      <span class="select_search-value" data-value="{{$klant->id}}" data-name="{{$klant->naam}}">
                        {{$klant->naam}}
                        <small class="text-muted">{{$klant->straat}} {{$klant->huisnummer}} {{$klant->plaats}}</small>
                      </span>
                    @endforeach
                  </div>
                </div>
              </div>
            @endisset
          </div>
        @endif
        @if($template->contactpersoon)
          <div class="col-md-6 col-12 my-2">
            <label>Contactpersoon</label>
            <div class="select_search-container" data-contact-select-search-container >
              <input type="hidden" name="contactpersoon" class="select_search-hidden-input" data-placeholder="Contactpersoon" data-disabled="true" >
              <div class="select_search-values" >
                <div class="select_search-box" data-contactpersoon-container >
                  @isset($werkbon->klant)
                    <span class="select_search-value" @if(!$werkbon->contactpersoon_id) data-selected="true" @endif data-value="" data-name="{{$werkbon->klant->contactpersoon_voornaam}} {{$werkbon->klant->contactpersoon_achternaam}}">{{$werkbon->klant->contactpersoon_voornaam}} {{$werkbon->klant->contactpersoon_achternaam}}</span>
                    @foreach($werkbon->klant->contactpersonen as $contact)
                      <span class="select_search-value" @if($werkbon->contactpersoon_id == $contact->id) data-selected="true" @endif data-value="{{$contact->id}}" data-name="{{$contact->voornaam}} {{$contact->achternaam}}">{{$contact->voornaam}} {{$contact->achternaam}}</span>
                    @endforeach
                  @endisset
                </div>
              </div>
            </div>
          </div>
        @endif
        <div class="col-md-6 col-12 my-2">
          <label>Medewerker</label>
          <div class="select_search-container">
            <input type="hidden" name="user" class="select_search-hidden-input" data-placeholder="Medewerker" required>
            <div class="select_search-values">
              <div class="select_search-box">
                @foreach(getUsers() as $user)
                  <span class="select_search-value flex-between" data-value="{{$user->id}}"
                        data-name="{{$user->name}} {{$user->lastname}}"
                        @if(isset($werkbon->user_id))
                          @if($werkbon->user_id == $user->id) data-selected="selected" @endif
                        @else
                          @if(Auth::user()->id == $user->id) data-selected="selected" @endif
                    @endif
                    >
                      <span>{{$user->name}} {{$user->lastname}}</span>
                      <span class="badge badge-lg badge-inverse-primary">{{$user->role->name}}</span>
                    </span>
                @endforeach
              </div>
            </div>
          </div>
        </div>
        @if($template->vestiging)
          <div class="col-md-6 col-12 my-2">
            <label>Vestiging</label>
            <div class="select_search-container">
              <input type="hidden" name="vestiging" class="select_search-hidden-input" data-placeholder="Vestiging">
              <div class="select_search-values">
                <div class="select_search-box">
                  @foreach(vestigingen() as $vestiging)
                    <span class="select_search-value"
                          @if(isset($werkbon->vestiging) && $werkbon->vestiging_id == $vestiging->id)
                            data-selected="1"
                          @endif data-value="{{$vestiging->id}}"
                          data-name="{{$vestiging->naam ?? $vestiging->plaats}}">{{$vestiging->naam ?? $vestiging->plaats}}</span>
                  @endforeach
                </div>
              </div>
            </div>
          </div>
        @endif
        <div class="col-md-6 col-12 taken-list">
          @if(count($werkbon->project_taken ?? []))
            <label>Taken</label>
            <div class="form-control-custom">
              @foreach($werkbon->project_taken ?? [] as $taak)
                <span class="mx-1 badge badge-primary" >{{$taak->name}}, </span>
              @endforeach
            </div>
          @endif
        </div>
      </div>

    </div>

    <div class="card p-2 my-2" >

{{--  opties--}}
      @foreach($opties as $index => $optie)
        <div class="row my-2">
          <label>{{$optie[0]->titel}}</label>
          <div class="col-12">
            <div class="p-0 pt-3 border border-bottom-0" style="background-color: rgba(0, 0, 0, 0.03)">
              <div class="row m-0">
                @foreach($optie as $optieIndex => $row)
                  <div onclick="selectOptie({{$index}},{{$optieIndex}})"
                       class="col-3 cursor-pointer d-inline-block border-bottom rounded-top tab{{$index}} tab"
                       data-tab=".tab{{$index}}">
                    <a class="py-1 text-center d-block">{{$row->naam}}</a>
                  </div>
                @endforeach
                @for($l = 1; $l <= 4-count($optie);$l++)
                  <div class="col-3 d-inline-block border-bottom rounded-top"></div>
                @endfor
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="p-0 pt-2 border border-top-0">
              <div class="py-2" id="row{{$index}}">
                @isset($werkbon)
                  @php
                    $temp = $werkbon->keywords->where('type', 'optie');
                    foreach ($temp as $optie){
                      $optiesFill[$optie->input->index][] = $optie;
                    }
                  @endphp
                  @foreach( $optiesFill[$index] ?? [] as $l => $record)
                    <div class="m-2 row py-2">
                      <div class="col-12 my-2">#{{$l+1}}</div>
                      @foreach(json_decode($record->value) as $name => $value)
                        @php
                          $type = '';
                          foreach(json_decode($record->input->rows) as $row){
                            if($row->name == $name){ $type = $row->type;}
                          }
                        @endphp
                        <div class="col-12 my-2">
                          <label>{{$name}}</label>
                          <input data-db="{{$record->input_id}}" name="opties[{{$record->input_id}}][{{$name}}][]" step="0.01" type="{{$type}}" class="form-control" placeholder="{{$name}}" value="{{$value}}">
                        </div>
                      @endforeach
                    </div>
                  @endforeach
                @endisset
              </div>
              <div class="text-right d-none" id="rowDiv{{$index}}">
                <a class="btn btn-primary text-white mr-2 mb-2" onclick="addRow({{$index}})">Regel toevoegen</a>
              </div>
            </div>
          </div>
        </div>
      @endforeach

{{--  Keywords--}}
      @php $inputSlot = false; @endphp
      @foreach($keywords as $keyword)
        @php
          if($keyword->type == 'input_slot'){ $inputSlot = true; }

          $data = json_decode($keyword->data ?? '[]');
          $keyword->value = isset($werkbon) ? ($werkbon->keywords->where('keyword', $keyword->keyword)->first()->value ?? null) : $keyword->value;
        @endphp
        <section class="keyword-container my-3" data-keyword="{{$keyword->keyword}}" >

          @if($keyword->type == 'header')
            <div class="p-2" style="background-color: {{$data->background ?? 'none'}}" >
              <span style="font-size: {{$data->size ?? '1rem'}}; color: {{$data->color ?? '#000'}}; font-weight: {{$data->weight ?? 500}};">{{$keyword->naam}}</span>
              <input type="hidden" name="items[{{$keyword->id}}]" value="header_filler" >
            </div>
          @endif

          @if($keyword->type == 'text' || $keyword->type == 'number' || $keyword->type == 'date' || $keyword->type == 'time')
              @php
              if($keyword->type == 'date' && $keyword->value == 'now'){
                $keyword->value = Carbon()->format('Y-m-d');
              }
              @endphp
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <input placeholder="{{$keyword->naam}}" value="{{$keyword->value}}" type="{{$keyword->type}}" step="{{$data->step ?? ''}}" class="form-control-custom keyword-input" data-keyword="{{$keyword->keyword}}" name="items[{{$keyword->id}}]" @if($keyword->required) required @endif>
            </div>
          @endif

          @if($keyword->type == 'checkbox')
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <label class="cursor-pointer" ><input placeholder="{{$keyword->naam}}" value="{{$keyword->value}}" type="{{$keyword->type}}" class="form-switch-custom keyword-input" data-keyword="{{$keyword->keyword}}" onchange="checkboxMessage(this, '{{$data->message ?? null}}')" name="items[{{$keyword->id}}]" @if($keyword->required) required @endif @if(isset($data->checked) && $data->checked) checked @endif @if(isset($data->disabled) && $data->disabled) disabled @endif></label>
              @if(isset($data->disabled) && $data->disabled) <input type="hidden" name="items_hidden[{{$keyword->id}}]" value="{{$keyword->value}}"> @endif
            </div>
          @endif

          @if($keyword->type == 'textarea')
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <textarea placeholder="{{$keyword->naam}}" type="{{$keyword->type}}" data-keyword="{{$keyword->keyword}}" class="form-control-custom keyword-input" name="items[{{$keyword->id}}]" @if($keyword->required) required @endif></textarea>
            </div>
          @endif

          @if($keyword->type == 'select' || $keyword->type == 'select_finish')
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <select class="form-select keyword-input" data-keyword="{{$keyword->keyword}}" name="items[{{$keyword->id}}]" @if($keyword->required) required @endif>
                <option value="">Selecteer optie</option>
                @foreach($data->options ?? [] as $option)
                  <option @if($option->value == $keyword->value) selected @endif value="{{$option->value}}">{{$option->name}}</option>
                @endforeach
              </select>
            </div>
          @endif

          @if($keyword->type == 'uursoorten')
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <select class="form-select keyword-input" data-keyword="{{$keyword->keyword}}" name="items[{{$keyword->id}}]" @if($keyword->required) required @endif>
                <option value="">Selecteer optie</option>
                @foreach(getBvUursoorten(Auth::user()->bv_id) ?? [] as $uursoort)
                  <option value="{{$uursoort->id}}">{{$uursoort->code}}. {{$uursoort->name}}</option>
                @endforeach
              </select>
            </div>
          @endif

          @if($keyword->type == 'list_select')
            @php
              if(isset($keyword->value)){$data = json_decode($keyword->value ?? '[]');}
            @endphp
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <div class="bg-light-grey rounded border p-2" >
                <div class="list-select-{{$keyword->id}}" >
                  @foreach($data->rows ?? [] as $r => $row)
                    <div class="flex-between my-1">
                      <span>{{$row->name}}</span>
                      <div class="btn-group" role="group" aria-label="Basic radio toggle button group">
                        @foreach($data->options ?? [] as $l => $option)
                          @php $string = randomString(); @endphp
                          <input type="radio" class="btn-check" name="items[{{$keyword->id}}][{{$row->name}}]" id="{{$string}}" value="{{$option->name}}" @isset($row->value) @if($row->value == $option->value) checked @endif @else @if($l == 0) checked @endif @endisset>
                          <label class="btn btn-outline-primary m-0" for="{{$string}}">{{$option->name}}</label>
                        @endforeach
                      </div>
                    </div>
                    @if(($r + 1) != count($data->rows ?? []))
                      <div class="text-center">
                        <div class="d-inline-block w-25 bg-secondary" style="height: 1px"></div>
                      </div>
                    @endif
                  @endforeach
                </div>
                @if($data->multiple)
                  <div class="text-center"><a class="btn btn-inverse-primary" onclick="addListSelect({{$keyword->id}})">Toevoegen</a></div>
                @endif
              </div>
            </div>
          @endif

          @if($keyword->type == 'list_input')
            @php
              $data = json_decode($keyword->data);
              if(!isset($werkbon)){ $keyword->value = $keyword->data; }
            @endphp
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <div class="list-input-{{$keyword->id}}" ></div>
              <div class="text-center mt-4">
                @if($data->multiple)
                  <div class="row justify-content-center my-2 w-auto my-1">
                    <div class="w-auto my-1 d-flex w-100 ">
                      <div class="btn-group border-0 ">
                        <button type="button" class="btn btn-inverse-primary @if(isset($keyword->listInputTemplates)) rounded-right @endif" onclick="addListInput({{$keyword->id}})">
                          <div class="flex-between mx--1">
                            <span class="mx-1" >Toevoegen</span>
                            <span class="mx-1" >@icon_plus</span>
                          </div>
                        </button>
                        @if(isset($keyword->listInputTemplates) && ($data->use_templates ?? false))
                          <button type="button" class="btn btn-inverse-primary rounded-right px-2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i  class="fas fa-angle-down m-0"></i></button>
                        @endif
                        <div class="dropdown-menu max-h-500 overflow-auto" list-input-template-dropdown-{{$keyword->id}}>
                          @forelse($keyword->listInputTemplates as $listInputTemplate)
                            <div class="d-flex justify-content-between align-items-center list-input-template-dropdown-{{$listInputTemplate->id}}" >
                              <div class="dropdown-item d-flex justify-content-between cursor-pointer add-detail" data-id="{{$keyword->id}}" data-prefill="{{$listInputTemplate->id}}" onclick="addListInputTemplate({{$keyword->id}}, {{$listInputTemplate->id}})">{{$listInputTemplate->name}} </div>
                              <div class="cursor-pointer text-center btn btn-outline-danger" onclick="confirmDeleteListInputTemplate({{$listInputTemplate->id}}, `{{$listInputTemplate->name}}`)" >@icon_trash</div>
                            </div>
                          @empty
                            <div class="d-flex justify-content-between align-items-center" >
                              <div class="dropdown-item d-flex justify-content-between cursor-pointer" data-id="{{$keyword->id}}" >Geen templates</div>
                            </div>
                          @endforelse
                        </div>
                      </div>
                      @if($data->use_templates ?? false)
                        <button type="button" class="btn btn-inverse-success ml-2 py-1" onclick="storeListInputTemplate({{$keyword->id}})">
                          <div class="flex-between mx--1">
                            <span class="mx-1" >Template opslaan</span>
                            <span class="mx-1" >@icon_save</span>
                          </div>
                        </button>
                      @endif
                    </div>
                  </div>
                @endif
              </div>
            </div>
          @endif

          @if($keyword->type == 'signature')
            <div>
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <div class="text-center signature-item-{{$keyword->id}}" >
                @isset($keyword->value)
                  <div class="bg-inverse-secondary border text-center rounded cursor-pointer" onclick="handtekening(14)">
                    <img src="{{url('api/file/'.$keyword->value)}}" class="w-50 bg-white rounded ">
                  </div>
                @else
                  <div class="cursor-pointer btn-inverse-secondary max-h-500 py-5" onclick="handtekening('{{$keyword->id}}')">Handtekening</div>
                @endif
              </div>
              <input type="hidden" class="keyword-input" data-keyword="{{$keyword->keyword}}" name="items[{{$keyword->id}}]" @isset($keyword->value) value="existing_{{$keyword->value}}" @endisset @if($keyword->required) required @endif>
            </div>
          @endif

          @if($keyword->type == 'image')
            <div class="image-container" >
              <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
              <input type="file" name="items[{{$keyword->id}}]" class="dropify keyword-input" data-keyword="{{$keyword->keyword}}"
                     @if($keyword->value)
                       data-default-file="{{url('api/file/explorer/files/'.$keyword->value)}}"
                     @else
                       @if($keyword->required) required @endif
                     @endif
               data-allowed-file-extensions="jpeg jpg png" >
              @isset($keyword->value)
                <input type="hidden" name="items_hidden[{{$keyword->id}}]" class="dropify-hidden" value="{{$keyword->value}}">
              @endisset
            </div>
          @endif

          @if($keyword->type == 'images')
              <div class="images-container" >
                <label>{{$keyword->naam}}@if($keyword->required) * @endif</label>
                <div data-images="{{$keyword->keyword}}" class="p-2 hover-shadow-inset-active" >
                  @foreach(json_decode($keyword->value ?? '[]') as $src)
                    @php $string = randomString() @endphp
                    <div class="flex-between my-2 {{$string}} ">
                      <div style="height: 75px"> <img class="h-100 rounded" src="{{url('/api/file/explorer/files/'.$src)}}"> </div>
                      <a class="btn btn-inverse-danger" onclick="deleteDiv('.{{$string}}')"><i class="fa fa-xmark m-0"></i></a>
                      <input type="hidden" name="items[{{$keyword->id}}][]" value="{{$src}}">
                    </div>
                  @endforeach
                </div>
                <div class="text-center my-2"> <a class="btn btn-inverse-primary" data-images-add data-id="{{$keyword->id}}" >Afbeelding @icon_plus</a> </div>
              </div>
          @endif

        </section>
      @endforeach

    </div>

    <div class="card p-2 my-2" >

{{--  input_slot--}}
      @if($inputSlot)
        <div class="row">
          @foreach($keywords as $keyword)
            @if($keyword->type != 'input_slot') @continue @endif
            @php
              $value = isset($werkbon) ? ($werkbon->keywords->where('keyword', $keyword->keyword)->first()->value ?? null) : null;
            @endphp
            <div class="col-12 my-2">
              <label>{{$keyword->naam}}</label>
              <input value="{{$value}}" type="text" class="form-control input" placeholder="{{$keyword->naam}}" name="items[{{$keyword->id}}]">
            </div>
          @endforeach
        </div>
      @endif

{{--  Definitief aantal personen--}}
      <div class="" id="offertePlanning">
        @php
          $defPersonen = isset($werkbon) ? json_decode(($werkbon->keywords->where("type", "offerte_aantal_personen")->first()->value ?? '[]'), true) : [];
        @endphp
        @if(count($defPersonen))
          <div class="my-2">
            <label  >Definitief aantal personen</label>
            @foreach($defPersonen as $id => $row)
              @php $activiteit = \App\OffertePlanning::where('id', $id)->first(); @endphp
              <div class="border p-2 rounded bg-light-grey my-2">
                <label>{{$activiteit->naam ?? ""}}</label>
                <div class="row">
                  <div class="col-md-6 col-12 my-2">
                    <label>Aantal</label>
                    <input type="number" class="form-control" name="offerteAantalPersonen[{{$id}}][aantal]" value="{{$row["aantal"] ?? 0}}">
                  </div>
                  <div class="col-md-6 col-12 my-2">
                    <label>Prijs</label>
                    <input type="number" class="form-control" name="offerteAantalPersonen[{{$id}}][prijs]" value="{{$row["prijs"] ?? 0}}">
                  </div>
                </div>
              </div>
            @endforeach
          </div>
        @endif
      </div>

{{--  PlanningRegels--}}
      <div class="row my-2 d-none" id="planningRegels">
        @foreach($werkbon->regels ?? [] as $regel)
          <div class="row my-2">
            <div class="row">
              <div class="col-md-6 col-12">
                <label>Naam</label>
                <input type="text" name="planningNaam" class="form-control" placeholder="Naam" value="{{$regel->naam}}">
              </div>
              <div class="col-md-2 col-12">
                <label>Aantal personen</label>
                <input type="number" name="planningAantal" class="form-control" placeholder="Aantal personen" value="{{$regel->aantal}}">
              </div>
              <div class="col-md-2 col-12">
                <label>Prijs</label>
                <input type="number" step="0.01" name="planningPrijs" class="form-control" placeholder="Prijs" value="{{$regel->prijs}}">
              </div>
              <div class="col-md-2 col-12">
                <label>BTW</label>
                <input type="number" step="0.01" name="planningBtw" class="form-control" placeholder="BTW" value="{{$regel->btw}}">
              </div>
            </div>
          </div>
        @endforeach
      </div>

{{--  Files--}}
      @if($template->file)
        <div class="row my-3" id="files">
          <div class="col-12">
            <label>Bestanden</label>
          </div>
          @isset($werkbon)
            @foreach(json_decode($werkbon->files ?? '[]') as $l => $src)
              <div class="col-md-6 col-12 my-2 image-container" >
                <input name="image[]" type="file" class="dropify mb-2" data-height="200" data-allowed-file-extensions="jpeg jpg png " data-default-file="{{asset("api/file/explorer/files/".$src)}}"/>
                <input type="hidden" name="image_hidden[]" class="dropify-hidden" value="{{$src}}">
              </div>
            @endforeach
          @else
            <div class="col-md-6 col-12 my-2">
              <input name="image[]" type="file" class="dropify mb-2" data-height="200" data-allowed-file-extensions="jpeg jpg png" @if($template->file == 2) required @endif />
            </div>
          @endisset
        </div>
        <div class="row">
          <div class="col-12 text-right">
            <a class="btn btn-primary text-white" onclick="addFile()">Bestand toevoegen</a>
          </div>
        </div>
      @endif

{{--  Betalingsmethode--}}
      @if($template->betalingsoptie)
        <div class="my-3">
          <label>Betalingsmethode</label>
          <select class="form-select" name="betalingsoptie">
            @if($template->betalingsoptie == 1)
              <option selected disabled >Selecteer een betaalmethode</option>
            @endif
            <option value="Pin" @if(($werkbon->betalingsoptie ?? null) == "Pin") selected @endif >Pin</option>
            <option value="Contant" @if(($werkbon->betalingsoptie ?? null) == "Contant") selected @endif >Contant</option>
            <option value="Factuur" @if(($werkbon->betalingsoptie ?? null) == "Factuur") selected @endif >Factuur</option>
            <option value="Online betaald" @if(($werkbon->betalingsoptie ?? null) == "Online betaald") selected @endif >Online betaald</option>
          </select>
        </div>
      @endif

{{--   Opmerking--}}
      @if($template->opmerking)
        <div class="my-3">
          <label>Opmerking</label>
          <input class="form-control" placeholder="Opmerking" name="opmerking" value="{{$werkbon->opmerking ?? ''}}">
        </div>
      @endif


    </div>

    <div class="my-3 text-center">
      <input type="submit" class="btn btn-success text-white" value="Opslaan">
      <input type="hidden" name="template" value="{{$template->id}}">
      @isset($werkbon)
        <input type="hidden" name="werkbon" value="{{$werkbon->id}}">
      @endisset
      @csrf
    </div>

  </form>



@endsection
@section('script')
  <script type="text/javaScript">
    var template = @json($template);
    var keywords = @json($keywords);
    var projecten = @json($projecten);
    var opties = @json($opties);
    var klanten = @json($klanten ?? []);
    var offerteCustom = @json(json_decode(getSettingValue("werkbon_offerte_info")));
    var selectedOpties = {};
    var optiesIndex = {};
    var project = false;

    const _editors = {};
    const _datasets = {};
    const _dom = {
      cp_container: $('[data-contactpersoon-container]'),
      cp_select_container: $('[data-contact-select-search-container]'),
    }

    $(document).ready(function () {
      editorsInit();
      listInputInit();
      verifyParentElement();
    });

    $(document).on('change', '.keyword-input', function(){
      verifyParentElement();
    })
    $(document).on('change', '.list-select-custom-input', function(){
      const id = $(this).attr('data-keyword-id');
      const string = $(this).attr('data-string');
      const name = $(this).val();

      $(`.input-${string}`).attr('name', `items[${id}][${name}]`);
    });
    $(document).on('click', '.dropify-clear', function(){
      findContainer('image-container', this).find(`.dropify-hidden`).val(null);
    })
    $(document).on('click', '[data-li-images-add]', function(){
      const container = findContainer('li-images-container', this);
      const rowContainer = findContainer('list-input-row-container', this);
      const singleImage = container.data('singleimage');
      const name = container.data('input-name');
      const string = rowContainer.data('string');
      const id = rowContainer.data('keyword-id');

      confirmModal({
        text: `<input type="file" class="dropify list-input-dynamic-image" data-height="200" data-allowed-file-extensions="jpeg jpg png" >`
      })
        .then(response => {
          if(!response.status){ return; }

          const files = $(`.list-input-dynamic-image`).prop('files');
          if(!files.length){ return }

          loader();
          ajaxFile('api/werkbonnen/upload', {file: files[0]})
            .then(fileResponse => {
              if(!fileResponse.status){ throw fileResponse }

              const imgString = randomString();
              container.find('[data-images]').append(`
                <div class="flex-between my-2 ${imgString} " data-list-input-image >
                    <div style="height: 75px" > <img class="h-100 rounded" src="${url}/api/file/explorer/files/${fileResponse.data.url}" > </div>
                    <a class="btn btn-inverse-danger" onclick="deleteListInputImage('.${imgString}')">@icon_close</a>
                    <input type="hidden" name="items[${id}][${string}][${name}][]" value="${fileResponse.data.url}" >
                </div>
              `)
              successLoader();

              if (singleImage){
                $(this).hide();
              }
            })
            .catch(err => { errorLoader(); })
        })

      dropifyInit();

    })
    $(document).on('click', '[data-images-add]', function(){
      const container = findContainer('images-container', this);
      const id = $(this).data('id')

      confirmModal({
        text: `<input type="file" class="dropify list-input-dynamic-image" data-height="200" data-allowed-file-extensions="jpeg jpg png" >`
      })
        .then(response => {
          if(!response.status){ return; }

          const files = $(`.list-input-dynamic-image`).prop('files');
          if(!files.length){ return }

          loader();
          ajaxFile('api/werkbonnen/upload', {file: files[0]})
            .then(fileResponse => {
              if(!fileResponse.status){ throw fileResponse }

              const imgString = randomString();
              container.find('[data-images]').append(`
                <div class="flex-between my-2 ${imgString} " >
                    <div style="height: 75px" > <img class="h-100 rounded" src="${url}/api/file/explorer/files/${fileResponse.data.url}" > </div>
                    <a class="btn btn-inverse-danger" onclick="deleteDiv('.${imgString}')" >@icon_close</a>
                    <input type="hidden" name="items[${id}][]" value="${fileResponse.data.url}" >
                </div>
              `)
              successLoader();
            })
            .catch(err => { errorLoader(); })
        })

      dropifyInit();

    })
    $(document).on('change', `[name='image[]']`, function(){
      findContainer('image-container', this).find(`.dropify-hidden`).val(null);
    });
    $(document).on('change', '[name=klant]', function(){
      const id = this.value;
      const klant = klanten.find(row => row.id == id);

      if(!klant){ return }

      selectKlant(klant);
    });

    $('[name=project]').change(function(){
      const id = this.value;
      selectProject(id);
    });

    $('.toggle-btn').click(function () {
      const attr = $(this).attr("data-div");
      $(attr).toggleClass("d-none")
    })
    $('.tab').click(function () {
      $($(this).attr("data-tab")).each(function () {
        $(this).removeClass(["bg-white", "border", "border-bottom-0", "border-bottom", "border-left-0", "border-right-0"]);
      })
      $(this).addClass(["bg-white", "border", "border-bottom-0"]).removeClass("border-bottom");
      if ($(this).is(":last-child")) {
        $(this).addClass("border-right-0")
      }
      if ($(this).is(":first-child")) {
        $(this).addClass("border-left-0")
      }
    })

    function deleteListInputImage(image_string){
      const image = $(image_string);
      const container = findContainerByAttr('data-list-input-image-container', image);
      const singleImage = container.data('singleimage');
      const images_count = container.find('[data-list-input-image]').length
      const plus = container.find('[data-li-images-add]');

     if (singleImage){
      plus.show();
     }

     image.remove();
    }

    function formPost(){
      $('[type=submit]').addClass('d-none').after('@spinner_success');
      return true;
    }

    function verifyParentElement(){
      for(const item of keywords){
        if(!item.parent_keyword){continue;}

        const container = $(`.keyword-container[data-keyword='${item.keyword}']`);
        container.addClass('d-none');

        if(item.type == 'list_input'){
          verifyParentElementListInput(item, container);
          continue;
        }

        const input = $(`.keyword-input[data-keyword='${item.keyword}']`)
        input.prop('required', false)

        const value = $(`.keyword-input[data-keyword='${item.parent_keyword}']`).val();
        if(value == item.parent_value){
          container.removeClass('d-none');
          if(!$(`[name='items_hidden[${item.id}]']`).length){
            input.prop('required', (item.required === '1'))
          }
        }
      }
    }

    function verifyParentElementListInput(item, container) {
        const itemValues = JSON.parse(item.value)
        const parent_value = $(`.keyword-input[data-keyword='${item.parent_keyword}']`).val();

        if (parent_value == item.parent_value) {
            container.removeClass('d-none');
        }

        (itemValues?.inputs || []).forEach(function (input) {
            let inputAttr = $(`.keyword-input[data-input-name='${input.name}']`)
            inputAttr.prop('required', false);

            if (parent_value == item.parent_value) {
                if (!$(`[name='items_hidden[${item.id}]']`).length) {
                    inputAttr.prop('required', (input.required === '1'))
                }
            }
        })
    }

    function editorsInit(){
      for(const item of keywords){
        if(item.type != 'textarea'){ continue; }

        editorInit(`.keyword-input[data-keyword='${item.keyword}']`, item.value)
          .then(editor => _editors[item.keyword] = editor);
      }
    }
    function listInputInit(){
      for(const item of keywords){

        if(item.type !== 'list_input' || !item.value){ continue; }
        try{

          const value = JSON.parse(item.value);
          if(!value.rows){ continue; }

          appendListinput(value, item)
        }
        catch (e){
          notification(`Er is iets foutgegaan (LII_${item.id})`);
          actError(e);
        }
      }
    }

    function addFile() {
      $("#files").append(
        '<div class="col-md-6 col-12 my-2">' +
        '<input name="image[]" type="file" class="dropify mb-2" data-height="200" data-allowed-file-extensions="jpeg jpg png "/>' +
        '</div>'
      );
      dropifyInit();
    }
    function addListSelect(id){
      const keyword = keywords.find(row => row.id == id);
      const data = JSON.parse(keyword.data || '[]');
      const string = randomString();

      let options = '';
      for(const option of data.options){
        randomString();
        options += `<input type="radio" class="btn-check input-${string}" name="items[${keyword.id}][]" id="${lastString()}" value="${option.name}">
                    <label class="btn btn-outline-primary m-0" for="${lastString()}">${option.name}</label>`
      }

      $(`.list-select-${keyword.id}`).append(`
      <div class="text-center ${string} ">
        <div class="d-inline-block w-25 bg-secondary" style="height: 1px"></div>
      </div>
      <div class="flex-between ${string} my-1">
        <div class="flex-between">
            <a class="btn text-danger" onclick="deleteDiv('.${string}')" >@icon_close</a>
            <input type="text" class="form-control-custom list-select-custom-input" data-keyword-id="${keyword.id}" data-string="${string}" placeholder="Naam">
        </div>
        <div class="btn-group" role="group" aria-label="Basic radio toggle button group">
          ${options}
        </div>
      </div>`)
    }
    function addListInput(id, name = undefined , itemid = undefined, templateId = undefined){
      const keyword = keywords.find(row => row.id == id);

      if(!keyword){
        notification(`Er is iets foutgegaan (ALI_${id})`);
        return;
      }

      const data = JSON.parse(keyword.data || '[]');
      const string = randomString(15);

      if(data.dataset && name === undefined){
        if(!_datasets[data.dataset]){
          loader();
          ajax('api/offertes/datasets/get', {id: data.dataset})
            .then(response => {
              successLoader();
              _datasets[data.dataset] = response.dataset;
              addListInput(id);
            })
            .catch(err => errorLoader());
          return
        }

        const dataset = _datasets[data.dataset];
        if(!dataset){ notification('Dataset niet gevonden!', 'danger'); return }

        let items = ``
        for(const item of dataset.items){
          items += `
                    <span class="select_search-value" onclick="addListInput(${id}, '${item.name}', ${item.id}); hideModal('dynamic-confirm-modal')">${item.name}</span>
                    `
        }

        let itemselect = `
        <div class="select_search-container">
          <input type="hidden" name="" class="select_search-hidden-input" data-placeholder="Selecteer item">
          <div class="select_search-values" >
            <div class="select_search-box" >
              <span class="select_search-value" onclick="addListInput(${id}, ''); hideModal('dynamic-confirm-modal')">Leeg item toevoegen</span>
              ${items}

            </div>
          </div>
        </div>
        <div class=" my-1 center">
          <label> Of </label>
          <button class="btn btn-block btn-primary" onclick="makeDatasetItem(${data.dataset}, ${id})">Item toevoegen aan dataset</button>
        </div>
        `

        confirmModal({
          text: itemselect,
          hideFooter: true,
        })
        initSelectSearch();
        return
      }

      let options = '';
      const itemvalues = JSON.parse(_datasets[data.dataset]?.items?.find(row => row.id == itemid)?.value ?? '[]');
      for(const input of data.inputs){
        let inp = ''
        if(input.type == 'text' || input.type == 'date' || input.type == 'time'){
          inp = `<input type="${input.type}" name="items[${id}][${string}][${input.name}]" data-input-name="${input.name}" data-type="${input.type}" class="form-control-custom input-${string} keyword-input" placeholder="${input.name}" ${Number(keyword.required) ? 'required' : ''} ${itemvalues[input.dprefill] ? `value="${itemvalues[input.dprefill]}"` : ''}>`
        }
        else if(input.type == 'number'){
          inp = `<input type="${input.type}" name="items[${id}][${string}][${input.name}]" step="${input.step ? input.step : '1'}" data-input-name="${input.name}" data-type="${input.type}" class="form-control-custom input-${string} keyword-input" placeholder="${input.name}" ${Number(keyword.required) ? 'required' : ''} ${itemvalues[input.dprefill] ? `value="${itemvalues[input.dprefill]}"` : ''}>`
        }
        else if(input.type == 'select'){
          let opt = '<option value="" class="text-muted" >Selecteer optie</option>';
          for(const option of input.data.options){
            opt += `<option value="${option.value}" >${option.name}</option>`
          }
          inp = `<select name="items[${id}][${string}][${input.name}]" data-input-name="${input.name}" data-type="select" class="form-select input-${string} keyword-input"   ${Number(keyword.required) ? 'required' : ''} >${opt}</select>`
        }
        else if(input.type == 'radio'){
          for(const option of input.data.options){
            inp += `<label class="cursor-pointer hover-mark m-0 px-2 py-1 rounded-5" > <input type="radio" name="items[${id}][${string}][${input.name}]" data-input-name="${input.name}" data-type="radio" class=" input-${string}  keyword-input" value="${option.value}" > ${option.name} </label>`
          }
          inp = `<div class="d-flex" > ${inp} </div>`
        }
        else if(input.type == 'images'){
          inp = `<div data-input-name="${input.name}" data-type="images" data-list-input-image-container data-singleimage="${data.singleImage ?? false}" class="li-images-container" >
                    <div data-images class="p-2 hover-shadow-inset-active" ></div>
                    <div class="center my-2" > <a class="btn btn-sm btn-inverse-primary" data-li-images-add >@icon_plus</a> </div>
                </div>`
        }


        options +=  `<div class="my-2">
                      ${ !input?.data?.hide_name ? `<nobr class="font-size-075" >${input.name}:</nobr>` : '' }
                      ${inp}
                    </div>`
      }
      $(`.list-input-${id}`).append(`
        <div class="flex-between my-1 bg-light-grey rounded border p-2 list-input-row-container ${data.direction == 'column' ? 'flex-column' : 'flex-row'}" data-string="${string}" data-keyword-id="${keyword.id}" >
          <div class="flex-between ${data.direction == 'column' ? 'w-100' : ''}">
            <a class="btn text-danger" onclick="deleteDiv('[data-string=${string}]')" >@icon_close</a>
            <input type="text" class="form-control-custom keyword-input" data-keyword="${data.keyword}" name="list_input_name[${string}]" value="${name ?? ''}" placeholder="Naam" required>
            <input type="hidden" class="form-control-custom" name="list_input_dataset_item_id[${string}]" value="${itemid ?? ''}">
          </div>
          <div class="${data.direction == 'column' ? 'w-100' : ''}"" >${options}</div>
      </div>`
      );

      return string;
    }

    async function addListInputTemplate(keywordId, listInputTemplateId) {
      const keyword = keywords.find(row => row.id == keywordId);
      const listInputTemplate =  await ajax('api/werkbonnen/list-input/get', {id: listInputTemplateId});

      appendListinput(JSON.parse(listInputTemplate.data), keyword);
    }
    function appendListinput(value, item) {
      for (const row of value.rows) {
        let itemId = row.id ? row.id : row.datasetitem?.id ?? null;
        const string = addListInput(item.id, row.name, itemId);
        const container = $(`.list-input-row-container[data-string=${string}]`);

        for (const name in row.values) {
          const v = row.values[name];
          const target = container.find(`[data-input-name="${name}"]`);
          const type = target.data('type');

          if (type === 'text' || type === 'number' || type === 'select' || type === 'date' || type === 'time') {
            target.val(v);
          } else if (type === 'radio') {
            container.find(`[data-input-name="${name}"][value="${v}"]`).prop('checked', true);
          } else if (type === 'images') {
            for (const src of v) {
              const singleImage = target.data('singleimage');
              const plus = target.find('[data-li-images-add]');
              const imgString = randomString(15);
              target.find('[data-images]').append(`
              <div class="flex-between my-2 ${imgString}">
                  <div style="height: 75px">
                      <img class="h-100 rounded" src="${url}/api/file/explorer/files/${src}">
                  </div>
                  <a class="btn btn-inverse-danger" onclick="deleteListInputImage('.${imgString}')">@icon_close</a>
                  <input type="hidden" name="items[${item.id}][${string}][${name}][]" value="${src}">
              </div>
          `);
              if (singleImage){
                plus.hide();
              }
            }
          }
        }
      }
    }
    async function confirmDeleteListInputTemplate(id, naam){
      const { status } = await confirmModal({
        text: `Weet je zeker dat je template ${naam} wilt verwijderen?`,
        btnColor: 'btn-danger',
        btnText: 'Verwijderen'
      })

      if(!status){ return; }

      ajax('api/werkbonnen/list-input/delete', {id: id})
        .then(response => {
          if(!response.status){ throw response; }
          successLoader('Template is verwijderd!');
          deleteDiv(`.list-input-template-dropdown-${id}`)
          $(`.list-input-template-dropdown-${id}`).remove();
        })
        .catch(handleCatchError)
    }
    async function storeListInputTemplate(keywordId){

      const { status } = await confirmModal({
        text: `<div>
                <label>Naam</label>
                <input type="text" class="form-control-custom" data-list-input-template-name placeholder="Naam" required>
              </div>`,
        buttonText: "Opslaan",
      })
      if(!status){ return; }

      const name = $('[data-list-input-template-name]').val();
      if(!name){
        notification('Naam is verplicht!', 'danger');
        return;
      }

      const data = $(`[data-keyword-id=${keywordId}] :input`).serializeArray();
      data.push({
        name: 'keyword_id',
        value: keywordId
      });
      data.push({
        name: 'name',
        value: name
      });

      const response = await ajax('api/werkbonnen/list-input/store', data)
      if(!response){ return; }

      successLoader('Template is opgeslagen!');
      $(`[list-input-template-dropdown-${keywordId}]`).append(`
        <div class="d-flex justify-content-between align-items-center list-input-template-dropdown-${response.templateId}" >
          <div class="dropdown-item d-flex justify-content-between cursor-pointer add-detail" data-id="${keywordId}" data-prefill="${response.templateId}" onclick="addListInputTemplate(${keywordId}, ${response.templateId})">${response.name}</div>
          <div class="cursor-pointer text-center btn btn-outline-danger" onclick="confirmDeleteListInputTemplate(${response.templateId})" >@icon_trash</div>
        </div>
      `)
    }

    function makeDatasetItem(datasetid, id){
      hideModal('dynamic-confirm-modal');
      addDatasetItem(datasetid)
        .then(response => {
          if(!response.status){return;}
          addListInput(id, response.data.name)
        })
    }

    function selectProject(id) {
      try{
        const { cp_container, cp_select_container } = _dom;
        project = projecten.find(row => row.id == id);

        $("#offerteAantalPersonen").addClass("d-none").html("")
        $("#planningRegels, #offertePlanning, .taken-list, [data-project-relations]").empty();


        cp_container.empty();
        cp_select_container.find('input').val('');

        if (project.klant) {
          selectKlant(project.klant);
        }
        if (project.offerte) {
          let offerte = project.offerte;
          $('[data-project-relations]').append(`<a class="btn btn-sm btn-inverse-primary mx-1" href='${url}/offertes/edit/${offerte.id}/preview' target="_blank" >Offerte @icon_external_link</a>`)
          if (offerte.offerteplanning.length) {

            $("#offertePlanning").removeClass("d-none").html(
              '<div class="my-2">' +
              '<label>Definitief aantal personen</label>' +
              '<div id="offerteAantalPersonen"></div>' +
              '</div>'
            );

            for (let planning of offerte.offerteplanning) {
              $('#offerteAantalPersonen').append(
                `<div class="border p-2 rounded bg-light-grey my-2">
                <label>${planning.naam}</label>
                <div class="row">
                  <div class="col-md-6 col-12 my-2">
                    <label>Aantal</label>
                    <input type="number" class="form-control" name="offerteAantalPersonen[${planning.id}][aantal]" value="${planning.aantal}">
                  </div>
                  <div class="col-md-6 col-12 my-2">
                    <label>Prijs</label>
                    <input type="number" class="form-control" name="offerteAantalPersonen[${planning.id}][prijs]" value="${planning.bedrag}">
                  </div>
                </div>
              </div>`
              );
            }

          }
        }
        if(project.uncompleted_taken.length){
          $('.taken-list').html(`
          <label>Taken</label>
          <div class="select_multiple-container" data-placeholder="Taken" >
            ${ project.uncompleted_taken.map(taak => {
            return `<label class="select_multiple-value" >${taak.name || ''} <input type="checkbox" class="d-none" name="taken[${taak.id}]" data-name="${taak.name || ''}""></label>`
          }).join('') }
          </div>
        `);
          initSelectMultiple();
        }

      }
      catch (e){
        actError(e);
        notification(e.message || 'Er is iets foutgegaan!');
      }

    }
    function selectKlant(klant){
      const { id, contactpersoon_voornaam, contactpersoon_achternaam } = klant;
      const cp_container = $('[data-contactpersoon-container]');

      if(contactpersoon_voornaam || contactpersoon_achternaam){
        cp_container.append(`<span class="select_search-value"  data-value="" data-name="${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}">${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}</span>`)
      }
      for(const contact of klant.contactpersonen){
        cp_container.append(`<span class="select_search-value" data-value="${contact.id}" data-name="${contact.voornaam || ''} ${contact.achternaam || ''}">${contact.voornaam || ''} ${contact.achternaam || ''}</span>`)
      }

      cp_container.find(`[data-value="${project.contactpersoon_id || ''}"]`).click();

      $('[data-project-relations]').append(`<a class="btn btn-sm btn-inverse-primary mx-1" onclick="showKlant(${id})" >Klant @icon_external_link</a>`);
    }

    function handtekening(id){
      getSignature()
        .then((response) => {
          if(!response.status){return}

          $(`.signature-item-${id}`).html(
            `<div class="bg-inverse-secondary border text-center rounded cursor-pointer" onclick="handtekening(${id})">
              <img src="${response.src}" class="w-50 bg-white rounded ">
            </div>`
          )
          $(`[name='items[${id}]']`).val(response.src);
        })
    }

    function selectOptie(i, x) {
      selectedOpties[i] = x;
      optiesIndex[i] = 1;
      $("#rowDiv" + i).removeClass("d-none");
      $("#row" + i).html("");
      const rows = JSON.parse(opties[i][x].rows);
      let string = "";
      for (let row of rows) {
        string +=
          '<div class="col-12 my-2" >' +
          '<label>' + row.name + '</label>' +
          '<input data-db="' + opties[i][selectedOpties[i]].id + '" name="opties[' + opties[i][selectedOpties[i]].id + '][' + row.name + '][]" type="' + row.type + '" step="0.01" class="form-control" placeholder="' + row.name + '" value="' + row.value + '">' +
          '</div>';
      }
      $("#row" + i).append(
        '<div class="m-2 row py-2">' +
        '<div class="col-12 my-2">#' + optiesIndex[i] + '</div>' + string +
        '</div>'
      )
    }
    function addRow(i) {
      optiesIndex[i] = optiesIndex[i] + 1;
      const rows = JSON.parse(opties[i][selectedOpties[i]].rows);
      let string = "";
      for (let row of rows) {
        string +=
          '<div class="col-12 my-2" >' +
          '<label>' + row.name + '</label>' +
          '<input data-db="' + opties[i][selectedOpties[i]].id + '" name="opties[' + opties[i][selectedOpties[i]].id + '][' + row.name + '][]" type="' + row.type + '" step="0.01" class="form-control" placeholder="' + row.name + '" value="' + row.value + '">' +
          '</div>';
      }
      $("#row" + i).append(
        '<div class="m-2 row py-2">' +
        '<div class="col-12 my-2">#' + optiesIndex[i] + '</div>' + string +
        '</div>'
      )
    }

    function checkboxMessage(checkbox, message = ''){
      if(checkbox.checked && message){
        notification(message)
      }
    }

  </script>
@endsection
