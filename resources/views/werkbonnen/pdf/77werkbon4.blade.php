<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{{$werkbon->werkbonnummer}}</title>
    <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
    <style>
        @page {
            size: A4;
            margin: 10mm 7.5mm;
            font-family: Arial, sans-serif;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #009FE3;
            color: white;
        }
       
        .header-table {
            width: 100%;
            border: none;
            margin-bottom: 10px;
        }
        .header-table td {
            border: none;
            vertical-align: middle;
        }
        .logo {
            width: auto;
            height: 150px;
            display: block;
        }
        .company-info {
            text-align: right;
            font-size: 15px;
        }
        table.artikel-table th:nth-child(1), 
        table.artikel-table td:nth-child(1) {
            width: 20%;
        }
        table.artikel-table th:nth-child(2), 
        table.artikel-table td:nth-child(2) {
            width: 60%;
        }
        table.artikel-table th:nth-child(3), 
        table.artikel-table td:nth-child(3) {
            width: 20%;
        }
        table.naam-table th:nth-child(1) {
            width: 30%;
        }
        table.naam-table th:nth-child(2) {
            width: 70%;
        }
        .voorwaarden {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            position: relative;
        }

        .voorwaarden img {
            max-width: 100%;
            max-height: 100%;
            display: block;
            margin: auto;
        }
        .klant-info p {
            margin: 0;
        }
    </style>
</head>
<body>
    <table class="header-table">
        <tr>
            <td style="width: 50%;">
                <img src="{{ url('client/public/img/pdf/77template1/bv59_jonck.png') }}" alt="JONCK Logo" class="logo">
            </td>
            <td style="width: 50%;" class="company-info">
                Jonck Windows and Doors Projects bv.<br>
                {{ $werkbon->_bv->straat }} {{ $werkbon->_bv->huisnummer }}<br>
                {{ $werkbon->_bv->postcode }} {{ $werkbon->_bv->plaats }}<br>
                Tel: {{ $werkbon->_bv->telefoon }}<br>
                E-mail: {{ $werkbon->_bv->email }}
            </td>
        </tr>
    </table>
    <h2>Leveringsbon : {{ $werkbon->werkbonnummer }}</h2>
    @isset($werkbon->klant)
        <div class="klant-info">
            <p><strong>Besteld door:</strong></p>
            <p>{{ $werkbon->klant->title() }}</p>
            <p>{{ $werkbon->klant->addressLine() }}</p>
            <p>{{ $werkbon->klant->postcode }} {{ $werkbon->klant->plaats }}</p>
        </div>
    @endisset
    <table>
        <tr>
            <th>Afhaaldatum</th>
            <th>Afgehaald door</th>
            <th>Afgehaald te</th>
            <th>Ordernummer</th>
        </tr>
        <tr>
            <td>{{ isset($keywords['afhaaldatum']->value) ? CarbonDmy($keywords['afhaaldatum']->value) : 'Niet beschikbaar' }}</td>
            <td>{{ $keywords['afgehaalddoor']->value ?? ''  }}</td>
            <td>{{ $keywords['afgehaaldte']->value ?? ''  }}</td>
            <td>{{ $keywords['ordernummer']->value }}</td>
        </tr>
    </table>
    <table>
        <tr>
            <th>Leverdatum</th>
            <th>Geleverd door</th>
            <th>Ontvangen door</th>
            <th>Opmerking</th>
        </tr>
        <tr>
            <td>{{ isset($keywords['leverdatum']->value) ? CarbonDmy($keywords['leverdatum']->value) : 'Niet beschikbaar' }}</td>
            <td>{{ $keywords['geleverddoor']->value ?? ''  }}</td>
            <td>{{ $keywords['ontvangendoor']->value ?? ''  }}</td>
            <td>
                @if(!empty($werkbon->opmerking))
                    {{ $werkbon->opmerking }}
                @endif
            </td>
        </tr>
    </table>
    @if (isset($keywords['artikelen']->value))
        <table class="artikel-table avoid-break">
            <tr>
                <th>Artikelnr.</th>
                <th>Artikel</th>
                <th>Mano's</th>
                <th>Stuks</th>
            </tr>
            @foreach (json_decode($keywords['artikelen']->value)->rows as $item)
                <tr>
                    <td>{{ $item->datasetitem->value->product_code ?? 'N.v.t.' }}</td>
                    <td>{{ $item->name }}</td>
                    <td>{{ $item->values->{'Mano\'s'} }}</td>
                    <td>{{ $item->values->Stuks }}</td>
                </tr>
            @endforeach
        </table>
    @endif
    @if ($keywords['volledigenaam']->value || isset($keywords['handtekening']->value))
        <table class="naam-table">
            <tr>
                <th>Naam</th>
                <th>Handtekening ontvanger</th>
            </tr>
            <tr>
                <td>{{ $keywords['volledigenaam']->value ?? '' }}</td>
                <td>
                    @if (isset($keywords['handtekening']->value))
                        <img style="width: 100%" src="{{ url('api/file/'.$keywords['handtekening']->value) }}">
                    @endif
                </td>
            </tr>
        </table>
    @endif

    <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_1.png')}}" alt="Algemene voorwaarden">
    </div>
    <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_2.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_3.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_4.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_5.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_6.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_7.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_8.png')}}" alt="Algemene voorwaarden">
    </div>
        <div class="page-break"></div>
    <div class="voorwaarden">
        <img src="{{url('client/public/img/pdf/77template1/voorwaarden_jonck_9.png')}}" alt="Algemene voorwaarden">
    </div>
</body>
</html>
