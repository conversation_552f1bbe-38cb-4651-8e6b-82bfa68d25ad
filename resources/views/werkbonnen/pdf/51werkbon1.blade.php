<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$werkbon->werkbonnummer}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 80%;
      margin: 170px 10% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      font-family: sans-serif;
      font-size: 15px;
      margin-left: 12px;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      width: 800px;
      top: -170px;
      left: -80px;
      right: 0px;
      height: 150px;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: -100px;
      left: -12.5%;
      right: 0cm;
      z-index: -1;
      width: 100.1%;

    }

    .sidebarLeft {
      position: fixed;
      top: -190px;
      left: -80px;
      width: 120px;
    }

    table {
      page-break-inside: auto;
    }

    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

  </style>
</head>
<header>
  <img src="{{url("/client/public/img/pdf/51template1/headerHaltronic.png")}}" class="w100 h-100"/>
</header>

<div class="sidebarLeft">
  <img src="{{url("/client/public/img/pdf/51template3/sidebar.png")}}" width="100%" style="margin-top: 7.5cm"/>
</div>

<footer>

</footer>

<body >
<main>
  <section class="w100 ">
    <div class="text-left" style="font-size: 0.9rem; padding-top: 20px;">
      <table style="margin-left: 5%; margin-top: 0%;">
        <tr class="border-bottom">
          <td><label>Klant</label></td>
          <td><b>{{$werkbon->klant->naam ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Taken</label></td>
          <td>
            <table style="margin-left: -2%">
              @foreach($werkbon->project_taken as $taak)
                <tr>
                  <td><b>{{$taak->name ?? ''}}</b></td>
                </tr>
              @endforeach
            </table>
          </td>
        </tr>
        <tr class="border-bottom">
          <td><label>Locatie</label></td>
          <td><b>{{$werkbon->project->locatie->naam ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Locatie Opmerking</label></td>
          <td><b>{{$werkbon->project->locatie->opmerking ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Opdrachtnummer</label></td>
          <td><b>{{$keywords['opdrachtnummer']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Contactpersoon op Locatie</label></td>
          <td><b>{{$keywords['contactpersoon_op_locatie']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Telefoonnummer Contactpersoon op Locatie</label></td>
          <td><b>{{$keywords['telefoon_nummer_contactpersoon_op_locatie']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Werkbonnummer</label></td>
          <td><b>{{$werkbon->werkbonnummer ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Afspraakdatum</label></td>
          <td><b>{{$keywords['Afspraak_datum']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Afspraaktijd</label></td>
          <td><b>{{$keywords['Afspraak_tijd']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Monteur</label></td>
          <td><b>{{$werkbon->user->firstname ?? ''}} {{$werkbon->user->lastname ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Werkomschrijving</label></td>
          <td><b>{!! $keywords['Werkomschrijving']->value ?? '' !!}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Oorzaak</label></td>
          <td><b>{!! $keywords['Oorzaak']->value ?? '' !!}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Melding Correct</label></td>
          <td><b>{{$keywords['Melding_correct']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Werkzaamheden</label></td>
          <td><b>{!! $keywords['Werkzaamheden']->value ?? '' !!}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Materialen</label></td>
          <td>
            <table>
              <tr>
                <th><b>Aantal</b></th>
                <th><b>Materiaal</b></th>
                <th><b>Artikelnummer</b></th>
              </tr>
              @if(isset($keywords['materialen']))
                @foreach(json_decode($keywords['materialen']->value)->rows as $materiaal)
                  <tr>
                    <td><b>{{$materiaal->values->Aantal ?? 0}}</b></td>
                    <td><b>{{$materiaal->name ?? ''}}</b></td>
                    <td><b>{{$materiaal->datasetitem->value->code ?? ''}}</b></td>
                  </tr>
                @endforeach
              @endif
            </table>
          </td>
        </tr>
        <tr class="border-bottom">
          <td><label>Extra Materialen</label></td>
          <td><b>{!! $keywords['extra_materialen']->value ?? '' !!}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Referentie opdrachtgever</label></td>
          <td><b>{{$keywords['referentie_opdrachtgever']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Storing</label></td>
          <td><b>Nee</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Handtekening</label></td>
            <td><img style="width: 80%" alt="" src="{{ url('api/file/'.$keywords['signature']->value) }}"></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Werk gereed</label></td>
          <td><b>{{$keywords['werk_gereed']->value ?? ''}}</b></td>
        </tr>
        <tr class="border-bottom">
          <td><label>Bijlage (n)</label></td>
          <td>
            @foreach(json_decode($werkbon->files) as $src)
              <img style="max-height: 800px;width: 85%; display: inline-block" src="{{asset("api/file/explorer/files/".$src)}}" >
            @endforeach
          </td>
        </tr>
      </table>
    </div>
  </section>
</main>
</body>
</html>
