@extends('layouts.app')

@section('title', 'Checklists')

@section('content')
  <form class="append-loader" method="post" id="ck-form" onsubmit="return post()" autocomplete="off" enctype="multipart/form-data">

    {{--        Klant & Aanvragen--}}
    <section class="card p-2 my-3">
      <div class="row">
        <div class="col-md-6 col-12 my-2">
          <div class="d-flex justify-content-between">
            <label>Klant</label>
            <div id="all-klanten-buttons" class="d-flex justify-content-between">
              <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy" onclick="newKlant()" data-tippy-content="Klant aanmaken" >@icon_plus</a>
              <div id="klanten-buttons">

              </div>
            </div>
          </div>

          @if(!isset($checklist->klant))
            <infor-select-search name="klant" id="klant" class="form-control-custom" placeholder="Selecteer klant" >
              @foreach($klanten as $klant)
                <infor-select-option data-value="{{$klant->id}}" ><div class="tippy" data-tippy-content="Adres: {{$klant->locatie()}}" >{{$klant->title()}}</div></infor-select-option>
              @endforeach
            </infor-select-search>
          @else
            <input class="form-control-custom" disabled value="{{$checklist->klant->title()}}">
          @endif
        </div>

        @if(isset($template->klant_locatie))
          <div class="col-md-6 col-12 my-2" id="klant_locatie_container">
            <label>Locatie</label>
            @if(!isset($checklist->klant_locatie))
              <infor-select-search name="klant_locatie" id="klant_locatie" class="form-control-custom" placeholder="Selecteer locatie" >
                @foreach($checklist->klant->locaties ?? [] as $locatie)
                  <infor-select-option data-value="{{$locatie->id}}" ><div class="tippy" data-tippy-content="Adres: {{$locatie->addressLine()}}" >{{$locatie->addressLine()}}</div></infor-select-option>
                @endforeach
              </infor-select-search>
            @else
              <input class="form-control-custom" disabled value="{{$checklist->klant_locatie->addressLine()}}">
            @endif
          </div>
        @endif

        @if($template->project)
          <div class="col-md-6 col-12 my-2">
            <label>Project</label>
            @if(!isset($checklist->klant))
              <infor-select-search name="project" id="project" class="form-control-custom" data-disabled placeholder="Selecteer project" ></infor-select-search>
            @elseif(!isset($checklist->project))
              <infor-select-search name="project" id="project" class="form-control-custom" placeholder="Selecteer project" >
                @foreach(getProjecten(['where' => ['klant_id' => $checklist->klant->id]]) as $project)
                  <infor-select-option data-value="{{$project->id}}" >{{$project->projectnaam ?? ''}} - {{$project->projectnr ?? ''}}</infor-select-option>
                @endforeach
              </infor-select-search>
            @else
              <input class="form-control-custom" disabled value="{{$project->projectnaam ?? ''}} - {{$project->projectnr ?? ''}}">
            @endif
          </div>
        @endif

        @if(hasModule("Aanvragen"))
          <div class="col-md-6 col-12 my-2">
            <label>Aanvragen</label>
            <div class="p-2 shadow rounded">
              <div id="aanvragen">
                @if(isset($checklist) && isset($checklist->klant)  && isset($aanvragen[$checklist->klant->id]))
                  @foreach($aanvragen[$checklist->klant->id] as $anv)
                    <div class="my-2">
                      <label class="cursor-pointer d-flex align-items-center my-1" ><input @if(isset($selectedAnv[$anv->id])) checked @endif class="cursor-pointer form-check-input position-unset m-0 mr-2" id="aanvraag-checkbox-{{$anv->id}}" type="checkbox" name="aanvragen[]" value="{{$anv->id}}" > {{$anv->aanvraagnummer}}</label>
                    </div>
                  @endforeach
                @endif
              </div>
            </div>
          </div>
        @endif
      </div>
    </section>


    {{--        Details--}}
    @php $detailsIndex = 0; @endphp
    <section id="details-div" >
      @foreach($checklist->details ?? [] as $detail)
        <div class="my-4 p-2 card" id="detail-{{$detailsIndex}}" >
          <div class="d-flex justify-content-between align-items-center" >
            <h4 class="my-2" ># {{($detailsIndex+1)}} {{$detail->name}}</h4>
            <div id="detail-buttons-{{$detailsIndex}}" class="d-flex overflow-auto" >
              <a class="btn btn-danger text-white mx-1" onclick="removeDetail({{$detailsIndex}})" >@icon_trash</a>
            </div>
          </div>
          <div id="detail-disable-{{$detailsIndex}}" class="row" >
            <div class="col-md-6 col-12 my-2" >
              <label>Titel</label>
              <input name="detail_title[{{$detailsIndex}}]" value="{{$detail->title}}" placeholder="Titel" class="form-control detail-title" required>
            </div>

            @if($detail->template->img === '1')
              <div class="col-12 my-2">
                <label>Afbeelding</label>
                <input type="file" class="dropify detail-img" name="detail_img[{{$detailsIndex}}]" @if(isset($detail->image)) data-default-file="{{asset('api/file/'.$detail->image)}}" @endif data-allowed-file-extensions="jpg jpeg png" data-max-file-size-preview="5M" >
                <input type="hidden" name="detail_img_hidden[{{$detailsIndex}}]" value="{{$detail->image}}" >
              </div>
            @endif
            @if($detail->template->description === '1')
              @php
                $editorString = randomStringLetters(10);
                $editorStrings[] = $editorString;
              @endphp
              <div class="col-12 my-2">
                <label>Omschrijving</label>
                <textarea name="detail_description[{{$detailsIndex}}]" id="{{$editorString}}" class="form-control detail-description" placeholder="Omschrijving" >{!! $detail->description !!}</textarea>
              </div>
            @endif
            @if($detail->template->price === "1")
              <div class="col-md-6 col-12 my-2">
                <label>Prijs</label>
                <input readonly type="number" step="0.01" value="{{$detail->price}}" name="detail_price[{{$detailsIndex}}]" id="detail-price-{{$detailsIndex}}" class="form-control detail-price" placeholder="Prijs" >
              </div>
            @endif
            @if($detail->template->adjustment === '1')
              <div class="col-md-6 col-12 my-2">
                <label>Commerciële aanpassing</label>
                <input type="number" step="0.01" value="{{$detail->adjustment}}" name="detail_adjustment[{{$detailsIndex}}]"  data-index="{{$detailsIndex}}" class="detail-price-element form-control detail-adjustment" placeholder="Omschrijving" >
              </div>
            @endif
            <div class="col-12">
              <hr>
            </div>
            <div class="col-12" >
              <div id="detail-content-{{$detailsIndex}}" class="row" >
                @foreach($detail->template->velden ?? [] as $row)
                  @php $name = "detail[".$detailsIndex."][".$row->keyword."]"; @endphp
                  @php $values = $detail->values @endphp

                  @if($row->type == "text")
                    <div class="col-12 my-2">
                      <label>{{$row->name}}</label>
                      <input name="{{$name}}" value="{{$values[$row->keyword]->value ?? ''}}" class="form-control mb-2" placeholder="{{$row->name}}" required/>
                    </div>
                  @elseif($row->type == "number")
                    <div class="col-12 my-2">
                      <label>{{$row->name}}</label>
                      <input name="{{$name}}" value="{{$values[$row->keyword]->value ?? ''}}" type="number" step="0.01" class="form-control mb-2" placeholder="{{$row->name}}" required/>
                    </div>
                  @elseif($row->type == "date")
                    <div class="col-12 my-2">
                      <label>{{$row->name}}</label>
                      <input name="{{$name}}" value="{{$values[$row->keyword]->value ?? ''}}" type="date" class="form-control mb-2" required/>
                    </div>
                  @elseif($row->type == "select")
                    <div class="col-md-12 mb-2">
                      <label>{{$row->name}}</label>
                      <select name="{{$name}}" class="form-select mb-2" required>
                        @foreach(json_decode($row->data) ?? [] as $option)
                          <option @if($option->value == $values[$row->keyword]->value ?? '') selected @endif value="{{$option->value}}" >{{$option->name}}</option>
                        @endforeach
                      </select>
                    </div>
                  @elseif($row->type == "select_edit")
                    <div class="col-md-12 my-2 select_edit-container">
                      <label>{{$row->name}}</label>
                      <input type="text" autocomplete="off" name="{{$name}}" class="select_edit-input form-select" value="{{$values[$row->keyword]->value ?? ''}}" placeholder="{{$row->name}}" >
                      <div class="select_edit-values" >
                        <div class="select_edit-box" >
                          @foreach(json_decode($row->data) ?? [] as $option)
                            <span class="select_edit-value" data-value="{{$option->value}}">{{$option->name}}</span>
                          @endforeach
                        </div>
                      </div>
                    </div>
                  @elseif($row->type == "prefill")
                    @php
                      $editorString = randomStringLetters(10);
                      $editorStrings[] = $editorString;
                    @endphp
                    <div class="col-md-12 mb-2">
                      <label>{{$row->name}}</label>
                      <textarea name="{{$name}}" class="form-control" id="{{$editorString}}" placeholder="{{$row->name}}" >{!! $values[$row->keyword]->value ?? '' !!}</textarea>
                    </div>
                  @endif
                @endforeach
              </div>
            </div>
            @if($detail->template->price === "1")
              <div class="col-12 text-right my-2">
                Totaal: €<span id="detail-price-preview-{{$detailsIndex}}" class="mx-1">{{$detail->price}}</span>
              </div>
            @endif
            <div class="col-12 text-right" >
              <div id="detail-confirm-{{$detailsIndex}}" >
                <a class="btn btn-success text-white" onclick="confirmDetail({{$detailsIndex}})" >@icon_confirm</a>
              </div>
              <input type="hidden" value="1" name="detail_confirmed[{{$detailsIndex}}]" >
              <input type="hidden" value="{{$detail->template->id}}" name="detail_template_id[{{$detailsIndex}}]" >
            </div>
          </div>
        </div>
        @php $detailsIndex++; @endphp
      @endforeach
    </section>

    {{--        Detail Templates--}}
    @if(count($template->details))
      <section class="my-5" >
        <div class="row justify-content-center">
          @foreach($template->details as $detailTemplate)
            <div class="col-lg-3 col-md-4 col-6 my-3" >
              <div class="d-block btn btn-inverse-primary" onclick="addDetail({{$detailTemplate->id}})" >
                <div class="d-flex justify-content-between align-items-center">
                  {{$detailTemplate->name}} @icon_plus
                </div>
              </div>
            </div>
          @endforeach
        </div>
      </section>
    @endif

    {{--        Keywords--}}
    <section class="append-loader" >
      <div class="card my-3 p-2">
        @foreach($template->keywords as $item)
          @php isset($item->data) ? $item->data = json_decode($item->data, true) : $item->data = null; @endphp
          <div class="keyword-container my-2 py-2" data-keyword="{{$item->keyword}}" >
            @php
              $excludeLabel = [
                'header' => true,
                'count_data_attribute' => true,
                'non_expected_values' => true,
              ]
            @endphp
            @if(!isset($excludeLabel[$item->type]))
              <label>{{$item->name}}@if($item->required == 1)*@endif</label>
            @endif
            <div class="d-flex">
              <div class="w-100" >
                @if($item->type == "text")
                  <input type="text" class="form-control keyword-input" data-keyword="{{$item->keyword}}" value="{{$item->value}}" placeholder="{{$item->name}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                @endif

                @if($item->type == "textarea")
                  @php $editorStrings[] = "editor-item-$item->id" @endphp
                  <textarea type="text" id="editor-item-{{$item->id}}" class="form-control keyword-input" data-keyword="{{$item->keyword}}" placeholder="{{$item->name}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>{!! $item->value !!}</textarea>
                @endif

                @if($item->type == "number")
                  <input type="number" class="form-control keyword-input" data-keyword="{{$item->keyword}}" value="{{$item->value}}" placeholder="{{$item->name}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                @endif

                @if($item->type == "date")
                  <input type="date" class="form-control keyword-input" data-keyword="{{$item->keyword}}" value="{{$item->value}}" placeholder="{{$item->name}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                @endif

                @if($item->type == "time")
                  <input type="time" class="form-control keyword-input" data-keyword="{{$item->keyword}}" value="{{$item->value}}" placeholder="{{$item->name}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                @endif

                @if($item->type == "image")
                  <div class="flex-align overflow-auto" >

                    <div class="flex-align" data-image-container="{{$item->keyword}}" >
                      @foreach($item->value ?? [] as $file)
                        <div style="background-image: url('{{url("/api/file/explorer/files/{$file->src}")}}')" class="mx-1 bg-img-center border-2 cursor-pointer rounded-5 w-px-150 h-px-150 position-relative" data-image-keyword >
                          <input type="hidden" name="items[{{$item->id}}][]" value="{{$file->id}}" >
                        </div>
                      @endforeach
                    </div>

                    <div class="border-2 border-dashed cursor-pointer flex-center font-size-15 hover-mark rounded-5 text-muted w-px-150 h-px-150 min-w-150" onclick="selectImage('{{$item->keyword}}')" >
                      @icon_cloud_upload
                    </div>

                  </div>
                @endif

                @if($item->type == "select")
                  <select id="select-{{$item->id}}" class="form-select keyword-input" data-keyword="{{$item->keyword}}"  name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                    <option value="">Selecteer optie</option>
                    @foreach($item->data['options'] ?? [] as $option)
                      <option @if($item->value == $option['value']) selected @endif value="{{$option['value']}}" >{{$option['name']}}</option>
                    @endforeach
                  </select>
                @endif

                @if($item->type == "select_edit")
                  <div class="select_edit-container" >
                    <input type="text" autocomplete="off" name="items[{{$item->id}}]" class="select_edit-input keyword-input form-select" data-keyword="{{$item->keyword}}" value="{{$item->value}}" placeholder="{{$item->name}}" @if($item->required) required @endif >
                    <div class="select_edit-values" >
                      <div class="select_edit-box" >
                        @foreach($item->data['options'] ?? [] as $option)
                          <span class="select_edit-value" data-value="{{$option['value']}}">{{$option['name']}}</span>
                        @endforeach
                      </div>
                    </div>
                  </div>
                @endif

                @if($item->type == "user_select")
                  <select id="select-{{$item->id}}" class="form-select keyword-input" data-keyword="{{$item->keyword}}"  name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                    <option value="">Selecteer medewerker</option>
                    @foreach($users ?? [] as $user)
                      <option @if($item->value == $user->id) selected @endif value="{{$user->id}}" >{{$user->name}} {{$user->lastname}}</option>
                    @endforeach
                  </select>
                @endif

                @if($item->type == "dataset")
                  <select class="form-select dataset-select keyword-input" data-keyword="{{$item->keyword}}" data-kw="{{$item->id}}" name="items[{{$item->id}}]" @if($item->required == 1) required @endif>
                    @if(!isset($item->value))
                      <option disabled selected >Dataset items</option>
                    @endif
                    @foreach(json_decode($datasets[$item->data['data']]->items) as $data)
                      <option @if($item->value == $data->value) selected @endif value="{{$data->value}}" >{{$data->name}}</option>
                    @endforeach
                  </select>
                  <div class="p-2 shadow rounded my-2 d-none" id="dataset-kw-{{$item->id}}"></div>
                @endif
                @if($item->type == "card_break")
              </div>
            </div>
          </div>
      </div>

      <div class="card my-3 p-2">
        <div class="keyword-container d-none" data-keyword="{{$item->keyword}}" >
          <div class="d-flex">
            <div class="w-100" >
              @endif

              @if($item->type == "header")
                <div style="font-size: {{$item->data['size']}}; color: {{$item->data['color']}}; font-weight: {{$item->data['weight']}}" >{!! $item->name !!}</div>
                <input type="hidden" name="items[{{$item->id}}]" value="header-filler">
              @endif

              @if($item->type == "custom_row")

                <div class="overflow-auto">
                  <table id="custom-rows-{{$item->keyword}}" class="table" >
                    <thead>
                    <tr>
                      @foreach($item->data['rows'] as $row)
                        <th class="min-w-150" >{{$row['name']}}</th>
                      @endforeach
                      <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach(json_decode($item->value ?? '[]') as $rows)
                      @php $string = randomStringLetters(); @endphp
                      <tr class="{{$string}}" >
                        @foreach($rows as $row)
                          <td>
                            @if($row->type == 'text')
                              <input type="text" class="form-control-custom" name="items[{{$item->id}}][{{$string}}][{{$row->keyword}}]" placeholder="{{$row->keyword}}" value="{{$row->value}}">
                            @elseif($row->type == 'date')
                              <input type="date" class="form-control-custom" name="items[{{$item->id}}][{{$string}}][{{$row->keyword}}]" value="{{$row->value}}" >
                            @elseif($row->type == 'select')
                              <select class="form-select" name="items[{{$item->id}}][{{$string}}][{{$row->keyword}}]">
                                @foreach($row->data->options ?? [] as $option)
                                  <option @if($option->value == $row->value) selected @endif value="{{$option->value}}">{{$option->name}}</option>
                                @endforeach
                              </select>
                            @endif

                          </td>
                        @endforeach
                        <td class="text-center"><a class="btn text-danger" onclick="deleteDiv('.{{$string}}')"><i class="fa fa-xmark m-0"></i></a></td>
                      </tr>
                    @endforeach
                    </tbody>
                  </table>
                </div>

                @if($item->data['multiple'])
                  <div class="my-2 text-center">
                    <a class="btn btn-inverse-primary" onclick="addCustomRow('{{$item->keyword}}')">@icon_plus</a>
                  </div>
                @endif
              @endif

              @if($item->type == "count_data_attribute")
                <div>
                  <span>{{$item->name}}</span>
                  <span class="count-data-attribute" data-item="{{$item->id}}">0</span>
                </div>
              @endif

              @if($item->type == "non_expected_values")
                <div class="non-expected-values-{{$item->id}}" ></div>
              @endif

              @if($item->type == "signature")
                <div>
                  <div class="text-center signature-item-{{$item->id}}" >
                    @if(isset($item->value))
                      <div class="bg-inverse-secondary border text-center rounded cursor-pointer" onclick="handtekening({{$item->id}})">
                        <img src="{{url("api/file/$item->value")}}" alt="" class="w-50 bg-white rounded ">
                      </div>
                    @else
                      <div class="cursor-pointer btn-inverse-secondary max-h-500 py-5" onclick="handtekening('{{$item->id}}')">Handtekening</div>
                    @endif
                  </div>
                  <input type="hidden" name="items[{{$item->id}}]" value="@if(isset($item->value))existing_{{$item->value}}@endif" >
                </div>
              @endif

              <div id="keyword-file-{{$item->id}}" class="file-container my-2 d-none" >
                <input type="file" name="img[{{$item->id}}]" @if(isset($item->file_src)) data-default-file="{{url("api/file/".$item->file_src)}}" @endif class="dropify" >
                <input type="hidden" class="img-hidden" name="img_hidden[{{$item->id}}]" value="{{$item->file_src}}" >
              </div>
              <div id="keyword-opmerking-{{$item->id}}" class="my-2 d-none" >
                <input type="text" name="opmerking[{{$item->id}}]" value="{{$item->opmerking_value}}" class="form-control" placeholder="Opmerking">
              </div>

            </div>
            <div>
              <nobr>
                @if($item->file && !$item->file_on_value)
                  <a class="btn btn-inverse-dark img-toggle-{{$item->id}} ml-1" onclick="toggleFile({{$item->id}})">@icon_img</a>
                @endif
                @if($item->opmerking && !$item->opmerking_on_value)
                  <a class="btn btn-inverse-dark opmerking-toggle-{{$item->id}} ml-1" onclick="toggleOpmerking({{$item->id}})">@icon_pencil</a>
                @endif
              </nobr>
            </div>
          </div>


        </div>
        @endforeach
      </div>
    </section>


    <section class="card p-2 my-2" preview-hide>
      <label>Bestanden</label>
      <div id="files">
        @foreach($checklist->files ?? [] as $file)
          @php $string = randomStringLetters(10); @endphp
          <div id="{{$string}}" class="my-1 d-flex justify-content-between">
            <div class="d-flex align-items-center">
              <a class="btn btn-inverse-primary" href="{{url("api/file/".$file->src)}}" target="_blank">
                <span class="mr-2" >@icon_download</span>
                {{$file->name}}
              </a>
            </div>
            <a class="btn btn-danger text-white align-self-center" onclick="deleteDiv('#{{$string}}')">@icon_trash</a>
            <input type="hidden" value="{{$file->src}}" name="old_files[]">
            <input type="hidden" value="{{$file->name}}" name="old_files_names[]">
          </div>
        @endforeach
      </div>
      <div class="my-3">
        <a onclick="addFile()" class="btn btn-primary text-white">@icon_plus</a>
        @if(hasModule("Bestanden"))
          <a onclick="openExplorer()" class="btn btn-dark text-white">@icon_file</a>
        @endif
      </div>
    </section>

    <section class="my-4 text-center" >
      <input type="submit" class="btn btn-success text-white" value="Opslaan">
      <input type="hidden" name="template" value="{{$template->id}}">
      @csrf
      @if(isset($checklist))
        <input type="hidden" name="checklist" value="{{$checklist->id}}">
      @endif

    </section>

  </form>
@endsection
@section('script')
  <script>
    var aanvragen = @json($aanvragen);
    var template = @json($template);
    var klanten = @json(getKlanten(['with' => 'locaties']));
    const preview = @json($preview ?? false);

    var detailIndex = @json($detailsIndex);
    var detailTemplates = @json($detailTemplates);

    var explorerFiles = @json($explorerFiles);
    var intervals = {
      explorerFile: null,
    }

    var _valueIfNull = {
      checked: false,
    }

    $(document).ready(function(){
      tippyInit();
      dropifyInit();
      keywordsInit();
      previewInit();

      const editorStrings = @json($editorStrings ?? []);
      confirmExistingDetails();

      editors(editorStrings);
      countDataAttribute();
      keywordFile();
      keywordOpmerking();
      verifyParentElement();
      nonExpectedValues()

      _inforSelectSearch.get('klant').onchange = klantSelect;

      if(localStorage.aanvraagId){
        selectAanvraag(localStorage.aanvraagId);
        localStorage.removeItem('aanvraagId');
      }
    })

    $(".dataset-select").change(function(){
      let id = $(this).attr('data-kw');
      if($(this).val()){
        let json = JSON.parse($(this).val());
        $("#dataset-kw-"+id).html('').removeClass('d-none');
        for(const n in json){
          $("#dataset-kw-"+id).append(
                  '<div>' +
                  '<h6 class="text-muted m-0" >'+n+'</h6>' +
                  '<h5>'+json[n]+'</h5>' +
                  '</div>'
          )
        }
      }

    });

    $(".btn-anders").click(function(){
      let item = $(this).attr('data-item');
      if($(this).hasClass('btn-primary')){
        $(this).removeClass('btn-primary').addClass('btn-danger');
        $("#select-"+item).prop('disabled', true).addClass('d-none');
        $("#input-"+item).prop('disabled', false).removeClass('d-none');
      }
      else{
        $(this).removeClass('btn-danger').addClass('btn-primary');
        $("#input-"+item).prop('disabled', true).addClass('d-none');
        $("#select-"+item).prop('disabled', false).removeClass('d-none');
      }
    });

    $(document).on('change', '.detail-price-element', function(){
      const index = $(this).attr('data-index');
      const total = calculateDetailPrice(index);
      fillDetailPrice(index, total)
    })

    $(document).on('change', '.keyword-input', function () {
      console.clear();
      verifyParentElement()
      countDataAttribute();
      keywordFile();
      keywordOpmerking();
      nonExpectedValues()
      copyKeywordValue($(this));
    });
    $(document).on('click', '.dropify-clear', function (){
      const container = findContainer('file-container', $(this));
      $(container).find('.img-hidden').val('')
    });

    $(document).on('mouseenter', '[data-image-keyword]', function(){
      $(this).append(`<div class="position-absolute w-100 h-100 flex-center bg-inverse-danger text-danger text-danger font-size-4" data-image-keyword-remove >@icon_close</div>`)
    });
    $(document).on('mouseleave', '[data-image-keyword]', function(){
      $('[data-image-keyword-remove]').remove();
    });
    $(document).on('click', '[data-image-keyword]', function(){
      $(this).remove();
    });

    async function klantSelect(id){
      let projectenSelect = _inforSelectSearch.get('project');
      $("#klanten-buttons").html('');

      if(!id){
        projectenSelect.clear();
        projectenSelect.disable();
        return
      }

      if(projectenSelect){
        projectenSelect.enable();

        const optionsContainer = projectenSelect.container.find('[data-infor-select-options-container]');
        optionsContainer.find('[data-infor-select-option]').remove();

        const { projecten } = await ajax('/api/projecten/get', {klant: id});

        if (projecten) {
          for(let project of projecten){
            const projectname = project.projectnaam+' - '+project.projectnr;
            projectenSelect.addValue({name: projectname, value: project.id})
          }
        }
      }


      if(aanvragen[id]){
        for(let anv of aanvragen[id]){
          $('#aanvragen').append(
                  '<div class="my-2">' +
                  '<label class="cursor-pointer my-1 d-flex align-items-center" ><input class="cursor-pointer form-check-input position-unset m-0 mr-2" id="aanvraag-checkbox-'+anv.id+'" type="checkbox" name="aanvragen[]" value="'+anv.id+'" > '+anv.aanvraagnummer+'</label>' +
                  '</div>'
          );
        }
      }

      const klant = klanten.find(klant  => klant.id == id);
      if(id && klant){
        $("#klanten-buttons").html(
                `<nobr>
          <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy" onclick="editKlant(${klant.id})" data-tippy-content="Klant wijzigen" >@icon_edit</a>
          <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy" onclick="showKlant(${klant.id})" data-tippy-content="Klant weergeven" >@icon_zoom</a>
        </nobr>`
        )

        $('#klant_locatie').remove();

        let locatiesHTML = '';
        for (let locatie of klant.locaties) {
          locatiesHTML += `
          <infor-select-option data-value="${locatie.id}"><div class="tippy" data-tippy-content="Adres: ${locatie.straat} ${locatie.huisnummer} ${locatie.toevoeging}">${locatie.straat} ${locatie.huisnummer} ${locatie.toevoeging}</div></infor-select-option>`;
        }

        $('#klant_locatie_container').append(`
          <infor-select-search name="klant_locatie" id="klant_locatie" class="form-control-custom" placeholder="Selecteer locatie">
            ${locatiesHTML}
          </infor-select-search>
        `);
      }else{
        $("#klanten-buttons").empty();
      }

      initInforSelectSearch();
      tippyInit()
    }

    function post(){

      for(let index = 0; index < detailIndex; index++){
        if($('input[name="detail_confirmed['+index+']"]').length && $('input[name="detail_confirmed['+index+']"]').val() !== '1'){
          notification('<div class="alert alert-danger">Bevestig alle details</div>');
          return false;
        }
      }

      if(!_valueIfNull.checked){
        for(const item of template.keywords){
          const value = $(`.keyword-input[data-keyword='${item.keyword}']`).val();
          if(value || !item.value_if_null){continue;}
          valueIfNull();
          return false;
        }
      }

      $('[type=submit]').addClass('d-none').after('@spinner_success');

      return true;
    }
    function keywordsInit(){
      for(const item of template.keywords){
        if(!item.file_on_value && item.file_src){ item.file_show = true; }
        if(!item.opmerking_on_value && item.opmerking_value){ item.opmerking_show = true; }
      }
    }
    function previewInit(){
      if(!preview){return;}

      $('input, select, textarea').prop('disabled', true);
      $('.non-preview').remove();
      $('[preview-hide]').hide();

    }

    function valueIfNull(){
      _valueIfNull.checked = true;

      let questions = ''
      for(const item of template.keywords){
        const value = $(`.keyword-input[data-keyword='${item.keyword}']`).val();
        if(value || !item.value_if_null){continue;}
        if(item.parent_keyword){
          const parent_value = $(`.keyword-container[data-keyword='${item.parent_keyword}']`).find('.keyword-input').val();
          if(parent_value != item.parent_value){continue;}
        }


        questions += `<tr>
                      <td>${item.name}</td>
                      <td>${item.value_if_null}</td>
                    </tr>`
      }

      if(!questions){
        $('#ck-form').find('[type=submit]').click();
        return;
      }

      let modal = `<div>Volgende vragen zijn niet beantwoord:</div>
                    <div class="overflow-auto" >
                      <table class="my-2 table" >
                        <thead>
                          <tr>
                            <th class="text-left" >Vraag</th>
                            <th class="text-left" >Standaard antwoord</th>
                          </tr>
                        </thead>
                        <tbody>
                            ${questions}
                        </tbody>
                      </table>
                    </div>
                    <div class="my-2" >Wilt u standaard antwoorden gebruiken:</div>`

      confirmModal({
        text: modal,
        large: true,
        btnText: 'Ja',
        btnRejectState: true,
        btnRejectText: 'Nee'
      }).then((response) => {
        if(!response.status){
          $('#ck-form').find('[type=submit]').click();
          return;
        }
        valueIfNullFill()
      })
    }
    function valueIfNullFill(){
      for(const item of template.keywords){
        const element = $(`.keyword-input[data-keyword='${item.keyword}']`);
        if(element.val() || !item.value_if_null){continue;}
        if(item.parent_keyword){
          const parent_value = $(`.keyword-container[data-keyword='${item.parent_keyword}']`).find('.keyword-input').val();
          if(parent_value != item.parent_value){continue;}
        }

        element.val(item.value_if_null);
      }
      $('#ck-form').find('[type=submit]').click();
    }

    function handtekening(id){
      getSignature()
              .then((response) => {
                if(!response.status){return}

                $(`.signature-item-${id}`).html(
                        `<div class="bg-inverse-secondary border text-center rounded cursor-pointer" onclick="handtekening(${id})">
              <img src="${response.src}" class="w-50 bg-white rounded ">
            </div>`
                )
                $(`[name='items[${id}]']`).val(response.src);
              })
    }
    function editors(strings){
      for(let string of strings){
        editorInit('#'+string);
      }
    }

    function countDataAttribute(){
      $('.count-data-attribute').each(function(){
        const id = $(this).attr('data-item');
        const item = template.keywords.find(key => key.id == id);
        const attr = item.data.attribute;
        let count = 0;

        keysLoop: for(const key of template.keywords){
          const value = $(`[name='items[${key.id}]']`).val();

          if(!key.data || !key.data.expected_value || !key.data[attr]){continue;}
          if(key.data[attr] != item.data.attribute_value){continue;}
          if(!value){continue;}
          for(const v of key.data.expected_value){
            if(value == v){continue keysLoop;}
          }

          count++;
        }

        $(this).html(count);

      });
    }
    function keywordFile(){
      for(const item of template.keywords){
        const value = $(`[name='items[${item.id}]']`).val();

        if(!Number(item.file)){continue;}

        if(item.file_on_value){
          if(value == item.file_on_value){$(`#keyword-file-${item.id}`).removeClass('d-none')}
          else{$(`#keyword-file-${item.id}`).addClass('d-none').find('.dropify-clear').click()}
          continue;
        }

        if(item.file_show){
          $(`#keyword-file-${item.id}`).removeClass('d-none');
          $(`.img-toggle-${item.id}`).removeClass('btn-inverse-dark').addClass('btn-dark')
        }
        else{
          $(`#keyword-file-${item.id}`).addClass('d-none').find('.dropify-clear').click();
          $(`.img-toggle-${item.id}`).addClass('btn-inverse-dark').removeClass('btn-dark')
        }

      }
    }
    function keywordOpmerking(){
      for(const item of template.keywords){
        const value = $(`[name='items[${item.id}]']`).val();

        if(!Number(item.opmerking)){continue;}

        if(item.opmerking_on_value){
          if(value == item.opmerking_on_value){
            $(`#keyword-opmerking-${item.id}`).removeClass('d-none');
          }
          else{
            $(`#keyword-opmerking-${item.id}`).addClass('d-none');
            $(`[name='opmerking[${item.id}]']`).val('');
          }
          continue;
        }

        if(item.opmerking_show){
          $(`#keyword-opmerking-${item.id}`).removeClass('d-none');
          $(`.opmerking-toggle-${item.id}`).removeClass('btn-inverse-dark').addClass('btn-dark')
        }
        else{
          $(`#keyword-opmerking-${item.id}`).addClass('d-none');
          $(`[name='opmerking[${item.id}]']`).val('');
          $(`.opmerking-toggle-${item.id}`).addClass('btn-inverse-dark').removeClass('btn-dark')
        }

      }
    }
    function verifyParentElement(){
      for(const item of template.keywords){
        if(!item.parent_keyword){continue;}

        const container = $(`.keyword-container[data-keyword='${item.keyword}']`);
        container.addClass('d-none');

        const value = $(`.keyword-container[data-keyword='${item.parent_keyword}']`).find('.keyword-input').val();
        if(value == item.parent_value){
          container.removeClass('d-none');
        }
      }
    }
    function nonExpectedValues(){
      for(const item of template.keywords){
        if(item.type != 'non_expected_values'){continue;}

        const container = $(`.non-expected-values-${item.id}`);
        container.empty();

        keysLoop: for(const row of template.keywords){

          const value = $(`.keyword-input[data-keyword='${row.keyword}']`).val();
          if(!value || !row.data || !row.data.expected_value){continue;}
          for(const v of row.data.expected_value){
            if(v == value){continue keysLoop;}
          }


          let attrs = '';
          if(item.data.additional_data){
            for(const attr of item.data.additional_data){
              attrs += `<div>
                          <span class="ml-1" >- ${attr.name}: ${row.data[attr.attribute]}</span>
                        </div>`
            }
          }

          container.append(
                  `<div class="hover-shadow p-2 rounded">
              <span>• ${row.name}</span>
                ${attrs}
            </div>`
          )

        }
      }
    }
    function copyKeywordValue(element){
      const value = element.val();
      const keyword = element.attr('data-keyword');

      for(const item of template.keywords){
        if(!item.copy_keyword_value || item.copy_keyword_value != keyword){continue;}

        $(`.keyword-input[data-keyword='${item.keyword}']`).val(value);
      }
    }
    function toggleFile(id){
      const item = template.keywords.find(key => key.id == id);
      item.file_show = !item.file_show;
      keywordFile();
    }
    function toggleOpmerking(id){
      const item = template.keywords.find(key => key.id == id);
      item.opmerking_show = !item.opmerking_show;
      keywordOpmerking();
    }

    function addCustomRow(keyword){
      const item = template.keywords.find(key => key.keyword == keyword);

      let tds = '';

      const string = randomString();
      for(const row of item.data.rows){
        if(row.type == 'text'){
          tds += `<td><input type="text" class="form-control-custom" name="items[${item.id}][${string}][${row.keyword}]" placeholder="${row.name}" ></td>`
        }
        else if(row.type == 'select'){
          let options = '';
          for(const option of row.data.options){options += `<option value='${option.value}' >${option.name}</option>`}
          tds += `<td><select class="form-select" name="items[${item.id}][${string}][${row.keyword}]" >${options}</select></td>`
        }
        else if(row.type == 'date'){
          tds += `<td><input type="date" class="form-control-custom" name="items[${item.id}][${string}][${row.keyword}]" ></td>`
        }
      }
      tds += `<td class='text-center'><a class='btn text-danger' onclick="deleteDiv('.${string}')" >@icon_close</a></td>`

      $(`#custom-rows-${item.keyword}`).append(`<tr class='${string}' >${tds}</tr>`);
    }

    function addDetail(id){
      let index = detailIndex;
      let value;
      const detail = detailTemplates[id];
      const editorStrings = {};

      let img ='';
      if(detail.img === '1'){
        img = '<div class="col-12 my-2">'+
                '<label>Afbeelding</label>'+
                '<input type="file" class="dropify detail-img" name="detail_img['+index+']"  data-allowed-file-extensions="jpg jpeg png" data-max-file-size-preview="5M" >'+
                '<input type="hidden" name="detail_img_hidden['+index+']">'+
                '</div>'
      }

      let description = '';
      if(detail.description === '1'){
        const editorString = randomString(10);
        editorStrings[editorString] = '';

        description = '<div class="col-12 my-2">'+
                '<label>Omschrijving</label>'+
                '<textarea name="detail_description['+index+']" rows="3" class="form-control detail-description" id="'+editorString+'" placeholder="Omschrijving" ></textarea>'+
                '</div>'
      }

      let price = '';
      let totalPreview = ''
      if(detail.price === '1'){
        price = '<div class="col-md-6 col-12 my-2">'+
                '<label>Prijs</label>'+
                '<input readonly type="number" step="0.01" name="detail_price['+index+']" value="0.00" id="detail-price-'+index+'" class="form-control detail-price" placeholder="Prijs" >'+
                '</div>';
        totalPreview = '<div class="col-12 text-right my-2">Totaal: €<span id="detail-price-preview-'+index+'" class="mx-1" >0.00</span></div>'
      }

      let adjustment = '';
      if(detail.adjustment === '1'){
        adjustment = '<div class="col-md-6 col-12 my-2">'+
                '<label>Commerciële aanpassing</label>'+
                '<input type="number" step="0.01" value="0.00" name="detail_adjustment['+index+']" data-index="'+index+'" class="detail-price-element form-control detail-adjustment" placeholder="Omschrijving" >'+
                '</div>'
      }

      $('#details-div').append(
              '<div class="my-4 p-2 card" id="detail-'+index+'" >'+
              '<div class="d-flex justify-content-between align-items-center" >' +
              '<h4 class="my-2" >#'+(index + 1)+' '+detail.name+'</h4>'+
              '<div id="detail-buttons-'+index+'" class="d-flex overflow-auto" >' +
              '<a class="btn btn-danger text-white mx-1 tippy" data-tippy-content="Verwijderen" onclick="removeDetail('+index+')" >@icon_trash</a>' +
              '</div>'+
              '</div>'+
              '<div id="detail-disable-'+index+'" class="row" >'+
              '<div class="col-md-6 col-12 my-2" >'+
              '<label>Titel*</label>'+
              '<input name="detail_title['+index+']" placeholder="Titel" class="form-control detail-title" required>'+
              '</div>'+
              img+
              description+
              price+
              adjustment+
              '<div class="col-12">'+
              '<hr>'+
              '</div>'+
              '<div class="col-12" ><div id="detail-content-'+index+'" class="row" ></div></div>'+
              totalPreview+
              '<div class="col-12 text-right" >' +
              '<div id="detail-confirm-'+index+'" >' +
              '<a class="btn btn-success text-white tippy" data-tippy-content="Bevestigen" onclick="confirmDetail('+index+')" >@icon_confirm</a>'+
              '</div>'+
              '<input type="hidden" value="0" name="detail_confirmed['+index+']" >'+
              '<input type="hidden" value="'+detail.id+'" name="detail_template_id['+index+']" >'+
              '</div>'+
              '</div>'+
              '</div>'
      );

      for(let row of detail.velden){
        let div;
        const name = 'detail['+index+']['+row.keyword+']'
        value = row.value ? row.value : '';

        if(row.type == "text"){
          div = '<div class="col-12 my-2">'+
                  '<label>'+row.name+'*</label>'+
                  '<input name="'+name+'" value="'+value+'" class="form-control mb-2" placeholder="'+row.name+'" required/>'+
                  '</div>'
        }
        else if(row.type == 'number'){
          div = '<div class="col-12 my-2">'+
                  '<label>'+row.name+'*</label>'+
                  '<input name="'+name+'" value="'+value+'" type="number" step="0.01" class="form-control mb-2" placeholder="'+row.name+'" required/>'+
                  '</div>'
        }
        else if(row.type == 'date'){
          div = '<div class="col-12 my-2">'+
                  '<label>'+row.name+'*</label>'+
                  '<input name="'+name+'" value="'+value+'" type="date" class="form-control mb-2"  required/>'+
                  '</div>'
        }
        else if(row.type == 'select'){
          let options = '';
          for(let option of JSON.parse(row.data)){
            let selected = '';
            if(option.value == value){
              selected = 'selected'
            }
            options += '<option '+selected+' value="'+option.value+'" >'+option.name+'</option>';
          }
          div = '<div class="col-md-12 mb-2">'+
                  '<label>'+row.name+'*</label>'+
                  '<select name="'+name+'" class="form-select mb-2" required>'+
                  options+
                  '</select>'+
                  '</div>'
        }
        else if(row.type == 'select_edit'){
          let options = '';
          for(let option of JSON.parse(row.data)){
            options += '<span class="select_edit-value" data-value="'+option.value+'">'+option.name+'</span>';
          }
          div = '<div class="col-md-12 my-2 select_edit-container">' +
                  '<label>'+row.name+'</label>' +
                  '<input type="text" autocomplete="off" name="'+name+'" class="select_edit-input form-select" value="'+value+'" placeholder="'+row.name+'" >' +
                  '<div class="select_edit-values" >' +
                  '<div class="select_edit-box" >' +
                  options +
                  '</div>' +
                  '</div>' +
                  '</div>'
        }
        else if(row.type == 'prefill'){
          const editorString = randomString(10);
          editorStrings[editorString] = value;

          div = '<div class="col-md-12 mb-2">'+
                  '<label>'+row.name+'*</label>'+
                  '<textarea name="'+name+'" class="form-control" id="'+editorString+'" placeholder="Omschrijving" >'+value+'</textarea>'+
                  '</div>'

        }

        $('#detail-content-'+index).append(div);
      }


      for(let string in editorStrings){
        editorInit('#'+string, editorStrings[string]);
      }

      detailIndex++;
      dropifyInit();
      tippyInit();
    }
    function confirmDetail(index){
      const div = $('#detail-'+index);

      let confirm = true;
      let scrollTo;

      $(div).find('input').each(function(){
        $(this).removeClass('border-danger');

        if($(this).prop('required') && !$(this).val()){
          scrollTo = $(this);
          $(this).addClass('border-danger');
          confirm = false;
        }
      })
      $(div).find('select').each(function(){
        $(this).removeClass('border-danger');

        if($(this).prop('required') && !$(this).val()){
          scrollTo = $(this);
          $(this).addClass('border-danger');
          confirm = false;
        }
      })
      $(div).find('textarea').each(function(){
        $(this).removeClass('border-danger');

        if($(this).prop('required') && !$(this).val()){
          scrollTo = $(this);
          $(this).addClass('border-danger');
          confirm = false;
        }
      })

      if(!confirm){
        let offset = $(scrollTo).offset().top;
        window.scroll(0, offset - 200);
        return false;
      }



      $(div).addClass(['border-success', 'border-bottom-bold']);
      $("input[name='detail_confirmed["+index+"]']").val('1');
      $('#detail-disable-'+index).addClass('d-none')
      $('#detail-confirm-'+index).html('')
      $('#detail-buttons-'+index).html(
              '<a class="btn btn-danger text-white mx-1 tippy" data-tippy-content="Verwijderen" onclick="removeDetail('+index+')" >@icon_trash</a>' +
              '<a class="btn btn-warning text-white mx-1 tippy" data-tippy-content="Wijzigen" onclick="redoDetail('+index+')" >@icon_redo</a>'
      )
    }
    function redoDetail(index){
      $('#detail-'+index).removeClass(['border-success', 'border-bottom-bold'])
      $("input[name='detail_confirmed["+index+"]']").val('0');
      $('#detail-disable-'+index).removeClass('d-none');
      $('#detail-confirm-'+index).html('<a class="btn btn-success text-white tippy" data-tippy-content="Bevestigen" onclick="confirmDetail('+index+')" >@icon_confirm</a>')
      $('#detail-buttons-'+index).html('<a class="btn btn-danger text-white mx-2 tippy" data-tippy-content="Verwijderen" onclick="removeDetail('+index+')" >@icon_trash</a>')
      tippyInit();
    }
    function removeDetail(index){
      $("#detail-"+index).remove();
    }
    function calculateDetailPrice(index){
      let div = $('#detail-'+index);
      let total = Number($(div).find('.detail-adjustment').length ? $(div).find('.detail-adjustment').val() : 0);

      return Number(total);
    }
    function fillDetailPrice(index, price){
      $('#detail-price-'+index).val(price.toFixed(2));
      $('#detail-price-preview-'+index).html(price.toFixed(2));
    }
    function confirmExistingDetails(){
      for(let index = 0; index < detailIndex; index++){
        const div = $('#detail-'+index);
        const total = calculateDetailPrice(index);

        $(div).addClass(['border-success', 'border-bottom-bold']);
        $("input[name='detail_confirmed["+index+"]']").val('1');
        $('#detail-disable-'+index).addClass('d-none')
        $('#detail-confirm-'+index).html('')
        $('#detail-buttons-'+index).html(
                '<a class="btn btn-danger text-white mx-1 tippy" data-tippy-content="Verwijderen" onclick="removeDetail('+index+')" >@icon_trash</a>' +
                '<a class="btn btn-warning text-white mx-1 tippy" data-tippy-content="Wijzigen" onclick="redoDetail('+index+')" >@icon_redo</a>'
        );

        fillDetailPrice(index, total);
        tippyInit();
      }
    }

    function selectAanvraag(id){
      let aanvraag;
      for(const klantId in aanvragen){
        for(const anv of aanvragen[klantId]){
          if(anv.id == id){aanvraag = anv;}
        }
      }

      if(!aanvraag || !aanvraag.klant_id){return false;}

      _inforSelectSearch.get('klant').setValue(aanvraag.klant_id);
      $('#aanvraag-checkbox-'+id).prop('checked', true);

    }

    function addFile(){
      $("#files").append('<input class="my-2 d-block" type="file" name="bijlage[]">');
    }
    async function openExplorer(){
      clearInterval(intervals.explorerFile)
      const { status, file } = await getExplorerFile();
      if(!status){ return; }

      const string = randomString(15);
      $('#files').append(
              '<div id="file'+string+'" class="my-1 d-flex justify-content-between" >' +
              '<div class="d-flex align-items-center" >' +
              '<img height="50" src="{{url('client/public/img/explorer/files')}}/'+file.icon+'" >' +
              '<h4 class="my-0 mx-2" >'+file.name+'</h4>'+
              '</div>'+
              '<a class="btn btn-danger text-white align-self-center" onclick="deleteDiv(\'#file'+string+'\')" >@icon_trash</a>' +
              '<input type="hidden" value="'+file.src+'" name="explorer_files[]"> ' +
              '<input type="hidden" value="'+file.name+'" name="explorer_files_names[]"> ' +
              '</div>'
      );
    }

    //keyword: image
    async function selectImage(keyword){
      const item = template.keywords.find(item => item.keyword == keyword);
      const multiple = item.data?.multiple || false

      const { status, file, files } = await uploadExplorerFile({
        multiple,
        path: '/Checklists/temp'
      });

      if(!status){ return; }

      const parse = file => {
        const { icon, src, id } = file;
        if(icon !== 'img.png'){ return ''; }

        return `
          <div style="background-image: url('${url}/api/file/explorer/files/${src}')" class="mx-1 bg-img-center border-2 cursor-pointer rounded-5 w-px-150 h-px-150 position-relative" data-image-keyword >
            <input type="hidden" name="items[${item.id}][]" value="${id}" >
          </div>
        `;
      }
      const container = $(`[data-image-container=${keyword}]`);

      multiple
        ? files.forEach( file => { container.append( parse(file) ) } )
        : container.html( parse(file) )
    }

  </script>
@endsection
