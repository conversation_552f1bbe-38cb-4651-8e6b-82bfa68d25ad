@extends('layouts.app')
@section('title', 'Inkoopbonnen')
@section('content')

  @php
    $hide_btw = getSettingValue('inkoopbonnen_hide_btw') == 'ja';
    $prefill_offertes = getSettingValue('inkoopbonnen_offertes_prefill', 'nee') == 'ja';
    $default_report = Carbon()->now()->addDays(getSettingValue("inkoopbonnen_reporting_date_default") ?? 0)->format("Y-m-d");

    $bc_accounts = businessCentralLocal()->getStoredAccounts('75');
  @endphp

  <form method="post" class="append-loader my-2" data-main-form >

    @isset($inkoopbon)
      <alert-primary class="my-2" >Inkoopbon: <b>{{$inkoopbon->bonnummer}}</b></alert-primary>
    @endif

    <div class="card hover-shadow my-2 p-2">
      <div class="row">
        <div class="col-md-6 col-12 my-2">
          <label>Project</label>
          <infor-search
            id="project"
            name="project"
            placeholder="Selecteer project"
            class="form-control-custom"
            data-content="projectnr"
            data-sub-content="projectnaam"
            data-api="api/projecten/search"
            data-errors="handleCatchError"
            @if(isset($inkoopbon->project))
              data-display-prefill="{{$inkoopbon->project->projectnr}}"
              data-value-prefill="{{$inkoopbon->project->id}}"
            @endif
          ></infor-search>
        </div>
        <div class="col-md-6 col-12 my-2">
          <label>Leverancier</label>
          <infor-search
            id="leverancier"
            name="leverancier"
            placeholder="Selecteer leverancier"
            class="form-control-custom"
            data-content="naam"
            data-api="api/leveranciers/search?active=1"
            data-errors="handleCatchError"
            @if(isset($inkoopbon->leverancier))
              data-display-prefill="{{$inkoopbon->leverancier->naam}}"
              data-value-prefill="{{$inkoopbon->leverancier->id}}"
            @endif
          ></infor-search>
        </div>
        <div class="col-md-6 col-12 select_search-container my-2">
          <label>BV</label>
          <select name="bv" class="form-select">
            @foreach(getBvs() as $bv)
              <option @if(($inkoopbon->bv ?? null) == $bv->id) selected @endif value="{{$bv->id}}">{{$bv->name}}</option>
            @endforeach
          </select>
        </div>
        @if(businessCentralLocal()->connected)
          <div class="col-md-6 col-12 my-2">
            <label>Kostendrager*</label>
            <infor-select-search class="form-control-custom" id="bc_kostendrager" name="bc_kostendrager" placeholder="Selecteer kostendrager">
              @foreach(businessCentralLocal()->getStoredDimensions('KOSTENDRAGER') as $kostendrager)
                <infor-select-option @if(($inkoopbon->business_central_kostendrager ?? null) == $kostendrager->code) data-selected @endif data-value="{{$kostendrager->code}}" data-name="{{$kostendrager->name}}" > <span class="badge badge-inverse-primary" >{{$kostendrager->code}}</span> {{$kostendrager->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
          </div>
        @endif
        @if(zenvoicesConnected())
          <div class="col-md-6 col-12 my-2">
            <label>Kostendrager*</label>
            <infor-select-search class="form-control-custom" id="zenvoices_kostendrager" name="zenvoices_kostendrager" placeholder="Selecteer kostendrager">
              @foreach(zenvoicesLocal()->getStoredCostUnits() as $kostendrager)
                <infor-select-option @if(($inkoopbon->zenvoices_kostendrager ?? null) == $kostendrager->id) data-selected @endif data-value="{{$kostendrager->id}}" data-name="{{$kostendrager->naam}}" > <span class="badge badge-inverse-primary" >{{$kostendrager->code}}</span> {{$kostendrager->naam}}</infor-select-option>
              @endforeach
            </infor-select-search>
          </div>
        @endif
        @if(getSettingCheckbox('inkoopbonnen_vestiging_select'))
          <div class="col-md-6 col-12 my-2">
            <label>Vestiging*</label>
            <infor-select-search class="form-control-custom" id="vestiging" name="vestiging" placeholder="Selecteer vestiging">
              @foreach(getVestigingen() as $vestiging)
                <infor-select-option @if(($inkoopbon->vestiging_id ?? null) == $vestiging->id) data-selected @endif data-value="{{$vestiging->id}}" data-name="{{$vestiging->straat ?? ''}} {{$vestiging->huisnummer ?? ''}}{{$vestigingtoeving ?? ''}}" ><span class="badge badge-inverse-primary" >{{$vestiging->plaats}}</span> {{$vestiging->straat ?? ''}} {{$vestiging->huisnummer ?? ''}}{{$vestigingtoeving ?? ''}}</infor-select-option>
              @endforeach
            </infor-select-search>
          </div>
        @endif
        @if($prefill_offertes)
          <div id="offerte-container" class="col-md-6 col-12 my-2">
            @if(isset($inkoopbon) && !$inkoopbon->offertes->isEmpty())
            <label>Offertes</label>
              <infor-select-multiple id="offertes" name="offertes" class="form-control-custom" placeholder="Selecteer offertes">
                @foreach($inkoopbon->project->allOffertes as $offerte)
                  <infor-select-option data-name="{{$offerte->offertenummer}}" data-value="{{$offerte->id}}"  @if(isset($inkoopbon) && $inkoopbon->offertes->contains('id', $offerte->id)) data-selected @endif>{{$offerte->offertenummer}} - {{$offerte->naam}}</infor-select-option>
                @endforeach
              </infor-select-multiple>
            @endif
          </div>
        @endif
      </div>
    </div>

    <div class="card hover-shadow my-2 p-2">
      @php
        $custom = json_decode(getSettingValue('inkoopbonnen_custom_rows') ?? '{}');
        $eenheden = resetIndex(getSettingJson('inkoopbonnen_eenheden'));
      @endphp
      <div class="overflow-auto" data-overflow-x-only  >
        <table id="inkoopbonTable" class="table" >
          <thead>
          <tr>
            <th class="w-0" ></th>
            @foreach($custom as $row)
              <th class="min-w-150" >{{$row->name ?? ''}}</th>
            @endforeach
            @if(getSettingValue('inkoopbonnen_reporting_date') == 'aan')
              <th>Rapportagedatum</th>
            @endif
            @if(countObject(json_decode(getSettingValue('leveranciers_tarieven_categories') ?? '{}')))
              <th class="min-w-300" >Categorie</th>
            @endif
            @if(businessCentralLocal()->connected)
              <th class="min-w-300" >Grootboek</th>
            @endif
              <th class="w-100 min-w-300" >Omschrijving</th>
            @if($prefill_offertes)
              <th class="w-0 min-w-125" >Reeds besteld</th>
              <th class="w-0 min-w-125" >Totaal te bestellen</th>
              <th class="w-0 min-w-125" >Nog te bestellen</th>
              <th class="w-0 min-w-125" >Aantal</th>
            @else
              <th class="w-0 min-w-125" >Aantal</th>
            @endif
            @if(!empty($eenheden))
              <th class="w-0 min-w-125" >Eenheden</th>
            @endif
              <th class="w-0 min-w-125" >Bedrag</th>
            @if(!$hide_btw)
              <th class="w-0 min-w-125" >BTW %</th>
              <th class="w-0 min-w-50" >Incl. BTW</th>
            @endif
          </tr>
          </thead>
          <tbody data-rows-container>
          @foreach($inkoopbon->regels ?? [] as $regel)
            @php $string = randomString(); @endphp
            <tr class="row-container {{$string}} @isset($regel->detail) offerte-tr-{{$regel->detail->offerte_id}} @endisset">
              <td>
                <div class='btns-container d-flex mx--1'>
                  <a class="btn btn-sm btn-inverse-danger w-100 mx-1 tippy" data-tippy-content="Regel verwijderen" onclick="deleteDiv('.{{$string}}')">@icon_close</a>
                </div>
                <input type="hidden" name="trade_item_id[{{$string}}]" value="{{ $regel->tweeba_trade_item ?? null }}">
                <input type="hidden" name="tweeba_product_id[{{$string}}]" value="{{ $regel->tweeba_product_id ?? null }}">
              </td>

              @foreach($custom as $row)
                @php $value = find('keyword', $row->keyword, $regel->custom); @endphp
                <td>
                  @if($row->type == 'text' || $row->type == 'number')
                    <input type="{{$row->type}}" class="form-control-custom row-{{$row->keyword}}" placeholder="{{$row->name}}" name="custom[{{$string}}][{{$row->keyword}}]" value="{{$value->value ?? ''}}">
                  @endif
                </td>
              @endforeach

              @if(getSettingValue('inkoopbonnen_reporting_date') == 'aan')
                <td>
                  <input type="date" class="form-control-custom" name="reporting_date_row[{{$string}}]" value="{{$regel->reporting_date ?? $default_report}}">
                </td>
              @endif

              @if(countObject(json_decode(getSettingValue('leveranciers_tarieven_categories') ?? '{}')))
                <td class="select_group-container row-categorie-container">
                  <input type="hidden" name="categorie[{{$string}}]" data-placeholder="Categorie" data-input-prefill="{{$regel->categorie}}" value="{{$regel->categorie}}" class="select_group-hidden-input row-categorie">
                  <div class="select_group-values">
                    <div class="select_group-box font-size-1">
                      @foreach(json_decode(getSettingValue('leveranciers_tarieven_categories') ?? '{}') as $groep)
                        @foreach($groep->sub ?? [] as $row)
                          <span class='select_group-value' data-group="{{$groep->name ?? ''}}" data-value='{{$row->omschrijving ?? ''}}' data-name='{{$row->omschrijving ?? ''}}'>{{$row->omschrijving ?? ''}}</span>
                        @endforeach
                      @endforeach
                    </div>
                  </div>
                </td>
              @endif

              @if(businessCentralLocal()->connected)
                <td>
                  <infor-select-search id="bc_account_{{$string}}" name="bc_account[{{$string}}]" placeholder="Selecteer grootboeknummer" class="form-control-custom bc_account">
                    @foreach($bc_accounts as $account)
                      <infor-select-option @if($account->number == $regel->business_central_account) data-selected @endif data-value="{{$account->number}}" data-name="{{$account->number}} | {{$account->name}}">
                        <span class="badge badge-inverse-primary">{{$account->number}}</span> {{$account->name}}
                      </infor-select-option>
                    @endforeach
                  </infor-select-search>
                </td>
              @endif

              <td class="select_edit-container">
                <input type="text" autocomplete="off" name="naam[{{$string}}]" value="{{$regel->naam}}" class="select_edit-input form-select row-naam" data-tarief-select placeholder="Omschrijving">
                <div class="select_edit-values font-size-1">
                  <div class="select_edit-box" data-tarieven-values></div>
                </div>
              </td>
              @if($prefill_offertes)
                <td>
                  <input type="number" disabled class="form-control-custom row-reeds-besteld" placeholder="0" value="{{ $regel->ingekochtAantal }}">
                </td>
                <td>
                  <input type="number" disabled class="form-control-custom row-totaal-te-bestellen" placeholder="0" value="{{ $regel->totBestellen }}">
                </td>
                <td>
                  <input type="number" disabled step="0.01" min="0" class="form-control-custom row-nog-te-bestellend" placeholder="0" value="{{ $regel->totBestellen - $regel->ingekochtAantal }}">
                </td>
                <td>
                  <input type="number" step="0.01" min="0" class="form-control-custom row-aantal" placeholder="Aantal" name="aantal[{{$string}}]" value="{{ $regel->aantal }}">
                </td>
                  <input type="hidden" name="detail_value_dataset_id[{{$string}}]" value="{{ $regel->detail_value_dataset_id }}">
                  <input type="hidden" name="detail_id[{{$string}}]" value="{{ $regel->detail_id  }}">
              @else
                <td>
                  <input type="number" step="0.01" min="0" class="form-control-custom row-aantal" placeholder="Aantal" name="aantal[{{$string}}]" value="{{ $regel->aantal ?? 0}}">
                </td>
              @endif
              @if(!empty($eenheden))
                <td class="select_edit-container">
                  <input type="text" autocomplete="off" name="eenheid[{{$string}}]" value="{{$regel->eenheid}}" class="select_edit-input form-select row-eenheid" placeholder="Eenheid">
                  <div class="select_edit-values font-size-1">
                    @foreach($eenheden as $eenheid)
                      <div class="select_edit-box"><span class="select_edit-value" data-value="{{$eenheid['eenheid']}}" >{{$eenheid['eenheid']}}</span></div>
                    @endforeach
                  </div>
                </td>
              @endif
              <td>
                <input type="number" step="0.01" class="form-control-custom row-prijs" placeholder="Bedrag" name="prijs[{{$string}}]" value="{{ $regel->prijs }}">
              </td>

              @if(!$hide_btw)
                <td>
                  <div class="select_edit-container">
                    <input type="text" autocomplete="off" name="btw[{{$string}}]" value="{{$regel->btw}}" class="select_edit-input form-select row-btw" placeholder="BTW %">
                    <div class="select_edit-values">
                      <div class="select_edit-box">
                        @foreach(json_decode(getSettingValue('factuur_btw_percs') ?? '[]') as $perc)
                          <span class="select_edit-value" data-value="{{$perc}}">{{$perc}}%</span>
                        @endforeach
                      </div>
                    </div>
                  </div>
                </td>
                <td class="text-center">
                  <input @if($regel->incl === '1') checked @endif name="incl[{{$string}}]" class="form-check-custom row-incl" type="checkbox">
                </td>
              @endif
            </tr>
          @endforeach
          </tbody>
          <tbody id="addRowBtn">
            <tr>
              @if(tweebaLocal()->connected)
                <td colspan="100%" class="text-right border-0" > <a class="btn btn-inverse-primary m-1" onclick="addTweeBaProduct()" > 2BA Product @icon_plus </a>   <a class="btn btn-primary text-white" onclick="addRow()" >Regel toevoegen @icon_plus</a></td>
              @else
                <td colspan="100%" class="text-right border-0" > <a class="btn btn-primary text-white" onclick="addRow()" >Regel toevoegen @icon_plus</a></td>
              @endif
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="flex-between my-2">
      <div data-flex-between-filler class="w-33" ></div>
      <div class="w-33 text-center" >
        <input type="submit" class="btn btn-success" value="Opslaan" >
        @csrf
        @isset($inkoopbon)
          <input type="hidden" name="inkoopbon" value="{{$inkoopbon->id}}" >
        @endisset
      </div>
      <div class="w-33 d-flex flex-column align-items-end">
          <div class="border-0 card flex-row form-control-custom hover-shadow my-1 w-auto d-flex">
            <div class="min-w-150" >Totaal excl. BTW:</div>
            <div class="form-control-icon-divider mx-2" ></div>
            <div class="mx-2">€</div>
            <div class="min-w-75 text-right" data-tot-excl >{{number_format((isset($inkoopbon) ? $inkoopbon->totaal()->excl : 0), 2, ',', '.')}}</div>
          </div>
          <div class="border-0 card flex-row form-control-custom hover-shadow my-1 w-auto {{$hide_btw ? 'd-none' : 'd-flex'}}">
            <div class="min-w-150" >BTW:</div>
            <div class="form-control-icon-divider mx-2" ></div>
            <div class="mx-2">€</div>
            <div class="min-w-75 text-right" data-tot-btw >{{number_format((isset($inkoopbon) ? $inkoopbon->totaal()->btw : 0), 2, ',', '.')}}</div>
          </div>
          <div class="border-0 card flex-row form-control-custom hover-shadow my-1 w-auto {{$hide_btw ? 'd-none' : 'd-flex'}}">
            <div class="min-w-150" >Totaal incl. BTW:</div>
            <div class="form-control-icon-divider mx-2" ></div>
            <div class="mx-2">€</div>
            <div class="min-w-75 text-right" data-tot-incl >{{number_format((isset($inkoopbon) ? $inkoopbon->totaal()->incl : 0), 2, ',', '.')}}</div>
          </div>
        </div>
    </div>

  </form>

@endsection
@section('script')
  <script>

    const inkoopbon = @json($inkoopbon ?? null);
    const utilities = {};

    const settings = {
      custom: @json($custom),
      eenheden: @json($eenheden),
      hide_btw: @json(getSettingValue('inkoopbonnen_hide_btw') == 'ja'),
      prefill_offertes: @json($prefill_offertes),
      btwPerc: @json(getSettingJson('factuur_btw_percs') ?? []),
      nul_euro_niet_toestaan: @json(getSettingCheckbox('inkoopbonnen_nul_euro_bonnen_niet_toestaan')),
      negatieve_bedragen_niet_toestaan: @json(getSettingCheckbox('inkoopbonnen_negatieve_bedragen_niet_toestaan')),
      vestiging_select: @json(getSettingCheckbox('inkoopbonnen_vestiging_select'))
    }

    const _this = {
      default_report: @json($default_report),
      tarieven: [],
      leverancier: null,
      project: null,
      offertes: @json($inkoopbon->project->allOffertes ?? null),
      selectedOffertes: @json($inkoopbon->offertes ?? null)
    }
    const _bc = {
      instance: new BusinessCentral(),
      settings: @json(getSettingJson('business_central_settings', '{}')),
      accounts: @json($bc_accounts),
    }
    const _zenvoices = {
      instance: new Zenvoices(),
    }

    $(document).ready(function(){
      utilitiesInit();
      editInit();
      previewInit();

      _inforSearch.get('project').onchange = selectProject;
      _inforSearch.get('leverancier').onchange = selectLeverancier;
    });

    $(document).on('keyup change', '[data-rows-container] input', function(){

      let _incl = 0;
      let _excl = 0;
      let _btw = 0;

      $('[data-rows-container]').find('tr').each(function(){
        const tr = $(this);

        const aantal = Number(tr.find('.row-aantal').val() ?? 0);
        const prijs = Number(tr.find('.row-prijs').val() ?? 0);
        const btw = Number(tr.find('.row-btw').val() ?? 0);
        const incl = tr.find('.row-incl').prop('checked');
        const tot = aantal * prijs;

        if(incl){
          const totEx = tot / ( 100 + btw ) * 100

          _incl += tot;
          _excl += totEx;
          _btw  += tot - totEx;
        }
        else{
          const totIn = tot * ( 1 + (btw / 100) )

          _incl += totIn;
          _excl += tot;
          _btw  += totIn - tot;
        }

      });

      $('[data-tot-excl]').html(toPrice(_excl.toFixed(2)));
      $('[data-tot-incl]').html(toPrice(_incl.toFixed(2)));
      $('[data-tot-btw]').html(toPrice(_btw.toFixed(2)));

    })
    $(document).on('change', '.row-naam', function(){
      const container = findContainer('row-container', this);
      const value = this.value;

      const tarief = _this.tarieven.find(row => row.naam == value);
      if(!tarief){ return }
      //prefill prijs
      container.find('.row-prijs').val(tarief.prijs ?? 0).trigger('change');
      //prefill bc_account
      _inforSelectSearch.get(container.find('.bc_account').attr('id')).setValue(tarief.bc_account ?? '');
      //prefill eenheid
      container.find('.unit-select').val(tarief.eenheid ?? '');

      container.find('.row-categorie-container').find('.select_group-hidden-input, .select_group-input').val(tarief.categorie ?? '');

    });
    $(document).on('submit', '[data-main-form]', function(){

      if(!_inforSearch.get('project').getValue().value){
        notification('Selecteer project!', 'danger');
        resetMainForm();
        return false;
      }

      if(!_inforSearch.get('leverancier').getValue().value){
        notification('Selecteer leverancier!', 'danger');
        resetMainForm();
        return false;
      }

      if(_bc.instance.connected){
        if(!_inforSelectSearch.get('bc_kostendrager').getValue().value){
          notification('Selecteer kostendrager!', 'danger');
          resetMainForm();
          return false;
        }
      }

      if(_zenvoices.instance.connected){
        if(!_inforSelectSearch.get('zenvoices_kostendrager').getValue().value){
          notification('Selecteer kostendrager!', 'danger');
          resetMainForm();
          return false;
        }
      }


      if (settings.negatieve_bedragen_niet_toestaan) {
        let prijzen = $('.row-prijs');

        let hasNegative = false;

        prijzen.each(function () {
          let prijs = parseFloat($(this).val());

          if (prijs < 0) {
            hasNegative = true;
          }
        });

        if (hasNegative) {
          notification('Bedrag mag niet negatief zijn!', 'danger');
          resetMainForm();
          return false;
        }
      }

      if(settings.vestiging_select){
        if(!_inforSelectSearch.get('vestiging').getValue().value){
          notification('Selecteer een vestiging!', 'danger');
          resetMainForm();
          return false;
        }
      }


      if(settings.nul_euro_niet_toestaan && $('[data-tot-excl]').html() == '0,00'){
        notification('Bedrag mag niet 0 zijn!', 'danger');
        resetMainForm();
        return false;
      }

      return true;
    });
    $(document).on('change', '.row-categorie', function(){
      const tr = findContainerByTag('tr', this);
      const categorie = $(this).val();

      //Business Central
      const bc = {
        perc: 0,
        value: 0,
      }
      const bc_account = findContainerByTag('infor-select-search', tr.find('.bc_account'));
      const bc_account_instance = _inforSelectSearch.get(bc_account.attr('id'));
      bc_account_instance.options().each(function(){
        const perc = compareStrings(
          $(this).attr('data-name').toLowerCase(),
          categorie.toLowerCase(),
        );

        if(perc > bc.perc){
          bc.perc = perc;
          bc.value = $(this).attr('data-value')
        }
      })
      if(bc.value){
        bc_account_instance.setValue(bc.value);
      }

    });

    //2BA
    $(document).on('input', '[data-tweeba-product-search-name]', searchTweebaProducts);

    function utilitiesInit(){
      const percs = @json(json_decode(getSettingValue("factuur_btw_percs") ?? '[]'));
      const standaard = @json(getSettingValue('factuur_btw_perc') ?? 21);
      let options = '';

      for(const perc of percs){
        options += `<span class="select_edit-value" data-value="${perc}">${perc}%</span>`
      }
      utilities.select = (string) => {
        return `<div class="select_edit-container">
                  <input type="text" autocomplete="off" name="btw[${string}]" value="${standaard}" class="select_edit-input form-select row-btw" placeholder="BTW %">
                  <div class="select_edit-values">
                    <div class="select_edit-box">${options}</div>
                  </div>
                </div>`
      };

    }
    function editInit(){
      if(!inkoopbon){
        addRow();
        return;
      }

      _this.project = inkoopbon.project;
      _this.leverancier = inkoopbon.leverancier;

      fetchLeverancierTarieven();
      bcKostendragerInit()
      zenvoicesKostendragerInit()

      if(settings.prefill_offertes){
        _inforSelectMultiple.get('offertes').onchangeOption = selectOfferte;
      }
    }

    function previewInit(){
      if(!Number(_get('preview'))){return;}

      $('input').prop('disabled', true);
      $('select').prop('disabled', true);

      $('#addRowBtn').remove();
    }

    function bcKostendragerInit(){
      if(inkoopbon?.business_central_kostendrager){
        _inforSelectSearch.get('bc_kostendrager').disable();
      }
    }
    function zenvoicesKostendragerInit(){
      if(inkoopbon?.zenvoices_kostendrager){
        _inforSelectSearch.get('zenvoices_kostendrager').disable();
      }
    }

    async function selectProject(id){
      try{
        if(!id){
          _this.project = null;
          if(_inforSelectSearch.get('bc_kostendrager')){
            _inforSelectSearch.get('bc_kostendrager').enable()
          }
          return;
        }
        loader();

        _this.project = await getProject(id);

        //Kostendrager prefill
        const kostendrager = _bc.settings?.default?.kostendrager_vestiging[_this.project.vestiging_id];
        const kostendrager_select = _inforSelectSearch.get('bc_kostendrager');

        if(kostendrager){
          kostendrager_select.setValue(kostendrager);
          kostendrager_select.disable();
        }
        else if(kostendrager_select){
          kostendrager_select.enable();
        }

        if(settings.prefill_offertes){
          await projectOffertes()
        }
        successLoader();
      }
      catch(e){ handleCatchError(e); }
    }
    async function selectLeverancier(id){
      try{
        if(!id){
          _this.leverancier = null
          return;
        }

        loader();

        _this.leverancier = await getLeverancier(id)
        await fetchLeverancierTarieven();

        successLoader();
      }
      catch(e){ handleCatchError(e); }
    }

    async function fetchLeverancierTarieven(){
      if(!_this.leverancier){
        _this.tarieven = [];
        return;
      }

      const { tarieven } = await ajax('api/leveranciers/tarieven/get', {id: _this.leverancier.id})
      _this.tarieven = tarieven;

      $('[data-tarieven-values]').empty();
      for(const tarief of _this.tarieven){
        $('[data-tarieven-values]').append(`<span class="select_edit-value" data-value="${tarief.naam}" >${tarief.naam}</span>`)
      }
    }

    async function projectOffertes(){
      const fullOfferte = await getOffertes({
        project: _this.project.id,
        active: 1,
        relations: ['details'],
      })

      _this.offertes = fullOfferte.offertes;

      $(`#offerte-container`).html('');

      $(`#offerte-container`).append(`
          <label>Offertes</label>
          <infor-select-multiple id="offertes" name="offertes" class="form-control-custom" placeholder="Selecteer offertes">
            ${_this.offertes.map(offerte => `<infor-select-option data-name="${offerte.offertenummer}" data-value="${offerte.id}" >${offerte.offertenummer} - ${offerte.naam}</infor-select-option>`).join('')}
          </infor-select-multiple>
        `
      )

      initInforSelectMultiple()
      _inforSelectMultiple.get('offertes').onchangeOption = selectOfferte;

    }
     async function selectOfferte(option){
      try {
        if(option && !option.checked){
          unsetOfferte(option.value);
          return;
        }

        const fullOfferte = await getOffertes({
          ids: [option.value],
          active: 1,
          materiaal_totaal: true,
          relations: ['details'],
        })

        const index = _this.offertes.findIndex(row => row.id == fullOfferte.offertes[0].id);

        if (index !== -1) {
          _this.offertes[index] = fullOfferte.offertes[0];
        }

        //zet de offerte erin met aanvullende informatie (zodat deze inclusief de berekening pas gedaan word bij het selecteren)
        offerte = _this.offertes.find(row => row.id == option.value);
        offerte.details.forEach(detail => {
          processDetailValues(detail.values, option);
        });

        $('[data-rows-container] input').trigger('keyup');
      } catch (e) {
        handleCatchError(e);
      }
    }
    function processDetailValues(values, option) {
      values
        .filter(value => value?.keyword === 'materialen')
        .forEach(value => {
          processDetailValuesDatasets(value.datasets, option, value.keyword, value.detail_id);
        });

      values
        .filter(value => value?.keyword === '2ba_products')
        .forEach(value => {
          processDetailValuesTweebaProducts(value.tweeba_products, option, value.keyword, value.detail_id);
        });
    }
    function processDetailValuesDatasets(datasets, option, valueKeyword, detail_id) {
      return datasets.map(dataset => {
        const itemValue = JSON.parse(dataset.value);
        const objectOptions = {
          detail_id: detail_id,
          omschrijving: itemValue.omschrijving,
          omschrijvingClass: `row-${valueKeyword}`,
          dataset: dataset,
          rowClasses: `offerte-tr-${option.value}`,
        };

        addRow(objectOptions);
      });
    }
    function processDetailValuesTweebaProducts(tweeba_products, option, valueKeyword, detail_id) {
      return tweeba_products.map(tweeba_product => {
        // const itemValue = JSON.parse(tweeba_product.value);
        const objectOptions = {
          detail_id: detail_id,
          omschrijving: tweeba_product.description,
          omschrijvingClass: `row-${valueKeyword}`,
          tweeba_product: tweeba_product,
          rowClasses: `offerte-tr-${option.value}`,
        };

        addRow(objectOptions);
      });
    }

    function unsetOfferte(id){
      deleteDiv(`.offerte-tr-${id}`)
    }

    function addRow(objectOptions = {}){
      const string = randomString();

      let options = ''
      const categories = @json(json_decode(getSettingValue('leveranciers_tarieven_categories') ?? '{}'));
      for(const group_string in categories){
        const groep = categories[group_string];
        for(const name_string in groep.sub){
          const row = groep.sub[name_string]
          options += `<span class='select_group-value' data-group="${groep.name}" data-value='${row.omschrijving}' data-name='${row.omschrijving}'>${row.omschrijving}</span>`
        }
      }

      let custom_tds = ''
      for(const row_string in settings.custom){
        const row = settings.custom[row_string];
        if(row.type == 'text' || row.type == 'number'){
          custom_tds += `<td><input type="${row.type}" class="form-control-custom row-${row.keyword}" placeholder="${row.name}" name="custom[${string}][${row.keyword}]"  ></td>`
        }
      }

      let eenhedenOptions = '';
      if (settings.eenheden && settings.eenheden.length > 0) {
        eenhedenOptions = settings.eenheden.map(row => {
          return `<span class="select_edit-value" data-value="${row['eenheid']}">${row['eenheid']}</span>`;
        }).join('');
      }

      const report = @json(getSettingValue('inkoopbonnen_reporting_date') == 'aan');
      $("[data-rows-container]").append(
        `<tr class="row-container ${string} ${objectOptions?.rowClasses}" >
           <td>
             <div class='btns-container d-flex mx--1'><a class="btn btn-sm btn-inverse-danger w-100 mx-1 tippy" data-tippy-content="Regel verwijderen" onclick="deleteDiv('.${string}')" >@icon_close</a></div>
           </td>
           ${custom_tds}
           ${report
              ? `<td> <input type="date" class="form-control-custom" name="reporting_date_row[${string}]" value="${_this.default_report}"  ></td>`
              : ''
           }
           ${Object.keys(categories).length ?
              `<td class="select_group-container row-categorie-container" >
                  <input type="hidden" name="categorie[${string}]" data-placeholder="Categorie" data-required="required" class="select_group-hidden-input row-categorie">
                  <div class="select_group-values" >
                    <div class="select_group-box font-size-1" >${options}</div>
                  </div>
              </td>`
            : ''}
          ${_bc.instance.connected
            ? `<td>
                  <infor-select-search id="bc_account_${string}" name="bc_account[${string}]" placeholder="Selecteer grootboeknummer" class="form-control-custom bc_account" >
                    ${_bc.accounts.map(account => `<infor-select-option ${_bc.settings?.default?.purchase_account == account.number ? 'data-selected' : ''} data-value="${account.number}" data-name="${account.number} | ${account.name}" > <span class="badge badge-inverse-primary" >${account.number}</span> ${account.name} </infor-select-option>`).join('')}
                  </infor-select-search>
                </td>`
            : ''
          }
           <td class="select_edit-container" >
            <input type="text" autocomplete="off" name="naam[${string}]" class="select_edit-input form-select row-naam ${objectOptions?.omschrijvingClass ?? ''}" data-tarief-select placeholder="Omschrijving" value="${objectOptions?.omschrijving ?? ''}">
            <div class="select_edit-values font-size-1">
              <div class="select_edit-box" data-tarieven-values ></div>
            </div>
           </td>
           ${settings.prefill_offertes ? `
              <td>
                <input type="number" disabled class="form-control-custom row-reeds-besteld" placeholder="0" value="${objectOptions?.dataset?.ingekochtAantal ?? objectOptions?.tweeba_product?.ingekochtAantal ?? 0}">
              </td>
              <td>
                <input type="number" disabled step="0.01" class="form-control-custom row-totaal-te-bestellen" placeholder="0" value="${objectOptions?.dataset?.aantal ?? objectOptions?.tweeba_product?.amount ?? 0}">
              </td>
              <td>
                <input type="number" disabled class="form-control-custom row-nog-te-bestellend" placeholder="0" min="0" value="${(objectOptions?.dataset?.aantal ?? objectOptions?.tweeba_product?.amount ?? 0) - (objectOptions?.dataset?.ingekochtAantal ?? objectOptions?.tweeba_product?.ingekochtAantal ?? 0)}">
              </td>
              <td>
                <input type="number"  class="form-control-custom row-aantal" placeholder="0" min="0" name="aantal[${string}]" value="${(objectOptions?.dataset?.aantal ?? objectOptions?.tweeba_product?.amount ?? 0) - (objectOptions?.dataset?.ingekochtAantal ?? objectOptions?.tweeba_product?.ingekochtAantal ?? 0)}">
              </td>
              `:`
              <td>
                <input type="number" step="0.01" class="form-control-custom row-aantal" placeholder="0" name="aantal[${string}]" value="1">
              </td>
           `}
           ${eenhedenOptions
             ? `<td>
                  <div class="select_edit-container">
                      <input name="eenheid[${string}]" step="0.01" class="select_edit-input form-select mx-1 w-100 unit-select" placeholder="Eenheid" data-placeholder="Eenheid" value="${objectOptions?.eenheid ?? ''}">
                      <div class="select_edit-values">
                        <div class="select_edit-box">
                            ${eenhedenOptions}
                        </div>
                      </div>
                  </div>
                </td>`
             : ''
           }
           <td><input type="number" step="0.01" class="form-control-custom row-prijs" placeholder="Bedrag" name="prijs[${string}]" value="${objectOptions?.bedrag ?? '0.00'}" ></td>
           ${_settings.inkoopbonnen_hide_btw != 'ja' ?
             `<td>${utilities.select(string)}</td>
             <td class="text-center" ><input ${Number(_settings.factuur_standaard_incl_btw) ? 'checked' : ''} name="incl[${string}]" class="form-check-custom row-incl" type="checkbox"></td>`
           : '' }
            <td>
              <input type="hidden" name="detail_value_dataset_id[${lastString()}]" value="${objectOptions?.dataset?.id ?? null}">
              <input type="hidden" name="detail_values_tweeba_producten_id[${lastString()}]" value="${objectOptions?.tweeba_product?.id ?? null}">
              <input type="hidden" name="detail_id[${lastString()}]" value="${objectOptions?.detail_id ?? null}">
              <input type="hidden" name="trade_item_id[${lastString()}]" value="${objectOptions?.trade_item_id ?? null}">
              <input type="hidden" name="tweeba_product_id[${lastString()}]" value="${objectOptions?.tweeba_product_id ?? null}">
            </td>
         </tr>`
      );

      initSelectGroup();
      initInforSelectSearch();
      initInforSelectEdit();
      fetchLeverancierTarieven();

      return string;
    }

    //2ba
    async function addTweeBaProduct(){
      const objectOptions = await getTweeBaProduct();
      if(!objectOptions){return}

      addRow(objectOptions);
    }

  </script>
@endsection
