<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{{$inkoopbon->bonnummer}}</title>
    <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
    <style>

        @page {
            size: A4;
            width: 88%;
            margin: 123px 6% 130px;
        }
        body {
            font-family: sans-serif;
            font-size: 11px;
        }
        header {
            position: fixed;
            top: -115px;
            left: -50px;
        }
        header img {
           width: 800px;
        }
        footer {
            position: fixed;
            bottom: -120px;
            left: -50px;
        }
        footer img {
            width: 800px;
        }
        .background-concept {
            position: fixed;  /* Set to fixed so it stays in place */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{{url("/client/public/img/offertes/concept_bg.png")}}');
            background-repeat: no-repeat;
            background-size: cover; /* Adjust if needed */
            z-index: -1; /* Ensure it's above the main background */
        }

        p {
            margin-top: 0;
            margin-bottom: 0;
        }

        th {
            text-align: left;
        }

        .mandagheader{
          position: fixed;
          left: -50px;
          top: -10px;
          padding-left: 50px;
          font-size: 20px;
          font-weight: 600;
          width: 200%;
          border-bottom: black solid 2px;
        }

    </style>
</head>
<header>
  <img src="{{url("/client/public/img/pdf/client81/briefpapier-header.png")}}"/>
</header>
<footer>
  <img src="{{url("/client/public/img/pdf/client81/briefpapier-footer.png")}}"/>
</footer>
<div class="background"></div>
@if($inkoopbon->status != 'Afgerond')
    <div class="background-concept"></div>
@endif

<body>
<main>

    <section>
      <table>
        <tr>
          <td>{{$inkoopbon->leverancier->naam}}</td>
          <td colspan="2"></td>
        </tr>
        <tr>
          <td>{{$inkoopbon->leverancier->addressLine()}}</td>
          <th>INKOOPORDERNUMMER:</th>
          <th>{{$inkoopbon->bonnummer}}</th>
        </tr>
        <tr>
          <td>{{$inkoopbon->leverancier->postcode}} {{$inkoopbon->leverancier->plaats}}</td>
          <th>Datum:</th>
          <th>{{CarbonDmy($inkoopbon->date)}}</th>
        </tr>
      </table>

        <table class="table-bordered mt-3">
            <tr>
              <th>Project</th>
              <th colspan="4">{{$inkoopbon->custom_import->name ?? ''}}</th>
            </tr>
            <tr>
                <th class="text-left">Omschrijving</th>
                <th class="text-right">Aantal</th>
                <th class="text-left">Eenheid</th>
                <th class="text-right">Prijs</th>
                <th class="text-right">Totaal</th>
            </tr>
            @foreach($inkoopbon->regels as $regel)
                <tr>
                    <td>{{$regel->naam ?? ''}}</td>
                    <td class="text-right">{{number_format(($regel->aantal ?? 0), 2, ",", ".")}}</td>
                    <td class="text-left">{{$regel->eenheid ?? ''}}</td>
                    <td class="text-right nobr">€ {{number_format($regel->prijs, 2, ",", ".")}}</td>
                    <td class="text-right nobr">€ {{number_format($regel->totaal()->excl, 2, ",", ".")}}</td>
                </tr>
            @endforeach
            <tr>
                <td style="border:none;" colspan="3" class="text-right"><b>Totaal:</b></td>
                <td style="border:none;" colspan="1"></td>
                <td style="border:none;" class="text-right nobr"><b>€ {{number_format($inkoopbon->totaal()->excl, 2, ",", ".")}}</b></td>
            </tr>
          <tr>
            <td style="border:none;" colspan="2" class="text-right"><b>TOTAAL TE FACTUREREN:</b></td>
            <td style="border:none;" colspan="2"></td>
            <td style="border-top:none; border-right: none; border-left: none;" class="text-right nobr"><b>€ {{number_format($inkoopbon->totaal()->excl, 2, ",", ".")}}</b></td>
          </tr>
        </table>

        <table>
          <tr>
            <td>Alle bedragen zijn exclusief BTW</td>
          </tr>
          <tr>
            <td>BTW verlegd naar NL850444524B01</td>
          </tr>
          <tr>
            <td>Facturen worden alleen in behandeling genomen indien deze bon is bijgevoegd</td>
          </tr>
          <tr>
            <td>30% van de loonsom wordt op de G-rekening betaald (indien van toepassing)</td>
          </tr>
          <tr>
            <td>Betalingstermijn: 14 dagen na factuurdatum</td>
          </tr>
          <tr>
            <td>Facturen <NAME_EMAIL></td>
          </tr>
        </table>
    </section>

  <div class="page-break"></div>

  <div class="mandagheader">MANDAGENREGISTER</div>
  @php
    $class = \App\Classes\imports\client81\VanRijnCircet::class;
    $mandagenhtml = $class::inkoopMandagTableHtml($inkoopbon->id);
  @endphp

  <div class="mt-5">
    {!! $mandagenhtml !!}
  </div>
</main>
</body>
</html>
