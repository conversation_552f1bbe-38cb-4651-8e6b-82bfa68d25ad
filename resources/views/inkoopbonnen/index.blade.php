@extends('layouts.app')
@section('title', 'Inkoopbonnen')
@section('content')

    <div style="display: none" data-batch-init-container class="w-100" >
        <div class="py-2">
            <div class="card p-2 m-0">
                <div class="flex-between" >
                    <span>Aantal geselecteerde inkoopbonnen: <b class="badge badge-lg badge-inverse-secondary font-size-09 text-dark" data-batch-amount >0</b></span>
                    <div>
                        <a class="btn btn-inverse-success" data-batch-preview-btn > @icon_check </a>
                        <a class="btn btn-inverse-danger" data-batch-init > @icon_close </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section class="append-loader" >

        <section class="my-3 h-px-100" data-stats-navigation data-rounded="true" >
            <div class="py-2 bg-modal z-index-999" data-content >
                <div class="card m-0 p-2">
                    <div class="row">
                        <div class="col select_search-container" >
                            <div class="w-100" data-label >Groeperen op</div>
                            <input type="hidden" name="sort_by" class="select_search-hidden-input" data-placeholder="Groeperen op">
                            <div class="select_search-values" >
                                <div class="select_search-box rounded-9 font-size-085">
                                    <span class="select_search-value nobr" data-value="all" data-name="Alles" data-selected="true">Alles</span>
                                    <span class="select_search-value nobr" data-value="leverancier" data-name="leverancier">Leverancier</span>
                                    <span class="select_search-value nobr" data-value="vestiging" data-name="vestiging">Vestiging</span>
                                </div>
                            </div>
                        </div>
                        <div class="col select_search-container" data-sort-option-container >
                            <div class="w-100" data-label >Optie</div>
                            <input type="hidden" name="sort_option" class="select_search-hidden-input" data-placeholder="Selecteer optie">
                            <div class="select_search-values" >
                                <div class="select_search-box rounded-9 font-size-085">
                                    @foreach(getLeveranciers(['select' => ['id', 'naam', 'crediteurnummer']]) as $leverancier)
                                        <span class="select_search-value nobr" data-option="leverancier" data-value="{{$leverancier->id}}" data-name="{{$leverancier->naam}}">{{$leverancier->naam}}</span>
                                    @endforeach
                                    @foreach(getVestigingen() as $vestiging)
                                        <span class="select_search-value nobr" data-option="vestiging" data-value="{{$vestiging->id}}" data-name="{{$vestiging->naam ?? $vestiging->plaats}}">{{$vestiging->naam ?? $vestiging->plaats}}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="col select_search-container" >
                            <div class="w-100" data-label >Stadium</div>
                            <input type="hidden" name="sort_stadium" class="select_search-hidden-input" data-placeholder="Selecteer stadium">
                            <div class="select_search-values" >
                                <div class="select_search-box rounded-9 font-size-085">
                                    <span class="select_search-value nobr" data-value="" data-name="">Alle stadiums</span>
                                    <span class="select_search-value nobr" data-value="Open" data-name="Open">Open</span>
                                    <span class="select_search-value nobr" data-value="Afgerond" data-name="Afgerond">Afgerond</span>
                                    @if(getSettingCheckbox('inkoopbonnen_status_voltooid'))
                                        <span class="select_search-value nobr" data-value="Voltooid" data-name="Voltooid">Voltooid</span>
                                    @endif
                                    <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
                                </div>
                            </div>
                        </div>
                        <div class="col select_search-container" >
                            <div class="w-100" data-label >status</div>
                            <input type="hidden" name="sort_status" class="select_search-hidden-input" data-placeholder="Selecteer status">
                            <div class="select_search-values" >
                                <div class="select_search-box rounded-9 font-size-085">
                                    <span class="select_search-value nobr" data-value="" data-name="">Alle statussen</span>
                                    <span class="select_search-value nobr" data-value="Open" data-name="Open">Open</span>
                                    @if(hasModule('Accorderen'))
                                        <span class="select_search-value nobr" data-value="Accorderen" data-name="Accorderen">Accorderen</span>
                                    @endif
                                    <span class="select_search-value nobr" data-value="Afgerond" data-name="Afgerond">Afgerond</span>
                                    @if(getSettingCheckbox('inkoopbonnen_status_voltooid'))
                                        <span class="select_search-value nobr" data-value="Voltooid" data-name="Voltooid">Voltooid</span>
                                    @endif
                                    <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </section>

        <section class="my-3" >
            <div class="flex-between">
                <div class="w-100 mx-3 dropdown-divider "></div>
                <h4 class="nobr m-0" data-inkoopbonnen-count >
                    <div class="text-placeholder-md" ></div>
                </h4>
                <div class="w-100 mx-3 dropdown-divider"></div>
            </div>
        </section>

        <section class="d-none mt-3" data-inkoopbonnen-table-container>

            <div class="card rounded-pill rounded-m-unset">
                <div class="row flex-md-nowrap">
                    <div class="col-md-8 col-12 flex-align flex-md-nowrap flex-wrap">
                        <div class="flex-align mx-2 min-w-200 py-2" >
                            <select class="form-select mr-2 rounded-pill min-w-100" name="order_by">
                                <option value="bonnummer">Bonnummer</option>
                                <option value="sent_to">Verzonden aan</option>
                                <option value="sent_at">Verzonden om</option>
                            </select>
                          <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_inkoopbon', 'DESC') == 'DESC') d-none @endif" data-order-direction="ASC" >@icon_asc</a>
                          <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_inkoopbon', 'DESC') == 'ASC') d-none @endif" data-order-direction="DESC" >@icon_desc</a>
                            <input type="hidden" name="order_direction" value="{{_COOKIE('order_direction_inkoopbon', 'DESC')}}" >
                        </div>
                        <div class="form-control-divider mx-2"></div>
                        <div class="ds-container flex-align bg-white shadow-none mx-2 px-0 py-2" data-display-select-container="pagination" data-can-be-empty >
                            <select class="form-select mx-1 rounded-pill min-w-100" name="per_page">
                                <option @if(($_COOKIE['inkoopbonnen_per_page'] ?? null) === "25") selected @endif value="25" >25</option>
                                <option @if(($_COOKIE['inkoopbonnen_per_page'] ?? null) === "50") selected @endif value="50" >50</option>
                                <option @if(($_COOKIE['inkoopbonnen_per_page'] ?? null) === "100") selected @endif value="100" >100</option>
                                <option @if(($_COOKIE['inkoopbonnen_per_page'] ?? null) === "250") selected @endif value="250" >250</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 flex-align">
                        <div class="form-control-divider mx-2 d-md-block d-none"></div>
                        <a class="btn btn-light rounded-pill" onclick="initInkoopbonnen()" >@icon_redo</a>
                        <div class="w-100 overflow-visible mx-2 py-2">
                            <div class="w-100" >
                                <input type="text" class="form-control-custom rounded-pill" placeholder="Zoeken..." name="search" >
                                <div class="position-relative">
                                    <div class="rounded-9 bg-white border font-size-085 p-2 position-absolute shadow w-100 z-index-9 d-none" data-search-results ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card my-2 p-3" >
                <div class="overflow-auto">
                    <table class="inkoopbonnen-table" >
                        <thead>
                        <tr>
                            <th>Bonnummer</th>
                            <th>Project</th>
                            <th>Leverancier</th>
                            <th>Vestiging</th>
                            <th>Bedrag</th>
                            <th>Status</th>
                            @if(hasModule('Inkoopfacturen'))
                                <th>Gefactureerd</th>
                            @endif
                            @if(hasModule('Accorderen') && getSettingValue('accorderen_flow_inkoopbonnen'))
                                <th>Akkoord</th>
                            @endif
                            <th>Verzonden aan</th>
                            <th>Mail geopend</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody data-inkoopbonnen-tbody ></tbody>
                    </table>
                </div>
            </div>

        </section>

    </section>

    {{--  Modals--}}
    <section>
        <div class="modal fade" id="email-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form data-email-form >
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12 my-2">
                                    <label>Afzender*</label>
                                    <input name="sender" placeholder="Afzender" type="text" class="form-control-custom" required="">
                                </div>
                                <div class="select_edit-container">
                                    <label>Aan*</label>
                                    <input name="email" autocomplete="{{randomString()}}" placeholder="<EMAIL>, <EMAIL>" class="select_edit-input form-select" required="">
                                    <div class="select_edit-values">
                                        <div class="select_edit-box" data-email-cps ></div>
                                    </div>
                                </div>
                                <div class="col-12 my-2">
                                    <label>Onderwerp*</label>
                                    <input name="subject" placeholder="Onderwerp" class="form-control-custom">
                                </div>
                                <div class="col-12 my-2">
                                    <label>Inhoud*</label>
                                    <textarea name="message" id="message"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input type="submit" class="btn btn-success" value="Versturen" >
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

@endsection
@section('script')
    <script>

      let noload = getParameter('filter');

      var inkoopbonnen = [];

      const modules = {
        accorderen: @json(hasModule('Accorderen')),
        inkoopfacturen: @json(hasModule('Inkoopfacturen')),
      }

      const permissions = {
        inkoopbonnen_terugzetten: @json(hasPermission('Inkoopbonnen terugzetten')),
        inkoopbon_verwijderen_na_verzenden: @json(hasPermission('Verwijderen na verzenden')),
      }

      const _eg = {
        instance: new ExactGlobe(),
        klant: {
          btn: null,
          spinner: null,
          errors: null,
        },
        project: {
          btn: null,
          spinner: null,
          errors: null,
        },
      }
      const _bc = {
        instance: new BusinessCentral(),
      }
      const _zenvoices = {
        instance: new Zenvoices(),
      }
      const _base = {
        request: null,
        table: null,
        total_count: 0,
        tbody: $(`[data-inkoopbonnen-tbody]`),
        dsi: null,
        all_ids: [],
        current_ids: [],
        per_page: () => { return $('[name=per_page]').val() },
        page: () => { return _displaySelect['pagination'].value },
        sort: () => { return $('[name=sort_by]').val() },
        order_by: () => { return $('[name=order_by]').val(); },
        order_direction: () => { return $('[name=order_direction]').val(); },
        sort_option: () => { return $('[name=sort_option]').val() },
        sort_status: () => { return $('[name=sort_status]').val() },
        sort_stadium: () => { return $('[name=sort_stadium]').val() },
      }
      const _search = {
        request: null,
        container: $('[data-search-results]'),
        input: $('[name=search]'),
        timeout: null,
        results: [],
        ids: [],
        is_loading: () => { return !!($('[data-search-results]').find('[data-spinner]').length) }
      }
      const _editors = {
        email: null,
      }
      const _modals = {
        email: $('#email-modal'),
      }
      const _this = {
        inkoopbon: null,
      }
      const _batch = {
          state: false,
          selected: {},
          editors: {},
      }

      const settings = {
          inkoopbonnen_pdf_niet_meer_inzien_na_versturen: @json(getSettingCheckbox('inkoopbonnen_pdf_niet_meer_inzien_na_versturen')),
          inkoopbonnen_status_voltooid: @json(getSettingCheckbox('inkoopbonnen_status_voltooid')),
          inkoopbonnen_aanmaker_afzender: @json(getSettingCheckbox('inkoopbonnen_aanmaker_afzender')),
          inkoopbonnen_email_afzender: @json(getSettingValue('inkoopbonnen_email_afzender')),
          inkoopbonnen_email_content: @json(getSettingValue('inkoopbonnen_email_content')),
      }

      pageInteractive(() => {
        editorInit('[name=message]')
          .then(editor => _editors.email = editor);

        _base.dsi = _displaySelect['pagination'];
        _base.dsi.onchange = page => {
          initInkoopbonnen({page: page})
        }
      })
      pageComplete(prefillGetValues)

      $(document).on('click', function(){
        _search.container.addClass('d-none').empty();
      });
      $(document).on('click', '[data-search-by-results]', function(){
        const target = $(this).data('search-by-results');

        $('[name=sort_by]').val('all');
        $('[data-sort-option-container]').find('.select_search-input').val('').prop('disabled', true);
        $('[data-sort-option-container]').find('.select_search-hidden-input').val('');
        $('[name=sort_status]').val('')
        $('[name=sort_stadium]').val('')

        correctSelectSearchDisplay();

        _search.ids = [];
        if (target == 'all'){
          _search.results.map(inkoopbon => _search.ids.push(Number(inkoopbon.id)));
        }
        else{
          _search.ids.push(target);
        }

        initInkoopbonnen();
      });
      $(document).on('input', '.select_search-hidden-input', correctSelectSearchDisplay);
      $('[data-order-direction]').click(function(){
        const selected = $(this).data('order-direction');
        const target = selected == 'ASC' ? 'DESC' : 'ASC';

        $('[data-order-direction]').addClass('d-none');
        $(`[data-order-direction=${target}]`).removeClass('d-none');
        $('[name=order_direction]').val(target).trigger('change');

        setCookie('order_direction_inkoopbon', target)
      });
      $('[name=search]').on('input', async function(){
        let { container, timeout, is_loading } = _search;

        if (timeout){
          clearTimeout(timeout);
          timeout = null;
        }

        if(!is_loading()){
          container.removeClass('d-none').html(`<div class="text-center py-2" data-spinner >@spinner</div>`);
        }

        if(!this.value){
          container.addClass('d-none').empty();
          _search.ids = null;
          _search.input.val('');
          prefillGetValues();
          return;
        }


        _search.timeout = setTimeout(findInkoopbonnen, 100);
      })
      $('[name=sort_by]').change(function(){

        const value = this.value || 'all';

        _search.ids = null;
        _search.input.val('');


        const container = $('[data-sort-option-container]');
        container.find('.select_search-input').val('').prop('disabled', false);
        container.find('.select_search-value').addClass('d-none');

        if(value == 'all'){
          initInkoopbonnen()
          container.find('.select_search-input').val('').prop('disabled', true);
          return;
        }

        container.find(`[data-option=${value}]`).removeClass('d-none');
      });
      $('[name=per_page], [name=order_by], [name=order_direction]').change(function(){
        setCookie('inkoopbonnen_per_page', _base.per_page());
        initInkoopbonnen()
      });
      $('[name=sort_option], [name=sort_status], [name=sort_stadium]').change(() => {
        _search.ids = null;
        _search.input.val('');
        initInkoopbonnen()
      })

      $('[data-email-form]').submit(function(event){
        event.preventDefault();

        const serialize = $(this).serializeArray();
        const data = serializeArrayToObject(serialize);

        data.message = _editors.email.getData();
        data.id = _this.inkoopbon.id;

        if(!data.sender || !data.email || !data.subject || !data.message){
          notification('Vul alle verplichte velden in!', 'warning');
          return false;
        }

        loader();
        _modals.email.hideModal();
        ajax('api/inkoopbonnen/send', data)
          .then(response => {
            initInkoopbonnen();
          })
          .catch(handleCatchError);
      })

      $(document).on('click', '[data-batch-init]', function(){
          _batch.state = !_batch.state;

          $(`[data-batch-init='main']`).toggleClass(['btn-light', 'btn-dark'])
          $('[data-batch-init-container]').toggle(250);

          if(!_batch.state){
              _batch.selected = {};
              $('[data-batch-checkbox-toggle]').toggle(250);
              $('[data-batch-amount]').html(0);

              setTimeout(() => {
                  $('[data-batch-checkbox-container]').remove();
              }, 250)

              return
          }

          $('.inkoopbonnen-table').find('tr').each(function(){
              const id = $(this).data('id');
              const bon = inkoopbonnen.find(bon => bon.id == id);

              //Append checkbox when: It's select all, !Accorderen module or Has accorderen and succeeded
              let checkbox = '<input type="checkbox" class="form-check-custom mx-3" disabled >'
              if(!id){
                  checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="all" >`
              }
              else if(!bon?.accorderen?.isActive || bon?.accorderen?.succeeded){
                  checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="${id}" >`
              }

              $(this).prepend(`
                  <td data-batch-checkbox-container class="p-0 w-0" > <div data-batch-checkbox-toggle style="display: none"> ${checkbox} </div> </td>
                `)
          })

          $('[data-batch-checkbox-toggle]').toggle(250);

      });
      $(document).on('click', '[data-batch-preview-btn]', function(){

          if(!Object.keys(_batch.selected).length){
              notification('Geen inkoopbonnen geselecteerd!', 'warning');
              return;
          }

          let tabs = '';
          for(const id in _batch.selected){
              const bon = inkoopbonnen.find(bon => bon.id == id);

              if(!bon){ continue; }

              const { bonnummer, leverancier, token } = bon;

              let sender = @json(getSettingValue('inkoopbonnen_email_afzender'));
              let content =  @json(getSettingValue('inkoopbonnen_email_content'));

              content = replaceContentKeywords(content, bon);

              tabs += `<div class="py-1 form-tab" data-batch-tab="${id}" >
                    <div class="border rounded bg-white" >

                      <div class="flex-between px-2" >
                        <div class="d-flex align-items-center " >
                            <div data-batch-tab-indicator></div>
                            <span>${ bonnummer }</span>
                        </div>
                        <div class="flex-align">
                          <a href="${url}/inkoopbonnen/token/${token}" target="_blank" class="btn btn-inverse-primary btn-sm py-1 rounded-pill" >PDF</a>
                          <a class="btn" data-preview-tab-toggle >@icon_down</a>
                        </div>
                      </div>

                      <form data-preview-form style="display: none" class="w-100">
                        <div class="row m-0" style="background-color: rgba(0, 0, 0, 0.03)">
                          <div class="col-md-6 col-12 my-2">
                            <label>Afzender*</label>
                            <input data-sender name="sender" class="form-control-custom" placeholder="Afzender" value="${sender || ''}" >
                          </div>
                          <div class="col-md-6 col-12 my-2">
                            <label>Aan*</label>
                            <input data-email name="email" class="form-control-custom" placeholder="<EMAIL>, <EMAIL>" value="${leverancier?.email ?? ''}" >
                          </div>
                          <div class="col-12 my-2">
                            <label>Onderwerp*</label>
                            <input data-subject name="subject" class="form-control-custom" placeholder="Onderwerp" value="${`Inkoopbon: ${bonnummer}`}" >
                          </div>
                          <div class="col-12 my-2">
                            <label>Inhoud</label>
                            <textarea data-content data-id="${id}" name="message" class='batch-editor-${id}' placeholder="Inhoud" >${content || ''}</textarea>
                          </div>

                          <input type="hidden" name="id" value="${id}" >

                        </div>
                      </form>

                    </div>
                </div>`
          }

          tabs += '<div class="my-2 text-right" data-batch-action  > <a class="btn btn-success text-white" data-batch-send> Versturen </a> </div>'

          confirmModal({
              text: tabs,
              hideFooter: true,
              large: true,
          })
              .then(() => {
                  $(`[data-batch-init='main']`).trigger('click');
              })

          $('[data-preview-form] [data-content]').each(function(){
              const id = $(this).data('id');
              editorInit(`.batch-editor-${id}`)
                  .then(editor => _batch.editors[id] = editor);
          })
      })
      $(document).on('click', '[data-preview-tab-toggle]', function(){
          const container = findContainer('form-tab', this);

          container.find('[data-preview-tab-toggle]').rotate(180)
          container.find('[data-preview-form]').toggle(250);
      });
      $(document).on('click', '[data-batch-send]', async function(){

          let ready = true;
          const forms = {};
          $('[data-batch-tab-indicator]').empty();

          for(const id in _batch.selected){
              const bon =  inkoopbonnen.find(bon => bon.id == id);
              const container = $(`[data-batch-tab="${bon.id}"]`);

              let data = container.find('[data-preview-form]').serializeArray();

              data.push({
                  name: '_token',
                  value: csrf
              })
              data.find(row => row.name == 'message').value =_batch.editors[id].getData();

              const requiredSet = {
                  email: false,
                  sender: false,
                  subject: false,
                  message: false,
              }
              for(const input of data){
                  if(input.name == 'email' && input.value != ''){ requiredSet.email = true; }
                  if(input.name == 'sender' && input.value != ''){ requiredSet.sender = true; }
                  if(input.name == 'subject' && input.value != ''){ requiredSet.subject = true; }
                  if(input.name == 'message' && input.value != ''){ requiredSet.message = true; }
              }

              if(!requiredSet.email || !requiredSet.sender || !requiredSet.subject || !requiredSet.message){
                  ready = false;
                  container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-danger" > @icon_close </div>')
              }

              forms[bon.id] = data;

          }

          if(!ready){
              notification('Vul alle verplichte velden in!', 'warning', 5);
              return;
          }

          for(const id in _batch.selected){

              if(_batch.selected[id] == 'sending'){ continue }

              _batch.selected[id] = 'sending'

              const container = $(`[data-batch-tab="${id}"]`);
              const bon = inkoopbonnen.find(bon => bon.id == id);
              const data = forms[id];

              container.find('[data-batch-tab-indicator]').html('<div class="mx-2" > @spinner_small </div>')

              try{
                  const response = await ajax('api/inkoopbonnen/send', data);
                  delete _batch.selected[id];
                  $(`[data-batch-tab=${id}]`).remove();
                  notification(`Inkoopbon <b>${bon.bonnummer ?? 'n.t.b.'}</b> verstuurd aan ${response.emails.join(', ')}!`, 'success', 5);
                  initInkoopbonnen();
              }
              catch(err){
                  _batch.selected[id] = 'true'
                  container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-warning" > @icon_exclamation </div>')
              }

          }
      });
      $(document).on('change', '[data-batch-checkbox]', function(){
          const id = $(this).data('batch-checkbox');
          const checked = $(this).prop('checked');

          if(id == 'all'){

              //No need to verify status when unchecking, uncheck all and return
              if(!checked){
                  $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').prop('checked', false).trigger('change');
                  return;
              }

              $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').each(function(){
                  const id = $(this).data('batch-checkbox');
                  const bon = inkoopbonnen.find(bon => bon.id == id);

                  if(bon && bon.stadium != 'Open'){ return true; }

                  $(this).prop('checked', checked).trigger('change')
              });

              return;
          }

          delete _batch.selected[id];
          if(checked){
              _batch.selected[id] = true
          }

          $('[data-batch-amount]').html(Object.keys(_batch.selected).length);
      });

      function prefillGetValues(){
        if (getParameter('sort_by') && getParameter('sort_option')){
          $('[name=sort_by]').val(getParameter('sort_by')).trigger('change');
          $(`[data-option=${getParameter('sort_by')}][data-value=${getParameter('sort_option')}]`).click();
        }
        if (getParameter('status')){
          $('[name=sort_status]').val(getParameter('status'));
        }
        if (getParameter('stadium')){
          $('[name=sort_stadium]').val(getParameter('stadium'));
        }

        correctSelectSearchDisplay();
        initInkoopbonnen();
      }
      function initInkoopbonnen(options = {}){
        if (noload){
          $('[data-inkoopbonnen-table-container]').removeClass('d-none');
          noload = false;
          return;
        }
        loader();

        const page = options?.page || 1;

        const data = {
          ids: _search.ids,
          page: page,
          paginate: _base.per_page(),
          status: _base.sort_status(),
          stadium: _base.sort_stadium(),
          sort_by: _base.order_by(),
          sort_type: _base.order_direction(),
          relations: [
              'project',
              'leverancier',
              'user'
          ],
        }

        if(_base.sort() && _base.sort_option()){
          data[_base.sort()] = _base.sort_option();
        }
        if(_base.request){
          _base.request.abort();
          _base.request = null;
        }

        _base.request = ajax('/api/inkoopbonnen/get', data);
        _base.request
          .always(() => {
            _base.request = null;
          })
          .then(response => {
            inkoopbonnen = response.inkoopbonnen;
            _base.total_count = response.total_count;
            _base.all_ids = response.all_ids;
            _base.current_ids = response.current_ids;

            fillInkoopbonnen();
            refreshPagination(options?.page);
            clearLoader();
          })
          .catch(err => {
            if (err.status === 0) {
              return
            }

            actError(err, `initProjecten()`);
            errorLoader();
            if (err?.message) {
              notification(err.message);
            }
            if (err?.responseJSON?.message) {
              notification(err.responseJSON.message);
            }
          });
      }
      function fillInkoopbonnen(){
        const { table, tbody } = _base;

        if(table){ table.destroy(); }

        tbody.empty();

        for(const inkoopbon of inkoopbonnen){
          const { id, bonnummer, project, leverancier, tot_incl, tot_excl, tot_btw, color, status, gefactureerd, accorderen, sent_at, sent_to, mail_geopend,
                  token, stadium, business_central_id, zenvoices_id, has_budget} = inkoopbon;
          tbody.append(`
            <tr data-id="${id}" >
                <td>${bonnummer}</td>
                <td> ${project ? `<a class="text-primary cursor-pointer" onclick="showProject(${project.id})" >${project.projectnr}</a>` : '' } </td>
                <td> ${leverancier ? `<a class="text-primary cursor-pointer" onclick="showLeverancier(${leverancier.id})" >${leverancier.naam}</a>` : '' } </td>
                <td>${ project?.vestiging?.plaats || '' }</td>
                <td>
                    <div class="flex-between w-100 text-primary mx--2 tippy" data-tippy-content="
                        <table >
                            <tr>
                                <td class='pr-3 text-left' >Excl. BTW:</td>
                                <td class='pr-1' >€</td>
                                <td class='text-right' >${toPrice(tot_excl)}</td>
                            </tr>
                            <tr>
                                <td class='pr-3 text-left' >BTW:</td>
                                <td class='pr-1' >€</td>
                                <td class='text-right' >${toPrice(tot_btw)}</td>
                            </tr>
                            <tr>
                                <th class='pr-3 text-left border-top' >Incl. BTW:</th>
                                <th class='pr-1 border-top' >€</th>
                                <th class='text-right border-top' >${toPrice(tot_incl)}</th>
                            </tr>
                        </table>
                    ">
                        <span class="mx-2" >€</span>
                        <span class="mx-2">${toPrice(tot_excl)}</span>
                    </div>
                </td>
                <td> <span class="badge badge-${color}" >${status}</span> </td>
                ${ modules.inkoopfacturen ? `<td><span class="badge badge-${Number(gefactureerd) ? 'success' : 'danger'}" >${Number(gefactureerd) ? 'Ja' : 'Nee'}</span></td>` : '' }
                ${ accorderen.isActive ? `<td>${accorderen?.nodes || ''}</td>` : '' }
                <td>
                    ${sent_at
                        ? `<small class="d-block">${now(sent_at).date.eu} ${now(sent_at).time}</small>
                          <small class="d-block" >${sent_to}</small>`
                        : `<i class="bi bi-dash h4 mx-0"></i>`
                      }
                </td>
                <td class="text-center" >
                    ${sent_to
                        ? (mail_geopend ? `<i data-tippy-content="${now(mail_geopend).date.eu} ${now(mail_geopend).time}" class="bi bi-check2 h3 mx-0 tippy"></i>` : `<i class="bi bi-hourglass-split h4 mx-0"></i>`)
                        : `<i class="bi bi-dash h4 mx-0"></i>`
                    }
                </td>
                <td>
                    <div class="d-flex justify-content-end mx--2">

                        ${_bc.instance.connected && (!accorderen.isActive || accorderen.succeeded)
                          ? `<a class="tippy btn btn-img mx-2 ${business_central_id ? 'btn-success' : 'btn-primary'}" data-business-central-inkoopbon-init="${id}" data-tippy-content="Upload naar Business Central">@btn_img_business_central</a>`
                          : ''
                        }
                        ${_zenvoices.instance.connected && (status == 'Afgerond' || status == 'Voltooid') && leverancier
                          ? `<a class="tippy btn btn-img mx-2 ${zenvoices_id ? 'btn-success' : 'btn-primary'}" data-zenvoices-inkoopbon-init="${id}" data-tippy-content="Upload naar Zenvoices">@btn_img_zenvoices_vrij</a>`
                          : ''
                        }
                        <div class="dropdown mx-2">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">Acties</button>
                            <div class="dropdown-menu">
                                ${ stadium == 'Open'
                                    ?   `
                                        <a class="dropdown-item" href="${url}/inkoopbonnen/token/${token}#toolbar=0" target="_blank">PDF</a>
                                        <a class="dropdown-item" href="${url}/inkoopbonnen/edit/${id}" >Wijzigen</a>
                                        ${!accorderen.isActive || accorderen.succeeded || has_budget ? `<a class="dropdown-item" onclick="sendConfirm(${id})" >Versturen</a>` : '' }
                                        ${!sent_at ? `<a class="dropdown-item" onclick="statusConfirm(${id}, 'Verwijderd')" >Verwijderen</a>` : ''}
                                        ${!has_budget && (accorderen.isActive && (!accorderen.inProcess || accorderen.failed))
                                            ? `<div class="dropdown-divider" ></div>
                                              <a  class="dropdown-item" onclick="statusConfirm(${id}, 'Accorderen')" >Accorderen</a>`
                                             : ''
                                        }
                                        ${!accorderen.isActive || accorderen.succeeded || has_budget
                                            ? `<div class="dropdown-divider"></div>
                                              <a class="dropdown-item" onclick="statusConfirm(${id}, 'Afgerond')" >Afgerond</a>`
                                             : ''
                                        }`
                                    :   ''
                                }
                                ${ (stadium == 'Afgerond' || stadium == 'Voltooid' || stadium == 'Verwijderd') ? `
                                  ${ !settings.inkoopbonnen_pdf_niet_meer_inzien_na_versturen ? `<a class="dropdown-item" href="${url}/inkoopbonnen/token/${token}#toolbar=0" target="_blank">PDF</a>` : '' }
                                  <a class="dropdown-item" href="${url}/inkoopbonnen/edit/${id}?preview=1" >Inzien</a>
                                  ${stadium == 'Afgerond' && settings.inkoopbonnen_status_voltooid ? `<a class="dropdown-item" onclick="statusConfirm(${id}, 'Voltooid')" >Voltooien</a>` : '' }
                                  ${ permissions.inkoopbonnen_terugzetten && stadium != 'Voltooid' ? `<a class="dropdown-item" onclick="statusConfirm(${id}, 'Open')" >Terugzetten</a>` : '' }
                                  ${ permissions.inkoopbon_verwijderen_na_verzenden && stadium != 'Verwijderd' ? `<a class="dropdown-item" onclick="statusConfirm(${id}, 'Verwijderd')" >Verwijderen</a>` : '' }
                                 ` : '' }
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            `);
        }

        initTable();
        appendDTButton();
        appendBatchButton();
        tippyInit();
      }
      function initTable(){
        $('[data-inkoopbonnen-table-container]').removeClass('d-none');
        _base.table = tableInit('.inkoopbonnen-table', {
          paging: false,
          ordering: false,
          searching: false,
          info: false,
          columnDefs: [{orderable: false, targets: [-1]}],
        })
      }
      function refreshPagination(page){

        const { dsi, total_count } = _base;
        const pages_count = Math.ceil((_base.total_count || 1) / (_base.per_page() || (_base.total_count || 1))) // if per_page == '' (no pagination) the pages_count must be 1, also when empty;

        if(!page){
          for(const node of dsi.values()){
            $(`[data-display-select-container='pagination']`).find(`.ds-node[data-value=${node.value}]`).remove();
          }
          for(let i = 1; i <= pages_count; i++){
            dsi.addValue({name: i, value: i});
          }
          dsi.silentSelect(1);
        }

        $('[data-inkoopbonnen-count]').html(`${total_count} ${(total_count !== 1) ? 'INKOOPBONNEN' : 'INKOOPBON'}`);
      }
      async function findInkoopbonnen(){
        let { container } = _search;
        const value = $('[name=search]').val()

        if (_search.request){
          _search.request.abort();
          _search.request = null;
        }

        _search.request = searchInkoopbonnen(value);
        _search.request
          .then(inkoopbonnen => {

            _search.results = inkoopbonnen;
            _search.timeout = null;
            if (!inkoopbonnen.length){
              container.html(`<div class='text-center text-muted py-2' >Geen resultaten gevonden</div>`)
              return;
            }

            container.empty();
            for(let i = 0; i <5; i++){
              if(!inkoopbonnen[i]){ break; }
              const inkoopbon = inkoopbonnen[i];

              container.append(`
                  <div class="cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5" data-search-by-results='${inkoopbon.id}' >
                      <div class='font-size-09' >${inkoopbon.bonnummer || ''}</div>
                    <div class='text-muted font-size-075' >${ inkoopbon.project_projectnr || ''}</div>
                  </div>
             `);
            }
            container.append(`<div class='cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5 text-center' data-search-by-results='all' >Alle resultaten weergeven ( ${inkoopbonnen.length} )</div>`);
          })
          .catch(handleCatchError)
      }

      function statusConfirm(id, status){
        const ikb = inkoopbonnen.find(row => row.id == id);

        confirmModal({
          text: `Weet je zeker dat je status van <b>${ikb.bonnummer}</b> wilt veranderen naar <b>${status}</b>?`
        })
          .then(confirm => {
            if(!confirm.status){ return; }

            loader();
            ajax('api/inkoopbonnen/status', {
              id: id,
              status: status,
            })
              .then(response => {
                initInkoopbonnen();
              })
              .catch(handleCatchError);

          })
      }
      function sendConfirm(id){
        const ikb = inkoopbonnen.find(row => row.id == id);
        const form = _modals.email.find('form');
        const cps = $('[data-email-cps]');
        const { leverancier, project } = ikb;

        _this.inkoopbon = ikb;

          let sender = settings.inkoopbonnen_email_afzender;
          let content = settings.inkoopbonnen_email_content;

          if(settings.inkoopbonnen_aanmaker_afzender){
              const user = ikb?.user;
              if(user && user.email){
                  sender = user.email;
              }
          }

        content = replaceContentKeywords(content, ikb)


        form.find('[name=sender]').val(sender || '');
        form.find('[name=subject]').val(`Inkoopbon: ${ikb.bonnummer}`);
        _editors.email.setData(content || '');

        //Append contactpersonen
        cps.empty();
        form.find('[name=email]').val('');
        if(leverancier){
          if(leverancier.email){
            cps.append(`<span class="select_edit-value flex-between" data-value="${leverancier.email}"> <span>${leverancier.naam || ''}</span> <span class="text-muted" >${leverancier.email}</span> </span>`)
            form.find('[name=email]').val(leverancier.email);
          }
          for(const cp of leverancier.contactpersonen){
            if(!cp.email){ continue }

            cps.append(`<span class="select_edit-value flex-between" data-value="${cp.email}"> <span>${cp.voornaam || ''} ${cp.achternaam || ''}</span> <span class="text-muted" >${cp.email}</span> </span>`)
          }
        }

        _modals.email.showModal();
      }

      function replaceContentKeywords(content, bon){
          const { leverancier, project } = bon;

          if(!content){ return '' }
          content = content.replace("&lt;{leverancier}&gt;", leverancier ? leverancier.naam : '');
          content = content.replace("&lt;{bv}&gt;", (project && project._bv) ? project._bv.name : '');
          content = content.replace("&lt;{user}&gt;", _user.name);

          return content;
      }

      function appendBatchButton(){
          $('.dataTables_filter').append('<a class="btn btn-light ml-1" data-batch-init="main" ><i class="fa-solid fa-paper-plane m-0"></i></a>');
      }

    </script>
@endsection
