@if(Auth::check() && routePrefix() != "iframe" && !stringContains(currentRoute(), '/uren/projectnummers/kastvakken'))
  <nav class="navbar default-layout col-lg-12 col-12 p-0 d-flex flex-row position-fixed no-print"
    style="height: 63px;z-index: 20">
    {{-- Logo --}}
    <div class="navbar-menu-wrapper d-flex align-items-center w-100">
      <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-toggle="minimize"
        data-navbar-toggle>
        <span class="mdi mdi-menu"></span>
      </button>
      <span class="mx-2" >{{ _username() }}</span>
      <ul class="navbar-nav navbar-nav-right">
        @if(hasPermission('Bestanden uploaden') || hasPermission('Alle bestanden bekijken') || hasPermission('Eigen bestanden bekijken') || hasPermission('Eigen medewerker map bekijken'))
          <li class="nav-item d-none d-lg-block color-setting cursor-pointer">
            <a class="nav-link" onclick="openExplorerPath()" >@icon_file</a>
          </li>
        @endif
        @if(Auth::user()->hasPermissionTo('Settings bewerken'))
          <li class="nav-item d-none d-lg-block color-setting">
            <a class="nav-link" href="{{ url('/settings')}}">
              <i class="mdi mdi-settings"></i>
            </a>
          </li>
        @endif
        @if(Auth::user()->hasPermissionTo('Store bekijken'))
          <li class="nav-item d-none d-lg-block color-setting">
            <a class="nav-link" href="{{ url('store') }}">
              <div class="d-flex align-items-center position-relative">
                <i class="mdi mdi-cart"></i>
                @if ((!isset($_COOKIE['store_last_viewed']) || count(getUnseenModules()) > 0) && !Request::is('store'))
                    <span class="badge badge-danger w-12 h-12 ml--10 mt--10 rounded-circle position-absolute top-0 start-100 translate-middle"></span>
                @endif
              </div>
            </a>
          </li>
        @endif
        <li class="nav-item d-none d-lg-block color-setting">
          <a class="nav-link" href="{{ url('logout') }}">
            <i class="mdi mdi-logout"></i>
          </a>
        </li>
      </ul>
      <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button"
        data-toggle="offcanvas">
        <span class="icon-menu"></span>
      </button>
    </div>
  </nav>
@endif
