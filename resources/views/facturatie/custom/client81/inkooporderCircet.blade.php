<div class="card hover-shadow p-2 my-3">
  <label>Inkooporder Circet</label>
  <div class="my-2 row circet-pdf-container">
    <div class="col-12 my-2">
      <label>Selecteer een import</label>
      <infor-select-search name="import" id="import" class="form-control-custom" placeholder="Selecteer een import" >
        @foreach(getImports() as $import)
          <infor-select-option data-value="{{$import->id}}" >{{$import->name}}</infor-select-option>
        @endforeach
      </infor-select-search>
    </div>
    <div class="col-12 my-2" id="dropify-container">
      <input class="dropify" type="file" name="circetpdf" data-allowed-file-extensions='["pdf"]'>
    </div>
    <div class="my-2 col-12">
      <a class="btn btn-success text-white" id="dataConfirm">Bevestigen</a>
    </div>
  </div>
</div>


<script>

  let importData = null;
  let importDataset = null;

  $('#dataConfirm').on('click', function() {
    $(this).hide().after('@spinner');
    const data = {
      fn: 'storeCircetOrderPdf',
      template: 1,
      import_id: _inforSelectSearch.get('import').getValue().value,
      file: $('input[name="circetpdf"]').prop('files')[0],
    }

    ajaxFile('/api/imports/trigger', data).then(res => {
      notification(res.data.message, (res.data.success ? 'success' : 'warning'), 4);
      if(!res.data.success) {
        $('#dataConfirm').show().next().remove();
        return;
      }
      $('#dataConfirm').next().remove();

      $('.circet-pdf-container').html(`
        <div class="row" style="height: 800px!important;">
          <div class="col-5">
            <iframe src="${url}/api/file/explorer/files/${res.data.src}" class="h-100"></iframe>
          </div>
          <div class="col-7 import-data-container h-100" style="overflow-y: auto;"></div>
        </div>
      `);

      importData = res.data.import_data;
      importDataset = res.data.dataset;

      drawImportDataTable();
    }).catch(handleCatchError);

  });

  $('.circet-pdf-container').on('click', '.row-select', function() {
    const count = $(this).data('count');
    const date = $(this).data('date');
    const import_id = $(this).data('import-id');

    const itemname = $(this).data('item');
    const item = importDataset.items.find(item => item.name == itemname);
    const itemValue = JSON.parse(item.value);

    const row = {
      naam: `${itemname} (${date})`,
      aantal: count,
      bedrag: itemValue.verkoop,
    }
    const string = addRow(row);

    $(`.${string}`).append(`<input type="hidden" name="custom_import_row_id" value="${import_id}"/>`)
    $(this).removeClass('cursor-pointer row-select').addClass('bg-inverse-success').attr('data-selected', 'true');
    $('.circet-pdf-container').find('.search-data').val('').trigger('input');
  });

  $('.circet-pdf-container').on('input', '.search-data', function () {
    const searchTerm = $(this).val().toLowerCase();
    const table = $('#circet-import-data-table');
    table.find('tbody tr').each(function () {
      const dataSearch = $(this).data('search') || '';
      $(this).toggle(dataSearch.toLowerCase().includes(searchTerm));
    });
  });

  function selectAll(){
    $('.circet-pdf-container').find('.row-select').each(function() {
      if ($(this).attr('data-selected') !== 'true') {
        $(this).trigger('click');
      }
    })
  }

  function drawImportDataTable() {
    const grouped = {};

    for(const importKey in importData) {
      const row = importData[importKey];
      const name = row.find(item => item.keyword == "preferred_resource_resource_name")?.value || "";
      const dateObj = excelDateToJSDate(row.find(item => item.keyword == "actual_start")?.value);
      const date = dateObj?.date?.eu || "";
      const item = row.find(item => item.keyword == "item_number")?.value || "";
      const itemName = row.find(item => item.keyword == "item_item_name")?.value || "";
      const import_id = row[0].import_id;

      const key = `${name}|${date}|${item}`;
      if (!grouped[key]) {
        grouped[key] = {
          name,
          date,
          item,
          itemName,
          count: 0,
          import_id,
        };
      }
      grouped[key].count++;
    }

    $('.import-data-container').html(`
      <div class="search-bar-container form-control-custom rounded-pill my-2 flex-align">
        <input type="text" class="search-data form-control-plaintext py-0" placeholder="Regel zoeken..." />
        <span class="form-control-divider selectAllRows"></span>
        <a class="btn btn-sm tippy" data-tippy-content="Taken beheren" onclick="selectAll()">@icon_confirm</a>
      </div>

      <div class="table-responsive w-100 my-2" style="overflow-x: auto;">
        <table class="table table-striped table-bordered table-hover w-100 m-0" id="circet-import-data-table" style="min-width: 600px">
          <thead>
            <tr>
              <th>Aantal</th>
              <th>Item</th>
              <th>Datum</th>
              <th>Naam</th>
            </tr>
          </thead>
          <tbody>
            ${Object.values(grouped).map(row => `
              <tr class="cursor-pointer row-select"
                  data-date="${row.date}"
                  data-item="${row.item}"
                  data-count="${row.count}"
                  data-import-id="${row.import_id}"
                  data-search="${row.name} ${row.date} ${row.itemName}"
                  data-selected="false">
                <td>${row.count}</td>
                <td>${row.itemName}</td>
                <td>${row.date}</td>
                <td>${row.name}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `);
  }

</script>
