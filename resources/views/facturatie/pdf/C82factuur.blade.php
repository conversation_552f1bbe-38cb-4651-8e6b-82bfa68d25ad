<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$factuur->factuurnummer ?? 'n.t.b.'}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&display=swap" rel="stylesheet">

  <style>
    @page {
      size: A4;
      width: 21cm;
      height: 29.7cm;
      margin: 250px 80px 120px;
      /*padding: 0 200px;*/
    }

    /*Global*/
    * {
      font-family: Quicksand;
      color: #333333;
      font-size: 15px;
      line-height: .9;
      font-weight: 500;
    }


    figure.table{
      margin: 0;
    }
    ul{
      padding: 0 0 0 1.25rem;
      margin-top: 0;
      margin-bottom: 1rem;
    }
    p{
      margin-top: 0;
      margin-bottom: 1rem;
    }
    b{
      font-weight: 700;
      font-size: 1.2rem;
    }
    th{
      font-weight: 700;
      text-align: left;
    }

    /*Header*/
    header{
      /*width: 21cm;*/
      height: 225px;
      width: 100%;
      background-color: black;
      margin: -250px -80px 0;
      padding: 0 80px;
      position: fixed
    }
    header td{
      padding: 10px 0;
    }
    header *{
      color: white!important;
      line-height: .8;
    }

    /*Footer*/
    footer{
      position: fixed;
      bottom: 0;
      margin: 0 -80px -120px;
      width: 100%;
    }
    footer .payment-text{
      width: 100%;
      text-align: center;
      padding: 10px 80px;
    }
    footer .payment-text *{
      text-align: center;
      font-size: .8rem!important;
      line-height: .8;
    }
    footer .footer-table-section{
      width: 100%;
      background-color: #222222;
      padding: 10px 80px;
    }
    footer .footer-table-section *{
      color: white!important;
      line-height: 1.25;
      font-size: .8rem;
    }

    /*Pages*/
    .px{
      padding-left: 1rem!important;
      padding-right: 1rem!important;
    }
    .py{
      padding-top: 1rem!important;
      padding-bottom: 1rem!important;
    }

    .mx{
      margin-left: 2rem!important;
      margin-right: 2rem!important;
    }
    .my{
      margin-top: 2rem!important;
      margin-bottom: 2rem!important;
    }

    /*Info*/
    .info-table{
      margin: 1rem 0;
    }
    .info-table tbody tr td{
      padding-bottom: 1px;
    }
    .info-table tbody tr td{
      padding-top: 1px;
      padding-bottom: 1px;
    }
    .info-table td:first-child{
      width: 0;
    }

    /*Rows*/
    .rows-table{
      margin: 1rem 0;
    }
    .rows-table th{
      font-size: 1.2rem;
    }
    .rows-table tr{
      border-bottom: 1px solid black;
    }
    .rows-table td, .rows-table th{
      padding: 10px 5px;
    }



  </style>
</head>
<body>

<header>
  <table>
    <tr>
      <td></td>
      <td class="text-right" >
        <img src="{{url('client/public/img/pdf/82template1/logo_dwm_w500.png')}}" height="100" >
      </td>
    </tr>
    <tr>
      <td>
        <div><b>{{$factuur->adres->klant}}</b></div>
        <div>{{$factuur->adres->tav}}</div>
        <div>{{$factuur->adres->straat}} {{$factuur->adres->huisnummer}}{{$factuur->adres->toevoeging}}</div>
        <div>{{$factuur->adres->postcode}} {{$factuur->adres->plaats}}</div>
      </td>
      <td class="text-right" >
        <div><b>Drone with a Mission</b></div>
        <div>{{$factuur->BV->straat}} {{$factuur->BV->huisnummer}}</div>
        <div>{{$factuur->BV->postcode}} {{$factuur->BV->plaats}}</div>
        <div>{{$factuur->BV->website}}</div>
      </td>
    </tr>
  </table>
</header>
<footer>

  <div class="payment-text" >
    <div>We verzoeken u het bedrag binnen 30 dagen o.v.v. het factuurnumer over te maken naar rekeningnummer</div>
    <div><b>{{$factuur->BV->iban}}</b> ten name van {{$factuur->BV->name}}</div>
  </div>

  <section class="footer-table-section">
    <table>
      <tr>
        <td>
          <img src="{{url('client/public/img/pdf/82template1/logo_dwm_w500.png')}}" height="50" >
        </td>
        <td class="w40" >
          <div><b>IBAN:</b> {{$factuur->BV->iban}}</div>
          <div><b>BTW:</b> {{$factuur->BV->btw}}</div>
        </td>
        <td class=" w40" >
          <div><b>KVK:</b> {{$factuur->BV->kvk}}</div>
        </td>
      </tr>
    </table>
  </section>
</footer>

<main>

  <section>

    <div class="center">
      <b>Factuur</b>
    </div>

    {{--Info--}}
    <table class="info-table" >
      <tbody>
      <tr>
        <td>Factuur:</td>
        <td>{{$factuur->factuurnummer ?? 'n.t.b.'}}</td>
      </tr>
      <tr>
        <td>Datum:</td>
        <td>{{CarbonDmy($factuur->datum)}}</td>
      </tr>
      <tr>
        <td>Vervaldatum:</td>
        <td>{{CarbonDmy($factuur->betalingstermijn)}}</td>
      </tr>

      <tr>
        <td colspan="2" class="py" ></td>
      </tr>

      <tr>
        <td>Inkoopnummer:</td>
        <td>{{$factuur->referentie}}</td>
      </tr>
      @if($factuur->project)
        <tr>
          <td>Project:</td>
          <td>{{$factuur->project->projectnr}} | {{$factuur->project->projectnaam}}</td>
        </tr>
      @endif

      </tbody>
    </table>

    {{--Regels--}}
    <table class="rows-table">
      <thead>
        <tr>
          <th class="w-100" >Omschrijving</th>
          <th class="nobr" >Aantal</th>
          <th class="nobr" >P.P.E. ex. BTW</th>
        </tr>
      </thead>
      <tbody>
        @foreach($factuur->regels as $regel)
            <tr>
              <td>{{$regel->naam}}</td>
              <td>{{$regel->aantal}}</td>
              <td>
                <table class="content-p-0 content-border-0" >
                  <tr>
                    <td>&euro;</td>
                    <td class="text-right" >{{number_format($regel->excl(), 2, ',', '.')}}</td>
                  </tr>
                </table>
              </td>
            </tr>
        @endforeach
        <tr class="border-bottom-0" >
          <td></td>
          <td class="text-right nobr" >Totaal ex. BTW</td>
          <td>
            <table class="content-p-0 content-border-0" >
              <tr>
                <td>&euro;</td>
                <td class="text-right" >{{number_format($factuur->totaal()->excl, 2, ',', '.')}}</td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td></td>
          <td class="text-right nobr" >BTW</td>
          <td>
            <table class="content-p-0 content-border-0" >
              <tr>
                <td>&euro;</td>
                <td class="text-right" >{{number_format($factuur->totaal()->btw, 2, ',', '.')}}</td>
              </tr>
            </table>
          </td>
        </tr>
        <tr class="border-bottom-0" >
          <th></th>
          <th class="text-right nobr" >Totaalbedrag incl. BTW</th>
          <th>
            <table class="content-p-0 content-border-0" >
              <tr>
                <th class="text-left" >&euro;</th>
                <th class="text-right" >{{number_format($factuur->totaal()->btw, 2, ',', '.')}}</th>
              </tr>
            </table>
          </th>
        </tr>
      </tbody>
    </table>

  </section>

</main>
</body>
</html>
