<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{{$factuur->factuurnummer ?? 'n.t.b.'}}</title>
    <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
    <style>

        @page {
            size: A4;
            width: 88%;
            margin: 123px 6% 130px;
        }
        body {
            font-family: sans-serif;
            font-size: 11px;
            margin-top: 20px;
        }
        header {
            position: fixed;
            top: -115px;
            left: -50px;
        }
        header img {
           width: 800px;
        }
        footer {
            position: fixed;
            bottom: -120px;
            left: -50px;
        }
        footer p {
          margin-left: 450px;
          margin-bottom: -100px;
        }
        footer img {
            width: 800px;
        }
        .background-concept {
            position: fixed;  /* Set to fixed so it stays in place */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{{url("/client/public/img/offertes/concept_bg.png")}}');
            background-repeat: no-repeat;
            background-size: cover; /* Adjust if needed */
            z-index: -1; /* Ensure it's above the main background */
        }

        th {
            text-align: left;
        }

        .mandagheader{
          position: fixed;
          left: -50px;
          top: -10px;
          padding-left: 50px;
          font-size: 20px;
          font-weight: 600;
          width: 200%;
          border-bottom: black solid 2px;
        }

        .klantdata {
          position: fixed;
          right: 200px;
          top: 20px;
        }

    </style>
</head>
<header>
  <img src="{{url("/client/public/img/pdf/client81/briefpapier-header.png")}}"/>
</header>
<footer>
  <p>S.v.p. betaling binnen 30 dagen na factuurdatum</p>
  <img src="{{url("/client/public/img/pdf/client81/briefpapier-footer.png")}}"/>
</footer>
<div class="background"></div>
@if(!$factuur->factuurnummer)
    <div class="background-concept"></div>
@endif

<body>
<main>

    <section>
      <div class="klantdata">
        <p><i>Factuur voor:</i></p>
        <p>{{$factuur->adres->klant}}</p>
        <p>{{$factuur->adres->straat}} {{$factuur->adres->huisnummer}}</p>
        <p>{{$factuur->adres->postcode}} {{$factuur->adres->plaats}}</p>
      </div>

      <table style="margin-top: 130px;">
        <tr>
          <td><i>Factuurnummer:</i></td>
          <td>{{$factuur->factuurnummer ?? 'n.t.b.'}}</td>
          <td><i>Datum:</i></td>
          <td>{{CarbonDmy($factuur->datum)}}</td>
        </tr>
        <tr>
          <td>Uw btw nummer:</td>
          <td>{{$factuur->klant->btw_nummer ?? ''}}</td>
          <td colspan="2"></td>
        </tr>
      </table>

      <p>Hierbij doen wij u een factuur toekomen, betreft uitgevoerde werkzaamheden {{$factuur->custom_import->name ?? ''}}</p>

      <table class="table-bordered mt-3">
        <tr>
          <th colspan="5">{{$factuur->referentie ?? ''}}</th>
        </tr>
        <tr>
          <th class="text-left">Omschrijving</th>
          <th class="text-right">Aantal</th>
          <th class="text-left">Eenheid</th>
          <th class="text-right">Prijs</th>
          <th class="text-right">Totaal</th>
        </tr>
        @foreach($factuur->regels as $regel)
          <tr>
            <td>{{$regel->naam ?? ''}}</td>
            <td class="text-right">{{number_format(($regel->aantal ?? 0), 2, ",", ".")}}</td>
            <td class="text-left">{{$regel->_eenheid() ?? ''}}</td>
            <td class="text-right nobr">€ {{number_format($regel->prijs, 2, ",", ".")}}</td>
            <td class="text-right nobr">€ {{number_format($regel->totaal()->excl, 2, ",", ".")}}</td>
          </tr>
        @endforeach
      </table>
      <table style="margin-top: 15px; border-spacing: 0; border-collapse: separate; border: 1px solid black;" class="text-right avoid-break mb-2">
        <tbody>
        <tr>
          <td><b>Netto totaal :</b></td>
          <td>€ {{number_format($factuur->totaal()->excl, 2, ",", ".")}}</td>
        </tr>
        @foreach ($factuur->groupedBtws() as $btw => $bedrag)
          <tr>
            <td>{{str_contains($btw, 'BTW') ? $btw : 'BTW '.$btw}}%:</td>
            <td>{{$factuur->btw_verlegd ? 'BTW verlegd' : '€ '.number_format($bedrag->btw_bedrag, 2, ",", ".")}}</td>
          </tr>
        @endforeach
        <tr>
          <td><b>TOTAAL DEZE FACTUUR :</b></td>
          <td><b>€ {{number_format($factuur->totaal()->incl, 2, ",", ".")}}</b></td>
        </tr>
        </tbody>
      </table>
      <table class="my-2">
        <tr>
          <td>Loonsom: € {{number_format($factuur->totaal()->incl, 2, ",", ".")}} waarvan 10% te storten op onze G-rekening.</td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Gelieve over te maken op G-rekeningnummer NL07RABO09910.83.946:</td>
          <td>€</td>
          <td>{{number_format($factuur->totaal()->incl * 0.1, 2, ",", ".")}}</td>
        </tr>
        <tr>
          <td>Gelieve over te maken op bankrekeningnummer NL79RABO01065.96.977:</td>
          <td>€</td>
          <td>{{number_format($factuur->totaal()->incl * 0.9, 2, ",", ".")}}</td>
        </tr>
      </table>
    </section>

  <div class="page-break"></div>

  <div class="mandagheader">MANDAGENREGISTER</div>
  @php
    $class = \App\Classes\imports\client81\VanRijnCircet::class;
    $mandagenhtml = $class::inkoopMandagTableHtml(1);
  @endphp

  <div class="mt-3">
    {!! $mandagenhtml !!}
  </div>
</main>
</body>
</html>
