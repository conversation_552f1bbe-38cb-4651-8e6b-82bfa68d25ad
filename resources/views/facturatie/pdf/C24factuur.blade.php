<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{ $factuur->factuurnummer ?? 'concept' }}</title>
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 75%;
      margin: 80px 10% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      font-family: sans-serif;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      top: -115px;
      left: -12.5%;
      z-index: -1;
      width: 100%;
      border-bottom: 3px solid black;
      text-align: center;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: 50px;
      left: 0;
      z-index: -1;
      width: 100%;
    }

    .page-break {
      page-break-after: always;
    }
    .center{text-align: center;}

    .h50{height: 50%}
    .h100{height: 29.69cm}

    .w5{width: 4.5%;}
    .w10{width: 9%;}
    .w15{width: 14.5%;}
    .w20{width: 20%;}
    .w25{width: 24.5%;}
    .w30{width: 28.5%}
    .w33{width: 32.2%;}
    .w35{width: 35%;}
    .w40{width: 38%;}
    .w45{width: 44%;}
    .w50{width: 50%;}
    .w55{width: 54%;}
    .w60{width: 58%;}
    .w75{width: 74.5%;}
    .w70{width: 68.5%;}
    .w80{width: 80%;}
    .w85{width: 85%;}
    .w90{width: 90%;}
    .w95{width: 95%;}
    .w100{width: 100%;}
    .d-inline{display: inline-block;}
    .d-block{display: block;}
    .spanIT{
      width: 90%;
      display: inline-block;
    }
    .spanIC{
      margin-left: 10px;
      display: inline-block;
    }
    img{width: 110px;}
    table{
      border-collapse: collapse;
      width: 100%;
    }
    .table{
      margin: 0!important;
      padding: 0 0px!important;
      width: 100%;

    }
    #pageCounter {
      position: fixed;
      float: right;
      right: 0;
      bottom: -1.3cm;
      z-index: -1;
    }
    td, th{
      padding: 5px 0px;
    }
    #pageCounter .counter:after { content: counter(page, decimal); }
    .border{border: 1px solid black;}
    .border-bottom{border-bottom: 1px solid black;}
    .border-bottom-50{border-bottom: 1px solid rgba(0,0,0,0.5);}
    .text-right{text-align: right!important;}
    .text-left{text-align: left!important;}
    small{font-size: 12px;}
    span, b, p{
      font-size: 13px;
    }
    .page-break{page-break-after: always;}

  </style>
</head>
{{--<header>--}}
{{--  <span style="margin: 0 auto; display: inline-block; background-color: white; width: 180px;border: 3px solid black;transform: translateY(15px)" ></span>--}}
{{--</header>--}}
{{--<div id="pageCounter"><p class="counter d-inline"></p></div>--}}
{{--<footer>--}}
{{--  <div  class="center" >--}}
{{--    <img style="display: inline-block;width: 200px;"  src="{{url("client/public/img/poweredByTessa.png")}}" >--}}
{{--  </div>--}}
{{--</footer>--}}
<div id="pageCounter"><p class="counter"></p></div>

<body>
<main>
  <!-- Page 1 -->
  <section class="w100">
    <table>
      <tbody>
      <tr style="vertical-align: top;" >
        <td>
          <img style="display: inline-block; width: 250px" src="{{url("client/public/img/logos/bv".$factuur->bv.".png")}}" >
        </td>
        <td style="width: 220px"  >
          <b class="d-block" >{{$factuur->BV->name}}</b>
          <span class="d-block">{{$factuur->BV->straat}}&nbsp;{{$factuur->BV->huisnummer}}</span>
          <span class="d-block">{{$factuur->BV->postcode}}</span>
          <span class="d-block">{{$factuur->BV->plaats}}</span>

          <div style="margin-top: 10px;" >  <b class="d-inline w25">Btw-nr:</b> <span class="d-inline">{{$factuur->BV->btw}}</span></div>
          <div>               <b class="d-inline w25">KvK:</b>    <span class="d-inline">{{$factuur->BV->kvk}}</span></div>
          <div>               <b class="d-inline w25">E-mail:</b> <span class="d-inline">{{$factuur->BV->email}}</span></div>
          <div>               <b class="d-inline w25">IBAN:</b>   <span class="d-inline">{{$factuur->BV->iban}}</span></div>
          <div>               <b class="d-inline w25">BIC:</b>    <span class="d-inline">{{$factuur->BV->bic}}</span></div>
        </td>
      </tr>

      <tr style="vertical-align: top;" >
        <td>
          <div style="padding-top: 60px;" >
            @if($factuur->status == "Betaald")
              <span class="d-block" style="margin: 5px 0;color: forestgreen;"><b>Factuur betaald!</b></span>
            @endif
            @if(isset($factuur->adres))
              <b class="d-block" >{{$factuur->adres->klant}}</b>
              @if(isset($klant->adres->tav))
                <span class="d-block">T.a.v. {{$factuur->adres->tav}}</span>
              @endif
              <span class="d-block">{{$factuur->adres->straat}}&nbsp;{{$factuur->adres->huisnummer}}{{$factuur->adres->toevoeging}}</span>
              <span class="d-block">{{$factuur->adres->postcode}}&nbsp;{{$factuur->adres->plaats}}</span>
            @elseif(isset($factuur->klant))
              <b class="d-block" >{{$factuur->klant->naam}}</b>
              <span class="d-block">T.a.v. {{$factuur->klant->contactpersoon_voornaam}}&nbsp;{{$factuur->klant->contactpersoon_achternaam}}</span>
              <span class="d-block">{{$factuur->klant->straat}}&nbsp;{{$factuur->klant->huisnummer}}{{$factuur->klant->toevoeging}}</span>
              <span class="d-block">{{$factuur->klant->postcode}}&nbsp;{{$factuur->klant->plaats}}</span>
            @endif
          </div>
        </td>
        <td style="width: 220px"  >
          <div style="padding-top: 60px;" >
            <div><span class="d-inline w45">Factuur:</span><span class="d-inline" >{{ $factuur->factuurnummer ?? 'concept' }}</span></div>
            <div><span class="d-inline w45">Referentie:</span><span class="d-inline" >{{$factuur->referentie ?? "-"}}</span></div>
            <div><span class="d-inline w45">Factuurdatum:</span><span class="d-inline">{{\Carbon\Carbon::parse($factuur->datum)->format("d-m-Y")}}</span></div>
            <div><span class="d-inline w45">Vervaldatum:</span><span class="d-inline">{{\Carbon\Carbon::parse($factuur->betalingstermijn)->format("d-m-Y")}}</span></div>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
    <div style="margin: 20px 0;" >
      {!! $factuur->inleiding !!}
    </div>
    <div>
      <table>
        <tr>
          <td class="border-bottom" style="width: 60px" ><small>Aantal</small></td>
          <td class="border-bottom"><small>Beschrijving</small></td>
          <td class="border-bottom text-right" style="width: 120px"><small>Bedrag excl. btw</small></td>
        </tr>
        @php $in = 0; $ex = 0; @endphp
        @foreach($factuur->regels as $row)
          <tr>
            <td class="border-bottom-50 center" style="width: 60px" >{{$row->aantal}}</td>
            <td class="border-bottom-50" >
              {{$row->naam}}<br>
              <small>
                @if($row->incl == 0)
                Stukprijs: € {{number_format($row->prijs, 2, ",", "")}}
                @else
                Stukprijs: € {{number_format($row->prijs / (100+$row->btw) * 100, 2, ",", "")}}
                @endif
              </small>
            </td>
            <td class="border-bottom-50 text-right" style="width: 120px" >
              @if($row->incl == 0)
                € {{number_format($row->prijs * $row->aantal, 2, ",", "")}}
                @php $ex = $ex + $row->prijs * $row->aantal @endphp
                @php $in = $in + ($row->prijs * $row->aantal) * (1 + $row->btw / 100) @endphp
              @elseif($row->incl == 1)
                € {{number_format(($row->prijs * $row->aantal) / (100+$row->btw) * 100, 2, ",", "")}}
                @php $ex = $ex + ($row->prijs * $row->aantal) / (100+$row->btw) * 100 @endphp
                @php $in = $in + $row->prijs * $row->aantal @endphp
              @endif
            </td>
          </tr>

        @endforeach
        <tr>
          <td colspan="2" class="text-right">Totaalbedrag excl. btw</td>
          <td class="text-right" style="width: 120px">€ {{number_format($ex, 2, ",", "")}}</td>
        </tr>
        <tr>
          <td colspan="2" class="text-right border-bottom">BTW</td>
          <td class="text-right border-bottom" style="width: 120px">€ {{number_format($in - $ex, 2, ",", "")}}</td>
        </tr>
        <tr>
          <td colspan="2" class="text-right"><b style="font-size: 16px;">Totaalbedrag incl. btw</b></td>
          <td class="text-right" style="width: 120px;"><b style="font-size: 16px;" >€ {{number_format($in, 2, ",", "")}}</b></td>
        </tr>
      </table>
    </div>
    <div>
      {!! $factuur->slot !!}
    </div>
    <footer>
      <div>
        <p>
          Wij verzoeken u vriendelijk bovenstaand bedrag binnen {{\Carbon\Carbon::parse($factuur->datum)->diffInDays(\Carbon\Carbon::parse($factuur->betalingstermijn))}} werkdagen
          over te maken ten name van Kloosterhof Neer B.V. op het ABN*ARMO rekeningnummer <b>{{$factuur->_bv->iban}}</b> o.v.v. het <b>{{ $factuur->factuurnummer ?? 'concept' }}</b>.
        </p>
      </div>
    </footer>
  </section>


</main>
</body>
</html>
