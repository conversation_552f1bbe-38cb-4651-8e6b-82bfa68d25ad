<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>{{$factuur->factuurnummer}}</title>
  <link rel="stylesheet" href="{{url('client/public/css/pdf.css')}}" >
  <style>
    /**
        Set the margins of the page to 0, so the footer and the header
        can be of the full height and width !
      **/
    @page {
      size: A4;
      width: 80%;
      margin: 170px 10% 130px;
    }

    /** Define now the real margins of every page in the PDF **/
    body {
      font-family: sans-serif;
      font-size: 15px;
      margin-left: 12px;
    }

    /** Define the header rules **/
    header {
      position: fixed;
      width: 800px;
      top: -170px;
      left: -80px;
      right: 0px;
      height: 150px;
    }

    /** Define the footer rules **/
    footer {
      position: fixed;
      bottom: -100px;
      left: -12.5%;
      right: 0cm;
      z-index: -1;
      width: 100.1%;
      /*font-size: 0.8rem;*/
    }

    table {
      page-break-inside: auto;
    }

    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

    table td p {
      margin: 0;
      padding: 0;
    }

    .sidebarLeft {
      position: fixed;
      top: -190px;
      left: -80px;
      width: 120px;
    }

    .text-08-rem{
      font-size: 0.8rem!important;
    }

    .border-row {
      border: 1px solid black;
    }

    .ml35 {
      margin-left: 35px;
    }

    .table-nobr, .table-nobr td{
      white-space: nowrap;!important
    }

  </style>
</head>
<header>
  <img src="{{url("/client/public/img/pdf/51template1/headerHaltronic.png")}}" class="w100 h-100"/>
</header>

<div class="sidebarLeft">
  <img src="{{url("/client/public/img/pdf/51template3/sidebar.png")}}" class="w100" style="margin-top: 7.5cm"/>
</div>

<footer style="text-align:center;" class="text-center text08rem">
  <div  style="margin-left: 120px" class="w85 text-08-rem">
    <span>Alle transakties vinden plaats conform onze algemene leverings- en betalingsvoorwaarden</span>
    <span>Installerende Bedrijven 2024 (ALIB2024), worden op eerste verzoek toegezonden</span>
  </div>

  <div class="bg"></div>
</footer>

<body >
<main>
  <section class="w100 ">
    <table class="w-66 text-08-rem ml35 table-nopad" style="margin-top: -20px;">
      <tr>
        <td>{{$factuur->_bv->straat}} {{$factuur->_bv->huisnummer}}</td>
      </tr>
        <td>{{$factuur->_bv->postcode}} {{$factuur->_bv->plaats}}</td>
      <tr>
        <td>Tel: {{$factuur->_bv->telefoon}}</td>
      </tr>
      <tr>
        <td>{{$factuur->_bv->website}}</td>
      </tr>
      <tr>
        <td>Rabobank 1377.58.995 EUR</td>
      </tr>
      <tr>
        <td>SWIFT: {{$factuur->_bv->bic}}</td>
      </tr>
      <tr>
        <td>IBAN: {{$factuur->_bv->iban}}</td>
      </tr>
      <tr>
        <td>K.v.K nr: {{$factuur->_bv->kvk}}</td>
      </tr>
      <tr>
        <td>BTW: {{$factuur->_bv->btw}}</td>
      </tr>
    </table>

    <table class="w-50 text-08-rem table-nopad" style="margin-top: -108px; margin-left: 400px">
      @if(isset($factuur->adres))
        <tr>
          <td>{{$factuur->adres->klant ?? ''}}</td>
        </tr>
        <tr>
          <td>{{$factuur->adres->straat ?? ''}} {{$factuur->adres->huisnummer ?? ''}}{{$factuur->adres->toevoeging ?? ''}}</td>
        </tr>
        <tr>
          <td>{{$factuur->adres->postcode ?? ''}} {{$factuur->adres->plaats ?? ''}}</td>
        </tr>
      @elseif(isset($factuur->klant))
        <tr>
          <td>{{$factuur->klant->naam ?? ''}}</td>
        </tr>
        <tr>
          <td>{{$factuur->klant->straat ?? ''}} {{$factuur->klant->huisnummer ?? ''}}{{$factuur->klant->toevoeging ?? ''}}</td>
        </tr>
        <tr>
          <td>{{$factuur->klant->postcode ?? ''}} {{$factuur->klant->plaats ?? ''}}</td>
        </tr>
      @endif
    </table>

    <h3 class="ml35" style="margin-top: 115px">FACTUUR</h3>
    <table class="table-nopad w95 ml35 text-08-rem" >
      @if($factuur->klant->debiteurnummer)
      <tr>
          <td class="w25"><strong>Debiteurnummer:</strong></td>
          <td class="w75">{{ $factuur->klant->debiteurnummer }}</td>
      </tr>
      @endif
      <tr>
        <td class="w25"><strong>Factuurnummer:</strong></td>
        <td class="w75">{{ $factuur->factuurnummer ?? 'concept'}}</td>
      </tr>
      @if($factuur->referentie)
          <tr>
            <td class="w25"><strong>Referentie:</strong></td>
            <td class="w75">{{ $factuur->referentie }}</td>
          </tr>
      @endif
      <tr>
        <td class="w25"><strong>Factuurdatum:</strong></td>
        <td class="w75">{{ CarbonDmy($factuur->datum) }}</td>
      </tr>
      @php $werkbonnen = $factuur->_werkbonnen;@endphp
      @if(!empty($werkbonnen))
        <tr>
          <td class="w25"><strong>Werkbon:</strong></td>
          <td class="w75">
            @foreach($werkbonnen as $werkbon)
              {{ $werkbon->werkbonnummer ?? '' }}
            @endforeach
          </td>
        </tr>

        <tr>
          <td class="w25"><strong>locatie:</strong></td>
          <td class="w75">
            @foreach($werkbonnen as $werkbon)
              {{ $werkbon->project->locatie->naam ?? '' }}
            @endforeach
          </td>
        </tr>
      @endif
      @if($factuur->inleiding != '')
        <tr>
          <td class="w25"><strong>Opmerkingen:</strong></td>
          <td class="w75">{!! $factuur->inleiding !!}</td>
        </tr>
      @endif
    </table>

    <table class="w95 ml35 mt-3">
      <tr class="border-row">
        <td class="w10">Aantal</td>
        <td class="w50">Omschrijving</td>
        <td class="w20 text-right">Eenheidsprijs</td>
        <td class="w20 text-right">Totaal</td>
      </tr>
      @foreach($factuur->regels as $factuurregel)
        <tr class="text-08-rem">
          <td class="w15" >{{ $factuurregel->aantal }}</td>
          <td class="w60" >{{ $factuurregel->naam }}</td>
          <td class="w15 text-right" >€ {{ number_format($factuurregel->prijs, 2, ",", ".") }}</td>
          <td class="w15 text-right" >€ {{number_format($factuurregel->excl() * $factuurregel->aantal, 2, ",", ".")}}</td>
        </tr>
      @endforeach
    </table>

    @php
      $factTotaal = $factuur->totaal();
      if($factuur->g_rekening_waarde){
          $grekeningPerc = $factuur->g_rekening_waarde;
      }else{
        $grekeningPerc = getKlantSettingValue($factuur->klant->id, 'g_rekening_percentage');
      }

      if($grekeningPerc){
        $loonkostenBedrag = $factTotaal->excl / 100 * 40;
        $grekeningBedrag = $loonkostenBedrag / 100 * $grekeningPerc;

        $loonkosten = number_format($loonkostenBedrag , 2, ',', '.');
        $testorten = number_format($grekeningBedrag, 2, ',', '.');
        $restant = number_format($factTotaal->excl - $grekeningBedrag, 2, ',', '.');
      }

      $btw = number_format($factuur->totaal()->btw, 2, ',', '.');
      $totaalExcl = number_format($factTotaal->excl, 2, ',', '.');
      $totaal = number_format($factTotaal->incl, 2, ',', '.');
    @endphp

    @if($grekeningPerc)
      <table class="w95 ml35 text-08-rem table-nobr" style="margin-top: 25px">
        <tr>
          <td class="w55">Loonkostenbestanddeel bedraagt</td>
          <td class="w15 text-right">40%</td>
          <td class="w15 text-right">€ {{ $totaalExcl }}</td>
          <td class="w10 text-right">€ {{ $loonkosten }}</td>
        </tr>
        <tr>
          <td class="w55">Te storten G-rekening nr: {{ $factuur->_bv->g_rekeningnummer }}</td>
          <td class="w15 text-right">{{ $grekeningPerc }}%</td>
          <td class="w15 text-right">€ {{ $loonkosten }}</td>
          <td class="w10 text-right">€ {{ $testorten }}</td>
        </tr>
        <tr>
          <td class="w55">Restant te storten op normale rekening</td>
          <td class="w15 text-right"></td>
          <td class="w15 text-right"></td>
          <td class="w10 text-right">€ {{$restant}}</td>
        </tr>
      </table>
      <br>
    @endif

    <table class="w95 ml35 text-08-rem" style="margin-top: 10px">
      <tr>
        <td class="w80">Totaal excl</td>
        <td class="w20" align="right">€ {{ $totaalExcl }}</td>
      </tr>
      @foreach($factuur->totalGroupedByPerc() as $perc => $total)
        @if($total["total_price_incl"])
          <tr>
            <td class="w85">BTW {{$factuur->btw_verlegd ? '0% (Verlegd)' : $perc.'%'}}</td>
            <td class="w15" align="right">€ {{ number_format($total["btw_bedrag"], 2, ",", ".") }}</td>
          </tr>
        @endif
      @endforeach
      <tr>
        <hr style="width:119%">
      </tr>
      <tr>
        <td class="w85"><strong>@if($factuur->betalingstermijn !== 0)Totaalbedrag te betalen voor {{ \Carbon\Carbon::parse($factuur->factuurdatum)->addDays($factuur->betalingstermijn)->format('d-m-Y') }}@else Contant @endif</strong></td>
        <td class="w15" align="right"><strong>€ {{ $totaal }}</strong></td>
      </tr>
      <tr>
        <td class="w100">{!! $factuur->slot !!}</td>
      </tr>
    </table>
  </section>

  @php $projecten = $factuur->periodProjecten($factuur->uren_van, $factuur->uren_tot); @endphp
  @if(count($projecten))
    <section class="w100">
      <div class="page-break"></div>
      <table class="w95 ml35 table-bordered">
        <tr>
          <td class="w20"><strong>Datum</strong></td>
          <td style="width: 28%"><strong>Locatie</strong></td>
          <td style="width: 27%"><strong>Monteur(s)</strong></td>
          <td class="w10"><strong>Gewerkte Uren</strong></td>
          <td class="w15"><strong>Werk gereed</strong></td>
        </tr>
        @foreach($projecten as $project)
          @foreach($project->uren ?? [] as $uren)
          <tr class="text-08-rem">
            <td class="w20"><strong>{{ \Carbon\Carbon::parse($uren->datum)->format('d-m-Y') }}</strong></td>
            <td style="width: 28%"><strong>{{ $project->locatie->naam ?? '' }}</strong></td>
            <td style="width: 27%"><strong>{{$uren->user->name}} {{$uren->user->lastname}}</strong></td>
            <td class="w10">{{ ceil($uren->totaaluren100) }}</td>
            <td class="w15">{{ $project->status == 'Afgerond' ? 'Ja' : 'Nee'}}</td>
          </tr>
          @endforeach
        @endforeach
      </table>

      @php
        $hasMaterialen = false;
        foreach ($werkbonnen as $werkbon) {
            foreach ($werkbon->keywords as $keyword) {
                if ($keyword->keyword == 'materialen') {
                    $hasMaterialen = true;
                    break 2; // exit both loops
                }
            }
        }
      @endphp

      @if($hasMaterialen)
        <h5 class="w95 ml35"><strong>Gebruikte Materialen</strong></h5>
        <table class="w95 ml35 table-bordered" style="margin-top:-20px;">
          <tr>
            <td class="w25"><strong>Aantal</strong></td>
            <td class="w75"><strong>Artikelcode</strong></td>
            <td class="w75"><strong>Artikel</strong></td>
          </tr>
          @foreach($werkbonnen as $werkbon)
            @foreach($werkbon->keywords as $keyword)
              @if($keyword->keyword != 'materialen') @continue @endif
              @foreach(json_decode($keyword->value)->rows as $row)
                <tr class="text-08-rem">
                  <td class="w25">{{ $row->values->Aantal ?? '' }}</td>
                  <td class="w75">{{ $row->datasetitem->value->code ?? '' }}</td>
                  <td class="w75">{{ $row->datasetitem->value->naam ?? '' }}</td>
                </tr>
              @endforeach
            @endforeach
          @endforeach
        </table>
      @endif

      @php
        $hasWerkzaamheden = false;
        foreach ($werkbonnen as $werkbon) {
            foreach ($werkbon->keywords as $keyword) {
                if ($keyword->keyword == 'Werkzaamheden') {
                    $hasWerkzaamheden = true;
                    break 2;
                }
            }
        }
      @endphp

      @if($hasWerkzaamheden)
        <table class="ml35 text-08-rem border-row w95 mt-4">
          <tr>
            <td class="w100"><br><strong>Uitgevoerde Werkzaamheden</strong>
              @foreach($werkbonnen as $werkbon)
                @foreach($werkbon->keywords as $keyword)
                  @if($keyword->keyword != 'Werkzaamheden') @continue @endif
                  <div>{!! $keyword->value !!}</div>
                @endforeach
              @endforeach
            </td>
          </tr>
        </table>
      @endif

      <table class="ml35 text-08-rem border-row w95 mt-4">
        <tr>
          @foreach($werkbonnen as $werkbon)
            <td class="w100"><br><strong>Getekend</strong>
            @foreach($werkbon->keywords as $keyword)
              @if($keyword->value && $keyword->keyword == 'naam_handtekening')
                <strong>door</strong> {{$keyword->value}}
              @endif
              @if($keyword->keyword != 'signature') @continue @endif
                @if($werkbon->datum)
                  <strong>op</strong> {{ $werkbon->datum }}
                @endif
                <div>
                  <img src="{{url('api/file/'.$keyword->value)}}" style="width: 150px">
                </div>
              @endforeach
            </td>
          @endforeach
        </tr>
      </table>
      <br><br>
    </section>
  @endif
</main>
</body>
</html>
