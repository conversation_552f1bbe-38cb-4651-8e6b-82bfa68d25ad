@extends('layouts.app')

@section('title',  isset($isProforma) ? "Pro Forma's" : 'Facturen')

@section('content')
  <div id="snelstart-alert"></div>

  <div style="display: none" data-batch-init-container class="w-100" >
    <div class="py-2">
      <div class="card p-2 m-0">
        <div class="flex-between" >
          <span>Aantal geselecteerde facturen: <b class="badge badge-lg badge-inverse-secondary font-size-09 text-dark" data-batch-amount >0</b></span>
          <div>
            <a class="btn btn-inverse-success" data-batch-preview-btn > @icon_check </a>
            <a class="btn btn-inverse-danger" data-batch-init > @icon_close </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <section class="append-loader">

    <section class="my-3 h-px-100" data-stats-navigation data-rounded="true" >
      <div class="py-2 bg-modal z-index-999" data-content >
        <div class="card m-0 p-2">
          <div class="row">
            <div class="col select_search-container" >
              <div class="w-100" data-label >Groeperen op</div>
              <input type="hidden" name="sort_by" class="select_search-hidden-input" data-placeholder="Groeperen op">
              <div class="select_search-values" >
                <div class="select_search-box rounded-9 font-size-085">
                  <span class="select_search-value nobr" data-value="all" data-name="Alles" data-selected="true" >Alles</span>
                  <span class="select_search-value nobr" data-value="bv" data-name="bv">B.V.</span>
                  <span class="select_search-value nobr" data-value="klant" data-name="klant">Klant</span>
                  <span class="select_search-value nobr" data-value="vestiging" data-name="vestiging">Vestiging</span>
                </div>
              </div>
            </div>
            <div class="col select_search-container" data-sort-option-container >
              <div class="w-100" data-label >Optie</div>
              <input type="hidden" name="sort_option" class="select_search-hidden-input" data-placeholder="Selecteer optie">
              <div class="select_search-values" >
                <div class="select_search-box rounded-9 font-size-085">
                  @foreach(getBvs() as $bv)
                    <span class="select_search-value nobr" data-option="bv" data-value="{{$bv->id}}" data-name="{{$bv->name}}">{{$bv->name}}</span>
                  @endforeach
                  @foreach(getKlanten() as $klant)
                    <span class="select_search-value nobr" data-option="klant" data-value="{{$klant->id}}" data-name="{{$klant->title()}}">{{$klant->title()}}</span>
                  @endforeach
                  @foreach(getVestigingen() as $vestiging)
                    <span class="select_search-value nobr" data-option="vestiging" data-value="{{$vestiging->id}}" data-name="{{$vestiging->naam}}">{{$vestiging->naam}}</span>
                  @endforeach
                </div>
              </div>
            </div>
            <div class="col select_search-container" >
              <div class="w-100" data-label >Stadium</div>
              <input type="hidden" name="sort_stadium" class="select_search-hidden-input" data-placeholder="Selecteer stadium">
              <div class="select_search-values" >
                <div class="select_search-box rounded-9 font-size-085">
                  @if (hasPermission('Afgeronde facturen inzien'))
                    <span class="select_search-value nobr" data-value="" data-name="">Alle stadiums</span>
                  @endif
                  <span class="select_search-value nobr" data-value="Open" data-name="Open">Open</span>
                  @if (hasPermission('Afgeronde facturen inzien'))
                    <span class="select_search-value nobr" data-value="Afgerond" data-name="Afgerond">Afgerond</span>
                  @endif
                  <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
                </div>
              </div>
            </div>
            <div class="col select_search-container" >
              <div class="w-100" data-label >status</div>
              <input type="hidden" name="sort_status" class="select_search-hidden-input" data-placeholder="Selecteer status">
              <div class="select_search-values" >
                <div class="select_search-box rounded-9 font-size-085">
                  <span class="select_search-value nobr" data-value="" data-name="">Alle statussen</span>
                  <span class="select_search-value nobr" data-value="Uitgebracht" data-name="Uitgebracht">Uitgebracht</span>
                  @if(hasModule('Accorderen'))
                    <span class="select_search-value nobr" data-value="Accorderen" data-name="Accorderen">Accorderen</span>
                  @endif
                  <span class="select_search-value nobr" data-value="Verzonden" data-name="Verzonden">Verzonden</span>
                  @if(isset($isProforma))
                    <span class="select_search-value nobr" data-value="Akkoord" data-name="Akkoord">Akkoord</span>
                    <span class="select_search-value nobr" data-value="Afgewezen" data-name="Afgewezen">Afgewezen</span>
                    <span class="select_search-value nobr" data-value="Gefactureerd" data-name="Gefactureerd">Gefactureerd</span>
                  @else
                    @if (hasPermission('Afgeronde facturen inzien'))
                      <span class="select_search-value nobr" data-value="Betaald" data-name="{{getSettingValue('facturatie_alt_betaald_text', 'Betaald')}}">{{getSettingValue('facturatie_alt_betaald_text', 'Betaald')}}</span>
                    @endif
                    <span class="select_search-value nobr" data-value="Opgesplitst" data-name="Opgesplitst">Opgesplitst</span>
                  @endif
                  <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
                </div>
              </div>
            </div>
            @if (getSettingValue('exact_xml_export') == 'Aan' || getSettingValue('facturen_export_xml_regels') != null)
              <div class="col-auto" >
                <div class="w-100 opacity-0" data-label >Filler</div>
                <div class="dropdown w-100">
                  <button class="btn btn-light rounded-9 border" type="button" data-toggle="dropdown" aria-expanded="false">XML @icon_wrench</button>
                  <div class="dropdown-menu select_search-box rounded-9">
                    @if (getSettingValue('exact_xml_export') == 'Aan')
                      <a class="dropdown-item" onclick="showModal('exact-xml-export-modal')">Exact XML export</a>
                    @endif
                    @if(getSettingValue('facturen_export_xml_regels') != null && getSettingValue('facturen_export_xml_wrapper') != null)
                      <a class="dropdown-item" href="{{url('facturatie/facturen/export/xml')}}" >XML export (alle facturen)</a>
                    @endif
                  </div>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>
    </section>

    <section class="my-3" >
      <div class="flex-between">
        <div class="w-100 mx-3 dropdown-divider "></div>
        <h4 class="nobr m-0" data-facturen-count >
          <div class="text-placeholder-md" ></div>
        </h4>
        <div class="w-100 mx-3 dropdown-divider"></div>
      </div>
    </section>

    <section class="d-none mt-3" data-facturen-table-container>

      <div class="card rounded-pill rounded-m-unset">
        <div class="row flex-md-nowrap">
          <div class="col-md-8 col-12 flex-align flex-md-nowrap flex-wrap">
            <div class="flex-align mx-2 min-w-200 py-2" >
              <select class="form-select mr-2 rounded-pill" name="order_by">
                <option value="datum">Datum</option>
                <option value="betalingstermijn">{{isset($isProforma) ? 'Beslissingstermijn' : 'Betalingstermijn'}}</option>
                @if(getSettingValue('facturatie_factuur_reporting_date') == 'aan' && !isset($isProforma))
                  <option value="reporting_date">Rapportagedatum</option>
                @endif
                @isset($isProforma)
                  <option value="proforma_accepted_at">Ondertekend op</option>
                @else
                  <option value="betaald_op">{{getSettingValue('facturatie_alt_betaald_text', 'Betaald')}} op</option>
                @endif
                <option value="percentage">Percentage</option>
                <option value="sent_to">Verzonden aan</option>
                <option value="mail_geopend">Mail geopend</option>
                <option value="factuurnummer">Factuurnummer</option>
              </select>
              <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_factuur', 'DESC') == 'DESC') d-none @endif" data-order-direction="ASC" >@icon_asc</a>
              <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_factuur', 'DESC') == 'ASC') d-none @endif" data-order-direction="DESC" >@icon_desc</a>
              <input type="hidden" name="order_direction" value="{{_COOKIE('order_direction_factuur') ?? 'DESC'}}" >
            </div>
            <div class="form-control-divider mx-2"></div>
            <div class="ds-container flex-align bg-white shadow-none mx-2 px-0 py-2" data-display-select-container="pagination" data-can-be-empty >
              <select class="form-select mx-1 rounded-pill min-w-100" name="per_page">
                <option @if(($_COOKIE['facturen_per_page'] ?? null) === "25") selected @endif value="25" >25</option>
                <option @if(($_COOKIE['facturen_per_page'] ?? null) === "50") selected @endif value="50" >50</option>
                <option @if(($_COOKIE['facturen_per_page'] ?? null) === "100") selected @endif value="100" >100</option>
                <option @if(($_COOKIE['facturen_per_page'] ?? null) === "250") selected @endif value="250" >250</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 col-12 flex-align">
            <div class="form-control-divider mx-2 d-md-block d-none"></div>
            <a class="btn btn-light rounded-pill" onclick="initFacturen()" >@icon_redo</a>
            <div class="w-100 overflow-visible mx-2 py-2">
              <div class="w-100" >
                <input type="text" class="form-control-custom rounded-pill" placeholder="Zoeken..." name="search" >
                <div class="position-relative">
                  <div class="rounded-9 bg-white border font-size-085 p-2 position-absolute shadow w-100 z-index-9 d-none" data-search-results ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card my-2">
        <div class="overflow-auto py-2 my-2">
          <table class="facturen-table" >
            <thead>
              <tr>
                @php $data = getSettingJson('facturatie_factuuroverzicht_hide'); @endphp
                @if(!isset($data['datum']))
                <th>Datum</th>
                @endif
                @if(!isset($data['betalingstermijn']))
                <th>{{isset($isProforma) ? 'Beslissingstermijn' : 'Betalingstermijn'}}</th>
                @endif
                @if(getSettingValue('facturatie_factuur_reporting_date') == 'aan' && !isset($isProforma))
                    <th>Rapportagedatum</th>
                @endif
                @if(!isset($data['betaald_op']))
                <th class="nobr" >{{isset($isProforma) ? 'Ondertekend op' : (getSettingValue('facturatie_alt_betaald_text', 'Betaald').' op')}}</th>
                @endif
                <th>Factuurnummer</th>
                @if(getSettingValue('facturatie_project_weergave') == 'Aan')<th>Projectnummer</th>@endif
                @if(!isset($data['factuurbedrag']))
                <th>Factuurbedrag</th>
                @endif
                @if(!isset($data['klant']))
                <th>Klant</th>
                @endif
                @if(!isset($data['debiteurnummer']))
                <th>Debiteurnummer</th>
                @endif
                @if(!isset($data['referentie']))
                <th>Referentie klant</th>
                @endif
                @if(!isset($data['adres']))
                <th>Adres</th>
                @endif
                @if (getSettingValue('facturatie_overzicht_hide_factuuradres') != 'ja' && !isset($data['factuuradres']) )
                  <th>Factuuradres</th>
                @endif
                @if(!isset($data['door']))
                <th>Door</th>
                @endif
                @if(!isset($isProforma) && !isset($data['percentage']))
                  <th>Percentage</th>
                @endif
                <th>Status</th>
                @if(hasModule('Accorderen') && (isset($isProforma) && getSettingValue('accorderen_flow_proformas') || !isset($isProforma) && getSettingValue('accorderen_flow_facturen')))
                  <th>Akkoord</th>
                @endif
                <th>Verzonden aan</th>
                <th>Mail geopend</th>
                <th class="center">Acties</th>
              </tr>
            </thead>
            <tbody data-facturen-tbody ></tbody>
          </table>
        </div>
      </div>

    </section>

  </section>

  {{--  Modals--}}
  <secion>
    {{--    Email modal--}}
    <div class="modal fade" id="emailModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div style="width: 580px" class="modal-content bg-light">
          <form data-email-form="main" action="{{url("api/facturatie/facturen/send")}}" enctype="multipart/form-data" method="post">
            <div class="modal-body">

              <div id="loading-spinner" class="text-center my-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">Data word ingeladen...</span>
                </div>
              </div>

              <div id="email-modal-content" style="display: none">
                <div class="mb-2">
                  <div class="flex-between mb-2">
                    <div>
                      @if(hasModule('Bestanden'))
                        <a class="btn btn-inverse-dark rounded-pill text-white tippy" data-tippy-content="Bestanden" data-email-attach-btn="explorer"><i class="fas fa-folder-open m-0"></i></a>
                      @endif
                    </div>
                    <div>
                      <a class="btn btn-inverse-dark rounded-pill text-white" data-email-attach-btn="CC" >CC</a>
                      <a class="btn btn-inverse-dark rounded-pill text-white" data-email-attach-btn="BCC" >BCC</a>
                    </div>
                  </div>
                </div>
                <div>
                  <div data-email-attach-container="explorer" class="d-none bg-white border my-2 overflow-auto p-2 rounded" >
                    <label>Bestanden</label>
                    <div data-email-attach-content="explorer" ></div>
                    <div class="text-center my-2">
                      <a class="btn btn-inverse-primary tippy" data-tippy-content="Selecteren" onclick="openExplorer()">@icon_cloud</a>
                      <a class="btn btn-inverse-primary tippy" data-tippy-content="Uploaden" onclick="openUploadExplorer()">@icon_upload</a>
                    </div>
                  </div>
                </div>

                <div class="row">

                  <div class="col-md-6 col-12 my-2">
                    <label>Afzender*</label>
                    <input name="sender" placeholder="Afzender" type="text" class="form-control-custom" required>
                  </div>
                  <div class="col-md-6 col-12 my-2">
                    <label>Onderwerp*</label>
                    <input name="subject" placeholder="Onderwerp" class="form-control-custom">
                  </div>
                  <div class="col-12 my-2 select_edit-container">
                    <label>Aan</label>
                    <input type="text" autocomplete="off" name="email" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>">
                    <div class="select_edit-values">  <!-- class 'show' to display the dropdown-->
                      <div class="select_edit-box" data-klant-emails ></div>
                    </div>
                  </div>
                  <div class="col-12 my-2 select_edit-container d-none" data-email-attach-container="CC">
                    <label>Cc</label>
                    <input type="text" autocomplete="off" name="ccemail" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>">
                    <div class="select_edit-values">  <!-- class 'show' to display the dropdown-->
                      <div class="select_edit-box" data-klant-cc-emails ></div>
                    </div>
                  </div>
                  <div class="col-12 my-2 select_edit-container d-none" data-email-attach-container="BCC">
                    <label>Bcc</label>
                    <input type="text" autocomplete="off" name="bccemail" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>">
                    <div class="select_edit-values">  <!-- class 'show' to display the dropdown-->
                      <div class="select_edit-box" data-klant-bcc-emails ></div>
                    </div>
                  </div>
                  {{--                <div class="col-md-6 col-12 my-2">--}}
                  {{--                  <label>Klant</label>--}}
                  {{--                  <input name="klant" type="text" placeholder="Klant" class="form-control-custom">--}}
                  {{--                </div>--}}

                  <div class="col-12 my-2">
                    <label>Inhoud*</label>
                    <textarea name="mess" id="message" class="mt-2 editor"></textarea>
                  </div>

                  <div class="col-12">
                    @if(!isset($isProforma) && getSettingValue('mollie_key') != null)
                      <div>
                        <label class="cursor-pointer my-1"><input  type="checkbox" value="on" name="mollie" class="form-check-custom" > Betaallink meesturen</label>
                      </div>
                    @endif
                    @if(hasModule('Offertes') && getSettingValue('facturatie_factuur_send_hide_offerte') != 'ja')
                      <div>
                        <label class="cursor-pointer my-1"><input type="checkbox" value="on" name="offerteAttach" class="form-check-custom"> Offerte toevoegen als bijlage</label>
                      </div>
                    @endif
                    @if(hasModule('Werkbonnen') && getSettingValue('facturatie_factuur_send_hide_werkbonnen') != 'ja')
                      <div data-wAttach></div>
                    @endif
                    @if(!isset($isProforma) && getSettingValue('facturatie_factuur_reporting_date') == 'aan' && getSettingValue('facturatie_factuur_reporting_date_overwrite') == 'aan')
                      <div>
                        <label class="cursor-pointer my-1"><input type="checkbox" value="on" name="customReportingDate" class="form-check-custom" > Aangepaste rapportagedatum</label>
                        <input type="date" class="form-control-custom d-none" name="customReportingValue" value="" disabled >
                      </div>
                    @endif
                  </div>
                  <div data-attachments-proforma ></div>
                  <div data-sent-emails ></div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <a class="btn btn-success text-white" data-send-email >Versturen</a>
              <input type="hidden" name="factuurId">
              <input type="hidden" name="klantId">
              <input type="hidden" name="reminder">
              @csrf
            </div>
          </form>
        </div>
      </div>
    </div>

    {{--  Doc modal--}}
    <div class="modal fade" id="doc-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div id="doc-content" class="p-2"></div>
          </div>
        </div>
      </div>
    </div>

    {{--  Snelstart--}}
    <div class="modal fade" id="snelstart-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div id="snelstart-flow" ></div>
          </div>
        </div>
      </div>
    </div>

    {{--  Snelstart grootboeken--}}
    <div class="modal fade" id="snelstart-grootboeken" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div id="snelstart-grootboeken-regels"></div>
          </div>
          <div class="modal-footer" id="snelstart-grootboeken-footer" ></div>
        </div>
      </div>
    </div>

    {{--  downloadmodal exact xml--}}
    <div class="modal fade" id="exact-xml-export-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div class="flex-center mb-2 w-100">
              <label for="exactxmlvan">Van :</label>
              <input type="date" class="form-control-custom mx-2" id="exactxmlvan" style="width: 300px;">
              <label for="exactxmltot">Tot :</label>
              <input type="date" class="form-control-custom mx-2" id="exactxmltot" style="width: 300px;">
            </div>
            <div class="mb-2 center d-none" id="facturen-download">
              <span id="klanten-inzien-link" class="btn w-50 btn-primary mb-2">Opgehaalde data inzien</span>
              <a id="facturen-download-link" class="btn w-50 btn-primary">Facturen XML Downloaden</a>
            </div>
            <div class="mb-2 center d-none" id="klanten-download">
              <a id="klanten-download-link" class="btn w-50 btn-primary">Klanten XML Downloaden</a>
            </div>
            <div id="datasets-download" class="center d-none">
              <a id="datasets-download-link" class="btn w-50 btn-primary">Dataset XML Downloaden</a>
              <div class="flex-center">
                <select class="form-select mt-2 w-50" name="dataset" id="dataset">
                  @foreach ($datasets as $dataset)
                    <option value="{{$dataset->id}}">{{$dataset->naam}}</option>
                  @endforeach
                </select>
              </div>
            </div>
            <div class="mt-3 border-top d-none" id="exact-xml-show-results">
              <table>
                <thead>
                  <tr>
                    <th>Factuurnummer</th>
                    <th>Factuurdatum</th>
                    <th>Klant</th>
                    <th>Export-status Klant</th>
                  </tr>
                </thead>
                <tbody id="exact-xml-results">
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </secion>

@endsection
@section('script')
  <script>

    var facturen = [];
    let noload = getParameter('filter');

    const modules = {
      accorderen: @json(hasModule('Accorderen')),
      bestanden: @json(hasModule('Bestanden')),
      offertes: @json(hasModule('Offertes')),
      projecten: @json(hasModule('Projecten')),
      werkbonnen: @json(hasModule('Werkbonnen')),
    }

    const permissions = {
      eg_facturen_uploaden: @json(hasPermission('Facturen uploaden', 'Exact Globe')),
      eg_klanten_uploaden: @json(hasPermission('Klanten uploaden', 'Exact Globe')),
      bestanden_uploaden: @json(hasPermission('Bestanden uploaden', 'Bestanden')),
      facturen_verwijderen: @json(hasPermission('Facturen verwijderen')),
    }
    const settings = {
      exact_xml_export: @json(getSettingValue('exact_xml_export')),
      snelstart_grootboeken: @json($snelstartGrootboeken),
      eboekhouden_grootboeken: @json(eboekGrootboeken()),
      eboek_standaard_grootboek: @json(getSettingValue('facturatie_eboekhouden_default_gb')),
      eboekhouden_btws: @json(eboekBtwCodes()),
      hide_adres: @json(getSettingValue('facturatie_overzicht_hide_factuuradres')),
      reporting_date: @json(getSettingValue('facturatie_factuur_reporting_date')),
      reporting_date_default: @json(getSettingValue('facturatie_factuur_reporting_date_default')),
      reporting_date_overwrite: @json(getSettingValue('facturatie_factuur_reporting_date_overwrite')),
      facturatie_project_weergave: @json(getSettingValue('facturatie_project_weergave')),
      factuur_opsplitsen_naam: @json(getSettingValue('factuur_opsplitsen_naam')),
      factuur_opsplitsen_datum: @json(getSettingValue('factuur_opsplitsen_datum')),
      factuur_standaard_splitsing: @json(json_decode(getSettingValue('factuur_standaard_splitsing'))),
      factuur_overzicht_hide: @json(getSettingJson('facturatie_factuuroverzicht_hide')),
      facturatie_alt_betaald_text: @json(getSettingValue('facturatie_alt_betaald_text', 'Betaald')),
      facturatie_werkbonnen_sturen: @json(getSettingValue('facturatie_factuur_send_always_werkbonnen') == 'ja'),
    }

    const _base = {
      request: null,
      table: null,
      total_count: 0,
      tbody: $('[data-facturen-tbody]'),
      dsi: null,
      all_ids: [],
      current_ids: [],
      per_page: () => { return $('[name=per_page]').val() },
      page: () => { return _displaySelect['pagination'].value },
      sort: () => { return $('[name=sort_by]').val() },
      order_by: () => { return $('[name=order_by]').val(); },
      order_direction: () => { return $('[name=order_direction]').val(); },
      sort_option: () => { return $('[name=sort_option]').val() },
      sort_status: () => { return $('[name=sort_status]').val() },
      sort_stadium: () => { return $('[name=sort_stadium]').val() },

      is_proforma: @json(isset($isProforma)),
    }
    const _search = {
      request: null,
      container: $('[data-search-results]'),
      input: $('[name=search]'),
      timeout: null,
      results: [],
      ids: [],
      is_loading: () => { return !!($('[data-search-results]').find('[data-spinner]').length) }
    }

    const editors = {
      email: null,
    }
    const _batch = {
      state: false,
      selected: {},
      editors: {},
    }
    const _tables = {}
    const _email = {
      factuur: null,
      reminder: $("input[name=reminder]"),
      klantId: $("input[name=klantId]"),
      email: $("input[name=email]"),
      ccemail: $("input[name=ccemail]"),
      bccemail: $("input[name=bccemail]"),
      emails: $('[data-klant-emails]'),
      ccemails: $('[data-klant-cc-emails]'),
      bccemails: $('[data-klant-bcc-emails]'),
      sender: $("input[name=sender]"),
      subject: $("input[name=subject]"),
    }
    const _eg = {
      instance: new ExactGlobe(),
      ledgers: @json(exactGlobe()->getStoredGL()),
      vat: @json(exactGlobe()->getStoredVat()),
      journals: @json(exactGlobe()->getStoredJournals()),
      klant: {
        btn: null,
        spinner: null,
        errors: null,
      },
      factuur: {
        btn: null,
        spinner: null,
        errors: null,
      },
      settings: {
        facturen_only_this_year: @json(getSettingCheckbox('facturatie_exact_globe_only_this_year')),
      },
    }
    const _eo = {
      instance: new ExactOnline(),
    }
    const _bc = {
      instance: new BusinessCentral(),
    }
    const _king = {
      instance: new King(),
    }
    const _snelstart = {
      connected_bvs: @json(getSnelstartBvs()),
      settings: {},
    }

    pageInteractive(() => {
      syncFacturen();
      editorInit("#message").then((editor) => editors.email = editor);

      _base.dsi = _displaySelect['pagination'];
      _base.dsi.onchange = page => { initFacturen({page: page}) }
    })
    pageComplete(() =>{
      setTimeout(() => {
      }, 500)
      prefillGetValues();
    });


    $(document).on('click', '[data-batch-init]', function(){
      _batch.state = !_batch.state;

      $(`[data-batch-init='main']`).toggleClass(['btn-light', 'btn-dark'])
      $('[data-batch-init-container]').toggle(250);

      if(!_batch.state){
        _batch.selected = {};
        $('[data-batch-checkbox-toggle]').toggle(250);
        $('[data-batch-amount]').html(0);

        setTimeout(() => {
          $('[data-batch-checkbox-container]').remove();
        }, 250)

        return
      }

      $('.facturen-table').find('tr').each(function(){
        const id = $(this).data('id');
        const factuur = facturen.find(factuur => factuur.id == id);

        //Append checkbox when: It's select all, !Accorderen module or Has accorderen and succeeded
        let checkbox = '<input type="checkbox" class="form-check-custom mx-3" disabled >'
        if(!id){
          checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="all" >`
        }
        else if(!factuur?.accorderen?.isActive || factuur?.accorderen?.succeeded){
          checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="${id}" >`
        }

        $(this).prepend(`
          <td data-batch-checkbox-container class="p-0 w-0" > <div data-batch-checkbox-toggle style="display: none"> ${checkbox} </div> </td>
        `)
      })

      $('[data-batch-checkbox-toggle]').toggle(250);

    });
    $(document).on('click', '[data-batch-preview-btn]', function(){
      fillBulkModal();
    })
    async function fillBulkModal(){
      loader()

      if(!Object.keys(_batch.selected).length){
        notification('Geen facturen geselecteerd!', 'warning');
        return;
      }

      const mollie = @json(getSettingValue('mollie_key') != null);
      const werkbon = @json(hasModule('Werkbonnen') && getSettingValue('facturatie_factuur_send_hide_werkbonnen') != 'ja');

      let tabs = '';

      const { reporting_date, reporting_date_overwrite } = settings;
      if(reporting_date == 'aan' && reporting_date_overwrite == 'aan'){
        tabs += `
          <div class="max-w-300 my-2" >
            <label class="flex-between" >
              <span>Aangepaste rapportagedatum</span>
              <input type="checkbox" value="on" name="customReportingDate" class="form-switch-custom">
            </label>
            <input type="date" class="form-control-custom d-none" name="customReportingValue" data-batch-reporting-date disabled >
          </div>
        `
      }

      for(const id in _batch.selected){
        const factuur = facturen.find(factuur => factuur.id == id);

        if(!factuur){ continue; }

        const { is_proforma, factuurnummer } = factuur;
        const project = factuur._projecten.length ? await getProject(JSON.parse(factuur.projecten)[0], { relations: ['vestiging', 'manager', 'custom'] }) : null;

        const klant = factuur.klant;
        const proformas = JSON.parse(factuur.proformas || '[]').length

        let content_factuur =  @json(getSettingValue('factuur_email_content'));
        let content_proforma =  @json(getSettingValue('proforma_email_content'));
        let content = Number(factuur.is_proforma) ? content_proforma : content_factuur;
        let sender =  Number(factuur.is_proforma) ? @json(getSettingValue('proforma_afzender')) : @json(getSettingValue('factuur_afzender'));
        if(content){
          if(klant){
            content = content.replace("&lt;{klant}&gt;", klant.contactpersoon_achternaam)
          }
          content = content.replace("&lt;{bv}&gt;", factuur._bv ? factuur._bv.name : '')
          content = content.replace("&lt;{user}&gt;", _user.name)
          content = content.replace("&lt;{user-email}&gt;", _user.email || '')
          content = content.replace("&lt;{user-phone}&gt;", _user.phone || '')

          const vestigingData = {
            adres: '',
            postadres: '',
            phone: '',
          }

          const managerData = {
            phone: '',
            email: '',
          }

          if(project){
            const { vestiging, manager } = project;
            if(vestiging){
              const { straat, huisnummer, toevoeging, postcode, plaats, telefoon} = vestiging;
              const { postadres_straat, postadres_huisnummer, postadres_toevoeging, postadres_postcode, postadres_plaats} = vestiging;
              vestigingData.adres = `${straat || ''} ${huisnummer || ''}${toevoeging || ''}<br>${postcode || ''} ${plaats || ''}`
              vestigingData.postadres = `${postadres_straat || ''} ${postadres_huisnummer || ''}${postadres_toevoeging || ''},<br>${postadres_postcode || ''} ${postadres_plaats || ''}`
              vestigingData.phone = telefoon || '';
            }

            if(manager){
              const { email, phone } = manager;
              managerData.phone = phone || '';
              managerData.email = email || '';
            }
          }

          content = content.replace("&lt;{project-vestiging-adres}&gt;", vestigingData.adres)
          content = content.replace("&lt;{project-vestiging-postadres}&gt;", vestigingData.postadres)
          content = content.replace("&lt;{project-vestiging-phone}&gt;", vestigingData.phone)
          content = content.replace("&lt;{project-manager-phone}&gt;", managerData.phone)
          content = content.replace("&lt;{project-manager-email}&gt;", managerData.email)
          editors.email.setData(content || '');
        }
        let mailto = '';
        if(klant){
          mailto = `${klant.factuuremail ? klant.factuuremail : klant.email || ''}`;
        }
        if(project?.custom?.contactpersoon_proformas && Number(is_proforma)){
          const cp = factuur.klant.contactpersonen.find(cp => cp.id == project.custom?.contactpersoon_proformas.value);
          if(cp){
            mailto = `${cp.email || ''}`;
          }
        }
        tabs += `<div class="py-1 form-tab" data-batch-tab="${factuur.id}" >
                    <div class="border rounded bg-white" >

                      <div class="flex-between px-2" >
                        <div class="d-flex align-items-center " >
                            <div data-batch-tab-indicator></div>
                            <span>${factuur.factuurnummer ?? 'n.t.b.'}</span>
                        </div>
                        <div class="flex-align">
                          <a href="${url}/facturatie/facturen/pdf/${factuur.id}" target="_blank" class="btn btn-inverse-primary btn-sm py-1 rounded-pill" >PDF</a>
                          <a class="btn" data-preview-tab-toggle >@icon_down</a>
                        </div>
                      </div>

                      <form data-preview-form style="display: none" class="w-100">
                        <div class="row m-0" style="background-color: rgba(0, 0, 0, 0.03)">
                          <div class="col-md-6 col-12 my-2">
                            <label>Afzender*</label>
                            <input data-sender name="sender" class="form-control-custom" placeholder="Afzender" value="${sender || ''}" >
                          </div>
                          <div class="col-md-6 col-12 my-2">
                            <label>Aan*</label>
                            <input data-email name="email" class="form-control-custom" placeholder="<EMAIL>, <EMAIL>" value="${mailto}" >
                          </div>
                          <div class="col-md-6 col-12 my-2">
                            <label>Klant</label>
                            <input data-klant name="klant" class="form-control-custom" placeholder="Klant" value="${klant ? (klant.naam || `${klant.contactpersoon_voornaam || ''} ${factuur.klant.contactpersoon_achternaam || ''}`) : ''}" >
                          </div>
                          <div class="col-md-6 col-12 my-2">
                            <label>Onderwerp*</label>
                            <input data-subject name="subject" class="form-control-custom" placeholder="Onderwerp" value="${Number(is_proforma) ? 'Pro forma' : 'Factuur'}: ${factuurnummer ?? '<{factuurnummer}>'}" >
                          </div>
                          <div class="col-12 my-2">
                            <label>Inhoud</label>
                            <textarea data-content data-id="${factuur.id}" name="mess" class='batch-editor-${factuur.id}' placeholder="Inhoud" >${content || ''}</textarea>
                          </div>
                          ${ !Number(factuur.is_proforma) && mollie
          ? `<div> <label class="cursor-pointer"><input  type="checkbox" value="on" name="mollie" class="form-check-custom" > Betaallink meesturen</label></div>`
          : ``
        }
                          ${ werkbon
          ? `<div> <label class="cursor-pointer col-form-label"><input type="checkbox" value="on" name="wAttach" class="form-check-custom" > Werkbonnen toevoegen aan bijlage</label> </div>`
          : ``
        }
                          ${ proformas
          ? `<label class="cursor-pointer"> <input type="checkbox" ${_settings.facturatie_send_default_attach_proforma == 'ja' ? 'checked' : 'nee'} value="on" class="form-check-custom" name="proforma_attachment"> Pro Forma's toevoegen aan bijlagen. </label>`
          : ``
        }

                          <input type="hidden" name="klantId" value="${factuur.klant_id}" >
                          <input type="hidden" name="factuurId" value="${factuur.id}" >

                        </div>
                      </form>

                    </div>
                </div>`
        successLoader()
      }

      tabs += '<div class="my-2 text-right" data-batch-action  > <a class="btn btn-success text-white" data-batch-send> Versturen </a> </div>'

      confirmModal({
        text: tabs,
        hideFooter: true,
        large: true,
      })
        .then(() => {
          $(`[data-batch-init='main']`).trigger('click');
        })

      $('[data-preview-form] [data-content]').each(function(){
        const id = $(this).data('id');
        editorInit(`.batch-editor-${id}`)
          .then(editor => _batch.editors[id] = editor);
      })
    }
    $(document).on('click', '[data-preview-tab-toggle]', function(){
      const container = findContainer('form-tab', this);

      container.find('[data-preview-tab-toggle]').rotate(180)
      container.find('[data-preview-form]').toggle(250);
    });
    $(document).on('click', '[data-batch-send]', async function(){

      let ready = true;
      const forms = {};
      $('[data-batch-tab-indicator]').empty();

      for(const id in _batch.selected){
        const factuur =  facturen.find(factuur => factuur.id == id);
        const container = $(`[data-batch-tab="${factuur.id}"]`);
        const reporting_date = $('[data-batch-reporting-date]').val();

        let data = container.find('[data-preview-form]').serializeArray();

        data.push({
          name: '_token',
          value: csrf
        })
        data.find(row => row.name == 'mess').value =_batch.editors[id].getData();

        if(reporting_date){
          data.push({
            name: 'customReportingValue',
            value: reporting_date
          });
        }

        const requiredSet = {
          email: false,
          sender: false,
          subject: false,
          mess: false,
        }
        for(const input of data){
          if(input.name == 'email' && input.value != ''){ requiredSet.email = true; }
          if(input.name == 'sender' && input.value != ''){ requiredSet.sender = true; }
          if(input.name == 'subject' && input.value != ''){ requiredSet.subject = true; }
          if(input.name == 'mess' && input.value != ''){ requiredSet.mess = true; }
        }

        if(!requiredSet.email || !requiredSet.sender || !requiredSet.subject || !requiredSet.mess){
          ready = false;
          container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-danger" > @icon_close </div>')
        }

        forms[factuur.id] = data;

      }

      if(!ready){
        notification('Vul alle verplichte velden in!', 'warning', 5);
        return;
      }

      for(const id in _batch.selected){

        if(_batch.selected[id] == 'sending'){ continue }

        _batch.selected[id] = 'sending'

        const container = $(`[data-batch-tab="${id}"]`);
        const factuur = facturen.find(factuur => factuur.id == id);
        const data = forms[id];

        container.find('[data-batch-tab-indicator]').html('<div class="mx-2" > @spinner_small </div>')

        try{
          const response = await ajax('api/facturatie/facturen/send', data);
          delete _batch.selected[id];
          $(`[data-batch-tab=${id}]`).remove();
          notification(`${Number(factuur.is_proforma) ? 'Pro forma' : 'Factuur'} <b>${factuur.factuurnummer ?? 'n.t.b.'}</b> verstuurd aan ${response.emails.join(', ')}!`, 'success', 5);
          initFacturen();
        }
        catch(err){
          _batch.selected[id] = 'true'
          container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-warning" > @icon_exclamation </div>')
        }

      }
    });
    $(document).on('change', '[data-batch-checkbox]', function(){
      const id = $(this).data('batch-checkbox');
      const checked = $(this).prop('checked');

      if(id == 'all'){

        //No need to verify status when unchecking, uncheck all and return
        if(!checked){
          $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').prop('checked', false).trigger('change');
          return;
        }

        $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').each(function(){
          const id = $(this).data('batch-checkbox');
          const factuur = facturen.find(factuur => factuur.id == id);

          if(factuur && factuur.status == 'Verzonden'){ return true; }

          $(this).prop('checked', checked).trigger('change')
        });

        return;
      }

      delete _batch.selected[id];
      if(checked){
        _batch.selected[id] = true
      }

      $('[data-batch-amount]').html(Object.keys(_batch.selected).length);
    });

    $(document).on('change', '[data-credit-row-switch]', function(){
      const toggle = $(this);
      const row = findContainerByAttr('data-credit-row', this);

      row.find(`input`).not(toggle).prop('disabled', !toggle.prop('checked'));
    });
    $(document).on('change', '.percInput', calcPerc);
    $(document).on('click', '.percDelete', calcPerc);

    $(document).on('click', function(){
      _search.container.addClass('d-none').empty();
    });
    $(document).on('click', '[data-search-by-results]', function(){
      const target = $(this).data('search-by-results');

      $('[name=sort_by]').val('all');
      $('[data-sort-option-container]').find('.select_search-input').val('').prop('disabled', true);
      $('[data-sort-option-container]').find('.select_search-hidden-input').val('');

      $('[name=sort_stadium]').val('')
      $('[name=sort_status]').val('')

      correctSelectSearchDisplay();

      _search.ids = [];
      if (target == 'all'){
        _search.results.map(factuur => _search.ids.push(Number(factuur.id)));
      }
      else{
        _search.ids.push(target);
      }

      initFacturen();
    });
    $(document).on('input', '.select_search-hidden-input', correctSelectSearchDisplay);

    $(document).on('change', '[name=customReportingDate]', function(){
      const checked = $(this).prop('checked');
      $('[name=customReportingValue]').toggleClass("d-none", !checked).prop('disabled', !checked);
    })

    $('[data-order-direction]').click(function(){
      const selected = $(this).data('order-direction');
      const target = selected == 'ASC' ? 'DESC' : 'ASC';

      $('[data-order-direction]').addClass('d-none');
      $(`[data-order-direction=${target}]`).removeClass('d-none');
      $('[name=order_direction]').val(target).trigger('change');

      setCookie('order_direction_factuur', target)
    });
    $('[name=search]').on('input', async function(){
      let { container, timeout, is_loading } = _search;

      if (timeout){
        clearTimeout(timeout);
        timeout = null;
      }

      if(!is_loading()){
        container.removeClass('d-none').html(`<div class="text-center py-2" data-spinner >@spinner</div>`);
      }

      if(!this.value){
        container.addClass('d-none').empty();
        _search.ids = null;
        _search.input.val('');
        prefillGetValues();
        return;
      }


      _search.timeout = setTimeout(findFacturen, 100);
    })
    $('[name=sort_by]').change(function(){
      const value = this.value || 'all';

      _search.ids = null;
      _search.input.val('');


      const container = $('[data-sort-option-container]');
      container.find('.select_search-input').val('').prop('disabled', false);
      container.find('.select_search-value').addClass('d-none');

      if(value == 'all'){
        initFacturen()
        container.find('.select_search-input').val('').prop('disabled', true);
        return;
      }

      container.find(`[data-option=${value}]`).removeClass('d-none');
    });
    $('[name=per_page], [name=order_by], [name=order_direction]').change(function(){
      setCookie('facturen_per_page', _base.per_page());
      initFacturen()
    });
    $('[name=sort_option], [name=sort_status], [name=sort_stadium]').change(() => {
      _search.ids = null;
      _search.input.val('');

      initFacturen()
    })

    $('#klanten-inzien-link').on('click', function(e){
      getFacturen({
        start: $('#exactxmlvan').val(),
        end: $('#exactxmltot').val(),
        exact_xml_export: true,
      }).then((response) => {
        $('#exact-xml-results').empty();
        if (response.facturen.length == 0) {
          $('#exact-xml-results').append(`
            <tr>
              <td colspan="4">Geen facturen gevonden</td>
            </tr>
          `);
          $('#exact-xml-show-results').removeClass('d-none');
          return;
        }
        for(const factuur of response.facturen){
          let facdate = now(factuur.datum);
          $('#exact-xml-results').append(`
            <tr>
              <td>${factuur.factuurnummer ?? 'n.t.b.'}</td>
              <td>${facdate.day}-${facdate.month}-${facdate.year}</td>
              ${factuur.klant
                ? `<td>${factuur.klant.naam ?? `${factuur.klant.contactpersoon_voornaam} ${factuur.klant.contactpersoon_achternaam}`}</td>
                  <td><span class="badge badge-${Number(factuur.klant.xml_exported) ? 'success' : 'danger'}" >${Number(factuur.klant.xml_exported) ? 'Geëxporteerd' : 'Nog niet geëxporteerd'}</span></td>`
                : `<td colspan="2">Klant niet gevonden</td>`
              }

            </tr>
          `);
        }
        $('#exact-xml-show-results').removeClass('d-none');
      });
    });

    $('#facturen-download-link').on('click', function(){
      $(this).addClass('pointer-event-none');
      $(this).addClass('btn-secondary');
      $(this).removeClass('btn-primary');
    });
    $('#klanten-download-link').on('click', function(){
      $(this).addClass('pointer-event-none');
      $(this).addClass('btn-secondary');
      $(this).removeClass('btn-primary');
    });
    $('#datasets-download-link').on('click', function(){
      $(this).addClass('pointer-event-none');
      $(this).addClass('btn-secondary');
      $(this).removeClass('btn-primary');
    });

    $('#exactxmlvan').change(function(){
      var vantot = '/' + $('#exactxmlvan').val() + '/' + $('#exactxmltot').val();
      var dataset = '/' + $('#dataset').val();
      var facturenlink = `${url}/facturatie/facturen/exact/xml/download`;
      var klantenlink = `${url}/facturatie/klanten/exact/xml/download`;
      var datasetslink = `${url}/facturatie/datasets/exact/xml/download`;
      $('#facturen-download-link').attr("href", facturenlink+vantot);
      $('#klanten-download-link').attr("href", klantenlink+vantot);
      $('#datasets-download-link').attr("href", datasetslink+vantot+dataset);
      $('#facturen-download-link').removeClass('pointer-event-none');
      $('#klanten-download-link').removeClass('pointer-event-none');
      $('#datasets-download-link').removeClass('pointer-event-none');
      $('#facturen-download-link').removeClass('btn-secondary');
      $('#facturen-download-link').addClass('btn-primary');
      $('#datasets-download-link').removeClass('btn-secondary');
      $('#datasets-download-link').addClass('btn-primary');
      $('#facturen-download-link').removeClass('btn-secondary');
      $('#facturen-download-link').addClass('btn-primary');
      if($('#exactxmlvan').val() == '' || $('#exactxmltot').val() == ''){
        $('#facturen-download').addClass("d-none");
        $('#klanten-download').addClass("d-none");
        $('#datasets-download').addClass("d-none");
      }else{
        $('#facturen-download').removeClass("d-none");
        $('#klanten-download').removeClass("d-none");
        $('#datasets-download').removeClass("d-none");
      }
    });
    $('#exactxmltot').change(function(){
      var vantot = '/' + $('#exactxmlvan').val() + '/' + $('#exactxmltot').val();
      var dataset = '/' + $('#dataset').val();
      var facturenlink = `${url}/facturatie/facturen/exact/xml/download`;
      var klantenlink = `${url}/facturatie/klanten/exact/xml/download`;
      var datasetslink = `${url}/facturatie/datasets/exact/xml/download`;
      $('#facturen-download-link').attr("href", facturenlink+vantot);
      $('#klanten-download-link').attr("href", klantenlink+vantot);
      $('#datasets-download-link').attr("href", datasetslink+vantot+dataset);
      $('#facturen-download-link').removeClass('pointer-event-none');
      $('#klanten-download-link').removeClass('pointer-event-none');
      $('#datasets-download-link').removeClass('pointer-event-none');
      $('#facturen-download-link').removeClass('btn-secondary');
      $('#facturen-download-link').addClass('btn-primary');
      $('#datasets-download-link').removeClass('btn-secondary');
      $('#datasets-download-link').addClass('btn-primary');
      $('#facturen-download-link').removeClass('btn-secondary');
      $('#facturen-download-link').addClass('btn-primary');
      if($('#exactxmlvan').val() == '' || $('#exactxmltot').val() == ''){
        $('#facturen-download').addClass("d-none");
        $('#klanten-download').addClass("d-none");
        $('#datasets-download').addClass("d-none");
      }else{
        $('#facturen-download').removeClass("d-none");
        $('#klanten-download').removeClass("d-none");
        $('#datasets-download').removeClass("d-none");
      }
    });
    $('#dataset').change(function(){
      var vantot = '/' + $('#exactxmlvan').val() + '/' + $('#exactxmltot').val();
      var dataset = '/' + $('#dataset').val();
      var facturenlink = `${url}/facturatie/facturen/exact/xml/download`;
      var klantenlink = `${url}/facturatie/klanten/exact/xml/download`;
      var datasetslink = `${url}/facturatie/datasets/exact/xml/download`;
      $('#facturen-download-link').attr("href", facturenlink+vantot);
      $('#klanten-download-link').attr("href", klantenlink+vantot);
      $('#datasets-download-link').attr("href", datasetslink+vantot+dataset);
      if($('#exactxmlvan').val() == '' || $('#exactxmltot').val() == '' || $('#dataset').val() == ''){
        $('#facturen-download').addClass("d-none");
        $('#klanten-download').addClass("d-none");
        $('#datasets-download').addClass("d-none");
      }else{
        $('#facturen-download').removeClass("d-none");
        $('#klanten-download').removeClass("d-none");
        $('#datasets-download').removeClass("d-none");
      }
    });

    $('[data-email-attach-btn]').click(function(){
      const btn = $(this);
      const target = btn.data('email-attach-btn');
      const container = $(`[data-email-attach-container='${target}']`);

      btn.toggleClass(['btn-inverse-dark', 'btn-dark']);
      container.toggleClass('d-none');

      if((target == 'BCC' || target == 'CC') && container.hasClass('d-none')){
        container.find('.select_edit-input').val('');
      }
    })
    $('[data-send-email]').click(() => {
      this.disabled = true;
      const data = $('[data-email-form]').serializeArray();
      const id = data.find(row => row.name == 'factuurId').value;
      const factuur = facturen.find(factuur => factuur.id == id)


      data.find(row => row.name == 'mess').value = editors.email.getData();

      const requiredSet = {
        email: false,
        sender: false,
        subject: false,
        mess: false,
      }
      for(const input of data){
        if(input.name == 'email' && input.value != ''){ requiredSet.email = true; }
        if(input.name == 'sender' && input.value != ''){ requiredSet.sender = true; }
        if(input.name == 'subject' && input.value != ''){ requiredSet.subject = true; }
        if(input.name == 'mess' && input.value != ''){ requiredSet.mess = true; }
      }

      if(!requiredSet.email || !requiredSet.sender || !requiredSet.subject || !requiredSet.mess){
        notification('Vul alle verplichte velden in!', 'danger', 5);
        return;
      }

      loader();
      hideModal('emailModal')
      ajax('api/facturatie/facturen/send', data)
              .then((response) => {
                notification(`${Number(factuur.is_proforma) ? 'Pro forma' : 'Factuur'} verstuurd aan ${response.emails.join(', ')}!`, 'success', 5);
                initFacturen();
              })
              .catch(err => {
                handleCatchError(err, '[data-send-email]')
              });
              this.disabled = false;

    })

    function prefillGetValues(){
      if (getParameter('sort_by') && getParameter('sort_option')){
        $('[name=sort_by]').val(getParameter('sort_by')).trigger('change');
        $(`[data-option=${getParameter('sort_by')}][data-value=${getParameter('sort_option')}]`).click();
      }
      if (getParameter('status')){
        $('[name=sort_status]').val(getParameter('status'));
      }
      if (getParameter('stadium')){
        $('[name=sort_stadium]').val(getParameter('stadium'));
      }

      correctSelectSearchDisplay();
      initFacturen();
    }
    function initFacturen(options){
      if (noload){
        $('[data-facturen-table-container]').removeClass('d-none');
        noload = false;
        return;
      }
      loader();

      const page = options?.page || 1;

      const data = {
        ids: _search.ids,
        page: page,
        proforma: Number(_base.is_proforma),
        paginate: _base.per_page(),
        status: _base.sort_status(),
        stadium: _base.sort_stadium(),
        sort_by: _base.order_by(),
        sort_type: _base.order_direction()
      }

      if(_base.sort() && _base.sort_option()){
        data[_base.sort()] = _base.sort_option();
      }
      if(_base.request){
        _base.request.abort();
        _base.request = null;
      }

      _base.request = ajax('/api/facturatie/get', data);
      _base.request
        .always(() => {
          _base.request = null;
        })
        .then(response => {
          facturen = response.facturen;
          _base.total_count = response.total_count;
          _base.all_ids = response.all_ids;
          _base.current_ids = response.current_ids;

          fillFacturen();
          refreshPagination(options?.page);
          clearLoader();
        })
        .catch(err => {
          handleCatchError(err, 'initFacturen()')
        });
    }
    function fillFacturen(){
      const { table, tbody, is_proforma } = _base;

      if(table){ table.destroy(); }

      tbody.empty();

      for (const factuur of facturen) {
        const { id, created_at, factuurnummer, datum, betalingstermijn, betaald_op, credit_factuur, slice_facturen, slice_parent_factuur, credit_parent_factuur, tot_incl, tot_excl, tot_btw, klant, referentie,
                adres, user, percentage, status, color, accorderen, sent_at, sent_to, mail_geopend, snelstart_id, eboekhouden, exact_globe_id, xml_export, files, factuur_files,
                _proformas, _offertes, _projecten, _werkbonnen, token, stadium, emails, credit, proforma_accepted, proforma_accepted_at, reporting_date, business_central_id, business_central_credit_memo_id,
                exact_online_metadata, king_uploaded, has_budget, bv } = factuur

        tbody.append(`
          <tr class="factuur-${factuurnummer ?? 'n.t.b.'} factuur-${id}" data-id="${id}">
            ${settings.factuur_overzicht_hide['datum'] ? '' : `<td data-factuur-datum="${id}" data-sort="${now(created_at).timestamp}" ><nobr>${now(datum).date.eu}</nobr></td>`}
            ${settings.factuur_overzicht_hide['betalingstermijn'] ? '' : `<td data-sort="${now(betalingstermijn).timestamp}" >${now(betalingstermijn).date.eu}</td>`}
            ${settings.reporting_date == 'aan' && !is_proforma
              ? `<td ${reporting_date ? `data-sort="${now(reporting_date).timestamp}"` : ''} class="nobr" >${reporting_date ? now(reporting_date).date.eu : ''}</td>`
              : ``
            }
            ${settings.factuur_overzicht_hide['betaald_op'] ? '' : `
              ${is_proforma
                ? `<td data-sort="${proforma_accepted_at ? now(proforma_accepted_at).timestamp : ''}" class="nobr" >${proforma_accepted_at ? now(proforma_accepted_at).date.eu : '<i class="bi bi-dash h4 mx-0"></i>'}</td>`
                : `<td data-sort="${betaald_op ? now(betaald_op).timestamp : ''}" class="nobr" >${betaald_op ? now(betaald_op).date.eu : '<i class="bi bi-dash h4 mx-0"></i>'}</td>`
              }
            `}
            <td data-factuurnummer="${id}" class="nobr" >
                <span>${factuurnummer ?? `n.t.b.`}</span>
                ${ credit_factuur ? `<div class="d-flex" ><a target="_blank" href="${url}/facturatie/facturen/pdf/${credit_factuur.id}" class="text-warning font-size-075 opacity-75 tippy" data-tippy-content="Creditfactuur: ${credit_factuur.factuurnummer ?? `n.t.b.`}" >Gecrediteerd</a></div>` : '' }
                ${ credit_parent_factuur ? `<div class="d-flex" ><a target="_blank" href="${url}/facturatie/facturen/pdf/${credit_parent_factuur.id}" class="text-warning font-size-075 opacity-75 tippy" data-tippy-content="Gecrediteerd factuur: ${credit_parent_factuur.factuurnummer ?? `n.t.b.`}" >Creditfactuur</a></div>` : '' }
                ${ slice_facturen.length ? `<div class="d-flex" ><a onclick="${slice_facturen.map(slice => `window.open('${url}/facturatie/facturen/pdf/${slice.id}')`).join(';')}"  class="text-warning font-size-075 opacity-75 tippy cursor-pointer" data-tippy-content="Opgesplitst in ${slice_facturen.length}: ${slice_facturen.map(slice => slice.percentage).join('/')}" >Opgesplitst in ${slice_facturen.length}</a></div>` : '' }
                ${ slice_parent_factuur ? `<div class="d-flex" ><a target="_blank" href="${url}/facturatie/facturen/pdf/${slice_parent_factuur.id}" class="text-warning font-size-075 opacity-75 tippy" data-tippy-content="${percentage}% deelfactuur van ${slice_parent_factuur.factuurnummer || 'n.t.b.'}" >Deelfactuur</a></div>` : '' }
            </td>
            ${settings.facturatie_project_weergave == 'Aan'
              ? `${_projecten.length == 0
                ? `<td>-</td>`
                : `${_projecten.length == 1
                    ? `<td>${_projecten[0].projectnr}</td>`
                    : `<td>
                          <a class="text-primary cursor-pointer" onclick="docModal('projecten', ${id})" >Projecten</a>
                        </td>`
                  }`
              }`
              : ``
            }
            ${settings.factuur_overzicht_hide['factuurbedrag'] ? '' :
              `<td class="tippy nobr" data-tippy-content="
                <table>
                  <tr>
                    <td class='pr-3 text-left' >Excl. BTW:</td>
                    <td class='pr-1' >€</td>
                    <td class='text-right' >${toPrice(tot_excl)}</td>
                  </tr>
                  <tr>
                    <td class='pr-3 text-left' >BTW:</td>
                    <td class='pr-1' >€</td>
                    <td class='text-right' >${toPrice(tot_btw)}</td>
                  </tr>
                  <tr>
                    <td class='pr-3 text-left border-top' >Incl. BTW:</td>
                    <td class='pr-1 border-top' >€</td>
                    <td class='text-right border-top' >${toPrice(tot_incl)}</td>
                  </tr>
                </table>">
                <span>${ slice_parent_factuur
                ? `€ ${toPrice(tot_excl)} / € ${toPrice((tot_excl / Number(percentage)) * 100)}`
                : `€ ${toPrice(tot_excl)}` }
                </span>
              </td>
              ${settings.factuur_overzicht_hide['klant'] ? '' : `<td>`}`
            }
              ${ klant && !settings.factuur_overzicht_hide['klant']
                ? `<a class="text-primary cursor-pointer" onclick="showKlant(${klant.id})" >${klant?.naam || `${klant.contactpersoon_voornaam || ''} ${klant.contactpersoon_achternaam || ''}`}</a>`
                : ''
              }
            ${settings.factuur_overzicht_hide['klant'] ? '' : `</td>`}
            ${settings.factuur_overzicht_hide['debiteurnummer'] ? '' : `
              <td>${klant?.debiteurnummer || ''}</td>
            `}
            ${settings.factuur_overzicht_hide['referentie'] ? '' : `
              <td>${referentie || ''}</td>
            `}
            ${settings.factuur_overzicht_hide['adres'] ? '' : `
              <td>
                <nobr class="d-block" >${klant?.straat || ''} ${klant?.huisnummer || ''}${klant?.toevoeging || ''}</nobr>
                <nobr class="d-block">${klant?.postcode || ''} ${klant?.plaats || ''}</nobr>
              </td>
            `}
            ${settings.hide_adres != 'ja' && !settings.factuur_overzicht_hide['adres']
            ? `<td>
                <nobr class="d-block" >${adres?.straat || ''} ${adres?.huisnummer || ''}${adres?.toevoeging || ''}</nobr>
                <nobr class="d-block">${adres?.postcode || ''} ${adres?.plaats || ''}</nobr>
              </td>`
            : ''}
            ${settings.factuur_overzicht_hide['door'] ? '' : `
            <td>${user?.name || ''} ${user?.lastname || ''}</td>
            `}

            ${ !is_proforma && !settings.factuur_overzicht_hide['percentage']
              ? `<td>${percentage}%</td>`
              : ``
            }
            <td data-factuur-status="${id}" > <span class="badge badge-${color}" >${status}</span></td>
            ${ accorderen.isActive
                ? `<td data-accorderen-nodes="${id}" >${accorderen?.nodes || ''}</td>`
                : ``
            }
            <td data-factuur-sent="${id}" data-sort="${now(sent_at).timestamp}" >
                ${sent_to
                  ? `<small class="d-block">${now(sent_at).date.eu} ${now(sent_at).time}</small>
                    <small class="d-block" >${sent_to}</small>`
                  : `<i class="bi bi-dash h4 mx-0"></i>`
                }
            </td>
            <td data-factuur-sent-status="${id}" class="text-center" >
                ${sent_to
                  ? (mail_geopend ? `<i data-tippy-content="${now(mail_geopend).date.eu} ${now(mail_geopend).time}" class="bi bi-check2 h3 mx-0 tippy"></i>` : `<i class="bi bi-hourglass-split h4 mx-0"></i>`)
                  : `<i class="bi bi-dash h4 mx-0"></i>`
                }
            </td>
            <td>
              <div class="d-flex align-items-center justify-content-end mx--1">
                ${ !is_proforma
                    ? `
                      ${_snelstart.connected_bvs.includes(Number(bv)) && (!accorderen.isActive || accorderen.succeeded) && factuurnummer ? `<a class="tippy svg-white mx-1 btn ${snelstart_id ? 'btn-success' : 'btn-primary'} snelstart-init-btn-${id}"data-tippy-content="Upload naar snelstart" onclick="snelstartInit(${id})" >@svg_snelstart</a>` : ''}
                      ${_global.eboekhouden_connected
                        ? ` ${(!accorderen.isActive || accorderen.succeeded) && factuurnummer ? `<a class="tippy svg-white mx-1 btn ${eboekhouden == 1 ? 'btn-success' : 'btn-primary'} eboekhouden-init-btn-${id}"data-tippy-content="Upload naar Eboekhouden" onclick="eboekhoudenInit(${id})" >@svg_eboekhouden</a>` : ''}`
                        : ''
                      }
                      ${_eg.instance.connected && permissions.eg_facturen_uploaden
                        ? ` ${(!accorderen.isActive || accorderen.succeeded) && factuurnummer && (!_eg.settings.facturen_only_this_year || now(datum).year == now().year) ? `<a class="tippy mx-1 svg-white btn ${exact_globe_id ? 'btn-success' : 'btn-primary'} exact-globe-init-btn-${id} " data-tippy-content="Upload naar Exact Globe" onclick="exactGlobeInit(${id})" >@svg_exact_globe</a>` : '' }`
                        : ''
                      }
                      ${_eo.instance.connected
                        ? ` ${(!accorderen.isActive || accorderen.succeeded) && factuurnummer ? `<a class="tippy mx-1 svg-white btn ${exact_online_metadata?.id ? 'btn-success' : 'btn-primary'}" data-exact-online-factuur-init="${id}" data-tippy-content="Upload naar Exact Online" >@svg_exact_globe</a>` : '' }`
                        : ''
                      }
                      ${_bc.instance.connected
                        ? ` ${(!accorderen.isActive || accorderen.succeeded) && factuurnummer ? `<a class="tippy mx-1 btn btn-img ${business_central_id || business_central_credit_memo_id ? 'btn-success' : 'btn-primary'}" data-business-central-factuur-init="${id}" data-tippy-content="Upload naar Business Central">@btn_img_business_central</a>` : '' }`
                        : ''
                      }
                      ${_king.instance.connected
                        ? ` ${(!accorderen.isActive || accorderen.succeeded) && factuurnummer ? `<a class="tippy mx-1 btn btn-img ${king_uploaded ? 'btn-success' : 'btn-primary'}" data-king-factuur-init="${id}" data-tippy-content="Upload naar King">@btn_img_king</a>` : '' }`
                        : ''
                      }
                      ${settings.exact_xml_export == 'Aan' ? ` <button class="text-white mx-1 btn btn-${xml_export ? 'success' : 'primary'}" type="button" ${!xml_export ? `onclick="xmlExport(${id})"` : ''} id="xml-${id}">XML</button> ` : ''}
                    `
                    : ``
                }

                <div class="dropdown mx-1">
                  <button class="btn btn-success dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">Documenten</button>
                  <div class="dropdown-menu max-h-300 overflow-auto">
                    ${ modules.bestanden && permissions.bestanden_uploaden
                      ? `<a class="dropdown-item cursor-pointer" onclick="openExplorerPath({path: '/${is_proforma ? 'Pro forma\\\'s' : 'Facturen'}/(${id})'})" ><span data-files-count="${id}" >${factuur_files.length}</span>&nbsp;Bestanden</a>`
                      : ``
                    }
                    ${!is_proforma ? `<a class="dropdown-item cursor-pointer" onclick="docModal('proformas', ${id})" >${_proformas.length}&nbsp;Proforma's</a>` : '' }
                    ${modules.offertes ? `<a class="dropdown-item cursor-pointer" onclick="docModal('offertes', ${id})" >${_offertes.length}&nbsp;Offertes</a>` : '' }
                    ${modules.projecten ? `<a class="dropdown-item cursor-pointer" onclick="docModal('projecten', ${id})" >${_projecten.length}&nbsp;Projecten</a>` : '' }
                    ${modules.werkbonnen ? `<a class="dropdown-item cursor-pointer" onclick="docModal('werkbonnen', ${id})" >${_werkbonnen.length}&nbsp;Werkbonnen</a>` : '' }
                  </div>
                </div>

                <div class="dropdown mx-1">
                  <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">Acties</button>
                  <div class="dropdown-menu max-h-300 overflow-auto">
                    <a class="dropdown-item" target="_blank" href="${url}/facturatie/facturen/pdf/${id}" >PDF</a>
                    ${modules.bestanden && permissions.bestanden_uploaden ? `<a class="dropdown-item" target="_blank" onclick="addExplorerFile(${id})" >Bestand uploaden</a>` : ''}
                    ${modules.bestanden && factuur_files.length ? `<a class="dropdown-item" target="_blank" onclick="deleteMergedFiles(${id})" >Bestanden in pdf verwijderen</a>` : ''}
                    ${credit_factuur ? ` <a class="dropdown-item" target="_blank" href="${url}/facturatie/facturen/open/${credit_factuur.token}" >Creditfactuur</a>` : ''}
                    ${!is_proforma
                      ? `
                        ${stadium == 'Open'
                          ? `
                              ${((!factuurnummer && !accorderen.isActive) || (!factuurnummer && accorderen.succeeded)) ? `<a class="dropdown-item" onclick="generateFactuurnummer(${id})" >Genereer factuurnummer</a>` : ''}
                              ${ !accorderen.isActive || accorderen.succeeded || has_budget
                                ? `<a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#emailModal" onclick="emailModal(${id})" >Versturen</a>
                                  ${emails.length ? `<a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#emailModal" onclick="emailModal(${id}, true)" >Reminder sturen</a>` : ''}`
                                : ''
                              }
                              ${ !factuurnummer
                                ? `<a class="dropdown-item cursor-pointer" href="${url}/facturatie/facturen/edit/${id}?type=factuur" >Wijzigen</a>`
                                : ''
                              }
                              ${ !accorderen.isActive || accorderen.succeeded || has_budget
                                  ? `<div class="dropdown-divider" ></div>
                                    <a class="dropdown-item cursor-pointer" onclick="factuurStatus(${id}, 'Betaald')" >${settings.facturatie_alt_betaald_text}</a>`
                                  : ''
                              }
                              <div class="dropdown-divider" ></div>
                              ${ !has_budget && (accorderen.isActive && (!accorderen.inProcess || accorderen.failed))
                                  ? `<a data-accorderen-btn="${id}" class="dropdown-item" onclick="factuurStatus(${id}, 'Accorderen')" >Accorderen</a>`
                                  : ''
                              }
                              ${ !credit_factuur && !Number(credit)
                                  ? `<a data-crediteren-btn="${id}" class="dropdown-item" onclick="factuurCredit(${id})" >Crediteren</a>`
                                  : ''
                              }
                              ${ (Number(percentage) === 100 && status != 'Verzonden') ? `<a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#sliceModal" onclick="slice(${id})" >Opsplitsen</a>` : '' }
                              ${(!factuurnummer && permissions.facturen_verwijderen) ? `<a class="dropdown-item cursor-pointer" onclick="factuurStatus(${id}, 'Verwijderd')" >Verwijderen</a>` : ''}
                            `
                          : ''
                        }
                        ${status == 'Verwijderd'
                          ? `<a class="dropdown-item" onclick="factuurStatus(${id}, 'Uitgebracht')" >Activeer</a>`
                          : ''
                        }
                      `
                      : ''
                    }
                    ${is_proforma
                      ? `
                        ${ Number(proforma_accepted)
                          ? `<a class="dropdown-item cursor-pointer" onclick="showSignature(${id})" >Handtekening inzien</a>`
                          : ''
                        }
                        ${ proforma_accepted === null
                          ? `
                              ${ !accorderen.isActive || accorderen.succeeded || has_budget
                                ? `<a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#emailModal" onclick="emailModal(${id})" >Versturen</a>
                                  ${emails.length ? `<a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#emailModal" onclick="emailModal(${id}, true)" >Reminder sturen</a>` : ''}`
                                : ''
                              }
                              <a class="dropdown-item cursor-pointer" href="${url}/facturatie/facturen/edit/${id}?type=proforma" >Wijzigen</a>
                              ${ !accorderen.isActive || accorderen.succeeded || has_budget
                                ? `<div class="dropdown-divider" ></div>
                                  <a class="dropdown-item cursor-pointer" onclick="proformaStatus(${id}, 1)" >Akkoord</a>
                                  <a class="dropdown-item cursor-pointer" onclick="proformaStatus(${id}, 0)" >Afgewezen</a>
                                  <a class="dropdown-item cursor-pointer" onclick="proformaStatus(${id}, 'Verwijderd')" >Verwijderen</a>`
                                : ''
                              }
                              ${ !has_budget && (accorderen.isActive && (!accorderen.inProcess || accorderen.failed))
                                ? `<div class="dropdown-divider" ></div>
                                  <a data-accorderen-btn="${id}" class="dropdown-item" onclick="proformaStatus(${id}, 'Accorderen')" >Accorderen</a>
                                  <a class="dropdown-item cursor-pointer" onclick="proformaStatus(${id}, 'Verwijderd')" >Verwijderen</a>`
                                : ''
                              }
                            `
                          : `
                              ${ !Number(proforma_accepted)
                                ? `<a class="dropdown-item cursor-pointer" href="${url}/facturatie/facturen/edit/${id}?type=proforma" >Wijzigen</a>`
                                : ''
                              }
                            `
                        }
                        ${status == 'Verwijderd'
                          ? `<a class="dropdown-item" onclick="proformaStatus(${id}, 'Uitgebracht')" >Activeer</a>`
                          : ''
                        }
                        `
                      : ''
                    }
                  </div>
                </div>

              </div>
            </td>
          </tr>
        `);
      }

      initTable();
      appendDTButton();
      appendBatchButton();
      tippyInit();
    }
    function initTable(){
      $('[data-facturen-table-container]').removeClass('d-none');
      _base.table = tableInit('.facturen-table', {
        paging: false,
        ordering: false,
        searching: false,
        info: false,
        columnDefs: [{orderable: false, targets: [-1]}],
      })
    }
    function refreshPagination(page){

      const { dsi, total_count, is_proforma } = _base;
      const pages_count = Math.ceil((_base.total_count || 1) / (_base.per_page() || (_base.total_count || 1))) // if per_page == '' (no pagination) the pages_count must be 1, also when empty;

      if(!page){
        for(const node of dsi.values()){
          $(`[data-display-select-container='pagination']`).find(`.ds-node[data-value=${node.value}]`).remove();
        }
        for(let i = 1; i <= pages_count; i++){
          dsi.addValue({name: i, value: i});
        }
        dsi.silentSelect(1);
      }
      $('[data-facturen-count]').html(`${total_count} ${(total_count !== 1) ? (is_proforma ? `PRO FORMA'S` : `FACTUREN`) : (is_proforma ? `PRO FORMA` : `FACTUUR`)}`);
    }
    async function findFacturen(){
      let { container } = _search;
      let { is_proforma } = _base;
      const value = $('[name=search]').val()

      if (_search.request){
        _search.request.abort();
        _search.request = null;
      }

      const searchFunction = (is_proforma ? searchProformas : searchFacturen);
      _search.request = searchFunction(value);
      _search.request
        .then(facturen => {

          _search.results = facturen;
          _search.timeout = null;

          if (!facturen.length){
            container.html(`<div class='text-center text-muted py-2' >Geen resultaten gevonden</div>`)
            return;
          }

          container.empty();
          for(let i = 0; i <5; i++){
            if(!facturen[i]){ break; }
            const factuur = facturen[i];

            container.append(`
              <div class="cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5" data-search-by-results='${factuur.id}' >
                  <div class='font-size-09' >${factuur.factuurnummer || 'n.t.b.'}</div>
              </div>
            `);
          }
          container.append(`<div class='cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5 text-center' data-search-by-results='all' >Alle resultaten weergeven ( ${facturen.length} )</div>`);
        })
        .catch(err => {
          handleCatchError(err, 'findFacturen()')
        })
    }

    function generateFactuurnummer(id){
      confirmModal({
        text: 'Weet je zeker dat je een factuurnummer wilt genereren?',
      })
        .then(response => {
          if(!response.status){ return; }

          loader();
          ajax('/api/facturatie/set/factuurnummer', {id: id})
            .then(response => {
              notification(`Factuurnummer <b>${response?.factuur?.factuurnummer}</b> aangemaakt!`, 'success');
              initFacturen();
            })
            .catch(handleCatchError);

        })
    }

    function appendBatchButton(){
        $('.dataTables_filter').append('<a class="btn btn-light ml-1" data-batch-init="main" ><i class="fa-solid fa-paper-plane m-0"></i></a>');
    }

    function factuurCredit(id){
      const factuur = facturen.find(factuur => factuur.id == id);


      confirmModal({
        text: `
          <div>${factuur.factuurnummer ?? 'n.t.b.'} crediteren:</div>
          <form class="overflow-auto" data-credit-form >
            ${factuur.regels.map(regel => {
          const { id, naam, aantal, prijs } = regel;
          return `<div class="flex-between my-1" data-credit-row="${id}" >
                          <input type="checkbox" class="form-switch-custom mx-1 tippy" data-tippy-content="Crediteren" checked data-credit-row-switch >
                          <input type="text" name="naam[]" class="form-control-custom mx-1 w-100 tippy" data-tippy-content="Omschrijving" value="${naam.htmlEncode()}" >
                          <input type="number" name="aantal[]" class="form-control-custom mx-1 w-auto tippy" data-tippy-content="Aantal" value="${aantal}" >
                          <input type="number" name="prijs[]" class="form-control-custom mx-1 w-auto tippy" data-tippy-content="Prijs" value="${getOppositeNumber(prijs)}" >
                          <input type="hidden" name="row_id[]" value="${id}" >
                      </div>`
        }).join('')}
            <input type="hidden" name="id" value="${id}">
          </form>
        `,
        large: true,
      })
        .then((response) => {
          if(!response.status){return}

          const data = $('[data-credit-form]').serializeArray();
          loader();
          ajax('api/facturatie/credit/create', data)
            .then((res) => {
              initFacturen();
            })
            .catch(err => {
              handleCatchError(err, 'factuurCredit()')
            });
        })

      tippyInit();
    }
    function factuurStatus(id, state){
      const factuur = facturen.find(factuur => factuur.id == id);

      confirmModal({
        text: `Weet je zeker dat je status van <b class="nobr">${factuur.factuurnummer ?? 'n.t.b.'}</b> wilt veranderen naar <b class="text-lowercase" >${state}</b>?`,
      })
        .then((response) => {
          if(!response.status){return}

          loader();
          ajax('api/facturatie/factuur/status', {id: id, status: state})
            .then((res) => {
              initFacturen();
            })
            .catch(err => {
              handleCatchError(err, 'factuurStatus()');
            });
        })
    }
    function proformaStatus(id, state){
      const factuur = facturen.find(factuur => factuur.id == id);
      const status = {
        '0': 'Afgewezen',
        '1': 'Akkoord',
        'Accorderen': 'Accorderen',
        'Verwijderd': 'Verwijderd',
        'Uitgebracht': 'Uitgebracht',

      }

      confirmModal({
        text: `Weet je zeker dat je status van <b class="nobr">${factuur.factuurnummer}</b> wilt veranderen naar ${status[state]}`,
      })
        .then((response) => {
          if(!response.status){return}

          loader();
          ajax('api/facturatie/proforma/status', {id: id, status: state})
            .then((res) => {
              successLoader();
              initFacturen();
            })
            .catch(err => {
              handleCatchError(err, 'proformaStatus()')
            });
        })
    }
    function showSignature(id){
      const factuur = facturen.find(factuur => factuur.id == id);
      confirmModal({
        text: `<div>
                <a href="${url}/api/file/${factuur.signature}" download onclick="_dynamicConfirmModal.modal.hideModal()" >
                  <img class="d-block hover-shadow bg-white border rounded w-100" style="min-height: 100px" alt="${factuur.signature}" src="${url}/api/file/${factuur.signature}" >
                </a>
              </div>`,
        hideFooter: true,
      })
    }

    function addExplorerFile(id){
      let factuur = facturen.find(factuur => factuur.id == id);
      uploadExplorerFile({
        path: `/${_base.is_proforma ? "Pro forma's" : 'Facturen'}/(${factuur.id || 'onbekend'})/Bijlagen`,
        params: [`&factuur=${id}`],
      })
        .then(response => {
          if(!response.status){ return; }
          try{
            factuur = facturen.find(factuur => factuur.id == id);
            if(factuur){ factuur.files.push(response.file) }

            $(`[data-files-count=${id}]`).html(factuur.files.length)
          }
          catch (e) {
            notification('Er is iets foutgegaan!', 'danger');
            actError(e);
          }

        })
        .catch(err => {
          notification('Er is iets foutgegaan!', 'danger');
          actError(err);
        })
    }
    function openExplorer(){
      $('#emailModal').hideModal();
      getExplorerFile()
        .then((response) => {
          $('#emailModal').showModal();
          if(!response.status){ return }

          bodyModalClass();
          insertFile(response.file);
        })
        .catch((e) => {

          bodyModalClass();
          $('#emailModal').showModal();
          actError(e);
          errorLoader();
        })
    }
    function openUploadExplorer(){
      hideModal('emailModal');
      uploadExplorerFile({ path: `/${_base.is_proforma ? "Pro forma's" : 'Facturen'}/(${_email?.factuur?.id || 'onbekend'})/Verzonden` })
        .then(response => {
          showModal('emailModal');

          if(!response.file){ return; }

          insertFile(response.file);
        })
        .catch(err => {
          showModal('emailModal');
          notification('Er is iets foutgegaan!', 'danger');
        });

    }
    function deleteMergedFiles(id){
      const factuur = facturen.find(factuur => factuur.id == id);

      confirmModal({
        text: `Weet je zeker dat je alle bestanden in de pdf van <b class="nobr">${factuur.factuurnummer}</b> wilt verwijderen?`,
      })
        .then((response) => {
          if(!response.status){return}

          loader();
          ajax('api/facturatie/delete/files', {id: id})
            .then((res) => {
              initFacturen();
              successLoader();
            })
            .catch(err => {
              handleCatchError(err, 'deleteMergedFiles()')
            });
        })
    }

    function docModal(target, factuur_id){
      const factuur = facturen.find(factuur => factuur.id == factuur_id);
      const { _proformas, _offertes, _projecten, _werkbonnen } = factuur;
      const container = $("#doc-content");

      container.empty();

      if(target == 'proformas'){
        if(!_proformas.length){
          container.html(`<h4 class="text-center text-muted m-0" >Geen proforma's gevonden</h4>`)
        }
        for(let row of _proformas) {
          container.append(
            `<div class="d-flex justify-content-between my-2" >
                <span>${row.factuurnummer}</span>
                <a class="btn btn-success cursor-pointer" target="_blank" href="${url}/facturatie/facturen/pdf/${row.id}" >PDF</a>
              </div>`
          );
        }
      }
      if(target == 'offertes'){
        if(!_offertes.length){
          container.html(`<h4 class="text-center text-muted m-0" >Geen offertes gevonden</h4>`)
        }
        for(let row of _offertes) {
          container.append(
            `<div class="d-flex justify-content-between my-2" >
                <span>${row.offertenummer}</span>
                <a class="btn btn-success cursor-pointer" target="_blank" href="${url}/offertes/token/${row.token}" >PDF</a>
              </div>`
          );
        }
      }
      if(target == 'projecten'){
        if(!_projecten.length){
          container.html(`<h4 class="text-center text-muted m-0" >Geen Projecten gevonden</h4>`)
        }
        for(let row of _projecten) {
          container.append(
            `<div class="my-2" >
                <a class="text-primary" href="${url}/uren/projectnummers/${row.id}/preview" >${row.projectnr}</a>
                  <span class="d-block text-muted" >${row.projectnaam}</span>
                </div>`
          );
        }
      }
      if(target == 'werkbonnen'){
        if(!_werkbonnen.length){
          container.html(`<h4 class="text-center text-muted m-0" >Geen offertes gevonden</h4>`)
        }
        for(let row of _werkbonnen){
          container.append(
            `<div class="d-flex justify-content-between my-2" >
                <span>${row.werkbonnummer}</span>
                <a class="btn btn-success cursor-pointer" target="_blank" href="${url}werkbonnen/pdf/${row.id}" >PDF</a>
              </div>`
          )
        }
      }

      showModal('doc-modal');
    }
    async function emailModal(id, is_reminder = false){
      try{


        let factuur = facturen.find(factuur => factuur.id == id);
        let { klant, is_proforma, factuurnummer, _werkbonnen } = factuur;

        $('#loading-spinner').show();
        $('#email-modal-content').hide();

        let project = await getProject(JSON.parse(factuur.projecten)[0], { relations: ['vestiging', 'manager', 'custom'] });

        $('#loading-spinner').hide();
        $('#email-modal-content').show();


        $('[data-email-attach-btn]').removeClass('btn-dark').addClass('btn-inverse-dark');
        $('[data-email-attach-container]').addClass('d-none');
        $(`[data-email-attach-content='explorer'], [data-attachments-proforma], [data-reminders-content]`).empty();
        $("#bijlagenRow").html('<div class="col-md-6 col-12 my-2"><input type="file" name="files[]" class="dropify" data-max-file-size="10M"></div>');
        $('[name=customReportingDate]').prop('checked', false);
        $('[name=customReportingValue]').addClass("d-none");

        _email.factuur = factuur;
        _email.klantId.val(null)
        _email.klantId.val(null)
        _email.reminder.val(null)
        _email.email.val('')
        _email.ccemail.val('')
        _email.bccemail.val('')
        _email.emails.empty();
        _email.ccemails.empty();
        _email.bccemails.empty();

        if(klant){
          const { factuuremail, email, naam, contactpersoon_voornaam, contactpersoon_achternaam, contactpersoon_email, contactpersonen } = klant;

          _email.klantId.val(klant.id);
          if(factuuremail){
            _email.emails.append(`<span class="select_edit-value flex-between" data-value="${factuuremail}"> <span>${naam || ''}</span> <span class="text-muted" >${factuuremail}</span> </span>`)
            _email.ccemails.append(`<span class="select_edit-value flex-between" data-value="${factuuremail}"> <span>${naam || ''}</span> <span class="text-muted" >${factuuremail}</span> </span>`)
            _email.bccemails.append(`<span class="select_edit-value flex-between" data-value="${factuuremail}"> <span>${naam || ''}</span> <span class="text-muted" >${factuuremail}</span> </span>`)
            _email.email.val(factuuremail);
          }
          if(email){
            _email.emails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${naam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            _email.ccemails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${naam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            _email.bccemails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${naam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            if(!_email.email.val()){ _email.email.val(email) }
          }
          if(contactpersoon_email){
            _email.emails.append(`<span class="select_edit-value flex-between" data-value="${contactpersoon_email}"> <span>${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}</span> <span class="text-muted" >${contactpersoon_email}</span> </span>`)
            _email.ccemails.append(`<span class="select_edit-value flex-between" data-value="${contactpersoon_email}"> <span>${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}</span> <span class="text-muted" >${contactpersoon_email}</span> </span>`)
            _email.bccemails.append(`<span class="select_edit-value flex-between" data-value="${contactpersoon_email}"> <span>${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}</span> <span class="text-muted" >${contactpersoon_email}</span> </span>`)
            if(!_email.email.val()){ _email.email.val(contactpersoon_email) }
          }
          if(project.custom?.contactpersoon_proformas && Number(is_proforma)){
            const cp = contactpersonen.find(cp => cp.id == project.custom.contactpersoon_proformas.value);
            if(cp){
              _email.email.val(cp.email);
            }
          }
          for(const cp of contactpersonen){
            const {voornaam, achternaam, email} = cp;
            if(!email){ continue; }
            _email.emails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${voornaam || ''} ${achternaam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            _email.ccemails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${voornaam || ''} ${achternaam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            _email.bccemails.append(`<span class="select_edit-value flex-between" data-value="${email}"> <span>${voornaam || ''} ${achternaam || ''}</span> <span class="text-muted" >${email}</span> </span>`)
            if(!_email.email.val()){ _email.email.val(email) }
          }
        }

        $("input[name=factuurId]").val(factuur.id);
        $('[name=customReportingValue]').val(factuur.reporting_date ?? now(addDaysToDate(new Date(factuur.datum), parseFloat(settings.reporting_date_default))).date.us);
        _email.sender.val('');
        _email.subject.val(`${Number(is_proforma) ? 'Pro forma' : 'Factuur'}: ${factuurnummer ?? '<{factuurnummer}>'}`);

        let content_factuur =  @json(getSettingValue('factuur_email_content'));
        let content_proforma =  @json(getSettingValue('proforma_email_content'));
        let content = Number(factuur.is_proforma) ? content_proforma : content_factuur;
        let sender =  Number(factuur.is_proforma) ? @json(getSettingValue('proforma_afzender')) : @json(getSettingValue('factuur_afzender'));

        if(is_reminder){
          const reminder_content_proforma =  @json(getSettingValue('facturatie_reminder_template_proforma'));
          const reminder_content_factuur =  @json(getSettingValue('facturatie_reminder_template_factuur'));
          content = Number(factuur.is_proforma) ? reminder_content_proforma : reminder_content_factuur;
          _email.reminder.val('1');
        }

        if(content){
          if(factuur.klant){
            content = content.replace("&lt;{klant}&gt;", factuur.klant.contactpersoon_achternaam)
          }
          content = content.replace("&lt;{bv}&gt;", factuur._bv ? factuur._bv.name : '')
          content = content.replace("&lt;{user}&gt;", _user.name || '')
          content = content.replace("&lt;{user-email}&gt;", _user.email || '')
          content = content.replace("&lt;{user-phone}&gt;", _user.phone || '')

          const vestigingData = {
            adres: '',
            postadres: '',
            phone: '',
          }

          const managerData = {
            name: '',
            phone: '',
            email: '',
          }

          if(factuur.projecten[0]){
            const { vestiging, manager } = project;
            if(vestiging){
              const { straat, huisnummer, toevoeging, postcode, plaats, telefoon} = vestiging;
              const { postadres_straat, postadres_huisnummer, postadres_toevoeging, postadres_postcode, postadres_plaats} = vestiging;
              vestigingData.adres = `${straat || ''} ${huisnummer || ''}${toevoeging || ''},<br>${postcode || ''} ${plaats || ''}`
              vestigingData.postadres = `${postadres_straat || ''} ${postadres_huisnummer || ''}${postadres_toevoeging || ''}<br>${postadres_postcode || ''} ${postadres_plaats || ''}`
              vestigingData.phone = telefoon || '';
            }
            if(manager){
              const { email, phone, name } = manager;
              managerData.name = name || '';
              managerData.phone = phone || '';
              managerData.email = email || '';
            }
          }

          content = content.replace("&lt;{project-vestiging-adres}&gt;", vestigingData.adres)
          content = content.replace("&lt;{project-vestiging-postadres}&gt;", vestigingData.postadres)
          content = content.replace("&lt;{project-vestiging-phone}&gt;", vestigingData.phone)
          content = content.replace("&lt;{project-manager-naam}&gt;", managerData.name)
          content = content.replace("&lt;{project-manager-phone}&gt;", managerData.phone)
          content = content.replace("&lt;{project-manager-email}&gt;", managerData.email)

          let reminder_count = '';
          if(factuur.reminders.length > 0){
            reminder_count = `${factuur.reminders.length + 1}<sup>de</sup> `;
          }
          content = content.replace("&lt;{reminder-count}&gt;", reminder_count)


          editors.email.setData(content);
        }

        _email.sender.val(sender || '');

        if(!Number(factuur.is_proforma)){
          const proformas = JSON.parse(factuur.proformas || '[]');
          if(proformas.length){
            fillProformasLinks(proformas);

            $('[data-attachments-proforma]').html(`
              <div class="flex-between my-1" >
                  <label class="cursor-pointer" > <input ${_settings.facturatie_send_default_attach_proforma == 'ja' ? 'checked' : 'nee'} type="checkbox" value="on" class="form-check-custom" name="proforma_attachment" > Pro Forma's toevoegen aan bijlagen. </label>
                  <a class="btn btn-sm btn-light" onclick="$('[data-proformas-links-container]').toggle(300); $('[data-proformas-links-toggle-icon]').toggleClass('rotate-180')" >
                      <i class="fa fa-chevron-down m-0 transition-03" data-proformas-links-toggle-icon ></i>
                  </a>
              </div>
              <div class="bg-inverse-secondary rounded font-size-09 w-100" style="display: none" data-proformas-links-container >
                  <div class="px-3 py-2" data-proformas-links >@spinner</div>
              </div>
          `);

          }
        }

        //werkbonnen
        if(settings.facturatie_werkbonnen_sturen) {
          let factuurId = $("input[name=factuurId]").val();
          if (!factuurId) {
            $('[data-werkbonnen-container-content]').append(`<div class="text-danger">Geen resultaten</div>`);
            return;
          }

          let factuur = facturen.find(factuur => factuur.id == factuurId);
          let werkbonnen = factuur?._werkbonnen || [];

          if (!werkbonnen.length) {
            $('[data-werkbonnen-container-content]').append(`<div class="text-danger">Geen resultaten</div>`);
            return;
          }

          $('[data-werkbonnen-container-content]').empty();

          $('[data-wAttach]').html(`
            <div class="flex-between my-2">
              <span>Werkbonnen toevoegen als bijlage</span>
              <a class="btn btn-sm btn-light" onclick="$('[data-werkbonnen-container]').toggle(300); $('[data-werkbonnen-icons]').toggleClass('rotate-180');">
                <i class="fa fa-chevron-down m-0 transition-03" data-werkbonnen-icons></i>
              </a>
            </div>
            <div class="w-100" style="display: none" data-werkbonnen-container>
              <div data-werkbonnen-container-content>
                <label class="cursor-pointer my-1">
                  <input type="checkbox" name="select_all_werkbonnen" onclick="toggleAllWerkbonnen()" class="form-check-custom"> Selecteer alles
                </label>
              </div>
            </div>
          `);

          let urenVan = factuur.uren_van ? new Date(factuur.uren_van) : null;
          let urenTot = factuur.uren_tot ? new Date(factuur.uren_tot) : null;

          for (const werkbon of werkbonnen) {
            let werkbonDatum = new Date(euToUsDate(werkbon.datum));
            let isChecked = urenVan && urenTot && werkbonDatum >= urenVan && werkbonDatum <= urenTot;

            $('[data-werkbonnen-container-content]').append(`
              <div class="d-flex justify-content-between">
                <label class="cursor-pointer my-1">
                  <input type="checkbox" value="${werkbon.id}" name="wAttach[]" class="form-check-custom" ${isChecked ? "checked" : ""}>
                  ${werkbon.werkbonnummer}
                </label>
                <a class="btn btn-light m-0 p-1 px-2 rounded-pill tippy" href="${url}/werkbonnen/pdf/${werkbon.id}" target="_blank" data-tippy-content="Werkbon bekijken">@icon_zoom</a>
              </div>
            `);
          }
        }

          $('[data-sent-emails]').html(`
            <div class="flex-between my-2" >
                <span>Verzonden emails</span>
                <a class="btn btn-sm btn-light" onclick="$('[data-sent-emails-container]').toggle(300); $('[data-sent-emails-icons]').toggleClass('rotate-180')" >
                    <i class="fa fa-chevron-down m-0 transition-03" data-sent-emails-icons ></i>
                </a>
            </div>
            <div class="w-100" style="display: none" data-sent-emails-container >
                <div data-sent-emails-container-content ></div>
            </div>
        `);
        if(!factuur.all_emails.length){ $('[data-sent-emails-container-content]').html('<div class="text-muted font-italic" >Geen emails gevonden!</div>') }
        for(const mail of factuur.all_emails){
          const string = randomString();
          $('[data-sent-emails-container-content]').append(`
            <div class="bg-inverse-secondary rounded py-1 px-2 my-1" >
              <div class="flex-between" >
                <div class="d-flex align-items-center" >
                  <span>${mail.subject}</span>
                  ${ Number(mail.is_reminder) ? `<span class="badge badge-warning ml-2" >Reminder</span>` : '' }
                  ${ Number(mail.is_auto) ? `<span class="badge badge-inverse-primary ml-2" >Auto</span>` : '' }
                </div>
                <a class="btn btn-sm" onclick="$('[data-sent-email-container=${string}]').toggle(300); $('[data-sent-email-icons=${string}]').toggleClass('rotate-180')" >
                    <i class="fa fa-chevron-down m-0 transition-03" data-sent-email-icons="${string}" ></i>
                </a>
            </div>
            <div class="w-100" style="display: none" data-sent-email-container="${string}" >
                <div class="font-size-09" data-sent-email-container-content="${string}" >
                  <div><b>Datum:</b> ${convert(mail.date)} ${mail.time.slice(0, 5)} </div>
                  <div><b>Afzender:</b> ${mail.sender || ''} </div>
                  <div><b>Aan:</b> ${JSON.parse(mail.emails || '[]').join(', ')} </div>
                  <div class="mt-2" >${mail.message}</div>
                </div>
            </div>
          </div>
          `)
        }
      }
      catch (e) { handleCatchError(e, 'emailModal()'); }
      dropifyInit();
    }

    function toggleAllWerkbonnen(){
      let checked = $('[name=select_all_werkbonnen]').prop('checked');
      $(`input[name="wAttach[]"]`).prop('checked', checked);
    }

    function fillProformasLinks(proformas){

      const appendProformaAttachments = @json(getSettingCheckbox('facturatie_append_proforma_sign_attachments'));

      getApiData('facturen', proformas)
        .then(response => {
          $('[data-proformas-links]').empty();
          for(const proforma of response.data){
            $('[data-proformas-links]').append(
              `<div>
                <div> <a href="${url}/facturatie/facturen/pdf/${proforma.id}" target="_blank">${proforma.factuurnummer}</a> </div>
                <div data-email-proforma-files="${proforma.id}" ></div>
              </div>`
            );

            if(appendProformaAttachments){
              for(const file_con of proforma.sign_files){
                $(`[data-email-proforma-files=${proforma.id}]`).append(
                  `<div class="pl-1 d-flex align-items-center " > <i class="fa-arrow-turn-up fa-solid text-muted rotate-90 mr-2"></i> <a class="font-size-08" href="${url}/api/filename/${encodeURIComponent(file_con.file.name)}/explorer/files/${file_con.file.src}" target="_blank" download >${file_con.name}: ${file_con.file.name}</a> </div>`
                )
              }
            }
          }
        })
    }

    async function slice(id){

      let large = false
      if(settings.factuur_opsplitsen_naam == 'ja' && settings.factuur_opsplitsen_datum == 'ja'){
        large = true;
      }

      let htmlContent = `
      ${settings.factuur_opsplitsen_datum != 'ja' ?
      `<div class="my-2">
          <label>Betalingstermijn</label>
          <input type="date" class="form-control" name="betalingstermijn" >
      </div>`
      : ''}
     <label class="my-2">Percentages</label>
      <div id="formPercs" class="my-2">
      ${((settings?.factuur_standaard_splitsing || []).length > 0) ?
              settings.factuur_standaard_splitsing.map(perc => `
            <div class="my-2 d-flex align-items-center w-100" id="splitsing_${randomString()}">
              <input type="number" min="1" max="100" step="1" class="form-control-custom percInput ${large ? 'max-w-100' : ''}" name="percs[${lastString()}][perc]" placeholder="20%" value="${perc ?? 0}">
              ${settings.factuur_opsplitsen_naam == 'ja' ? `<input type="text" class="form-control-custom splitNameInput ml-1" placeholder="Naam" name="percs[${lastString()}][name]">` : ''}
              ${settings.factuur_opsplitsen_datum == 'ja' ? `<input type="date" class="form-control-custom splitDateInput ml-1 ${large ? 'max-w-200' : ''}" name="percs[${lastString()}][date]">` : ''}
              <a onclick="deleteDiv('#splitsing_${lastString()}')" class="btn btn-danger text-white percDelete ml-2">@icon_trash</a>
            </div>`).join('') :
              `
            <div class="my-2 d-flex align-items-center" id="splitsing_${randomString()}">
              <input type="number" min="1" max="100" step="1" class="form-control-custom percInput ${large ? 'max-w-100' : ''}" name="percs[${lastString()}][perc]" placeholder="20%" value="0">
              ${settings.factuur_opsplitsen_naam == 'ja' ? `<input type="text" class="form-control-custom splitNameInput ml-1" placeholder="Naam" name="percs[${lastString()}][name]">` : ''}
              ${settings.factuur_opsplitsen_datum == 'ja' ? `<input type="date" class="form-control-custom splitDateInput ml-1  ${large ? 'max-w-200' : ''}" name="percs[${lastString()}][date]">` : ''}
              <a onclick="deleteDiv('#splitsing_${lastString()}')" class="btn btn-danger text-white percDelete ml-2">@icon_trash</a>
            </div>
        `}

      </div>
      <div class="my-2 text-right" >
          <a class="btn btn-primary text-white cursor-pointer" onclick="addPerc()" ><i class="fas fa-plus m-0"></i></a>
      </div>

      <div class="my-2">
          <label><span id="totPercLabel">100</span>%</label>
          <div class="overflow-hidden border rounded" >
              <div id="totPercBar"  class="py-1 bg-success transition-05" ></div>
          </div>
           <input type="hidden" name="id" value="${id}">
      </div>
      `;

      const { status, inputs } = await confirmModal({
        text: htmlContent,
        large: large,
        btnText: 'Bevestigen',
      })
      if(!status){ return; }

      if(!checkPercs()){ return; }

      const result = await ajax('/api/facturatie/facturen/slice', inputs);

      if(result?.msg){
        initFacturen()
        successLoader(`${result.msg}`);
      }
    }
    function addPerc(){
      let sum = 0;
      let v = 0;
      $(".percInput").each(function(){
        sum += parseInt($(this).val());
      });
      if(sum < 100){ v = 100 - sum; }
      $("#formPercs").append(
        `<div class="my-2 d-flex align-items-center" id="splitsing_${randomString()}">
          <input type="number" min="1" max="100" step="1" class="form-control-custom percInput ${settings.factuur_opsplitsen_naam == 'ja' || settings.factuur_opsplitsen_datum == 'ja' ? 'max-w-100' : ''}" name="percs[${lastString()}][perc]" placeholder="20%" value="0">
            ${settings.factuur_opsplitsen_naam == 'ja' ? `<input type="text" class="form-control-custom splitNameInput ml-1 " placeholder="Naam" name="percs[${lastString()}][name]">` : ''}
            ${settings.factuur_opsplitsen_datum == 'ja' ? `<input type="date" class="form-control-custom splitDateInput ml-1 max-w-200" name="percs[${lastString()}][date]">` : ''}
            <a onclick="deleteDiv('#splitsing_${lastString()}')" class="btn btn-danger text-white percDelete ml-2">@icon_trash</a>
        </div>`
      );
      calcPerc();
    }
    function checkPercs(){
      let sum = 0;
      $(".percInput").each(function(){
        sum += parseInt($(this).val());
      });
      if(sum != 100){
        errorLoader('Totale percentage moet gelijk zijn aan 100%')
        return false;
      }
      return true;
    }
    function calcPerc(){
      let sum = 0;
      $(".percInput").each(function(){
        if (!$(this).val()) {
          $(this).val(0);
        }

        sum += parseInt($(this).val());
      });

      if(sum == 100){
        $("#totPercBar").removeClass("bg-primary").removeClass("bg-danger").addClass("bg-success").attr("style", "width: 100%");
      }
      else if(sum > 100){
        $("#totPercBar").removeClass("bg-primary").removeClass("bg-success").addClass("bg-danger").attr("style", "width: 100%");
      }
      else{
        $("#totPercBar").removeClass("bg-danger").removeClass("bg-success").addClass("bg-primary").attr("style", "width: "+sum+"%");
      }
      $("#totPercLabel").html(sum);
    }

    function syncFacturen(){
      if(!_snelstart.connected_bvs.length || _base.is_proforma){return;}

      loader('Snelstart');

      $.ajax({
        type: "POST",
        url: "{{url("snelstart/sync/facturen")}}",
        data: {
          _token: "{{csrf_token()}}",
        },
        success: function (response) {
          successLoader();
          if(!response.facturen.length){return;}

          let facturenString = '';
          for(const factuur of response.facturen){
            $(`.factuur-${factuur.id}`).remove();
            facturenString += `<b>${factuur.factuurnummer ?? 'n.t.b.'}</b>, `;
          }
          $("#snelstart-alert").html(`<div class="alert alert-success my-2 d-flex justify-content-between" >
                                        <span>Volgende facturen zijn via SnelStart afgerond: ${facturenString}</span>
                                        <span class="cursor-pointer px-2" onclick="emptyDiv('#snelstart-alert')" >@icon_close</span>
                                      </div>`);
        },
        error: function () {
          errorLoader('Snelstart synchronisatie niet gelukt!');
        }
      });
    }
    function insertFile(file){
      const string = randomString(15);
      $(`[data-email-attach-content='explorer']`).append(
        `<div id="file${string}" class="my-1 d-flex justify-content-between" >
              <div class="d-flex align-items-center" >
                <img height="50" src="${url}/client/public/img/explorer/files/${file.icon}" >
                <h4 class="my-0 mx-2" >${file.name}</h4>
              </div>
              <a class="btn text-danger align-self-center" onclick="deleteDiv('#file${string}')" >@icon_close</a>
              <input type="hidden" value="${file.id}" name="explorer_files[]">
            </div>`
      );
    }

    function exactGlobeInit(id){
      const klantenBeheren = @json(Auth::user()->hasPermissionTo('Klanten beheren'));

      const factuur = facturen.find(factuur => factuur.id == id);
      const { klant, regels, exact_globe_journal, exact_globe_id } = factuur;
      let div = '';

      if(!exact_globe_journal){
        exactGlobeRowInit(id);
        return;
      }
      for(const row of regels){
        if(!Number(row.tekstregel) && (!row.exact_globe_gl || !row.exact_globe_vat)){
          exactGlobeRowInit(id);
          return;
        }
      }

      const klantData = {
        button: `<a class="btn btn-success text-white btn-block" id="exact-globe-flow-klant-btn" onclick="uploadGlobeKlant(${klant.id})" >Klant Updaten</a>`,
        status: `<span id="exact-globe-flow-klant-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(!klant.exact_globe_id){
        klantData.button = `<a class="btn btn-primary text-white btn-block" id="exact-globe-flow-klant-btn" onclick="uploadGlobeKlant(${klant.id})" >Klant Uploaden</a>`;
        klantData.status = `<span id="exact-globe-flow-klant-status" class="mx-2" >@icon_minus</span>`;
      }

      div += `<div class="my-3" >
                    <div class="my-2 d-flex align-items-center" >
                        ${klantData.button}
                        ${klantenBeheren ? `<a class="btn btn-dark text-white mx-2" onclick="editKlant(${klant.id})" >@icon_edit</a>` : ''}
                        <a class="btn btn-dark text-white" onclick="showKlant(${klant.id})" >@icon_zoom</a>
                    </div>
                    <div class="bg-inverse-secondary rounded p-2 border" >
                        <div class="d-flex justify-content-between align-items-center" >
                            <span>Status: </span>
                            ${klantData.status}
                        </div>
                        <div id="exact-globe-flow-klant-errors" ></div>
                    </div>
                  </div>`

      const factuurData = {
        button: `<a class="btn btn-success text-white btn-block" id="exact-globe-flow-factuur-btn" onclick="uploadGlobeFactuur(${factuur.id})" >Factuur Updaten</a>`,
        status: `<span id="exact-globe-flow-factuur-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(!exact_globe_id){
        factuurData.button = `<a class="btn btn-primary text-white btn-block" id="exact-globe-flow-factuur-btn" onclick="uploadGlobeFactuur(${factuur.id})" >Factuur Uploaden</a>`;
        factuurData.status = `<span id="exact-globe-flow-factuur-status" class="mx-2" >@icon_minus</span>`;
      }
      div += `<div class="my-3">
                    <div class="my-2 d-flex align-items-center" >
                        ${factuurData.button}
                        <a class="btn btn-dark text-white ml-2" onclick="exactGlobeRowInit(${id})" >@icon_list</a>
                        <a class="btn btn-dark text-white ml-2" href="${url}/facturatie/facturen/pdf/${factuur.id}" target="_blank" >@icon_zoom</a>
                    </div>
                    <div class="bg-inverse-secondary rounded p-2 border" >
                        <div class="d-flex justify-content-between align-items-center" >
                            <span>Status: </span>
                            ${factuurData.status}
                        </div>
                        <div id="exact-globe-flow-factuur-errors" ></div>
                    </div>
                  </div>`

      confirmModal({
        text: div,
        hideFooter: true,
      })

      _eg.klant = {
        btn: $('#exact-globe-flow-klant-btn'),
        spinner: $('#exact-globe-flow-klant-status'),
        errors: $('#exact-globe-flow-klant-errors'),
      }
      _eg.factuur = {
        btn: $('#exact-globe-flow-factuur-btn'),
        spinner: $('#exact-globe-flow-factuur-status'),
        errors: $('#exact-globe-flow-factuur-errors'),
      }
    }
    function exactGlobeRowInit(id){
      const factuur = facturen.find(factuur => factuur.id == id);
      const { regels, exact_globe_journal } = factuur;

      let div = '';

      // Journal
      let _journal = null;
      if(exact_globe_journal){
        _journal = _eg.journals.find(row => row.journal_number == exact_globe_journal);
      }

      let options = '';
      for(const journal of _eg.journals){
        const { journal_number, description } = journal;
        options += `<span class="select_search-value" data-value="${journal_number}" data-name="${description}" ${_journal?.journal_number == journal_number ? `data-selected="true"` : ''}>
                        <span class="badge badge-primary" >${journal_number}</span>
                        ${description}
                    </span>`
      }

      div += `<div class="select_search-container my-2">
                <div class="font-size-11 font-weight-semibold my-2">Dagboek</div>
                <input type="hidden" data-journal class="select_search-hidden-input" data-placeholder="Dagboek">
                <div class="select_search-values" >
                  <div class="select_search-box" >${options}</div>
                </div>
              </div>`
      div += `<div class="font-size-11 font-weight-semibold mt-3">Grootboeken</div>`;

      for(const regel of regels){
        if(Number(regel.tekstregel)){ continue; }
        div += exactGlobeRowSelect(regel);
      }

      div += `<div class="mt-3 text-center"> <a onclick="storeExactGlobeRowGL(${factuur.id})" class="btn btn-success text-white" >Opslaan</a> </div>`;

      confirmModal({
        text: div,
        hideFooter: true,
      })
      initSelectGroup();
      initSelectSearch();

    }
    function exactGlobeRowSelect(regel){
      let div = '';

      // General ledger
      let options = '';
      for(const ledger of _eg.ledgers){
        let code = ledger.code.replaceAll(' ', '');
        let first = code.slice(0, 1);

        options += `<span class='select_group-value' data-group="${first}${ '0'.repeat(code.length - 1) }" data-value='${ledger.code}' data-name='${ledger.description || ''}'> <span class="badge badge-primary" >${ledger.code}</span> ${ledger.description}</span>`
      }

      let _ledger = null;
      if(regel.exact_globe_gl){
        _ledger = _eg.ledgers.find(row => row.code == regel.exact_globe_gl);
      }

      div += `<div class="select_group-container my-2" >
                <input type="hidden" data-eggl="${regel.id}" data-input-prefill="${_ledger?.description || ''}" value="${_ledger?.code || ''}" data-placeholder="Grootboek" data-required="required" class="select_group-hidden-input" required>
                <div class="select_group-values" >
                  <div class="select_group-box" >${options}</div>
                </div>
              </div>`;



      //VAT
      let _vat = null;
      if(regel.exact_globe_vat){
        _vat = _eg.vat.find(row => row.code == regel.exact_globe_vat);
      }

      options = '';
      for(const vat of _eg.vat){
        options += `<span class="select_search-value" data-value="${vat.code}" data-name="${vat.description}" ${_vat?.code == vat.code ? `data-selected="true"` : ''}>
                        <span class="badge badge-primary" >${vat.code}</span>
                        <span class="badge badge-inverse-primary" >${vat.percentage}%</span>
                        ${vat.description}
                    </span>`
      }

      div += `<div class="select_search-container my-2">
                <input type="hidden" data-egvat="${regel.id}" class="select_search-hidden-input" data-placeholder="BTW">
                <div class="select_search-values" >
                  <div class="select_search-box" >${options}</div>
                </div>
              </div>`



      // Display
      const btw = Number(regel.btw);
      const prijsExcl = Number(regel.incl) ? regel.prijs / (100 + btw) * 100 : regel.prijs;
      const prijsIncl = Number(regel.incl) ? regel.prijs : regel.prijs / 100 * (100 + btw);
      const totaalExcl = (prijsExcl * regel.aantal).toFixed(2);
      const totaalIncl = (prijsIncl * regel.aantal).toFixed(2);

      return `<div class="hover-shadow-inset-active rounded p-2 my-3" >
                <label class="d-flex justify-content-between" >
                  <span class="w-100" ><b>${regel.naam}</b></span>
                  <span class="w-100" >
                    <table class="w-100" >
                      <tr>
                        <td>Totaal Excl.</td>
                        <td class="text-right" ><b>€&nbsp;${totaalExcl}</b></td>
                      </tr>
                      <tr>
                        <td>Totaal Incl.</td>
                        <td class="text-right" ><b>€&nbsp;${totaalIncl}</b></td>
                      </tr>
                      <tr>
                        <td>BTW.</td>
                        <td class="text-right" ><b>${btw}%</b></td>
                      </tr>
                      <tr>
                        <td>Incl BTW.</td>
                        <td class="text-right" ><b>${ Number(regel.incl) ? '@icon_check' : '@icon_close' }</b></td>
                      </tr>
                    </table>
                  </span>
                </label>
                ${div}
              </div>`;
    }
    function storeExactGlobeRowGL(id){
      let empty = false;
      const journal = $('[data-journal]').val();
      if(!journal){
        notification('Dagboek kan niet leeg zijn!');
        return;
      }

      const ledgers = {};
      $('[data-eggl]').each(function(){
        const row_id = $(this).data('eggl');
        const GL = $(this).val();

        if(!GL){ empty = true; }
        ledgers[row_id] = GL;
      });
      if(empty){
        notification('Alle grootboeknummers zijn verplicht!');
        return;
      }

      const vat = {};
      $('[data-egvat]').each(function(){
        const row_id = $(this).data('egvat');
        const code = $(this).val();

        if(!code){ empty = true; }
        vat[row_id] = code;
      });
      if(empty){
        notification('Alle BTW codes zijn verplicht!');
        return;
      }

      loader();
      ajax('api/exact/globe/facturen/setrows', {factuur: id, journal: journal, ledgers: ledgers, vat: vat})
        .then(response => {
          try{
            const factuur = facturen.find(factuur => factuur.id == id);
            factuur.exact_globe_journal = journal;
            for(const regel of factuur.regels){
              regel.exact_globe_gl = ledgers[regel.id];
              regel.exact_globe_vat = vat[regel.id];
            }

            successLoader();
            exactGlobeInit(id);
          }
          catch (e) {
            actError(e);
            errorLoader();
          }

        })
        .catch(err => {
          actError(err);
          errorLoader();
        })
    }
    function uploadGlobeKlant(id){
      const eg = new ExactGlobe();

      _eg.klant.spinner.html(`@spinner_small`);
      _eg.klant.errors.empty();

      eg.uploadKlant(id)
        .then(response => {

          _eg.klant.spinner.html(`<span class="text-success" >@icon_confirm</span>`);
          _eg.klant.errors.empty();
          _eg.klant.btn.removeClass('btn-primary').addClass('btn-success').html('Klant Updaten');

          if(response?.klant?.guid){
            for(const factuur of facturen){
              if(factuur.klant?.id != id){ continue }
              factuur.klant.exact_globe_id = response.klant.guid;
            }
          }
        })
        .catch(err => {
          _eg.klant.spinner.html(`<span class="text-danger" >@icon_close</span>`);

          if(err?.responseJSON?.errors){
            let messages = '';
            for(const message of err.responseJSON.errors){
              messages += `<div class="text-danger font-size-08" >${message.replace(/\\r\\n/g, "<br />")}</div>`
            }
            _eg.klant.errors.html(`<div data-toggle-container >
                                  <div data-toggle-content style="display: none" class="w-100" >${messages}</div>
                                  <div class="cursor-pointer font-size-09 text-center text-muted" data-toggle-btn >
                                    <span>Errors</span>
                                    <a class="btn btn-sm" >@icon_down</a>
                                  </div>
                                </div>`)
          }
        })

    }
    function uploadGlobeFactuur(id){
      const eg = new ExactGlobe();

      _eg.factuur.spinner.html(`@spinner_small`);
      _eg.factuur.errors.empty();

      eg.uploadFactuur(id)
        .then(response => {

          _eg.factuur.spinner.html(`<span class="text-success" >@icon_confirm</span>`);
          _eg.factuur.errors.empty();
          _eg.factuur.btn.removeClass('btn-primary').addClass('btn-success').html('Factuur Updaten');
          $(`.exact-globe-init-btn-${id}`).removeClass('btn-primary').addClass('btn-success');

          if(response.id){
            facturen.find(factuur => factuur.id == id).exact_globe_id = response.id;
          }

        })
        .catch(err => {
          _eg.factuur.spinner.html(`<span class="text-danger" >@icon_close</span>`);

          if(err?.responseJSON?.errors){
            let messages = '';
            for(const message of err.responseJSON.errors){
              messages += `<div class="text-danger font-size-08" >${message.replace(/\\r\\n/g, "<br />")}</div>`
            }
            _eg.factuur.errors.html(`<div data-toggle-container >
                                  <div data-toggle-content style="display: none" class="w-100" >${messages}</div>
                                  <div class="cursor-pointer font-size-09 text-center text-muted" data-toggle-btn >
                                    <span>Errors</span>
                                    <a class="btn btn-sm" >@icon_down</a>
                                  </div>
                                </div>`)
          }
        })

    }

    function snelstartInit(id){
      const factuur = facturen.find(factuur => factuur.id == id);

      if(!factuur.klant){
        $("#snelstart-flow").html(`<h4 class="text-muted text-center my-2">Snelstart ondersteunt geen losse facturen!</h4>`);
        showModal('snelstart-modal');
        return false;
      }

      for(const regel of factuur.regels){
        if((!regel.snelstart_grootboek_id || regel.snelstart_grootboek_id == 'undefined') && !Number(regel.tekstregel)){
          snelstartGrootboekenInit(factuur.id);
          return false;
        }
      }

      const klantData = {
        button: `<a class="btn btn-success text-white btn-block" id="snelstart-flow-klant-btn" onclick="postKlantToSnelstart(${factuur.klant.id})" >Klant Updaten</a>`,
        status: `<span id="snelstart-flow-klant-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(!factuur.klant.snelstart_id){
        klantData.button = `<a class="btn btn-primary text-white btn-block" id="snelstart-flow-klant-btn" onclick="postKlantToSnelstart(${factuur.klant.id})" >Klant Uploaden</a>`;
        klantData.status = `<span id="snelstart-flow-klant-status" class="mx-2" >@icon_minus</span>`;
      }
      $("#snelstart-flow").html(
        `<div class="my-3" >
            <div class="my-2 d-flex align-items-center" >
              ${klantData.button}
              <a class="btn btn-dark text-white mx-2" onclick="editKlant(${factuur.klant.id})" >@icon_edit</a>
              <a class="btn btn-dark text-white" onclick="showKlant(${factuur.klant.id})" >@icon_zoom</a>
            </div>
            <div class="bg-inverse-secondary rounded p-2 border" >
              <div class="d-flex justify-content-between align-items-center" >
                <span>Status: </span>
                ${klantData.status}
              </div>
              <div id="snelstart-flow-klant-errors" ></div>
            </div>
          </div>`
      );

      const factuurData = {
        button: `<a class="btn btn-success text-white btn-block" id="snelstart-flow-factuur-btn" onclick="postFactuurToSnelstart(${factuur.id})" >Factuur Updaten</a>`,
        status: `<span id="snelstart-flow-factuur-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(!factuur.snelstart_id){
        factuurData.button = `<a class="btn btn-primary text-white btn-block" id="snelstart-flow-factuur-btn" onclick="postFactuurToSnelstart(${factuur.id})" >Factuur Uploaden</a>`;
        factuurData.status = `<span id="snelstart-flow-factuur-status" class="mx-2" >@icon_minus</span>`;
      }
      $("#snelstart-flow").append(
        `<div class="my-3" >
            <div class="my-2 d-flex align-items-center" >
              ${factuurData.button}
              <a class="btn btn-dark text-white ml-2" onclick="snelstartGrootboekenInit(${factuur.id})" >@icon_list</a>
            </div>
            <div class="bg-inverse-secondary rounded p-2 border" >
              <div class="d-flex justify-content-between align-items-center" >
                <span>Status: </span>
                ${factuurData.status}
              </div>
              <div id="snelstart-flow-factuur-errors" ></div>
            </div>
          </div>`
      );

      showModal('snelstart-modal');
    }
    async function snelstartGrootboekenInit(id){
      hideModal('snelstart-modal');
      bodyModalClass();

      const factuur = facturen.find(factuur => factuur.id == id);

      $("#snelstart-grootboeken-regels").html(
        `<div class="text-right" >
            <a class="btn btn-dark text-white tippy" onclick="syncSnelstartGrootboeken(${id})" data-tippy-content="Grootboeken synchroniseren" >@icon_cloud_download</a>
        </div>`
      );

      $("#snelstart-grootboeken-footer").html(`<a class="btn btn-success text-white" onclick="storeSnelstartGrootboeken(${id})" >Opslaan</a>`);

      let options = '';
      for(const grootboek of settings.snelstart_grootboeken.filter(row => row.bv_id == factuur.bv)){
        options += `<span class='select_group-value' data-group="${grootboek.functie}" data-value='${grootboek.snelstart_id}' data-name='${grootboek.omschrijving}'><div class="d-inline-block w-5-rem" >${grootboek.nummer}</div>${grootboek.omschrijving}</span>`
      }



      for(const regel of factuur.regels){
        if(Number(regel.tekstregel)){ continue;}

        const btw = new Decimal(regel.btw);
        const prijs = new Decimal(regel.prijs);
        const aantal = new Decimal(regel.aantal);
        const incl = Boolean(Number(regel.incl));

        let prijsExcl;

        if (incl) {
          prijsExcl = prijs.dividedBy(btw.dividedBy(100).plus(1)).toDecimalPlaces(2, Decimal.ROUND_HALF_UP);
        } else {
          prijsExcl = prijs.toDecimalPlaces(2, Decimal.ROUND_HALF_UP);
        }

        const totaalExcl = prijsExcl.times(aantal).toDecimalPlaces(2, Decimal.ROUND_HALF_UP).toString();
        const totaalIncl = new Decimal(totaalExcl).times(btw.dividedBy(100).plus(1)).toDecimalPlaces(2, Decimal.ROUND_HALF_UP).toString();

        let name = '';
        let value = '';

        if(regel.snelstart_grootboek_id){
          const grootboek = settings.snelstart_grootboeken.find(row => row.snelstart_id == regel.snelstart_grootboek_id);
          value = regel.snelstart_grootboek_id;
          name = grootboek ? grootboek.omschrijving : '';
        }
        else{
          const default_setting = `facturatie_snelstart_grootboek_bv${factuur.bv}_perc${regel.btw}`;

          if(!_snelstart.settings[default_setting]){
            loader();
            _snelstart.settings[default_setting] = await getSettingValue(default_setting, true)
            clearLoader();
          }

          value = _snelstart.settings[default_setting]?.value || '';
          name = _snelstart.settings[default_setting]?.name || '';
        }


        $("#snelstart-grootboeken-regels").append(
          `<div class="select_group-container bg-light-grey border my-2 p-2 rounded" >
            <label class="d-flex justify-content-between" >
              <span class="w-100" ><b>${regel.naam}</b></span>
              <span class="w-100" >
                <table class="w-100" >
                  <tr>
                    <td>Totaal Excl.</td>
                    <td class="text-right" ><b>€&nbsp;${totaalExcl}</b></td>
                  </tr>
                  <tr>
                    <td>Totaal Incl.</td>
                    <td class="text-right" ><b>€&nbsp;${totaalIncl}</b></td>
                  </tr>
                  <tr>
                    <td>BTW.</td>
                    <td class="text-right" ><b>${btw}%</b></td>
                  </tr>
                </table>
              </span>
            </label>
             <input type="hidden" value="${value}" data-row="${regel.id}" data-input-prefill="${name}" data-placeholder="Grootboeken" data-required="required"  class="select_group-hidden-input factuur-grootboeken">
             <div class="select_group-values" >
               <div class="select_group-box" >
                  ${options}
               </div>
             </div>
           </div>`
        )
      }


      $("#snelstart-grootboeken-regels").append(`<div id="snelstart-grootboeken-post"></div>`);

      initSelectGroup();
      tippyInit();
      $('#snelstart-grootboeken').showModal().then(() => {
        bodyModalClass(0);
      })
    }

    function postKlantToSnelstart(klantId){
      $("#snelstart-flow-klant-status").html('@spinner');
      $("#snelstart-flow-klant-errors").empty();
      $("#snelstart-flow-klant-btn").attr('onclick', '').toggleClass(['opacity-25']);

      $.ajax({
        type: "POST",
        url: "{{url("/snelstart/post/klant")}}",
        data: {
          id: klantId,
          _token: "{{csrf_token()}}",
        },
        success: function (response) {
          if(response.status != 201 && response.status != 200){
            $("#snelstart-flow-klant-status").html('<span class="text-danger" >@icon_close</span>');
            $("#snelstart-flow-klant-btn").attr('onclick', `postKlantToSnelstart(${klantId})`).toggleClass(['opacity-25']);

            for(const error of response.data){
              $("#snelstart-flow-klant-errors").append(
                `<small class="text-danger d-block my-2" >${error.message}</small>`
              );
            }
          }
          else{
            $("#snelstart-flow-klant-btn").attr('onclick', `postKlantToSnelstart(${klantId})`).removeClass(['opacity-25', 'btn-primary']).addClass('btn-success').text('Klant updaten');
            $("#snelstart-flow-klant-status").html('<span class="text-success" >@icon_confirm</span>');
            updateKlantenSnelstartId(klantId, response.data.id);
          }
        },
        error: function () {
          $("#snelstart-flow-klant-status").html('<span class="text-danger" >@icon_close</span>');
          $("#snelstart-flow-klant-btn").attr('onclick', `postKlantToSnelstart(${klantId})`).toggleClass(['opacity-25']);
          $("#snelstart-flow-klant-errors").append(`<span class="text-danger d-block my-2" >Er is iets foutgegaan</span>`);
        }
      });
    }
    function updateKlantenSnelstartId(klantId, snelstartId){
      for(const factuur of facturen){
        if(factuur.klant && factuur.klant.id == klantId){
          factuur.klant.snelstart_id = snelstartId;
        }
      }
    }

    function postFactuurToSnelstart(factuurId){
      $("#snelstart-flow-factuur-status").html('@spinner');
      $("#snelstart-flow-factuur-errors").empty();
      $("#snelstart-flow-factuur-btn").attr('onclick', '').toggleClass(['opacity-25']);

      $.ajax({
        type: "POST",
        url: "{{url("/snelstart/post/factuur")}}",
        data: {
          id: factuurId,
          _token: "{{csrf_token()}}",
        },
        success: function (response) {
          if(response.status != 201 && response.status != 200){
            $("#snelstart-flow-factuur-status").html('<span class="text-danger" >@icon_close</span>');
            $("#snelstart-flow-factuur-btn").attr('onclick', `postFactuurToSnelstart(${factuurId})`).toggleClass(['opacity-25']);

            for(const error of response.data){
              $("#snelstart-flow-factuur-errors").append(
                `<small class="text-danger d-block my-2" >${error.message}</small>`
              );
            }
          }
          else{
            $("#snelstart-flow-factuur-btn").attr('onclick', `postFactuurToSnelstart(${factuurId})`).removeClass(['opacity-25', 'btn-primary']).addClass('btn-success').text('Factuur updaten');
            $("#snelstart-flow-factuur-status").html('<span class="text-success" >@icon_confirm</span>');
            $(`.snelstart-init-btn-${factuurId}`).removeClass('btn-primary').addClass('btn-success');
            facturen.find(factuur => factuur.id == factuurId).snelstart_id = response.data.id;
          }
        },
        error: function () {
          $("#snelstart-flow-factuur-status").html('<span class="text-danger" >@icon_close</span>');
          $("#snelstart-flow-factuur-btn").attr('onclick', `postFactuurToSnelstart(${factuurId})`).toggleClass(['opacity-25']);
          $("#snelstart-flow-factuur-errors").append(`@alert_error_post`);
        }
      });
    }

    function syncSnelstartGrootboeken(factuurId){
      $("#snelstart-grootboeken-regels").html(`<div class="my-4 py-4 text-center" >@spinner_large</div>`);

      $.ajax({
        type: "POST",
        url: "{{url("snelstart/sync/grootboeken")}}",
        data: {
          _token: "{{csrf_token()}}",
        },
        success: function () {
          snelstartGrootboekenInit(factuurId);
        },
        error: function () {
          $("#snelstart-grootboeken-regels").html(`@alert_post_error`);
        }
      });
    }
    function storeSnelstartGrootboeken(factuurId){
      const factuur = facturen.find(factuur => factuur.id == factuurId)
      let ready = true;

      $("#snelstart-grootboeken").find("input").each(function(){
        $(this).removeClass(["border-danger"]);
        if($(this).prop("required") && !this.value){
          $(this).addClass(["border-danger"]);
          ready = false;
        }
      });

      if(!ready){return}

      const rows = {};
      $(".factuur-grootboeken").each(function(){
        rows[$(this).attr("data-row")] = this.value;
      });

      $("#snelstart-grootboeken-footer").html('@spinner');
      $.ajax({
        type: "POST",
        url: "{{url("snelstart/store/factuurregels-grootboeken")}}",
        data: {
          rows: JSON.stringify(rows),
          _token: "{{csrf_token()}}",
        },
        success: function () {
          rowsLoop: for(const rowId in rows){
            const value = rows[rowId];
            for(const row of factuur.regels){
              if(row.id == rowId){
                row.snelstart_grootboek_id = value;
                continue rowsLoop;
              }
            }
          }
          hideModal('snelstart-grootboeken');
          snelstartInit(factuurId);
        },
        error: function () {
          $("#snelstart-grootboeken-post").html('@alert_post_error ');
          $("#snelstart-grootboeken-footer").html(`<a class="btn btn-success text-white" onclick="storeSnelstartGrootboeken(${factuurId})" >Opslaan</a>`);

        }
      });
    }

    function eboekhoudenInit(id){
      const klantenBeheren = @json(Auth::user()->hasPermissionTo('Klanten beheren'));

      const factuur = facturen.find(factuur => factuur.id == id);
      const { klant, regels, eboekhouden } = factuur;
      for(const regel of regels){
        if(!regel.eboek_grootboek){
          eboekGrootboekenInit(factuur.id);
          return false;
        }
      }
      let div = '';

      const klantData = {
        button: `<a class="btn btn-success text-white btn-block" id="eboekhouden-flow-klant-btn" onclick="uploadEboekhoudenKlant(${klant.id})" >Klant Updaten</a>`,
        status: `<span id="eboekhouden-flow-klant-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(!klant.eboek_id){
        klantData.button = `<a class="btn btn-primary text-white btn-block" id="eboekhouden-flow-klant-btn" onclick="uploadEboekhoudenKlant(${klant.id})" >Klant Uploaden</a>`;
        klantData.status = `<span id="eboekhouden-flow-klant-status" class="mx-2" >@icon_minus</span>`;
      }

      div += `<div class="my-3" >
                    <div class="my-2 d-flex align-items-center" >
                        ${klantData.button}
                        ${klantenBeheren ? `<a class="btn btn-dark text-white mx-2" onclick="editKlant(${klant.id})" >@icon_edit</a>` : ''}
                        <a class="btn btn-dark text-white" onclick="showKlant(${klant.id})" >@icon_zoom</a>
                    </div>
                    <div class="bg-inverse-secondary rounded p-2 border" >
                        <div class="d-flex justify-content-between align-items-center" >
                            <span>Status: </span>
                            ${klantData.status}
                        </div>
                        <div id="eboekhouden-flow-klant-errors" ></div>
                    </div>
                  </div>`

      const factuurData = {
        button: `<a class="btn btn-success text-white btn-block" id="eboekhouden-flow-factuur-btn" onclick="uploadEboekhoudenFactuur(${factuur.id})" >Factuur Updaten</a>`,
        status: `<span id="eboekhouden-flow-factuur-status" class="mx-2" ><span class="text-success" >@icon_confirm</span></span>`
      }
      if(eboekhouden != 1){
        factuurData.button = `<a class="btn btn-primary text-white btn-block" id="eboekhouden-flow-factuur-btn" onclick="uploadEboekhoudenFactuur(${factuur.id})" >Factuur Uploaden</a>`;
        factuurData.status = `<span id="eboekhouden-flow-factuur-status" class="mx-2" >@icon_minus</span>`;
      }
      div += `<div class="my-3">
                    <div class="my-2 d-flex align-items-center" >
                        ${factuurData.button}
                        <a class="btn btn-dark text-white ml-2" onclick="eboekGrootboekenInit(${id})" >@icon_list</a>
                        <a class="btn btn-dark text-white ml-2" href="${url}/facturatie/facturen/pdf/${factuur.id}" target="_blank" >@icon_zoom</a>
                    </div>
                    <div class="bg-inverse-secondary rounded p-2 border" >
                        <div class="d-flex justify-content-between align-items-center" >
                            <span>Status: </span>
                            ${factuurData.status}
                        </div>
                        <div id="eboekhouden-flow-factuur-errors" ></div>
                    </div>
                  </div>`

      confirmModal({
        text: div,
        hideFooter: true,
      })

      _eg.klant = {
        btn: $('#eboekhouden-flow-klant-btn'),
        spinner: $('#eboekhouden-flow-klant-status'),
        errors: $('#eboekhouden-flow-klant-errors'),
      }
      _eg.factuur = {
        btn: $('#eboekhouden-flow-factuur-btn'),
        spinner: $('#eboekhouden-flow-factuur-status'),
        errors: $('#eboekhouden-flow-factuur-errors'),
      }
    }

    function eboekGrootboekenInit(id){
      const factuur = facturen.find(factuur => factuur.id == id);
      let div = '';
      let options = '';
      for(const grootboek of settings.eboekhouden_grootboeken){
        options += `<span class='select_group-value' data-group="${grootboek.categorie}" data-value='${grootboek.code}' data-name='${grootboek.omschrijving}'><div class="d-inline-block w-5-rem" >${grootboek.code}</div>${grootboek.omschrijving}</span>`
      }

      for(const regel of factuur.regels){

        const btw = settings.eboekhouden_btws.find(row => row.code == regel.eboek_btw);
        const prijsExcl = Number(regel.incl) ? regel.prijs / (100 + Number(btw?.percentage ?? 0)) * 100 : regel.prijs;
        const prijsIncl = Number(regel.incl) ? regel.prijs : regel.prijs / 100 * (100 + Number(btw?.percentage ?? 0));

        const totaalExcl = (prijsExcl * regel.aantal).toFixed(2);
        const totaalIncl = (prijsIncl * regel.aantal).toFixed(2);

        let name = settings.eboekhouden_grootboeken.find(row => row.code == settings.eboek_standaard_grootboek).omschrijving ?? '';
        let val = settings.eboek_standaard_grootboek ?? '';

        if(regel.eboek_grootboek){
          const grootboek = settings.eboekhouden_grootboeken.find(row => row.code == regel.eboek_grootboek);
          name = grootboek ? grootboek.omschrijving : '';
          val = regel.eboek_grootboek;
        }

        div += `
          <div class="select_group-container bg-light-grey border my-2 p-2 rounded" >
            <label class="d-flex justify-content-between" >
              <span class="w-100" ><b>${regel.naam}</b></span>
              <span class="w-100" >
                <table class="w-100" >
                  <tr>
                    <td>Totaal Excl.</td>
                    <td class="text-right" ><b>€&nbsp;${totaalExcl}</b></td>
                  </tr>
                  <tr>
                    <td>Totaal Incl.</td>
                    <td class="text-right" ><b>€&nbsp;${totaalIncl}</b></td>
                  </tr>
                  <tr>
                    <td>BTW.</td>
                    <td class="text-right" ><b>${btw?.omschrijving ?? 'Geen btw geselecteerd!'}</b></td>
                  </tr>
                </table>
              </span>
            </label>
            <input type="hidden" value="${val}" data-row="${regel.id}" data-input-prefill="${name}" data-placeholder="Grootboeken" data-required="required"  class="select_group-hidden-input factuur-eboek-grootboeken">
            <div class="select_group-values" >
              <div class="select_group-box" >
                ${options}
              </div>
            </div>
          </div>
        `;
      }
      confirmModal({
        text: div,
      }).then((response) => {
        if(!response.status){ return }
        let rows = {};
        $('.factuur-eboek-grootboeken').each(function(){
          const rowId = $(this).data('row');
          const value = $(this).val();
          rows[rowId] = value;
        })
        ajax('api/eboekhouden/facturen/setrows', {rows: rows})
          .then(response => {
            for(const rowId in rows){
              const value = rows[rowId];
              for(const row of factuur.regels){
                if(row.id == rowId){
                  row.eboekhouden_grootboek_id = value;
                  continue;
                }
              }
            }
            initFacturen();
          })
          .catch(handleCatchError);
      })
      initSelectGroup();
      tippyInit();
    }

    function uploadEboekhoudenKlant(klantid){
      $('#eboekhouden-flow-klant-status').html('@spinner');
      ajax('api/eboekhouden/post/klant', {id: klantid})
        .then(response => {
          if(!response){
            $('#eboekhouden-flow-klant-status').html('<span class="text-danger" >@icon_close</span>');
            return;}
          $('#eboekhouden-flow-klant-btn').removeClass('btn-primary').addClass('btn-success').html('Klant Updaten');
          $('#eboekhouden-flow-klant-status').html('<span class="text-success" >@icon_confirm</span>');
        })
        .catch(handleCatchError);
    }

    function uploadEboekhoudenFactuur(facid){
      $('#eboekhouden-flow-factuur-status').html('@spinner');
      ajax('api/eboekhouden/post/factuur', {id: facid})
        .then(response => {
          if(!response){
            $('#eboekhouden-flow-factuur-status').html('<span class="text-danger" >@icon_close</span>');
            return;}
          $('#eboekhouden-flow-factuur-btn').removeClass('btn-primary').addClass('btn-success').html('Factuur Updaten');
          $('#eboekhouden-flow-factuur-status').html('<span class="text-success" >@icon_confirm</span>');
          $(`.eboekhouden-init-btn-${facid}`).removeClass('btn-primary').addClass('btn-success');
        })
        .catch(handleCatchError);
    }

    function xmlExport(id){
      $('#xml-'+id).html('@spinner');
      $.ajax({
        type: "POST",
        url: "{{url("facturatie/facturen/exact/xml")}}",
        data: {
          id: id,
          _token: "{{csrf_token()}}",
        },
        success: function(){
          $('#xml-'+id).removeClass('btn-primary');
          $('#xml-'+id).addClass('btn-success');
          $('#xml-'+id).html('XML');
          $('#xml-'+id).attr("onclick","")
        }
      });
    }

  </script>
@endsection
