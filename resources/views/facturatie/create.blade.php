@extends('layouts.app')
@section('title', 'Nieuwe factuur')
  @php
    $type = $_GET['type'] ?? 'factuur';
    $_required = getSettingJson('facturatie_required');
    $_required = $_required[$type] ?? [];


    $ledgers = exactGlobe(['no_curl' => true])->getStoredGLByCode(8);
    if(getSettingCheckbox('facturatie_exact_globe_only_selected_ledgers')){
        $codes = resetIndex(json_decode((getSettingValue('facturatie_exact_globe_ledgers') ?? '[]'), true));
        $ledgers = $ledgers->whereIn('code', $codes);
    }
    $ledgers = resetIndex($ledgers);
  @endphp
@section('content')
  <form @if(!isset($factuur) && $type == 'proforma')  action="{{url("facturatie/proforma/new")}}" @endif class="append-loader" method="post" data-factuur-form>
    @isset($factuur)
      <input type="hidden" name="factuur" value="{{$factuur->id}}">
    @endisset
    <div class="card hover-shadow p-2 my-3">
      <div class="row">
        <div class="col-md-6 col-12 my-2">
          <div class="d-flex justify-content-between">
            <label>Klant</label>
            <div id="klanten-buttons"></div>
          </div>
          <infor-search
            id="klant"
            name="klantId"
            placeholder="Selecteer klant"
            class="form-control-custom"
            data-content="naam||contactpersoon_voornaam, contactpersoon_achternaam"
            data-sub-content="straat, huisnummer,toevoeging"
            data-api="api/klanten/search"
          ></infor-search>
        </div>

        <div class="col-md-6 col-12 my-2">
          <label>Referentie klant</label>
          <input type="text" name="referentie" class="form-control-custom" placeholder="Referentie klant" value="{{isset($factuur) ? $factuur->referentie : ''}}">
        </div>
        @if(businessCentralLocal()->connected)
          <div class="col-md-6 col-12 my-2">
            <label>Kostendrager*</label>
            <infor-select-search class="form-control-custom" id="bc_kostendrager" name="bc_kostendrager" placeholder="Selecteer kostendrager">
              @foreach(businessCentralLocal()->getStoredDimensions('KOSTENDRAGER') as $kostendrager)
                <infor-select-option data-value="{{$kostendrager->code}}" data-name="{{$kostendrager->name}}" @if(isset($factuur) && $factuur->business_central_kostendrager == $kostendrager->code) data-selected @endif> <span class="badge badge-inverse-primary" >{{$kostendrager->code}}</span> {{$kostendrager->name}}</infor-select-option>
              @endforeach
            </infor-select-search>
          </div>
        @endif
        @if(king()->connected)
          <div class="col-md-6 col-12 my-2">
            <label>Dagboek*</label>
            <infor-select-search class="form-control-custom" id="king_dagboek" name="king_dagboek" placeholder="Selecteer dagboek">
              @foreach(king()->getStored('Dagboeken') as $dagboek)
                <infor-select-option data-value="{{$dagboek->code}}" data-name="{{$dagboek->naam}}" @if(getSettingJson('king_defaults')['dagboek'] == $dagboek->king_id || (isset($factuur) && $factuur->king_dagboek == $dagboek->king_id)) data-selected="true" @endif > <span class="badge badge-inverse-primary" >{{$dagboek->king_id}}</span> {{$dagboek->naam}}</infor-select-option>
              @endforeach
            </infor-select-search>
          </div>
        @endif
        <div class="col-md-6 col-12 my-2">
          <label>{{$type != 'factuur' ? 'Beslissingstermijn' : 'Betalingstermijn'}}</label>
          <input readonly type="date" class="form-control-custom" name="betalingstermijn" value="{{isset($factuur) ? $factuur->betalingstermijn : \Carbon\Carbon::now()->addDays(getSettingValue('factuur_'.($type != 'factuur' ? 'beslissingstermijn' : 'betalingstermijn'), 30))->format("Y-m-d")}}" required>
        </div>
        @if(exactGlobe(['no_curl' => true])->connected && $type == 'factuur')
          <div class="col-md-6 col-12 my-2 select_search-container">
            <label>Dagboek</label>
            @php
              foreach(exactGlobe(['no_curl' => true])->getStoredJournals() as $journal){
                if(getSettingValue('facturatie_exact_globe_default_journal') == $journal->journal_number){
                  $selectedjournal = $journal;
                  break;
                }
              }
            @endphp
            <input disabled name="journal" class="form-control-custom" value="{{$selectedjournal->journal_number ?? ''}}">
          </div>
        @endif
        @if(getSettingValue('facturatie_factuur_reporting_date') == 'aan' && $type == 'factuur')
          <div class="col-md-6 col-12 my-2">
            <label>Rapportagedatum</label>
            <input readonly type="date" class="form-control-custom" name="reporting_date" value="{{isset($factuur) ? $factuur->reporting_date : \Carbon\Carbon::now()->addDays(getSettingValue("facturatie_factuur_reporting_date_default") ?? 0)->format("Y-m-d")}}" required>
          </div>
        @endif
        @if(getSettingValue('facturatie_factuur_date_periode') == 'aan')
          <div class="col-md-6 col-12 my-2">
            <label>Periode</label>
            <select class="form-select" name="periode">
              @foreach(getFourWeeklyPeriods() as $period)
                <option @if(isset($factuur) ? $factuur->periode == $period->index : $period->is_current) selected  @endif value="{{$period->index}}">{{$period->index}}:&nbsp;&nbsp;{{$period->start->day}} {{getShortMaanden($period->start->month)}}  -  {{$period->end->day}} {{ getShortMaanden($period->end->month) }}</option>
              @endforeach
            </select>
          </div>
        @endif
        @if(getSettingValue('facturatie_posting_description') !== 'uit')
          <div class="col-md-6 col-12 my-2">
            <label>Boekingsomschrijving*</label>
            @if(getSettingValue('facturatie_posting_description') == 'year_month')
              <infor-select-search id="posting_description" class="form-control-custom" name="posting_description" placeholder="Selecteer boekingsomschrijving" required>
                @foreach(CarbonPeriod( Carbon(date('Y-m-d'))->firstOfYear()->subMonths(3), Carbon(date('Y-m-d'))->lastOfYear(), '1 month' ) as $period)
                  <infor-select-option data-value="{{$period->format('Ym')}}" data-name="{{$period->format('Ym')}}" @if(isset($factuur) && $factuur->posting_description == $period->format('Ym')) data-selected @endif>{{$period->format('Ym')}}</infor-select-option>
                @endforeach
              </infor-select-search>
            @endif
          </div>
        @endif
      </div>
    </div>

    <div class="card hover-shadow p-2 my-3 g_rekening_container d-none">
      <div class="row">
          <div class="col-md-6 col-12 my-2" >
            <label>G rekening nummer</label>
            <input class="form-control-custom mx-1 w-100" name="factuur_grekeningNr" placeholder="G rekening nummer" >
          </div>
          <div class="col-md-6 col-12 my-2">
            <label>Percentage naar G-Rekening</label>
            <input type="number" class="form-control-custom" name="factuur_grekening">
          </div>
      </div>
    </div>

    <div class="klant-divs card hover-shadow p-2 my-3 d-none">
      <b>Factuuradres</b>
      @php $lockFa = getSettingValue('facturatie_factuur_lock_factuuradres') == 'ja' @endphp
      <div class="row">
        <div class="col-md-6 col-12 my-2">
          <label>Klant</label>
          <input @if($lockFa) readonly @endif name="fa_klant" class="form-control-custom" placeholder="Klant" >
        </div>
        <div class="select_edit-container col-md-6 col-12 my-2">
          <label>T.a.v.</label>
          <input type="text" autocomplete="off" name="fa_tav" class="select_edit-input form-select" placeholder="T.a.v">
          <div class="select_edit-values">
            <div class="select_edit-box klant-contactpersonen"></div>
          </div>
        </div>
        <div class="col-md-6 col-12 my-2">
          <label>Straat</label>
          <input @if($lockFa) readonly @endif name="fa_straat" class="form-control-custom" placeholder="Straat" >
        </div>
        <div class="col-md-3 col-6 my-2">
          <label>Huisnummer</label>
          <input @if($lockFa) readonly @endif name="fa_huisnummer" class="form-control-custom" placeholder="Huisnummer" >
        </div>
        <div class="col-md-3 col-6 my-2">
          <label>Toevoeging</label>
          <input @if($lockFa) readonly @endif name="fa_toevoeging" class="form-control-custom" placeholder="Toevoeging" >
        </div>
        <div class="col-md-6 col-12 my-2">
          <label>Postcode</label>
          <input @if($lockFa) readonly @endif name="fa_postcode" class="form-control-custom" placeholder="Postcode" >
        </div>
        <div class="col-md-6 col-12 my-2">
          <label>Plaats</label>
          <input @if($lockFa) readonly @endif name="fa_plaats" class="form-control-custom" placeholder="Plaats" >
        </div>
        <div class="col-md-6 col-12 my-2">
          <label>Land</label>
          <select @if($lockFa) readonly @endif name="fa_land" class="form-select">
            @foreach(getLanden() as $land)
                <option value="{{$land->code}}" @if(isset($factuur) && $factuur->fa_land == $land->code) selected @endif >{{$land->naam}}</option>
            @endforeach
          </select>
        </div>
      </div>
    </div>

    @if(hasModule("Projecten"))
      <div class="klant-divs card hover-shadow p-2 my-3 projecten-card-container d-none">
        <div class="row">
          <div class="my-2 col-md-6 col-12 projecten-container d-none">
            <span>Projecten</span>
            <span class="projecten-select"></span>
          </div>
        </div>
      </div>
    @endif

    <div class="d-none project-data-container"></div>

    @if($type == 'proforma' && (hasModule("Offertes") || hasModule("Projecten")))
      <div class="klant-divs card hover-shadow p-2 my-3 project-proformas-container d-none">
        <label>Project Pro Forma's</label>
        <div data-project-proformas-content></div>
      </div>
    @endif

    @if(getSettingValue('facturatie_factuur_mandagen') == 'ja')
      <div class="klant-divs card hover-shadow p-2 my-3 d-none">
        <label>Mandagenregister</label>
        <div>
          <a class="btn btn-primary text-white m-1 " onclick="addMandagen()">Periode kiezen @icon_plus</a>
        </div>
        <div class="mandagen-container overflow-auto">
          <table class="table-bordered mandagen-table"></table>
        </div>
      </div>
    @endif

    <div class="card hover-shadow p-2 my-3 @if (getSettingValue('facturatie_factuur_disable_inleiding') == 'ja') d-none @endif">
      <label>Inleiding</label>
      <textarea name="inleiding" placeholder="Inleiding">{!! $factuur->inleiding ?? getSettingValue('factuur_inleiding') !!}</textarea>
    </div>

    @php
        $customfunction = getSettingValue('facturatie_custom_functie');
        $clientid = getClientId();
    @endphp
    @if(isset($customfunction))
      @include("facturatie.custom.client$clientid.$customfunction")
    @endisset

    <div class="card hover-shadow p-2 my-3">
      <div class="py-1 overflow-auto" data-overflow-x-only >
        <table class="table" >
          <thead>
            <tr>
              <th class="w-0" ></th>
              @if(exactGlobe(['no_curl' => true])->connected && $type == 'factuur')
                <th class="min-w-250" >Grootboek*</th>
              @endif
              @if(businessCentralLocal()->connected && $type == 'factuur')
                <th class="min-w-250" >Grootboek*</th>
              @endif
              @if(exactOnlineLocal()->connected && $type == 'factuur')
                <th>Artikel*</th>
                <th>Grootboek*</th>
              @endif
              @if(king()->connected)
                <th class="min-w-250" >Grootboek*</th>
              @endif
              <th class="min-w-300 w-100" >Omschrijving</th>
              @foreach (getSettingJson('factuur_rows_custom_fields') as $cfield)
                <th class="min-w-150" >{{$cfield['label']}}</th>
              @endforeach
              <th class="min-w-150" style="width: 150px" >Aantal</th>
              @if (getSettingValue('facturatie_factuurregels_eenheden') == 'aan')
                <th class="min-w-250">Eenheid</th>
              @endif
              <th class="min-w-150" style="width: 150px" >Bedrag</th>

              <th class="w-0 min-w-150" >BTW</th>
              @if(!exactGlobe(['no_curl' => true])->connected && !exactOnlineLocal()->connected && !businessCentralLocal()->connected && !isEboekhouden())
                <th class="w-0" >Incl. BTW</th>
              @endif
              <th class="w-0" >Totaal excl. BTW</th>
              <th></th>
              <th></th>
            </tr>
          </thead>
          <tbody id="regels" data-grab-container></tbody>
          <tfoot data-footer-totaal>
            <td></td>
            @if((exactGlobe(['no_curl' => true])->connected || businessCentralLocal()->connected) && $type == 'factuur')
              <td></td>
            @endif
            @if(exactOnlineLocal()->connected && $type == 'factuur')
              <td colspan="2" ></td>
            @endif
            @if(king()->connected)
              <td></td>
            @endif
            <td></td>
            @foreach (getSettingJson('factuur_rows_custom_fields') as $cfield)
              <td></td>
            @endforeach
            <td></td>
            @if (getSettingValue('facturatie_factuurregels_eenheden') == 'aan')
              <td></td>
            @endif
            <td></td>
            @if(!exactGlobe(['no_curl' => true])->connected && !exactOnlineLocal()->connected && !isEboekhouden())
              <td></td>
            @endif
            <td class="text-right" colspan="1">Totaal bedrag</td>
            <td id="totaalBedrag" colspan="2"></td>
          </tfoot>
        </table>
      </div>
      <div class="d-flex justify-content-end mx--1 my-3 ">
        @if(count($details))
          <div class="m-1">
            <div>
              <a class="btn btn-inverse-primary text-white" onclick="showModal('detail-modal')" >Details</a>
            </div>
          </div>
        @endif
        @foreach(getFactuurSettingsDatasets() as $id => $set)
          <a class="btn btn-inverse-primary m-1" onclick="initDatasetItem({{$id}})" >{{$set['dataset']->naam}} toevoegen</a>
        @endforeach
        @if(getSettingValue('facturatie_factuur_disable_single_rows') != 'ja')
          <a class="btn btn-primary text-white m-1" onclick="addRow()">Regel toevoegen @icon_plus</a>
        @endif
        @if(getSettingValue('facturatie_factuur_disable_text_rows') != 'ja')
          <a class="btn btn-primary text-white m-1" onclick="addTextRow()">Tekstregel toevoegen @icon_plus</a>
        @endif
      </div>
    </div>

    <div class="modal fade" id="detail-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
          @foreach($details->groupBy('template_id') as $detail_templates)
              @foreach($detail_templates[0]->templates as $template)
                  <div class="py-1 px-1 my-2 w-100 border rounded-10" data-toggle-container>
                    <span class="py-1 px-2 d-block  cursor-pointer rounded-1 w-100" data-toggle-btn>{{$template->naam ?? 'Template'}} <span class="float-end">@icon_down</span></span>
                    <div data-toggle-content style="display: none" class="w-100 pt-2" >
                      @foreach($details as $detail)
                        @if($detail->templates->contains('id', $template->id))
                          <div class="w-auto my-2" >
                            <div class="btn-group border-0 d-flex w-100">
                            <span class="w-100 p-2 rounded-pill hover-shadow  @if(!count($detail->prefilled)) rounded-right @endif" data-toggle="dropdown" aria-haspopup="true"  aria-expanded="false" data-id="{{$detail->id}}" >
                              <div class="">
                                <span class="" >{{$detail->name}}</span>
                                <span class="float-end" ><i class="fas fa-angle-down m-0"></i></span>
                              </div>
                            </span>
                              <div class="dropdown-menu max-h-500 overflow-auto w-100" id="search-div-{{$detail->id}}">
                                <div>
                                  <input type="text" class="form-control-plaintext px-4 detail-search"  placeholder="Zoeken..." data-search-target="#search-div-{{$detail->id}}" >
                                </div>
                                <div class="search-row detail-template-{{$detail->template_id}}" data-search="{{$template->naam}}">
                                  <div class="d-flex justify-content-between align-items-center" >
                                    <div class="dropdown-item d-flex justify-content-between cursor-pointer " data-id="{{$detail->id}}" onclick="addFactuurDetail({{$detail->id}}, null, null, {{$detail->id}}); hideModal('detail-modal')" data-id="{{$detail->id}}">
                                      <span class="mr-3">Nieuw detail</span>
                                      <span class="">@icon_plus</span>
                                    </div>
                                  </div>
                                </div>
                                @foreach($detail->prefilled as $prefilled)
                                  <div class="search-row detail-template-{{$prefilled->id}}" data-search="{{$prefilled->title}}">
                                    <div class="d-flex justify-content-between align-items-center" >
                                      <div class="dropdown-item d-flex justify-content-between cursor-pointer " data-id="{{$detail->id}}" onclick="addFactuurDetail({{$detail->id}}, null, {{$prefilled->id}}, {{$detail->id}}); hideModal('detail-modal')" data-prefill="{{$prefilled->id}}">
                                        <span class="mr-3" >{{$prefilled->title}}</span>
                                        @if(isset($prefilled->price))
                                          <span >€&nbsp;{{number_format($prefilled->price, 2, ',', '.')}}</span>
                                        @endif
                                      </div>
                                      @if(Auth::user()->hasPermissionTo('Details beheren'))
                                        <div class="cursor-pointer text-center btn btn-outline-danger" onclick="confirmDeleteDetailTemplate({{$prefilled->id}})" >
                                          @icon_trash
                                        </div>
                                      @endif
                                    </div>
                                  </div>
                                @endforeach
                              </div>
                            </div>
                          </div>
                        @endif
                      @endforeach
                    </div>
                  </div>
                @endforeach
            @endforeach
          </div>
          <div class="modal-footer">
            <span class="btn-primary py-2 px-4 rounded-2 cursor-pointer" onclick="hideModal('detail-modal')">Close</span>
          </div>
        </div>
      </div>
    </div>

    <div class="card hover-shadow p-2 my-3 @if (getSettingValue('facturatie_factuur_disable_slot') == 'ja') d-none @endif">
      <label>Slot</label>
      <textarea name="slot" placeholder="Slot">{!! $factuur->slot ?? getSettingValue('factuur_slot') !!}</textarea>
    </div>

      {{--Files--}}
      @if(hasModule("Bestanden"))
        <section class="card section-container">
          <div class="section-content">

            <div>
              <label>Bijlage</label>
              <div id="files"></div>
              <div>
                <a onclick="selectFromExplorer()" class="btn btn-dark text-white rounded-pill my-2" data-tippy-content="Bestand selecteren" >@icon_file</a>
                <a onclick="uploadToExplorer()" class="btn btn-dark text-white rounded-pill my-2" data-tippy-content="Bestand uploaden" >@icon_upload</a>
              </div>
            </div>

          </div>
          <a class="section-toggle">Bestanden</a>
        </section>
      @endif

    <div class="flex-between my-4">
      <div class="w-33">
        @if(getSettingValue('factuur_btw_verlegd_per_factuur') == 'ja')
          <div>
            <label class="cursor-pointer my-0 mx-1" ><input class="form-switch-custom mx-1" type="checkbox" name="btw_verlegd"> BTW verlegd</label>
          </div>
        @endif
      </div>
      <div class="w-33 text-center ">
        <input type="submit" class="btn btn-success" value="Opslaan">
        @csrf
      </div>
      <div class="w-33"></div>
    </div>

  </form>

  {{--  Modals--}}
  <section>
    <div class="modal fade" id="datasets-items" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div class="my-2" >
              <input class="form-control-custom search" id="dataset_search" placeholder="Zoeken">
            </div>
            <div class="items"></div>
          </div>
        </div>
      </div>
    </div>
  </section>
@endsection
@section('script')
  <script src="{{url('client/public/js/offertes.js')}}"></script>
  <script>

    const factuur = @json($factuur ?? null);

    const standaardtarief = @json(getStandaardUurTarief());
    const users = @json(getUsers());
    const _required = @json($_required);

    var klant_listener;


    const utilities = {};
    const editors = {}
    const _this = {
      type: @json($type),
      klant: null,
      project_type: 'Inactive',
      get_prefilled: false,
    }


    //Data
    const _offertes = {
      all: [],
      meerwerk: [],
      selected: {},
      container: $('.offertes-container'),
      select: $('.offertes-select'),
    }
    const _projecten = {
      all: [],
      selected: 0,
    }
    const _werkbonnen = {
      all: [],
      selected: {},
    }
    const _proformas = {
      all: [],
      selected: {},
    }
    const _mandagen = {
      container: $('.mandagen-container'),
      table: $('.mandagen-table'),
      dates: [],
    }
    const _uren = {
      uren: 0,
      machines: {},
      uursoorten: {},
      urenGroupedByTarief: {},
    }
    const _datasets = {
      all: @json(getFactuurSettingsDatasets()),
      modal: $('#datasets-items'),
      search: $('#datasets-items').find('.search'),
    }


    //Koppelingen
    const _eg = { //Exact Globe
      instance: new ExactGlobe(),
      ledgers: @json($ledgers),
      vat: @json(exactGlobe(['no_curl' => true])->getStoredVat(null, ['percentage', json_decode(getSettingValue('factuur_btw_percs'))])),
    }
    const _eo = { //Exact Online
      instance: new ExactOnline(),
      vat: @json(exactOnlineLocal()->getStoredVat()),
      items: @json(exactOnlineLocal()->getStoredItems()),
      general_ledgers: @json(exactOnlineLocal()->getStoredGL()),
    }
    const _bc = { //Business Central
      instance: new BusinessCentral(),
      tax_groups: @json(businessCentralLocal()->getStoredTaxGroups()),
      accounts: @json(businessCentralLocal()->getStoredAccounts(8)),
    }
    const _eb = { //Eboekhouden
      connected: _global.eboekhouden_connected,
      vat: @json(isEboekhouden() ? eboekBtwCodes() : []),
    }
    const _king = { //King
      instance: new King(),
      dagboeken: @json(king()->getStored('Dagboeken')),
      btw_codes: @json(king()->getStored('BtwCodes')),
      grootboeken: @json(king()->getStored('Grootboeken')),
    }

    //2ba
    const tweeba = {
      settings: @json(getSettingJson('2ba_settings')),
      leveranciers: null,
      search_request: null,
      loaded_product_trade_items: {},
      temp_products: [],
      temp_trade_items: []
    }


    const settings = {
      prefillRows: @json(getSettingJson('factuur_custom_rows')),
      prefillDetails: @json(getSettingCheckbox('proforma_details_prefil_most_recent')),
      business_central: @json(getSettingJson('business_central_settings')),
      exact_online: @json(getSettingJson('exact_online_settings')),
      projecttaken_factureren: @json(getSettingValue('factuur_projecttaken_factureren') == 'ja'),
      projecturen_overnemen: @json(getSettingValue('projecturen_overnemen') == 'ja'),
      uursoorten_per_week: @json(getSettingValue('factuur_uursoorten_per_week') == 'ja'),
      factuur_mandagen: @json(getSettingValue('facturatie_factuur_mandagen') == 'ja'),
      posting_description: @json(getSettingValue('facturatie_posting_description')),
      termijnprefill: @json(getSettingValue('factuur_termijn_prefill')),
      datasetItemsFromWerkbon : @json(getSettingValue('datasetitems_from_werkbon') == 'ja'),
      urenFromWerkbon : @json(getSettingValue('uren_from_werkbon') == 'ja'),
      kmsFromWerkbon : @json(getSettingValue('kms_from_werkbon') == 'ja'),
      reistijdFromWerkbon : @json(getSettingValue('reistijd_from_werkbon') == 'ja'),
      werkbon_zonder_factuurregel : @json(getSettingValue('werkbon_zonder_factuurregel') == 'ja'),
      king_defaults: @json(getSettingJson('king_defaults')),
      geenProjecturenSelectOfferte: @json(getSettingValue('geen_projecturen_select_offerte') == 'ja'),
      grekeningObvKlant: @json(getSettingValue('facturatie_grekening_obv_klant') == 'ja'),
      eenhedenOn: @json(getSettingValue('facturatie_factuurregels_eenheden') == 'aan'),
      eenheden: @json(getEenheden()),
      customFieldsOn: @json(!empty(getSettingJson('factuur_rows_custom_fields'))),
      customFields: resetIndex(@json(getSettingJson('factuur_rows_custom_fields'))),
      factuur_uren_omhoog_afronden: @json(getSettingValue('factuur_project_uren_afronden_naar_eerst_volgende_uur') == 'ja'),
      proforma_regels_samenvoegen: @json(getSettingCheckbox('facturatie_proforma_regels_samenvoegen')),
    }

    $(document).ready(() => {

      _inforSearch.get('klant').onchange = selectKlant;

      checkAccorderenProformaEdit();
      utilitiesInit();
      prefillRows();
      getInit();
      initEditors();
      editInit();
    });

    //Listeners
    $(document).on("keyup", "#dataset_search", function(){
      let search = $(this).val().toLowerCase().split(' ');
      $(".item-row").each(function(){
        let attr = $(this).attr("data-search").toLowerCase().replace(' ', '');
        attr = attr.replace(new RegExp(' ', 'g'), '');
        let includes = true;
        for(let s of search){
          if(!attr.includes(s)){
            includes = false;
          }
        }
        if(includes){
          $(this).removeClass("d-none")
        }
        else{
          $(this).addClass("d-none");
        }
      });
    });
    $('[data-factuur-form]').submit(function(){

      if(!$('[name=klantId]').val()){
        notification("Selecteer een klant", 'danger');
        return false;
      }

      if(_required['project']){
        if(!_projecten.selected){
          notification('Selecteer een project!', 'danger');
          return false;
        }
      }
      if(_required['offerte']){
        if(!Object.keys(_offertes.selected).length){
          notification('Selecteer minstens een offerte!', 'danger');
          return false;
        }
      }

      if(_bc.instance.connected){
        if(!_inforSelectSearch.get('bc_kostendrager').getValue().value){
          notification('Selecteer kostendrager!', 'danger');
          return false;
        }
      }
      if (_king.instance.connected) {
        if (!_inforSelectSearch.get('king_dagboek').getValue().value) {
          notification('Selecteer dagboek!', 'danger');
          return false;
        }
      }
      if(_eo.instance.connected){
        let valid = true;

        $('[data-exact-online-vat], [data-exact-online-item], [data-exact-online-gl]').each(function(){
          const container = findContainerByTag('infor-select-search', this);
          const instance = _inforSelectSearch.instanceByContainer(container);
          const input_valid = !!instance.getValue().value

          instance.input_display().toggleClass('border-danger', !input_valid);
          if(!input_valid){ valid = false; }
        });

        if(!valid){
          notification('Vul alle Exact Online velden in!');
          return false;
        }
      }
      if(settings.posting_description != 'uit' && !$('[name=posting_description]').val()){
        notification('Selecteer boekingsomcshrijving!', 'danger');
        return false;
      }

      $(this).find('[type=submit]').addClass('d-none').after('@spinner_success');
      return true;
    });
    $(document).on('change', '#exact_btw_select', function(){
      let container = findContainerByAttr('data-tippy-content', this);
      let description = _eg.vat.find(row => row.code == $(this).val()).description;
      let content = description + ' | ' + $("#exact_btw_select").val();
      container.attr('data-tippy-content', content);
      tippyInit()
      tippyUpdate()
    });
    $(document).on('change', '#eb_btw_select', function(){
      let container = findContainerByAttr('data-tippy-content', this);
      let description = _eb.vat.find(row => row.code == $(this).val()).omschrijving;
      let content = description + ' | ' + $("#eb_btw_select").val();
      container.attr('data-tippy-content', content);
      tippyInit()
      tippyUpdate()
    });

    //Inits
    function initEditors(){
      editorInit('[name=inleiding]').then(editor => editors['inleiding'] = editor);
      editorInit('[name=slot]').then(editor => editors['slot'] = editor);
    }
    function utilitiesInit(){
      const percs = @json(json_decode(getSettingValue("factuur_btw_percs") ?? '[]'));
      const standaard = @json(getSettingValue('factuur_btw_perc') ?? 21);
      const standaard_exact = @json(getSettingValue('facturatie_exact_globe_default_vat') ?? '');
      const standaard_eb = @json(getSettingValue('facturatie_eboekhouden_default_vat') ?? '');

      utilities.select = (string, btw = null) => {
        let options = '';

        for(const perc of percs){
          options += `<span class="select_edit-value" data-value="${perc}">${perc}%</span>`
        }

        return `<div class="select_edit-container">
                  <input type="text" autocomplete="off" name="btw[${string}]" value="${btw ?? standaard}" class="select_edit-input form-select row-btw" data-btw placeholder="BTW %" >
                  <div class="select_edit-values">
                    <div class="select_edit-box">${options}</div>
                  </div>
                </div>`
      };
      utilities.exactBtw = (string) => {
        let options = '';
        for(const vat of _eg.vat){
          const selected = vat.code == standaard_exact ? 'data-selected="true"' : '';
          options += `<span class="select_search-value" ${selected} data-name="${vat.percentage}% ${Number(vat.incl) ? 'Incl.' : 'Excl.'}" data-value="${vat.code}" > <span class="badge badge-primary" >${vat.percentage}%</span> ${vat.description} </span>`
        }

        return `<div class="select_search-container tippy" data-tippy-content="">
                  <input type="hidden" id="exact_btw_select" name="exact_btw[${string}]" data-required="required"  class="select_search-hidden-input row-exact-btw" data-placeholder="BTW">
                  <div class="select_search-values">
                    <div class="select_search-box " >${options} </div>
                  </div>
                </div>`
      }
      utilities.exactOnlineBtw = (string) => {
        const def = settings.exact_online?.default?.vat;
        return `<infor-select-search id="eo_vat_${string}" class="form-select min-w-225" name="exact_online[${string}][vat]" data-exact-online-vat="${string}" placeholder="BTW" >
                  ${_eo.vat.map(vat => `<infor-select-option data-params=":eo-division-option=${vat.division}" data-value="${vat.code}" data-name="${vat.description}" ${vat.code == def ? 'data-selected' : ''} >
                                          <span class="badge badge-inverse-primary">${vat.percentage}% ${Number(vat.incl) ? 'Incl.' : 'Excl.'} </span>
                                          <span>${vat.description}</span>
                                        </infor-select-option>`).join('')}
                </infor-select-search>`
      }
      utilities.businessCentralBtw = (string) => {
        const def = settings.business_central?.default?.tax_group
        return `<select class="form-select bc_tax_group min-w-225" name="bc_tax_group[${string}]" required >
                <option value="" >Selecteer BTW optie</option>
                ${_bc.tax_groups.map(row => `<option value="${row.id}" ${row.id == def ? 'selected' : ''} >${row.name}</option>`).join('')}
              </select>`
      }
      utilities.ebBtw = (string) => {
        let options = '';
        for(const vat of _eb.vat){
          const selected = vat.code == standaard_eb ? 'data-selected="true"' : '';
          options += `<span class="select_search-value" ${selected} data-name="${vat.percentage}%" data-value="${vat.code}" > <span class="badge badge-primary" >${vat.percentage}%</span> ${vat.omschrijving} </span>`
        }

        return `<div class="select_search-container tippy" data-tippy-content="">
                  <input type="hidden" id="eb_btw_select" name="eb_btw[${string}]" data-required="required"  class="select_search-hidden-input row-eb-btw" data-placeholder="BTW">
                  <div class="select_search-values">
                    <div class="select_search-box " >${options} </div>
                  </div>
                </div>`
      }
      utilities.kingBtw = (string) => {
        const def = settings.king_defaults?.btw
        return `<select class="form-select king_btw_code min-w-225" name="king_btw_code[${string}]" required >
                <option value="" >Selecteer BTW optie</option>
                ${_king.btw_codes.map(row => `<option value="${row.king_id}" ${row.king_id == def ? 'selected' : ''} >${row.nummer} | ${row.naam}</option>`).join('')}
              </select>`
      }
    }
    function checkAccorderenProformaEdit(){
      if(factuur && factuur.is_proforma && localStorage.accorderen_proformas_edit){
        $('[data-factuur-form]').append('<input type="hidden" name="accorderen_edit" value="true">');
      }
      localStorage.removeItem('accorderen_proformas_edit');
    }

    async function getInit(){
      if(_get('project')){
        loader('Project');
        const project = await getProject(_get('project'));

        if(!project){
          notification(`Project niet gevonden`);
          return;
        }
        if(!project.klant_id){
          notification(`Project ${project.projectnr} heeft geen geselecteerde klant`);
          return;
        }

        loader('Klant');
        const klant = await getKlant(project.klant_id);

        if(!klant){
          notification('Er is iets foutgegaan!');
          return;
        }

        _inforSearch.get('klant').setValue({
          name: klant.titel,
          value: klant?.id
        })
      }

      if(_get('werkbon')){
        loader('Werkbon');
        const werkbon = (await getWerkbonnen({ids: [_get('werkbon')], relations: ['project', 'klant']})).werkbonnen[0];

        const{ project, klant, id } = werkbon;

        $("#klant").replaceWith(`<input type="text" class="form-control-custom" value="${klant.naam != '' ? klant.naam : klant.contactpersoon_voornaam + ' - ' +klant.contactpersoon_achternaam}" readonly><input type="hidden" name="klantId" value="${klant.id}">`);
        await selectKlant(klant.id);

        selectProject(project.id);
        $('.projecten-container').html(`<div class="my2">Projecten</div><a href="${url}/uren/projectnummers/${project.id}/preview' }}" target="_blank">${project.projectnr}</a>`);

        if(_inforSelectMultiple.get('werkbonnen')){
          _inforSelectMultiple.get('werkbonnen').setValues([id]);
          _werkbonnen.selected = [id];
        }
        successLoader();
      }
    }
    async function editInit(){
      if(!factuur){return}
      const {klant, regels, referentie, adres, project_taken, project} = factuur;
      const factuur_offertes = factuur._offertes;
      const factuur_proformas = factuur._proformas;
      const factuur_werkbonnen = factuur._werkbonnen;

      $("#klant").replaceWith(`<input type="text" class="form-control-custom" value="${klant.naam != '' ? klant.naam : klant.contactpersoon_voornaam + ' - ' +klant.contactpersoon_achternaam}" readonly><input type="hidden" name="klantId" value="${klant.id}">`);
      await selectKlant(klant.id);

      $("input[name=fa_klant]").val(adres?.klant ?? klant.naam);
      $("input[name=fa_tav]").val(adres?.tav ?? klant.contactpersoon_voornaam + ' ' + klant.contactpersoon_achternaam);
      $("input[name=fa_straat]").val(adres?.straat ?? klant.postadres_straat);
      $("input[name=fa_huisnummer]").val(adres?.huisnummer ?? klant.postadres_huisnummer);
      $("input[name=fa_toevoeging]").val(adres?.toevoeging ?? klant.postadres_toevoeging);
      $("input[name=fa_postcode]").val(adres?.postcode ?? klant.postadres_postcode);
      $("input[name=fa_plaats]").val(adres?.plaats ?? klant.postadres_plaats);
      $("input[name=fa_land]").val(adres?.land ?? klant.postadres_land);

      $("input[name=btw_verlegd]").prop('checked', !!Number(factuur.btw_verlegd));

      if(project){
        selectProject(project.id);
        $('.projecten-container').html(`<div class="my2">Projecten</div><a href="${url}/uren/projectnummers/${project.id}/preview' }}" target="_blank">${project.projectnr}</a>`);
      }

      if(_inforSelectMultiple.get('offertes')){
        _inforSelectMultiple.get('offertes').setValues(factuur_offertes.map(offerte => offerte.id), false, false);
        _offertes.selected = factuur_offertes.map(offerte => offerte.id);
      }
      if(_inforSelectMultiple.get('proformas')){
        _inforSelectMultiple.get('proformas').setValues(factuur_proformas.map(proforma => proforma.id), false, false);
        _proformas.selected = factuur_proformas.map(proforma => proforma.id);
      }
      if(_inforSelectMultiple.get('werkbonnen')){
        _inforSelectMultiple.get('werkbonnen').setValues(factuur_werkbonnen.map(werkbon => werkbon.id), false, false);
        _werkbonnen.selected = factuur_werkbonnen.map(werkbon => werkbon.id);
      }
      if(_inforSelectMultiple.get('taken')){
        _inforSelectMultiple.get('taken').setValues(project_taken.map(taak => taak.id), false, false);
      }

      await prefillEditRows(regels, factuur.id);

    }

    //Prefills
    function prefillTermijn(){
      if(settings.termijnprefill != 'ja'){return}

      const { klant } = _this;

      const date = new Date();
      date.setDate(date.getDate() + Number(klant.betalingstermijn));

      const dateString = date.toISOString().split('T')[0];
      $("[name=betalingstermijn]").val(dateString);

      $("#klanten-buttons").html(
        `<nobr>
          <a class="btn btn-dark text-white mx-1 tippy" onclick="updateKlant()" data-tippy-content="Klant wijzigen" >@icon_edit</a>
          <a class="btn btn-dark text-white ml-1 tippy" onclick="showKlant(${klant.id})" data-tippy-content="Klant weergeven" >@icon_zoom</a>
        </nobr>`
      )
    }
    function prefillRows(){
      if(!settings.prefillRows) {return}

      for (const key in settings.prefillRows){
        let row = settings.prefillRows[key];
        let checked = "";

        if (row.inclBtw) {checked = "checked";}
        row.checked = checked;
        addRow(row)
      }
    }
    async function prefillEditRows(regels, id){
      $('#regels').empty();
      for(const regel of regels){
        const { naam, aantal, prijs, btw, incl, detail, tekstregel } = regel;
          if(Number(tekstregel)){
            const string = addTextRow();
            $(`[name='naam[${string}]']`).val(naam || '');
            continue;
          }
          const string = addRow();

          $(`[name='naam[${string}]']`).val(naam || '');
          $(`[name='aantal[${string}]']`).val(aantal || 0);
          $(`[name='prijs[${string}]']`).val(prijs || '').prop('readonly', !!(detail));
          $(`[name='btw[${string}]']`).val(btw);
          $(`[name='incl[${string}]']`).prop('checked', !!(Number(incl)));

          if(regel.detail){
            const { id, title, detail_template_id } = regel.detail
            $(`.${string}`).addClass(`detail-tr-${id}`)
            $(`.${string}`).find('.btns-container').append(
              `<a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-100" data-tippy-content="${title} wijzigen" onclick="editFactuurDetail(${detail_template_id}, ${id})">@icon_edit</a>
            <input type="hidden" name="detail[${string}]" class="detail-id" value="${id}">`
            );
          }
          if(regel.eenheid_id){
            $(`[name='eenheid[${string}]']`).val(regel.eenheid_id);
          }
          if(regel.custom_fields){
            for(const field in regel.custom_fields){
              $(`[name='${field}[${string}]']`).val(regel.custom_fields[field]);
            }
          }
          if(_bc.instance.connected){
            $(`[name='business_central_tax_group[${string}]']`).val(regel.bc_tax_group);
          }
          if(_eo.instance.connected){
            _inforSelectSearch.get(`eo_item_${string}`).setValue(regel.exact_online_item?.code ?? null);
            _inforSelectSearch.get(`eo_gl_${string}`).setValue(regel.exact_online_gl?.code ?? null);
            _inforSelectSearch.get(`eo_vat_${string}`).setValue(regel.exact_online_btw?.code ?? null);
          }
          if(_eb.connected){
            $(`[name='eb_btw[${string}]']`).val(regel.eboek_btw);
          }
          if(_king.instance.connected){
            $(`[name='king_btw_code[${string}]']`).val(regel.king_btw);
            $(`[name='king_grootboek[${string}]']`).val(regel.king_grootboek);
          }
      }
      correctSelectSearchDisplay();
      updateRowTotaal();
    }

    //Klanten
    async function selectKlant(id){
      if(!id){
        deselectKlant();
        return;
      }

      try{
        unsetProject(_projecten.selected);
        loader('Klant');
        _this.klant = await getKlant(id);

        grekeningToggle();

        $("#klanten-buttons").html(
          `<nobr>
          <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy" onclick="updateKlant(${_this.klant.id})" data-tippy-content="Klant wijzigen" >@icon_edit</a>
          <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy" onclick="showKlant(${_this.klant.id})" data-tippy-content="Klant weergeven" >@icon_zoom</a>
        </nobr>`
        )

        const {naam, contactpersoon_voornaam, contactpersoon_achternaam, postadres_straat, postadres_huisnummer, postadres_toevoeging, postadres_postcode, postadres_plaats, postadres_land, contactpersonen,  btw_verlegd} = _this.klant;

        $("input[name=fa_klant]").val(naam || '');
        $("input[name=fa_tav]").val((contactpersoon_voornaam || '') + ' ' + (contactpersoon_achternaam || ''));
        $("input[name=fa_straat]").val(postadres_straat || '');
        $("input[name=fa_huisnummer]").val(postadres_huisnummer || '');
        $("input[name=fa_toevoeging]").val(postadres_toevoeging || '');
        $("input[name=fa_postcode]").val(postadres_postcode || '');
        $("input[name=fa_plaats]").val(postadres_plaats || '');
        $("input[name=fa_land]").val(postadres_land || '');
        $("input[name=btw_verlegd]").prop('checked', !!Number(btw_verlegd));

        $('.klant-contactpersonen').empty();
        let name = `${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}`;
        if(name.replace(' ', '')){
          $('.klant-contactpersonen').append(`<span class="select_edit-value" data-value="${name}">${name}</span>`);
        }
        for(const cp of contactpersonen){
          name = `${cp.voornaam || ''} ${cp.achternaam || ''}`;
          if(!name.replace(' ', '')){continue;}
          $('.klant-contactpersonen').append(`<span class="select_edit-value" data-value="${name}">${name}</span>`);
        }

        loader('Projecten ophalen');
        const data = {
          klant: id,
          relations: ['offerte', 'klant', 'contactpersoon', 'uren', 'vestiging', 'inkoopfacturen', 'taken.facturen', 'werkbonnen', 'offertes', 'meerwerk_offertes'],
          status: ['Opdracht', 'Afgerond'],
          append: ['proformas'],
          proformas_relations: ['regels'],
          sort_by: 'status ASC, projectnr',
        }
        if(factuur?.project){
          data.ids = [factuur.project.id];
        }

        _projecten.all = (await getProjecten(data)).projecten;

        if(!factuur){
          await filterProjectData();
        }

        $(".klant-divs").removeClass("d-none");

        unsetProject(_projecten.selected);
        setProjecten();
        setExactOnlineOptions();


        //Prefill project only on the first ( Automatic ) klant select
        if(!_this.get_prefilled && _get('project')){
          _inforSelectSearch.get('projecten').setValue([_get('project')], false);
        }

        _this.get_prefilled = true;
        successLoader();
      }
      catch (e) {handleCatchError(e)}
    }
    function deselectKlant(){
      _this.klant = null;

      $('#klanten-buttons').empty();

      setExactOnlineOptions();
      unsetProject(_projecten.selected);
    }
    function updateKlant(){
      localStorage.removeItem('klantUpdated');
      clearInterval(klant_listener);

      editKlant(_this.klant.id);

      klant_listener = setInterval(async () => {
        if(localStorage.klantUpdated){
          clearInterval(klant_listener);
          localStorage.removeItem('klantUpdated');

          loader();
          _this.klant = await getKlant(_this.klant.id)

          const { postadres_straat, postadres_huisnummer, postadres_toevoeging, postadres_postcode, postadres_plaats, postadres_land } = _this.klant;

          $("[name=fa_straat]").val(postadres_straat || '');
          $("[name=fa_huisnummer]").val(postadres_huisnummer || '');
          $("[name=fa_toevoeging]").val(postadres_toevoeging || '');
          $("[name=fa_postcode]").val(postadres_postcode || '');
          $("[name=fa_plaats]").val(postadres_plaats || '');
          $("[name=fa_land]").val(postadres_land || '');

          clearLoader();
        }
      },750);
    }
    async function grekeningToggle(){
      const gRekeningPerc = await ajax('api/klanten/getklantsettingvalue', {klantId: _this.klant.id, setting: 'g_rekening_percentage'});
      const state = gRekeningPerc.klantSettingValue !== null && $('.g_rekening_container').length;

      $('.g_rekening_container').toggleClass('d-none', !state);

      $('[name=factuur_grekeningNr]').val(factuur ? factuur.g_rekening_nummer : _this.klant._bv.g_rekening);
      $('[name=factuur_grekening]').val(factuur ? factuur.g_rekening_waarde : gRekeningPerc.klantSettingValue);

      $('[name=factuur_grekeningNr], [name=factuur_grekening]').prop('disabled', !state);
    }

    //Proformas
    function setProformas(project){
      _proformas.all = project.proformas;
      if(!_proformas.all.length){return;}
      $(`.data-${project.id}-container`).append(`
        <div class="col-md-6 col-12 my-2">
          <label>Proformas</label>
          <infor-select-multiple name="proformas" id="proformas" class="form-control-custom" placeholder="Selecteer proformas" >
            ${_proformas.all.map(row => `<infor-select-option data-name="${row.factuurnummer}" data-value="${row.id}" >${row.factuurnummer}</infor-select-option>`).join('')}
          </infor-select-multiple>
        </div>
      `);
      initInforSelectMultiple();
      _inforSelectMultiple.get('proformas').onchangeOption = selectProforma;
    }
    async function selectProforma(option){
      try{
        if(option && !option.checked){
          unsetProforma(option.value);
          return;
        }
        id = option.value ?? 0;
        _proformas.selected[id] = true;

        if(!id){return;}


        const proforma = await getFactuur(id, {relations: ['regels', '_offertes', 'project']});
        const { factuurnummer, token, project, _offertes } = proforma;

        for(const offerte of _offertes){
          _inforSelectMultiple.get('offertes').setValues([offerte.id], false);
        }

        removeEmptyRows();

        if(settings.proforma_regels_samenvoegen){
          totaal = 0;
          for(const regel of proforma.regels){
            let excl = Number(regel.incl) ? regel.prijs / (1 + (regel.btw / 100)) : regel.prijs;
            totaal += excl * regel.aantal;
          }
          const string = addRow();
          $(`[name='naam[${string}]']`).val(factuurnummer);
          $(`[name='prijs[${string}]']`).val(totaal);
          $(`[name='aantal[${string}]']`).val(1);
        }else{
          for(const regel of proforma.regels){
            const { naam, aantal, prijs, btw, incl, detail } = regel;
            const string = addRow();

            $(`.${string}`).addClass(`proforma-tr-${proforma.id}`);
            $(`[name='naam[${string}]']`).val(naam || '');
            $(`[name='aantal[${string}]']`).val(aantal || 0);
            $(`[name='prijs[${string}]']`).val(prijs || '').prop('readonly', !!(detail));
            $(`[name='btw[${string}]']`).val(btw);
            $(`[name='incl[${string}]']`).prop('checked', !!(Number(incl)));

            if(regel.detail){
              const { id, title, detail_template_id } = regel.detail
              $(`.${string}`).addClass(`detail-tr-${id}`)
              $(`.${string}`).find('.btns-container').append(
                `<a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-100" data-tippy-content="${title} wijzigen" onclick="editFactuurDetail(${detail_template_id}, ${id})">@icon_edit</a>
              <input type="hidden" name="detail[${string}]" class="detail-id" value="${id}">`
              );
            }
          }
        }
      }
      catch (e) { handleCatchError(e); }
    }
    function unsetProforma(id){
      delete _proformas.selected[id];

      deleteDiv(`.proforma-tr-${id}`)
      deleteDiv(`.proforma${id}`)
    }
    function projectProformasInit(){
      const container = $('.project-proformas-container');
      const content = container.find('[data-project-proformas-content]');

      container.removeClass('d-none');
      content.empty();

      const id = _projecten.selected;
      const project = _projecten.all.find(row => row.id == id);
      if(!project.proformas.length){ return; }


      content.append(
        `<div data-toggle-container data-project-proformas="${id}" class="bg-inverse-secondary rounded-3 px-2 my-2 font-size-09" >
          <div class="flex-between cursor-pointer " data-toggle-btn >
            <span> <b>${project.projectnr}</b> <span class="mx-2">${project.projectnaam || ''}</span> </span>
            <a class="btn">@icon_down</a>
          </div>
          <div data-toggle-content style="display: none" class="w-100" > <div class="py-1" data-proformas-container=${id} ></div> </div>
        </div>`
      );

      const proformasContainer = $(`[data-proformas-container=${id}]`)
      for(const proforma of project.proformas){
        proformasContainer.append(
          `<div class="py-1" >
            <div data-toggle-container="PF${proforma.id}" class="bg-white rounded-3 px-2" >
              <div class="flex-between cursor-pointer " data-toggle-btn >
                <span>${proforma.factuurnummer}</span>
                <a class="btn">@icon_down</a>
              </div>
              <div data-toggle-content style="display: none" class="w-100" >
                <div class="overflow-auto">
                  <table class="table mb-2" >
                    <thead>
                        <tr>
                            <th></th>
                            <th class="w-100" >Omschrijving</th>
                            <th class="w-px-150" >Aantal</th>
                            <th class="w-px-150 text-right" >Bedrag</th>
                            <th class="w-px-50" >Incl. BTW</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                  </table>
                </div>
              </div>
            </div>`
        );

        const proformaContainer = proformasContainer.find(`[data-toggle-container=PF${proforma.id}]`);
        for(const regel of proforma.regels){
          proformaContainer.find('tbody').append(
            `<tr>
              <td>${
                regel.detail
                  ? `<a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-50" data-tippy-content="Detail inzien" onclick="showDetail(${regel.detail.detail_template_id}, ${regel.detail.id})">@icon_show</a>
                      <a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-50" data-tippy-content="Detail toevoegen aan huidige proforma" onclick="addFactuurDetail(${regel.detail.detail_template_id}, null,  ${regel.detail.id})">@icon_copy</a>`
                  : ``
                }
              </td>
              <td>${regel.naam || ''}</td>
              <td>${regel.aantal || ''}</td>
              <td> <div class="flex-between" > <span>€</span> <span>${regel.prijs || ''}</span> </div> </td>
              <td class="text-center" >
                  ${Number(regel.incl)
                    ? '<span class="btn text-primary cursor-unset" >@icon_check</span>'
                    : '<span class="btn text-danger cursor-unset" >@icon_close</span>'
                  }
              </td>
          </tr>`
          )
        }
      }

      if(content.is(':empty')){ container.addClass('d-none'); }
      tippyInit();
    }
    function prefillProformaDetails(project){
      let latestProforma = project.proformas.reduce((max, current) => { return current.amount > max.amount ? current : max; }, project.proformas[0]);
      if (!latestProforma){ return; }
      let vestiging_plaats = project?.vestiging?.plaats ?? null;
      let ledgerId = '';
      if (vestiging_plaats){
        for(const ledger of _eg.ledgers){
          if(ledger.description.toLowerCase().includes(vestiging_plaats.toLowerCase())){
            ledgerId = ledger.code
            break;
          }
        }
      }

      for (const regel of latestProforma.regels ?? []){
        if(regel.detail){
        let detail = regel.detail;
        const string = addRow();

        $(`.${string}`).addClass(`detail-tr-${detail.id}`).addClass(`project-tr-${project.id}`)
        $(`[name='naam[${string}]']`).val(detail.regel.titel);
        $(`[name='aantal[${string}]']`).val(detail.regel.aantal);
        $(`[name='prijs[${string}]']`).val(detail.regel.prijs).prop('readonly', true);
        $(`[name='btw[${string}]']`).val(detail.regel.btw);
        $(`[name='incl[${string}]']`).prop('checked', (Number(latestProforma.inclBtw)));
        $(`[name='totaalBedrag[${string}]']`).val("€" + toPrice(detail.regel.totaal));
        $(`[name='ledger[${string}]']`).val(ledgerId ?? null);

        $(`.${string}`).find('.btns-container').append(
          `<a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-100" data-tippy-content="${detail.title} wijzigen" onclick="editFactuurDetail(${detail.detail_template_id}, ${detail.id})">@icon_edit</a>
        <input type="hidden" name="detail[${string}]" class="detail-id" value="${detail.id}">
        <input type="hidden" name="detail_based_on[${string}]" class="detail-based-on-id" value="${detail.id}">`
        );
        }else if(regel.tekstregel){
          const string = addTextRow();
          $(`.${string}`).addClass(`project-tr-${project.id}`)
          $(`[name='naam[${string}]']`).val(regel.naam);
        }else{
          const string = addRow();
          $(`.${string}`).addClass(`project-tr-${project.id}`)
          $(`[name='naam[${string}]']`).val(regel.naam);
          $(`[name='aantal[${string}]']`).val(regel.aantal);
          $(`[name='prijs[${string}]']`).val(regel.prijs).prop('readonly', true);
          $(`[name='btw[${string}]']`).val(regel.btw);
          $(`[name='incl[${string}]']`).prop('checked', (Number(latestProforma.inclBtw)));
          $(`[name='totaalBedrag[${string}]']`).val("€" + toPrice(regel.totaal));
          $(`[name='ledger[${string}]']`).val(ledgerId ?? null);
        }
      }
    }

    //Werkbonnen
    function setWerkbonnen(project){
      _werkbonnen.all = project.werkbonnen;
      if(!_werkbonnen.all.length){return;}
      $(`.data-${project.id}-container`).append(`
        <div class="col-md-6 col-12 my-2">
          <div class="d-flex justify-content-between">
            <label>Werkbonnen</label>
            <a class="btn btn-light m-0 p-1 px-2 rounded-pill tippy hide" id="showWerkbonBtn" onclick="showWerkbonnen(getWerkbonnenIds())" data-tippy-content="Werkbonnen bekijken">@icon_zoom</a>
          </div>
          <infor-select-multiple name="werkbonnen" id="werkbonnen" class="form-control-custom" placeholder="Selecteer werkbonnen" >
            ${_werkbonnen.all.map(row => `<infor-select-option data-name="${row.werkbonnummer}" data-value="${row.id}" >${row.werkbonnummer} (${row.datum})</infor-select-option>`).join('')}
          </infor-select-multiple>
        </div>
      `);
      initInforSelectMultiple();
      _inforSelectMultiple.get('werkbonnen').onchangeOption = selectWerkbon;
    }
    function getWerkbonnenIds() {
      return _inforSelectMultiple.get('werkbonnen').getValues().map(item => item.value).join(",");
    }
    function selectWerkbon(option){
      try{
        if (getWerkbonnenIds().length) {
          $('#showWerkbonBtn').show()
        }else{
          $('#showWerkbonBtn').hide()
        }


        if(option && !option.checked){
          unsetWerkbon(option.value);
          return;
        }
        id = option.value ?? 0;
        _werkbonnen.selected[id] = true;

        if(!id){return;}
        ajax('api/werkbonnen/getwerkbon', {id:id})
        .then(response => {
          let werkbon = response.werkbon;
          if(!werkbon){return;}
          removeEmptyRows();
          ValuesFromWerkbon(werkbon);
        });
      }
      catch (e) { handleCatchError(e); }
    }
    function ValuesFromWerkbon(werkbon){
      if(werkbon.values && settings.datasetItemsFromWerkbon){
        for(let value of werkbon.values){
          if(value.type == 'list_input'){
            const list = JSON.parse(value.value);
            if(list.dataset){
              for(let row of list.rows){
                const string = addRow();
                $(`.${string}`).addClass(`werkbon-tr-${werkbon.id}`)
                if(row.id){
                  getDatasetItem(row.id).then(response => {
                    if(!response.item){
                      $(`[name='naam[${string}]']`).val(row.name);
                      $(`[name='aantal[${string}]']`).val(Number(row.values.Aantal));
                      $(`[name='prijs[${string}]']`).val(Number(row.values.prijs ?? 0).toFixed(2));
                    }
                    const value = JSON.parse(response.item.value);
                    $(`[name='naam[${string}]']`).val(response.item.name).attr('name', `dataset_naam[${string}]`).after(
                      `<input type="hidden" name="dataset_item[${string}]" value='${response.item.value}'>
                      <input type="hidden" name="dataset_dataset[${string}]" value='${row.id}'>`
                    );
                    $(`[name='aantal[${string}]']`).val(Number(row.values.Aantal)).attr('name', `dataset_aantal[${string}]`);
                    $(`[name='prijs[${string}]']`).val(Number(value.prijs ?? 0).toFixed(2)).attr('name', `dataset_prijs[${string}]`);
                    $(`[name='btw[${string}]']`).attr('name', `dataset_btw[${string}]`);
                    $(`[name='incl[${string}]']`).attr('name', `dataset_incl[${string}]`);
                  });
                }else if(row.datasetitem){
                  $(`[name='naam[${string}]']`).val(row.datasetitem.name).attr('name', `dataset_naam[${string}]`).after(
                      `<input type="hidden" name="dataset_item[${string}]" value='${JSON.stringify(row.datasetitem)}'>
                      <input type="hidden" name="dataset_dataset[${string}]" value='0'>`
                    );
                    $(`[name='aantal[${string}]']`).val(Number(row.values.Aantal)).attr('name', `dataset_aantal[${string}]`);
                    $(`[name='prijs[${string}]']`).val(Number(row.datasetitem.value?.prijs ?? 0).toFixed(2)).attr('name', `dataset_prijs[${string}]`);
                    $(`[name='btw[${string}]']`).attr('name', `dataset_btw[${string}]`);
                    $(`[name='incl[${string}]']`).attr('name', `dataset_incl[${string}]`);

                    if('2ba_code' in row.datasetitem.value){
                      $(`[name='2ba_code[${string}]`).val(row.datasetitem.value['2ba_code']);
                      $(`[name='calculate_2ba_price[${string}]`).show();
                    }
                }else{
                  $(`[name='naam[${string}]']`).val(row.name);
                  $(`[name='aantal[${string}]']`).val(Number(row.values.Aantal));
                  $(`[name='prijs[${string}]']`).val(Number(row.values.prijs ?? 0).toFixed(2));
                }
              }
            }else{
              for(let row of list.rows){
                const string = addRow();
                $(`.${string}`).addClass(`werkbon-tr-${werkbon.id}`)
                $(`[name='naam[${string}]']`).val(row.name);
                $(`[name='aantal[${string}]']`).val(Number(row.values.Aantal));
                if(row.values.prijs){
                  $(`[name='prijs[${string}]']`).val(Number(row.values.prijs ?? 0).toFixed(2));
                }else if(row.values.inkoop && row.values.marge){
                  let verkoop = (Number(row.values.inkoop) * (1 + (Number(row.values.marge)/100)));
                  $(`[name='prijs[${string}]']`).val(verkoop.toFixed(2));
                }
              }
            }
          }
        }
      }
      if(werkbon.values && settings.urenFromWerkbon){
        var gewerkte_uren = 0;
        for(let value of werkbon.values){
          if(value.keyword == 'gewerkte_uren'){
            gewerkte_uren = value.value;
            break;
          }
          if(value.keyword == 'begintijd'){
            var begintijd = value.value;
          }else if(value.keyword == 'eindtijd'){
            var eindtijd = value.value;
          }
        }
        if (!gewerkte_uren) {
          const begin = new Date('2023-04-18T'+begintijd);
          const eind = new Date('2023-04-18T'+eindtijd);
          const diffInMilliseconds = eind - begin;
          const diffInHours = diffInMilliseconds / (1000 * 60 * 60);
          const roundedDiffInHours = Math.round(diffInHours * 4) / 4;
          gewerkte_uren = roundedDiffInHours;
        }

        const string = addRow();
        $(`.${string}`).addClass(`werkbon-tr-${werkbon.id}`)
        $(`[name='naam[${string}]']`).val('Uren');
        $(`[name='aantal[${string}]']`).val(Number(gewerkte_uren ?? 0));
        $(`[name='prijs[${string}]']`).val(Number(@json(getStandaardUurTarief()->tarief ?? 0)));
      }
      if(werkbon.values && settings.reistijdFromWerkbon){
        for(let value of werkbon.values){
          if(value.keyword == 'reistijd'){
            var reistijd = value.value;
          }
        }

        const string = addRow();
        $(`.${string}`).addClass(`werkbon-tr-${werkbon.id}`)
        $(`[name='naam[${string}]']`).val('Reisuren');
        $(`[name='aantal[${string}]']`).val(Number(reistijd));
        $(`[name='prijs[${string}]']`).val(Number(@json(getStandaardUurTarief()->tarief ?? 0)));
      }
      if(werkbon.values && settings.kmsFromWerkbon){
        for(let value of werkbon.values){
          if(value.keyword == 'km'){
            var kms = value.value;
          }
        }

        const string = addRow();
        $(`.${string}`).addClass(`werkbon-tr-${werkbon.id}`)
        $(`[name='naam[${string}]']`).val('Kilometers');
        $(`[name='aantal[${string}]']`).val(Number(kms));
        $(`[name='prijs[${string}]']`).val(Number(@json(getStandaardKmTarief()->tarief ?? 0)));
      }
    }
    function unsetWerkbon(id){
      delete _werkbonnen.selected[id];
      deleteDiv(`.werkbon-tr-${id}`)
    }

    //Offertes
    function setOffertes(project){
      _offertes.all = project.offertes;
      _offertes.meerwerk = project.meerwerk_offertes;
      if(!_offertes.all.length && !_offertes.meerwerk.length){return;}

      $(`.data-${project.id}-container`).append(`
        <div class="col-md-6 col-12 my-2">
          <label>Offertes</label>
          <infor-select-multiple name="offertes" id="offertes" class="form-control-custom" placeholder="Selecteer offertes" >
            ${_offertes.all.map(row => `<infor-select-option data-name="${row.offertenummer}" data-value="${row.id}" >${row.offertenummer} - ${row.naam}</infor-select-option>`).join('')}
            ${_offertes.meerwerk.map(row => `<infor-select-option data-name="${row.offertenummer}" data-value="${row.id}" >${row.offertenummer} - ${row.naam}</infor-select-option>`).join('')}
          </infor-select-multiple>
        </div>
      `);
      initInforSelectMultiple();
      _inforSelectMultiple.get('offertes').onchangeOption = selectOfferte;
    }
    async function selectOfferte(option){
      try{
        toggleUurRows();

        if(option && !option.checked){
          unsetOfferte(option.value);
          return;
        }

        id = option ? option.value : id;
        _offertes.selected = _inforSelectMultiple.get('offertes').getValues().map(option => option.value)

        if(!id || _offertes.selected[id]){return;}

        offerte = (await getOffertes({
          ids: [id],
          relations: ['project', 'details', 'klant', 'contactpersoon', 'regels', 'offerteplanning', 'locatie', 'opties'],
        })).offertes[0]
        removeEmptyRows();

        if(offerte.contactpersoon){
          $("[name=fa_tav]").val((offerte.contactpersoon.voornaam || '') + ' ' + (offerte.contactpersoon.achternaam || ''));
        }

        for(const detail of offerte.details){
        if(!Number(detail.regel.aantal) || !Number(detail.regel.active)){ continue; }
        const string = addRow();
        $(`.${string}`).addClass(`detail-tr-${detail.id}`).addClass(`offerte-tr-${offerte.id}`)
        $(`[name='naam[${string}]']`).val(detail.regel.titel);
        $(`[name='aantal[${string}]']`).val(detail.regel.aantal);
        $(`[name='prijs[${string}]']`).val(detail.regel.prijs).prop('readonly', true);
        $(`[name='btw[${string}]']`).val(detail.regel.btw);
        $(`[name='incl[${string}]']`).prop('checked', (Number(offerte.inclBtw)));

        $(`.${string}`).find('.btns-container').append(
          `<a class="btn btn-sm btn-inverse-primary tippy btn-edit-detail w-100" data-tippy-content="${detail.title} wijzigen" onclick="editFactuurDetail(${detail.detail_template_id}, ${detail.id})">@icon_edit</a>
        <input type="hidden" name="detail[${string}]" class="detail-id" value="${detail.id}">`
        );
        }
        for(const regel of offerte.regels){
          if(!Number(regel.aantal) || !Number(regel.active)){ continue; }
          const string = addRow();
          $(`.${string}`).addClass(`offerte-tr-${offerte.id}`)
          $(`[name='naam[${string}]']`).val(regel.tekst);
          $(`[name='aantal[${string}]']`).val(regel.aantal);
          $(`[name='prijs[${string}]']`).val(regel.stukprijs);
          $(`[name='btw[${string}]']`).val(regel.btw);
          $(`[name='incl[${string}]']`).prop('checked', (Number(offerte.inclBtw)));
        }
        for(let row of offerte.offerteplanning){
          const string = addRow();
          $(`.${string}`).addClass(`offerte-tr-${offerte.id}`)
          $(`[name='naam[${string}]']`).val(row.naam);
          $(`[name='aantal[${string}]']`).val(row.aantal);
          $(`[name='prijs[${string}]']`).val(row.bedrag);
          $(`[name='btw[${string}]']`).val(row.btw);
          $(`[name='incl[${string}]']`).prop('checked', (Number(offerte.inclBtw)));
        }
        if(Number(offerte.opties) && offerte.optie_index !== null){
          for (let index of JSON.parse(offerte.optie_index)){
            for(let optie of offerte.opties){
              if(index === optie.index){

                const string = addRow();
                $(`.${string}`).addClass(`offerte-tr-${offerte.id}`)
                $(`[name='naam[${string}]']`).val(optie.tekst);
                $(`[name='aantal[${string}]']`).val(optie.aantal);
                $(`[name='prijs[${string}]']`).val(optie.stukprijs).prop('readonly', true);
                $(`[name='btw[${string}]']`).val(optie.btw);
                $(`[name='incl[${string}]']`).prop('checked', (Number(offerte.inclBtw)));
              }
            }
          }
        }

        updateRowTotaal();
      }
      catch (e) { handleCatchError(e); }
    }
    function unsetOfferte(id){
      delete _offertes.selected[id];
      deleteDiv(`.offerte-tr-${id}`)
    }

    //Projecten
    function setProjecten(){
      if(!_projecten.all.length){
        $('.projecten-card-container').addClass('d-none');
        return;
      }

      $('.projecten-container').removeClass('d-none');
      $('.projecten-select').html(`
        <infor-select-search name="project" id="projecten" class="form-control-custom" placeholder="Selecteer project" >
          ${_projecten.all.map(row => `<infor-select-option data-name="${row.projectnr} - ${row.projectnaam}" data-value="${row.id}" ><span class="badge badge-${row.background}">${row.status}</span>&nbsp;${row.projectnr} - ${row.projectnaam}</infor-select-option>`).join('')}
        </infor-select-search>
      `);

      initInforSelectSearch();
      _inforSelectSearch.get('projecten').onchange = selectProject;
    }
    function selectProject(id){
      try{
        if(!id || _projecten.selected != id){
          unsetProject(_projecten.selected ?? 0);
        }
        if(_projecten.selected == id){return;}
        _projecten.selected = id

        if(!id) {return;}

        const project = _projecten.all.find(row => row.id == id);

        setBusinessCentralKostendrager(project)

        if(project.contactpersoon){
          $("[name=fa_tav]").val((project.contactpersoon.voornaam || '') + ' ' + (project.contactpersoon.achternaam || ''));
        }
        if(project.custom.contactpersoon_proformas && _this.type == 'proforma'){
          let contactpersoon = project.klant.contactpersonen.find(row => row.id == project.custom.contactpersoon_proformas.value);
          if(contactpersoon){
            $("[name=fa_tav]").val((contactpersoon.voornaam || '') + ' ' + (contactpersoon.achternaam || ''));
          }
        }

        addProjectData(project);
        tippyInit();
        removeEmptyRows();
        if(_this.type == 'proforma'){
         projectProformasInit()
         if (settings.prefillDetails) { prefillProformaDetails(project); }
        }

        if(project.inkoopfacturen){
          if (project.inkoopfacturen.length == 1){
            addInkoopfactuurRegel(project.inkoopfacturen[0]);
          }else{
            for (let inkoopfactuur of project.inkoopfacturen){
              addInkoopfactuurRegel(inkoopfactuur);
            }
          }
        }

        updateRowTotaal();
      }
      catch (e) { handleCatchError(e); }
    }
    function unsetProject(id){
      let project = _projecten.all.find(row => row.id == id);

      _projecten.selected = 0;

      for(const offerte of _offertes.all){unsetOfferte(offerte.id);}
      _offertes.all = [];

      for(const proforma of _proformas.all){unsetProforma(proforma.id);}
      _proformas.all = [];

      for(const werkbon of _werkbonnen.all){unsetWerkbon(werkbon.id);}
      _werkbonnen.all = [];

      unsetUren(id);

      deleteDiv(`.project-tr-${id}`)
      deleteDiv(`.project${id}`)
      deleteDiv(`.inkoopfactuur-regel-tr-${id}`)
    }
    async function filterProjectData(){
      for(const project of _projecten.all){
        project.taken = project.taken.filter(row => row.facturen.length == 0);
        project.offertes = project.offertes.filter(row => row.status == 'Akkoord' && row.gefactureerd == '0');
        project.meerwerk_offertes = project.meerwerk_offertes.filter(row => row.status == 'Akkoord' && row.gefactureerd == '0');
        project.werkbonnen = project.werkbonnen.filter(row => row.status == 'Afgerond' && row.gefactureerd == '0');
        project.uren = project.uren.filter(row => row.gefactureerd == '0' && row.tijdelijk == '0');
        if(_this.type == 'factuur'){
          project.proformas = project.proformas.filter(row => row.proforma_accepted == "1" && row.proforma_gefactureerd == "0" && row.status != 'Gefactureerd');
        }
      }
    }
    function addProjectData(project){
      if(!project.taken.length && !project.offertes.length && !project.werkbonnen.length && (!project.uren.length || !settings.projecturen_overnemen) && !project.proformas.length){return;}
      $('.project-data-container').removeClass('d-none').append(`
          <div class="card hover-shadow p-2 my-3 project${project.id}">
            <div class="row data-${project.id}-container"></div>
          </div>
        </div>
      `);

      if(project.taken.length){setTaken(project);}
      if(project.offertes.length || project.meerwerk_offertes.length){setOffertes(project);}
      if(project.werkbonnen.length && (settings.datasetItemsFromWerkbon || settings.urenFromWerkbon || settings.kmsFromWerkbon || settings.reistijdFromWerkbon || settings.werkbon_zonder_factuurregel)){
        setWerkbonnen(project);
      }
      if(project.uren.length && !factuur && settings.projecturen_overnemen){setUren(project);}
      if(project.proformas.length && _this.type == 'factuur'){setProformas(project);}
    }

    //Projecttaken
    function setTaken(project){
      const taken = project.taken;
      if(!taken.length){return;}

      $(`.data-${project.id}-container`).append(`
        <div class="col-md-6 col-12 my-2">
          <label>Taken</label>
          <infor-select-multiple name="taken[${project.id}]" id="taken" class="form-control-custom" placeholder="Selecteer taken" >
            ${taken.map(row => `<infor-select-option data-name="${row.name}" data-value="${row.id}" >${row.name}&nbsp;<span style="font-size: 0.7rem;" class="badge badge-${row.completed ? 'success' : 'primary'}">${row.completed ? 'Afgerond' : 'Open'}</span></infor-select-option>`).join('')}
          </infor-select-multiple>
        </div>
      `);
      initInforSelectMultiple();
    }

    //Uren
    function setUren(project){
      const uren = project.uren
      let datums = uren.map(row => row.datum);
      datums.sort((a, b) => new Date(a) - new Date(b));
      datums = [...new Set(datums)];
      $(`.data-${project.id}-container`).append(`
        <div class="col-md-6 col-12 my-2">
          <div class="d-flex justify-content-between">
            <label>Uren factureren uit periode:</label>
            <a class="btn btn-light h- m-0 p-1 px-2 rounded-pill tippy"
               onclick="showProjectUren(${project.id}, $('input[name=uren_van]').val(), $('input[name=uren_tot]').val())"
               data-tippy-content="Project uren bekijken">@icon_zoom</a>
          </div>
          <div class="flex-between">
            <input type="date" name="uren_van" onchange="selectUurPeriode()" class="form-control-custom" value="${datums[0]}" >
            <span class="mx-2">tot</span>
            <input type="date" name="uren_tot" onchange="selectUurPeriode()" class="form-control-custom" value="${datums[datums.length - 1]}" >
          </div>
        </div>
      `);

      selectUurPeriode();
    }
    function selectUurPeriode(){
      unsetUren(_projecten.selected);

      const van = $('[name=uren_van]').val();
      const tot = $('[name=uren_tot]').val();
      const project = _projecten.all.find(row => row.id == _projecten.selected);

      for (const uur of project.uren) {
        if (uur.gefactureerd == '1') { continue; }
        if (uur.datum >= van && uur.datum <= tot) {
          if (uur.machineuren.length) {
            for (const machineuur of uur.machineuren) {
              if (machineuur.totaal) {
                if (!_uren.machines[machineuur.machine.name]) {
                  _uren.machines[machineuur.machine.name] = { aantal: 0, prijs: 0 };
                }
                _uren.machines[machineuur.machine.name]['prijs'] = machineuur.machine.prijs;
                _uren.machines[machineuur.machine.name]['aantal'] += machineuur.totaal;
              }
            }
          } else if (uur.uursoort) {
            const uursoort = settings.uursoorten_per_week
              ? `${uur.uursoort.name} week ${now(uur.datum).weekNumber}`
              : uur.uursoort.name;

            if (!_uren.uursoorten[uursoort]) {
              _uren.uursoorten[uursoort] = { aantal: 0, prijs: 0 };
            }
            _uren.uursoorten[uursoort]['prijs'] = uur.uursoort.verkoopprijs;

            let aantal = parseFloat(
              uur.gewerkte_uren != 0
                ? uur.gewerkte_uren
                : (timeToTimestamp(uur.eindtijd) - timeToTimestamp(uur.begintijd)) / 3600 - uur.pauze
            );

            if (settings.factuur_uren_omhoog_afronden) {
              aantal = Math.ceil(aantal);
            }

            _uren.uursoorten[uursoort]['aantal'] += aantal;

          } else {
            let tarief = uur.user.role.tarief;

            let uren = parseFloat(
              uur.gewerkte_uren != 0
                ? uur.gewerkte_uren
                : (timeToTimestamp(uur.eindtijd) - timeToTimestamp(uur.begintijd)) / 3600 - uur.pauze
            );

            if (settings.factuur_uren_omhoog_afronden) {
              uren = Math.ceil(uren);
            }

            _uren.uren += uren;

            if (tarief && !_uren.urenGroupedByTarief[tarief.name]) {
              _uren.urenGroupedByTarief[tarief.name] = { id: 0, uren: 0, prijs: 0 };
            }

            if(tarief && tarief.eenheid == 'uur'){
              _uren.urenGroupedByTarief[tarief.name]['uren'] += uren;
              _uren.urenGroupedByTarief[tarief.name]['prijs'] = tarief.tarief;
              _uren.urenGroupedByTarief[tarief.name]['id'] = tarief.id;
            }
          }
        }
      }

      addUurRows();
    }
    function addUurRows(){
      for(const machine in _uren.machines){
        const string = addRow();
        $(`.${string}`).addClass(`uren-${_projecten.selected}-tr`)
        $(`[name='naam[${string}]']`).val(machine);
        $(`[name='aantal[${string}]']`).val(_uren.machines[machine].aantal);
        $(`[name='prijs[${string}]']`).val(_uren.machines[machine].prijs);
      }
      for(const uursoort in _uren.uursoorten){
        const string = addRow();
        $(`.${string}`).addClass(`uren-${_projecten.selected}-tr`)
        $(`[name='naam[${string}]']`).val(uursoort);
        $(`[name='aantal[${string}]']`).val(_uren.uursoorten[uursoort].aantal);
        $(`[name='prijs[${string}]']`).val(_uren.uursoorten[uursoort].prijs);
      }
      if( _uren.urenGroupedByTarief && Object.keys(_uren.urenGroupedByTarief).length){
        for (const tarief in _uren.urenGroupedByTarief){
          const string = addRow(null, true);
          $(`.${string}`).addClass(`uren-${_projecten.selected}-tr`)
          $(`[name='naam[${string}]']`).val(`Project uren, ${_projecten.all.find(row => row.id == _projecten.selected)?.projectnr ?? ''}` + " " + tarief);
          $(`[name='aantal[${string}]']`).val(_uren.urenGroupedByTarief[tarief].uren);
          $(`[name='prijs[${string}]']`).val(_uren.urenGroupedByTarief[tarief].prijs);
        }
      }else if(_uren.uren){
        const string = addRow(null, true);
        $(`.${string}`).addClass(`uren-${_projecten.selected}-tr`)
        $(`[name='naam[${string}]']`).val(`Project uren, ${_projecten.all.find(row => row.id == _projecten.selected)?.projectnr ?? ''}`);
        $(`[name='aantal[${string}]']`).val(_uren.uren.toFixed(2));
        $(`[name='prijs[${string}]']`).val((standaardtarief && standaardtarief.tarief) ? standaardtarief.tarief : '0.00');
      }
    }
    function unsetUren(id){
      _uren.uren = 0;
      _uren.uursoorten = {};
      _uren.machines = {};
      _uren.urenGroupedByTarief = {};
      deleteDiv(`.uren-${id}-tr`)
    }
    function toggleUurRows(){
      if(!settings.geenProjecturenSelectOfferte){return}

      if(!_inforSelectMultiple.get('offertes').getValues().length){
        $('[name=uren_van]').prop('readonly', false);
        $('[name=uren_tot]').prop('readonly', false);
        addUurRows();
      }else{
        $('[name=uren_van]').prop('readonly', true);
        $('[name=uren_tot]').prop('readonly', true);

        $(`.uren-${_projecten.selected}-tr`).remove();
      }
    }

    //Inkoopfacturen
    function addInkoopfactuurRegel(inkoopfactuur) {
      for (let regel of inkoopfactuur.regels){
        const string = addRow();
        $(`.${string}`).addClass(`inkoopfactuur-regel-tr-${inkoopfactuur.project_id}`)
        $(`[name='naam[${string}]']`).val(`inkoop regel, ${regel.naam}`);
        $(`[name='aantal[${string}]']`).val(regel.aantal);
        $(`[name='prijs[${string}]']`).val(regel.prijs);
        $(`[name='btw[${string}]']`).val(regel.btw);
        if(regel.incl == '1'){
          $(`[name='incl[${string}]']`).prop("checked", true);
        }
      }
    }

    //Datasets
    function initDatasetItem(id){
      const items = _datasets.all[id].dataset.items;
      const key = _datasets.all[id].key;

      _datasets.modal.find('.items').empty();
      for(const i in items){
        const item = items[i];
        const json = JSON.parse(item.value);

        if(!json[key]){continue;}

        _datasets.modal.find('.items').append(
          `<div class="flex-between my-2 item-row" data-search="${json[key]}" >
            <span>${json[key]}</span>
            <span><a class="btn btn-inverse-success" onclick="addDatasetItem(${id}, ${i})">@icon_plus</a></span>
          </div>`
        );
      }

      _datasets.modal.showModal();
      updateRowTotaal();
    }
    function addDatasetItem(id, index){
      const set = _datasets.all[id];
      const item = set.dataset.items[index];
      const json = JSON.parse(item.value);
      const prijs = json['prijs'] || (json['Prijs'] || 0);

      const string = addRow();
      $(`[name='naam[${string}]']`).val(json[set.key]).attr('name', `dataset_naam[${string}]`).after(
        `<input type="hidden" name="dataset_item[${string}]" value='${item.value}'>
        <input type="hidden" name="dataset_dataset[${string}]" value='${id}'>`
      );
      $(`[name='aantal[${string}]']`).val(1).attr('name', `dataset_aantal[${string}]`);
      $(`[name='prijs[${string}]']`).val(prijs).attr('name', `dataset_prijs[${string}]`);
      $(`[name='btw[${string}]']`).attr('name', `dataset_btw[${string}]`);
      $(`[name='incl[${string}]']`).attr('name', `dataset_incl[${string}]`);

    }

    //Mandagen
    function addMandagen(){
      let projecttaken = [];
      $('.project-taak-input').each(function(){
        projecttaken.push($(this).val());
      });
      if (projecttaken.length){
        ajax('/api/projecten/taken/get', { ids: projecttaken }).then(res => {
          confirmModal({
            text: `
              Mandagen toevoegen voor de volgende taken:
              <div class="my-2">
                ${res.taken.map(taak => `<span class="badge badge-primary mx-1">${taak.name}</span>`).join('')}
              </div>
              Tussen datums:
              <div class="d-flex">
                <input type="date" class="form-control-custom mx-1" name="mandagen_start_datum" required >
                <input type="date" class="form-control-custom mx-1" name="mandagen_eind_datum" required >
              </div>
            `
          }).then(response => {
            if(!response.status){ return; }

            const start_datum = $('[name=mandagen_start_datum]').val();
            const eind_datum = $('[name=mandagen_eind_datum]').val();

            let projectids = [...new Set(projecttaken.map(taak => res.taken.find(row => row.id == taak).project_id))];
            fillMandagen(projectids, start_datum, eind_datum);
          })
        })
      }else if(Object.keys(_projecten.selected).length) {
        confirmModal({
          text: `
            Mandagen toevoegen voor alle geselecteerde projecten?
            <div class="d-flex">
              <input type="date" class="form-control-custom mx-1" name="mandagen_start_datum" required >
              <input type="date" class="form-control-custom mx-1" name="mandagen_eind_datum" required >
            </div>
          `
        }).then(response => {
          if(!response.status){ return; }

          const start_datum = $('[name=mandagen_start_datum]').val();
          const eind_datum = $('[name=mandagen_eind_datum]').val();

          let projectids = Object.keys(_projecten.selected);
          fillMandagen(projectids, start_datum, eind_datum);
        })
      }
      else {
        notification('Selecteer eerst een project!', 'warning');
      }
    }
    function addEmptyMandagenRow(){
      const string = randomString();
      _mandagen.table.find('tbody').append(`<tr id="${randomString()}"><td><a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a></td><td><select class="form-select" name="mandagen[${string}][user]" required >${users.map(user => `<option value="${user.id}">${user.name} ${user.lastname}</option>`).join('')}</select></td>${_mandagen.dates.map(date => `<td><input class="form-control-custom p-1" type="number" name="mandagen[${string}][dates][${date}]"></td>`).join('')}</tr>`);
    }
    function fillMandagen(projectids, start_datum, eind_datum){
      ajax('/api/urenregistratie/get', { projectids: projectids, start: start_datum, end: eind_datum, groupByGroupBy: ['datum', 'medewerker_id'] }).then(res => {
          const uren = res.uren;

          _mandagen.dates = getDatesBetween(start_datum, eind_datum);

          const userIds = Array.from(new Set(Object.values(uren).flatMap(obj => Object.keys(obj))));

          const string = randomString();

          const tableHeader = `<thead><tr><th></th><th class="nobr">Medewerker</th>${_mandagen.dates.map(date => `<th class="nobr">${date}</th>`).join('')}</tr></thead>`;
          const tableRows = userIds.map(userId => {
              return `<tr id="${randomString()}"><td><a class="btn btn-inverse-danger mx-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a></td><td class="nobr"><input type='hidden' name="mandagen[${string}][user]" value="${userId}">${users.find(row => row.id == userId).name} ${users.find(row => row.id == userId).lastname}</td>${_mandagen.dates.map(date => {
                  const totalHours = Number(uren[date] && uren[date][userId] ? uren[date][userId].map(row => row.totaaluren100).reduce((a, b) => a + b, 0) : '0');
                  return `<td><input class="form-control-custom p-1" type="number" value="${totalHours}" name="mandagen[${string}][dates][${date}]"></td>`;
              }).join('')}</tr>`;
          });

          const tableBody = `<tbody>${tableRows.join('')}</tbody>`;
          _mandagen.table.html(tableHeader + tableBody);
          if(!_mandagen.container.find('.add-empty-mandagen-row-btn').length){
              _mandagen.container.append('<a class="btn btn-success text-white m-2 add-empty-mandagen-row-btn" onclick="addEmptyMandagenRow()">Persoon toevoegen</a>');
          }
      });
    }

    //ExactOnline
    function setExactOnlineOptions(){
      if(!_eo.instance.connected){ return; }


      const options = $(`[data-eo-division-option]`);
      options.removeClass('d-none');

      const { exact_online_division } = _this.klant?._bv || {};
      if(!exact_online_division){ return; }

      //Hide other options
      options.not(`[data-eo-division-option=${exact_online_division}]`).addClass('d-none');

      //Verify selected data and clear if it doesn't correspond to the selected klant division
      $('[data-exact-online-item], [data-exact-online-gl], [data-exact-online-vat]').each(function(){
        const instance = _inforSelectSearch.get(this.id)
        const value = instance.getValue().value;
        if(!value){ return true; }

        const selected_option = instance.options().filter(`[data-value="${value}"]`);
        if(!selected_option.length){ return true; }

        const division = selected_option.attr('data-eo-division-option');
        if(division !== exact_online_division){
          instance.clear();
        }
      });
    }

    //HTML
    function addRow(row = null, prepend = false){
      const string = randomString();

      // filler can't be '' as it will return true on ledger.includes(name)
      let vestiging_plaats = 'vestiging_name_filler';
      if(_projecten.selected){
        const project = _projecten.all.find(row => row.id == _projecten.selected);
        vestiging_plaats = project?.vestiging?.plaats || 'vestiging_name_filler';
      }

      // General ledger
      let gl_options = '';
      for(const ledger of _eg.ledgers){
        let data_selected = ledger.description.toLowerCase().includes(vestiging_plaats.toLowerCase()) ? 'data-selected="true"' : ''
        gl_options += `<span class="select_search-value" ${data_selected}  data-value="${ledger.code}" data-name="${ledger.code.replaceAll(' ', '')} | ${ledger.description}"><span class="badge badge-primary" >${ledger.code}</span> ${ledger.description}</span>`
      }
      let html = `
          <tr class="${string} factuur-tr" data-grab-element>
          <td>
            <div class='btns-container d-flex mx--1'>
              <a class="btn btn-inverse-danger mx-1" onclick="deleteGrabDropspot(this); deleteDiv('.${string}'); updateRowTotaal()" >@icon_close</a>
            </div>
          </td>
          ${_this.type == 'factuur' ? `
            ${_eg.instance.connected
              ? `<td class="select_search-container" data-grootboeken="${string}" >
                          <input type="hidden" name="ledger[${string}]" class="select_search-hidden-input row-ledger" data-placeholder="Grootboeknummer">
                          <div class="select_search-values" >
                            <div class="select_search-box" >${gl_options}</div>
                          </div>
                        </td>`
              : ''
            }
            ${_eo.instance.connected
              ? `
                <td>
                  <infor-select-search id="eo_item_${string}"class="form-select min-w-225" name="exact_online[${string}][item]" placeholder="Selecteer artikel" data-exact-online-item="${string}" >
                    ${_eo.items.map(item => `<infor-select-option data-params=":eo-division-option=${item.division}" ${_this.klant?._bv?.exact_online_division !== item.division ? 'class="d-none"' : ''} data-value="${item.code}" ${settings.exact_online?.default?.item == item.code ? 'data-selected' : ''} >${item.description}</infor-select-option>`).join('')}
                  </infor-select-search>
                </td>
                <td>
                  <infor-select-search id="eo_gl_${string}" class="form-select min-w-225" name="exact_online[${string}][gl]" placeholder="Selecteer grootboek" data-exact-online-gl="${string}" >
                    ${_eo.general_ledgers.map(ledger => `<infor-select-option data-params=":eo-division-option=${ledger.division}" class="d-none" data-value="${ledger.code}" >${ledger.description}</infor-select-option>`).join('')}
                  </infor-select-search>
                </td>`
              : ''
            }
            ${_bc.instance.connected
              ? `<td>
                    <select class="form-select bc_account" name="bc_account[${string}]" id="bc_account_${string}" required >
                        <option value="">Selecteer grootboeknummer</option>
                        ${_bc.accounts.map(account => `<option value="${account.number}" ${settings.business_central?.default?.sale_account == account.number ? 'selected' : ''} >${account.number} | ${account.name} </option>`)}
                    </select>
                  </td>`
              : ''
            }
            ${_king.instance.connected
              ? `<td>
                  <div class="select_group-container" >
                    <input type="hidden" name="king_grootboek[${string}]" data-placeholder="Selecteer grootboek" data-required="required" data-input-prefill="${(_king.grootboeken.find(row => row.king_id == settings.king_defaults?.grootboek).nummer +' | '+ _king.grootboeken.find(row => row.king_id == settings.king_defaults?.grootboek).naam) ?? ''}" value="${settings.king_defaults?.grootboek ?? ''}" class="select_group-hidden-input">
                    <div class="select_group-values" >
                      <div class="select_group-box" >
                        ${_king.grootboeken.map(grootboek => `<span class='select_group-value' data-group="${grootboek.soort}" data-value='${grootboek.nummer}' data-name='${grootboek.nummer} | ${grootboek.naam}'>${grootboek.naam}</span>`).join('')}
                      </div>
                    </div>
                  </div>
                </td>`
              : ''
            }
          ` : ''}

          <td><input class="form-control-custom mx-1 w-100 row-naam" name="naam[${string}]" placeholder="Naam" value="${row?.naam || ''}" ></td>
          ${settings.customFieldsOn ? settings.customFields.map(field => `<td><input class="form-control-custom mx-1 w-100" name="custom[${string}][${field.keyword}]" placeholder="${field.label}" value="${row?.custom?.[field.keyword] ?? field.default ?? ''}" ></td>`).join('') : ''}
          <td><input type="number" class="form-control-custom mx-1 w-100 row-aantal" step="0.01" min="0"  name="aantal[${string}]" placeholder="Aantal" onchange="updateRowTotaal()" value="${row?.aantal || ''}" data-aantal></td>
          ${settings.eenhedenOn ? `<td><div class="col-md-12 my-2 select_search-container">
                                      <input type="hidden" autocomplete="off" name="eenheid[${string}]" class="select_search-hidden-input form-select" placeholder="Eenheid">
                                      <div class="select_search-values">
                                        <div class="select_search-box">
                                          ${settings.eenheden.map(eenheid => `<span class="select_search-value" data-value="${eenheid.id}" data-name="${eenheid.enkelvoud}/${eenheid.meervoud}">${eenheid.enkelvoud}/${eenheid.meervoud}</span>`).join('')}
                                        </div>
                                      </div>
                                    </div></td>` : ''}
          <td><input type="number" class="form-control-custom mx-1 w-100 row-prijs" step="0.01" name="prijs[${string}]" placeholder="Bedrag" onchange="updateRowTotaal()" value="${row?.bedrag || ''}" data-bedrag></td>
          <td>
             ${_eg.instance.connected ? utilities.exactBtw(string) : ``}
             ${_eo.instance.connected ? utilities.exactOnlineBtw(string) : ``}
             ${_bc.instance.connected ? utilities.businessCentralBtw(string) : ``}
             ${_eb.connected ? utilities.ebBtw(string) : ``}
             ${_king.instance.connected ? utilities.kingBtw(string) : ``}
             ${(!_eg.instance.connected && !_eo.instance.connected && !_bc.instance.connected && !_eb.connected && !_king.instance.connected) ? utilities.select(string) : ''}
           </td>
           ${(!_eg.instance.connected && !_eo.instance.connected && !_bc.instance.connected && !_eb.connected && !_king.instance.connected)
              ? `<td class="text-center" ><input ${row ? row.checked : (Number(_settings.factuur_standaard_incl_btw) ? 'checked' : '')} name="incl[${string}]" class="form-check-custom" type="checkbox"></td>`
              : ''
            }
          <td>
            <input type="text" class="form-control-custom mx-1 w-px-125"  name="totaalBedrag[${string}]" readonly placeholder="€0.00" value="" data-totaal-bedrag>
            <input type="hidden" name="order_index[${string}]" value="-">
          </td>
          <td>
            <input type="hidden" name="2ba_code[${string}]">
            <a class="btn btn-inverse-success hide mx-1 tippy" data-tippy-content="Bereken 2ba prijs" name="calculate_2ba_price[${string}]" onclick="calculate_2ba_price('${string}')" >@icon_calculator</a>
          </td>
         </tr>
       `;
       if(prepend){
        $("#regels").prepend(html);
       }else{
        $("#regels").append(html);
       }

      updateRowTotaal();
      initInforSelectSearch();
      initSelectSearch();
      initSelectGroup();
      initDraggable();
      setExactOnlineOptions();
      tippyInit()
      return string;
    }

    async function calculate_2ba_price(string){
      const tweeba_code = $(`[name='2ba_code[${string}]`).val();

      if(!tweeba.leveranciers){
        loader('Leveranciers ophalen');
        const { leveranciers } = await ajax('/api/leveranciers/get', {
          is_tweeba: true,
          relations: [ 'tweebaMetadata' ],
          active: 1,
          sort_by: 'naam',
          sort_type: 'ASC',
        });
        tweeba.leveranciers = leveranciers;
        clearLoader();
      }

      const default_suppliers = tweeba.settings?.default_suppliers || [];

      let suppliers = tweeba.leveranciers
        .filter(leverancier => default_suppliers.includes(String(leverancier.id)))
        .map(leverancier => {
          const meta = leverancier.tweeba_metadata.find(meta => meta.meta_key == 'GLN');
          return meta?.meta_value;
        }).filter(Boolean);

      const query = tweeba_code;

      loader('Producten ophalen');
      const { products } = await ajax('/api/tweeba/products/search', {
        search: { query, suppliers }
      });
      clearLoader()

      loader('Prijzen ophalen');
      const { trade_items } = await ajax('api/tweeba/tradeitems/forproduct', {
        trade_item: {
          productid: products[0].Id,
          supplierGLNs: suppliers[0],
        },
        include_net_price: true,
      })
      clearLoader()

      let price = trade_items[0].GrossPriceInOrderUnit || 0;
      let roundedPrice = Math.round(price * 100) / 100;
      $(`[name='dataset_prijs[${string}]']`).val(roundedPrice);
      updateRowTotaal();
    }

    function addTextRow(){
      const string = randomString();
      const container = $("#regels");
      let colspan = getColspan(container.find('.factuur-tr').first()) - 2;
      container.append(
        `<tr class="${string}" data-grab-element>
           <td><div class='btns-container d-flex mx--1'><a class="btn btn-sm btn-inverse-danger w-100 mx-1 tippy" data-tippy-content="Regel verwijderen" onclick="deleteGrabDropspot(this); deleteDiv('.${string}')" >@icon_close</a></div></td>
           <td colspan="${colspan}"><input type="text" class="form-control-custom row-naam" placeholder="Naam" name="naam[${string}]"><input type="hidden" name="tekstregel[${string}]" value="1"><input type="hidden" name="order_index[${string}]" value="-"></td>
         </tr>`
      );

      initDraggable();

      return string;
    }
    function updateRowTotaal(){
      const regels = $('#regels');
      let totaalBedragTbl = 0;

      for (let regel of regels.children()) {
        regel = $(regel)
        const aantal = regel.find('[data-aantal]').val()
        const bedrag = regel.find('[data-bedrag]').val()

        if(aantal == undefined && bedrag == undefined){continue;}

        const totaalbedrag = aantal * bedrag;
        totaalBedragTbl += totaalbedrag;

        $(regel).find('[data-totaal-bedrag]').val("€"+toPrice(totaalbedrag))

      }
      $('#totaalBedrag').text("€"+toPrice(totaalBedragTbl))
    }
    function removeEmptyRows(){
      $('#regels').find('tr').each(function(){
        const container = $(this);
        const grabTd = container.find('.grabTd');
        if(grabTd.length){ return; }

        const naam = container.find('.row-naam').val();
        const prijs = container.find('.row-prijs').val();

        if(!naam && !prijs){ container.remove(); }
      })
    }

    function setBusinessCentralKostendrager(project){
      if(!_bc.instance.connected){ return; }

      const kostendrager_vestiging = settings?.business_central?.default?.kostendrager_vestiging || {};
      _inforSelectSearch.get('bc_kostendrager').setValue(kostendrager_vestiging[project.vestiging_id] || null);
    }

    //Explorer Files
    async function selectFromExplorer(){
      const response = await getExplorerFile();
      if(!response.status){ return; }

      appendExplorerFile({
        file: response.file,
        id: '#files',
        name: 'explorer_files[]'
      });
    }
    async function uploadToExplorer(){
      const response = await uploadExplorerFile({path: '/Facturen/Tijdelijk'});
      if(!response.status){ return; }

      appendExplorerFile({
        file: response.file,
        id: '#files',
        name: 'explorer_files[]'
      });
    }
  </script>
@endsection
