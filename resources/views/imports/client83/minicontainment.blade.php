@extends('layouts.app')

@section('title', 'Projecten Excel import')


@section('content')
<div class="mx-5">
  <div class="my-4">
    <div class="font-size-155">Projecten Excel import</div>
    <div class="small">Upload hier de Excel met projecten om deze te verwerken in Tessa!</div>
  </div>

  <div class="flex-between align-items-start">
    <div class="card p-4 mr-2 w-100 min-vh-50">
      <div class="append-loader">
        <span class="mb-2 font-size-12">Benodigde data verzamelen</span>
        <div class="my-2">
          <label>Import naam*</label>
          <input name="name" type="text" class="form-control-custom" placeholder="Naam">
        </div>
        <div class="my-2" id="dropify-container">
          <label>Projecten Excel*</label>
          <input class="dropify" type="file" name="excel" data-allowed-file-extensions='["xlsx", "xls"]'>
        </div>
        <div class="mt-4 center">
          <a class="btn btn-success text-white" id="dataConfirm">Bevestigen</a>
        </div>
      </div>
    </div>

    <div class="card p-4 ml-2 w-100 min-vh-50">
      <div class="append-loader">
        <span class="mb-2 font-size-12">Data verwerken in Tessa</span>
        <div class="my-3 py-3 form-control-custom flex-between" id="processExcel" readonly>
          <span><span class="mr-2">@icon_excel</span>Excel verwerken</span>
          <span id="excel-spinner">@icon_right</span>
        </div>
        <div class="my-3 py-3 form-control-custom flex-between" id="processProjecten" readonly>
          <span><span class="mr-2">@icon_clock</span>Projecten verwerken</span>
          <span id="project-spinner">@icon_right</span>
        </div>
        <div class="my-3 py-3 form-control-custom flex-between" id="finishImport" readonly>
          <span><span class="mr-2">@icon_confirm</span>Import afronden</span>
          <span id="finish-spinner">@icon_right</span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('script')
  <script>
    const _this = {
      import_id: null,
    }
    const template =  @json($template);

    $('#dataConfirm').on('click', function() {
      $(this).hide().after('@spinner');
      const excel = $('input[name="excel"]').prop('files')[0];
      const name = $('input[name="name"]').val();

      const data = {
        fn: 'storeExcel',
        template: template.id,
        file: excel,
        name: name,
      }

      ajaxFile('/api/imports/trigger', data).then(res => {
        notification(res.data.message, (res.data.success ? 'success' : 'warning'), 4);
        if(!res.data.success) {
          $('#dataConfirm').show().next().remove();
          return;
        }
        _this.import_id = res.data.import_id;
        $('#dataConfirm').next().remove();
        disableImport(res.data);

        $('#processExcel').attr('readonly', false).addClass('cursor-pointer');
      }).catch(handleCatchError);

    });

    $('#processExcel').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#excel-spinner').html('@spinner');

      const data = {
        fn: 'processExcel',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('/api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#excel-spinner').html('@icon_right');
          return;
        }

        $('#excel-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processProjecten').attr('readonly', false).addClass('cursor-pointer');
      }).catch(e => {
        handleCatchError(e);
        $('#excel-spinner').html('@icon_right');
      });
    });
    $('#processProjecten').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#project-spinner').html('@spinner');

      const data = {
        fn: 'processProjecten',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#project-spinner').html('@icon_right');
          return;
        }

        $('#project-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');

        $('#finishImport').attr('readonly', false).addClass('cursor-pointer');
      }).catch(handleCatchError);
    });

    $('#finishImport').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#finish-spinner').html('@spinner');

      const data = {
        fn: 'finishImport',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#finish-spinner').html('@icon_right');
          return;
        }

        $('#finish-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        loader('Alles succesvol verwerkt, redirecten naar de home pagina...');
        setTimeout(function() {
          window.location.href = url;
        }, 3000);
      }).catch(handleCatchError);
    });

    function disableImport(data) {
      $('input[name="name"]').attr('readonly', true);
      const link = `${url}/api/file/explorer/files/${data.src}`;
      $('#dropify-container').html(`
          <div class="center mt-5">
            <img onclick="downloadLink('${link}')" class="hoverTranslateY" src="${url}/client/public/img/explorer/files/xlsx.png" alt="Excel">
          </div>
      `)
    }

  </script>
@endsection
