@extends('layouts.app')

@section('title', 'Circet Excel import')


@section('content')
<div class="mx-5">
  <div class="my-4">
    <div class="font-size-155">Circet Excel import</div>
    <div class="small">Upload hier de Excel van Circet om deze te verwerken in Tessa!</div>
  </div>

  <div class="flex-between align-items-start">
    <div class="card p-4 mr-2 w-100 min-vh-50">
      <div class="append-loader">
        <span class="mb-2 font-size-12">Benodigde data verzamelen</span>
        <div class="row my-2">
          <div class="col-6">
            <label>Project*</label>
            <infor-search
              id="project"
              name="project"
              placeholder="Selecteer project"
              class="form-control-custom"
              data-content="projectnaam"
              data-sub-content="projectnr"
              data-api="api/projecten/search"
              data-errors="handleCatchError"
            ></infor-search>
          </div>
          <div class="col-6">
            <label>Import naam*</label>
            <input name="name" type="text" class="form-control-custom" placeholder="Naam">
          </div>
        </div>
        <div class="my-2" id="dropify-container">
          <label>Circet Excel*</label>
          <input class="dropify" type="file" name="excel" data-allowed-file-extensions='["xlsx", "xls"]'>
        </div>
        <div class="mt-4 center">
          <a class="btn btn-success text-white" id="dataConfirm">Bevestigen</a>
        </div>
      </div>
    </div>

    <div class="card p-4 ml-2 w-100 min-vh-50">
      <div class="append-loader">
        <span class="mb-2 font-size-12">Data verwerken in Tessa</span>
        <div class="my-3 py-3 form-control-custom flex-between" id="processExcel" readonly>
          <span><span class="mr-2">@icon_excel</span>Excel verwerken</span>
          <span id="excel-spinner">@icon_right</span>
        </div>
        <div class="my-3 py-3 form-control-custom flex-between" id="processUren" readonly>
          <span><span class="mr-2">@icon_clock</span>Uren verwerken</span>
          <span id="uren-spinner">@icon_right</span>
        </div>
        <div class="my-3 py-3 form-control-custom flex-between" id="processInkoop" readonly>
          <span><span class="mr-2">@icon_file_text</span>Inkoopbonnen aanmaken</span>
          <span id="inkoop-spinner">@icon_right</span>
        </div>
        <div class="my-3 py-3 form-control-custom flex-between" id="finishImport" readonly>
          <span><span class="mr-2">@icon_confirm</span>Import afronden</span>
          <span id="finish-spinner">@icon_right</span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('script')
  <script>
    const _this = {
      editImport: @json($import),
      import_id: null,
    }
    const template =  @json($template);

    $(document).ready(function() {
      prefillImport();
    });

    async function prefillImport(){
      if(!_this.editImport){return;}
      const project = await getProject({id: _this.editImport.data.find(d => d.keyword == 'project_id')});
      console.log(project);

      _this.import_id = _this.editImport.id;

      $('#project').replaceWith(`
        <input readonly type="text" class="form-control-custom" value="${project.projectnr} - ${project.projectnaam}">
      `);
      $('input[name="name"]').attr('readonly', true).val(_this.editImport.name);
      $('#dropify-container').html(`
        <div class="center mt-5">
          <img onclick="downloadLink('${_this.editImport.src}')" class="hoverTranslateY" src="${url}/client/public/img/explorer/files/xlsx.png" alt="Excel">
        </div>
      `);
      $('#dataConfirm').hide().next().remove();

      if(!_this.editImport.last_step){
        $('#processExcel').attr('readonly', false).addClass('cursor-pointer');
      } else if(_this.editImport.last_step == 'processExcel') {
        $('#processExcel').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processUren').attr('readonly', false).addClass('cursor-pointer');
      } else if(_this.editImport.last_step == 'processUren') {
        $('#processExcel').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processUren').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processInkoop').attr('readonly', false).addClass('cursor-pointer');
      } else if(_this.editImport.last_step == 'processInkoop') {
        $('#processExcel').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processUren').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processInkoop').attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#finishImport').attr('readonly', false).addClass('cursor-pointer');
      }
    }

    $('#dataConfirm').on('click', function() {
      $(this).hide().after('@spinner');
      const projectid = _inforSearch.get('project').getValue().value;
      const excel = $('input[name="excel"]').prop('files')[0];
      const name = $('input[name="name"]').val();

      const data = {
        fn: 'storeExcel',
        template: template.id,
        projectid: projectid,
        file: excel,
        name: name,
      }

      ajaxFile('/api/imports/trigger', data).then(res => {
        notification(res.data.message, (res.data.success ? 'success' : 'warning'), 4);
        if(!res.data.success) {
          $('#dataConfirm').show().next().remove();
          return;
        }
        _this.import_id = res.data.import_id;
        $('#dataConfirm').next().remove();
        disableImport(res.data);

        $('#processExcel').attr('readonly', false).addClass('cursor-pointer');
      }).catch(handleCatchError);

    });

    $('#processExcel').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#excel-spinner').html('@spinner');

      const data = {
        fn: 'processExcel',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('/api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#excel-spinner').html('@icon_right');
          return;
        }

        $('#excel-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processUren').attr('readonly', false).addClass('cursor-pointer');
      }).catch(e => {
        handleCatchError(e);
        enableImport();
        $('#excel-spinner').html('@icon_right');
      });
    });

    $('#processUren').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#uren-spinner').html('@spinner');

      const data = {
        fn: 'processUren',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('/api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#uren-spinner').html('@icon_right');
          return;
        }

        $('#uren-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        $('#processInkoop').attr('readonly', false).addClass('cursor-pointer');
      }).catch(handleCatchError);
    });

    $('#processInkoop').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#inkoop-spinner').html('@spinner');

      const data = {
        fn: 'processInkoop',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#inkoop-spinner').html('@icon_right');
          return;
        }

        $('#inkoop-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');

        $('#finishImport').attr('readonly', false).addClass('cursor-pointer');
      }).catch(handleCatchError);
    });

    $('#finishImport').on('click', function() {
      if($(this).attr('readonly') === 'readonly') return;
      $('#finish-spinner').html('@spinner');

      const data = {
        fn: 'finishImport',
        template: template.id,
        import_id: _this.import_id,
      }

      ajax('api/imports/trigger', data).then(res => {
        notification(res.message, (res.success ? 'success' : 'warning'), 4);
        if(!res.success) {
          $('#finish-spinner').html('@icon_right');
          return;
        }

        $('#finish-spinner').html('@icon_check');
        $(this).attr('readonly', true).addClass('bg-inverse-success').removeClass('cursor-pointer');
        loader('Alles succesvol verwerkt, redirecten naar de imports pagina...');
        setTimeout(function() {
          window.location.href = url + '/imports';
        }, 3000);
      }).catch(handleCatchError);
    });

    function enableImport() {
      $('#dataConfirm').show().next().remove();
      $('#project').replaceWith(`
        <infor-search
          id="project"
          name="project"
          placeholder="Selecteer project"
          class="form-control-custom"
          data-content="projectnaam"
          data-sub-content="projectnr"
          data-api="api/projecten/search"
          data-errors="handleCatchError"
        ></infor-search>
      `);
      $('input[name="name"]').attr('readonly', false);
      $('#dropify-container').html(`
        <label>Circet Excel*</label>
        <input class="dropify" type="file" name="excel" data-allowed-file-extensions='["xlsx", "xls"]'>
      `);

      $('#processExcel').attr('readonly', true).removeClass('cursor-pointer').removeClass('bg-inverse-success');

      dropifyInit()
    }

    function disableImport(data) {
      $('#project').replaceWith(`<input readonly type="text" class="form-control-custom" value="${_inforSearch.get('project').getValue().name}">`);
      $('input[name="name"]').attr('readonly', true);
      const link = `${url}/api/file/explorer/files/${data.src}`;
      $('#dropify-container').html(`
          <div class="center mt-5">
            <img onclick="downloadLink('${link}')" class="hoverTranslateY" src="${url}/client/public/img/explorer/files/xlsx.png" alt="Excel">
          </div>
      `)
    }

  </script>
@endsection
