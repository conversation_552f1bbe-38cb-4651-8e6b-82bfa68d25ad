@extends('layouts.app')

@section('title', 'Statistieken')

@section('content')

  @php
    $show = json_decode((getSettingValue('statistieken_show') ?? '{}'), true);
    $show = $show['urenregistratie'] ?? [];
  @endphp

  <section class="my-4 append-loader h-px-100" data-stats-navigation >
    <div class="py-3 bg-modal z-index-999" data-content >
      <div class="card m-0 p-2">
        <div class="row">
          <div class="col-md-3 col-6 my-2">
            <div class="w-100" data-label >Groeperen op</div>
            <select name="sort_by" class="form-select" >
              <option value="all" >Alles</option>
              <option value="bv" >B.V.</option>
              <option value="user" >Medewerker</option>
            </select>
          </div>
          <div class="col-md-3 col-6 my-2 select_search-container" data-sort-option-container >
            <div class="w-100" data-label >Optie</div>
            <input type="hidden" name="sort_option" class="select_search-hidden-input" data-placeholder="Selecteer optie">
            <div class="select_search-values" >
              <div class="select_search-box"></div>
            </div>
          </div>
          <div class="col-md-3 col-6 my-2">
            <div class="w-100" data-label >Van</div>
            <input type="date" class="form-control-custom" name="date_start" value="{{Carbon()->now()->addDays(-30)->format('Y-m-d')}}" >
          </div>
          <div class="col-md-3 col-6 my-2">
            <div class="w-100" data-label >Tot</div>
            <input type="date" class="form-control-custom" name="date_end" value="{{Carbon()->now()->format('Y-m-d')}}" >
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="w-100 my-4" data-stats style="display: none" >
    <div class="flex-between">
      <div class="w-100 mx-3 dropdown-divider " ></div>
      <h4 class="nobr" data-uren-count >0 UREN</h4>
      <div class="w-100 mx-3 dropdown-divider" ></div>
    </div>
  </section>

  <section class="w-100" data-stats style="display: none" >
    <div class="row">
      <div class="col-12 my-3  @if(!isset($show['uren_per_medewerker'])) d-none @endisset ">
        <div class="card m-0 p-2">
          <h4>
            <span>Uren per medewerker</span>
            <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Het totale aantal ingevoerde uren per categorie in de urenregistratiemodule binnen de geselecteerde periode">@icon_question</span>
          </h4>
          <div class="h-px-250">
            <canvas id="user_worktime"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_verhouding_uren'])) d-none @endisset ">
        <div class="card m-0 p-2">
          <h4>
            <span>Verhouding uren</span>
            <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Procentuele verhouding van alle ingevoerde uren door geselecteerde medewerkers per categorie in de urenregistratiemodule binnen de geselecteerde periode">@icon_question</span>
          </h4>
          <div class="h-px-250" >
            <canvas id="work_hours_ratio"></canvas>
          </div>
          <div data-toggle-container class="hover-mark m-1 rounded-5">
            <div data-toggle-btn class="flex-between cursor-pointer p-2"><span>Toelichting</span> @icon_down</div>
            <div data-toggle-content style="display: none" class="w-100">

              <div class="overflow-auto" >
                <table class="table" >
                  <thead>
                    <th>Medewerker</th>
                    <th>Werkuren</th>
                    <th>Pauze</th>
                    <th>Reis</th>
                    <th>Verlof</th>
                    <th>Bijzonderverlof</th>
                    <th>Feest</th>
                    <th>Ziekteuren</th>
                    <th>Totaal</th>
                  </thead>
                  <tbody data-chart-explanation="work_hours_ratio" ></tbody>
                </table>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_gemiddeld_aantal_uren_per_week'])) d-none @endisset ">
        <div class="card m-0 p-2 position-relative">
          <h4 class="flex-between" >
            <div>
              <span>Gemiddeld aantal uren per week</span>
              <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Gemiddeld aantal gewerkte uren per medewerker per week in de geselecteerde periode in de urenregistratie module.">@icon_question</span>
            </div>
            <a class="btn py-0" data-average-week-worktime-toggle > @icon_down </a>
          </h4>
          <div class="w-100" style="display: none" data-average-week-worktime-container >
            <div class="hover-shadow-inset-active rounded p-2" >

              <label class="flex-between w-100 my-2 cursor-pointer">Werkuren <input data-average-week-worktime-sub="totaaluren100" checked class="form-switch-custom" type="checkbox"> </label>
              <label class="flex-between w-100 my-2 cursor-pointer">Pauze <input data-average-week-worktime-sub="pauze" class="form-switch-custom" type="checkbox"></label>
              <label class="flex-between w-100 my-2 cursor-pointer">Reis <input data-average-week-worktime-sub="reisuren" checked class="form-switch-custom" type="checkbox"></label>
              <label class="flex-between w-100 my-2 cursor-pointer">Verlof <input data-average-week-worktime-sub="verlof" checked class="form-switch-custom" type="checkbox"></label>
              <label class="flex-between w-100 my-2 cursor-pointer">Bijzonderverlof <input data-average-week-worktime-sub="bijzonderverlof" checked class="form-switch-custom" type="checkbox"></label>
              <label class="flex-between w-100 my-2 cursor-pointer">Feest <input data-average-week-worktime-sub="feesturen" checked class="form-switch-custom" type="checkbox"></label>
              <label class="flex-between w-100 my-2 cursor-pointer">Ziekteuren <input data-average-week-worktime-sub="ziekteuren" class="form-switch-custom" type="checkbox"></label>


            </div>
          </div>
          <div class="d-flex justify-content-between">
            <div class="h-px-250 v-center " >
              <div class="h-px-200"><canvas id="average_week_worktime"></canvas></div>
            </div>
            <div class="h-px-250 w-100 pr-4 overflow-auto" data-average-week-worktime-list></div>
          </div>
          <div data-toggle-container class="hover-mark m-1 rounded-5">
            <div data-toggle-btn class="flex-between cursor-pointer p-2"><span>Toelichting</span> @icon_down</div>
            <div data-toggle-content style="display: none" class="w-100">

              <div class="overflow-auto" >
                <table class="table" >
                  <thead>
                  <th>Medewerker</th>
                  <th>Gewerkte uren</th>
                  <th>Aantal geselecteerde weken</th>
                  <th>Gemiddeld aantal uren per week</th>
                  </thead>
                  <tbody data-chart-explanation="average_week_worktime" ></tbody>
                </table>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_top_projecten'])) d-none @endisset ">
        <div class="card m-0 p-2">
          <h4>
            <span>Top 5 Projecten (meeste uren)</span>
            <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="De 5 projecten in de geselecteerde periode met de meest geregistreerde uren in de urenregistratie module.">@icon_question</span>
          </h4>
          <div class="d-flex justify-content-between">
            <div class="h-px-250 v-center " >
              <div class="h-px-200"><canvas id="top_5_projects"></canvas></div>
            </div>
            <div class="w-100 pr-4 overflow-hidden" data-top-five-projects-list>
            </div>
          </div>
          <div data-toggle-container class="hover-mark m-1 rounded-5">
            <div data-toggle-btn class="flex-between cursor-pointer p-2"><span>Alle projecten</span> @icon_down</div>
            <div data-toggle-content style="display: none" class="w-100">

              <div class="overflow-auto" >
                <table class="table" >
                  <thead>
                  <th>Project</th>
                  <th>Gewerkte uren</th>
                  </thead>
                  <tbody data-chart-explanation="top_5_projects" ></tbody>
                </table>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_gemiddeld_aantal_uren_per_project'])) d-none @endisset ">
        <div class="card m-0 p-2 position-relative">
          <div class="position-absolute text-center w-100" style="bottom: 70px">
            <h3 class="font-weight-semibold" data-average-project-time ></h3>
          </div>
          <h4 class="flex-between" >
            <div>
              <span>Gemiddeld aantal uren per project</span>
              <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Gemiddeld aantal uren per project in de geselecteerde periode in de urenregistratie module.">@icon_question</span>
            </div>
            <small class="text-muted" > <span data-average-project-time-scale ></span> </small>
          </h4>
          <div class="h-px-250" >
            <canvas id="average_project_time"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_op_andere_bv'])) d-none @endisset ">
        <div class="card m-0 p-2">
          <h4>
            <span>Uren op andere bv's</span>
            <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Medewerkers die uren geboekt hebben op een project van een andere bv">@icon_question</span>
          </h4>
          <div class="d-flex justify-content-between">
            <div class="h-px-250 v-center " >
              <div class="h-px-200"><canvas id="uren_op_andere_bv"></canvas></div>
            </div>
            <div class="w-100 pr-4 overflow-hidden" data-hours-other-bv-list>
            </div>
          </div>
          <div data-toggle-container class="hover-mark m-1 rounded-5">
            <div data-toggle-btn class="flex-between cursor-pointer p-2"><span>Toelichting</span> @icon_down</div>
            <div data-toggle-content style="display: none" class="w-100">
              <div class="overflow-auto" >
                <table class="table" >
                  <thead>
                    <th>Medewerker</th>
                    <th>B.V. medewerker</th>
                    <th>Gewerkte uren</th>
                    <th>Project</th>
                    <th>B.V. project</th>
                  </thead>
                  <tbody data-chart-explanation="uren_op_andere_bv" ></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-12 my-3  @if(!isset($show['uren_zonder_project'])) d-none @endisset ">
        <div class="card m-0 p-2 position-relative">
          <h4 class="flex-between" >
            <div>
              <span>Uren zonder project per medewerker</span>
              <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Uren zonder project per medewerker in de geselecteerde periode in de urenregistratie module.">@icon_question</span>
            </div>
          </h4>
          <div class="d-flex justify-content-between">
            <div class="h-px-250 v-center " >
              <div class="h-px-200"><canvas id="uren_zonder_project"></canvas></div>
            </div>
            <div class="h-px-250 w-100 pr-4 overflow-auto" data-uren-zonder-project-list></div>
          </div>
        </div>
      </div>
    </div>
    <section class="w-100 my-3 @if(!isset($show['verlof_per_medewerker'])) d-none @endif" data-stats style="display: none">
      <div class="card m-0 p-2 position-relative">

        <div class="position-absolute font-size-125 font-weight-semibold transition-025 text-success" style="top: 200px; right: 50px " data-total-revenue-label="incl" ></div>

        <h4>
          <span>Verlof per medewerker</span>
          <span class="text-muted tippy mx-2 font-size-11" data-tippy-content="Vergelijking van verlofsaldo en gebruikt verlof per medewerker.">@icon_question</span>
        </h4>
        <div class="mb-2">
          <label for="verlof_sort_select" class="me-2">Sorteren op:</label>
          <select id="verlof_sort_select" class="form-select form-select-sm w-auto d-inline-block">
            <option value="standaard">Standaard</option>
            <option value="meest_gebruikt">Meest gebruikte verlofuren</option>
            <option value="alfabetisch">Alfabetisch</option>
          </select>
        </div>
        <div class="h-px-250">
          <canvas id="verlof_medewerkers"></canvas>
        </div>
        <div class="d-flex align-items-center justify-content-between mt-2">
          <div>
            <button id="verlof_prev_page" class="btn btn-sm btn-outline-secondary me-2">Vorige</button>
            <button id="verlof_next_page" class="btn btn-sm btn-outline-secondary">Volgende</button>
          </div>
          <span id="verlof_page_info" class="text-muted font-size-075">Pagina 1 van 1</span>
        </div>
      </div>
    </section>
  </section>
@endsection
@section("script")
  <script>
    var uren = [];
    var verlof = [];

    const users = @json($users);
    const bvs = @json(getBvs());

    let verlofPage = 0;
    const verlofPageSize = 5;
    let verlofSortType = 'standaard';

    const _base = {
      showSet: @json($show),
      request: null,
      show: false,
      start: () => { return $('[name=date_start]').val() },
      end: () => { return $('[name=date_end]').val() },
      sort: () => { return $('[name=sort_by]').val() },
      sort_option: () => { return $('[name=sort_option]').val() },
    }
    const _charts = {
      workHoursRatio: null,
      topFiveProjects: null,
      userWorktime: null,
      averageProjectTime: null,
      averageWeekWorktime: null,
      hoursOtherBv: null,
      urenZonderProject: null,
      verlofPerMedewerker: null,
    }

    $(document).ready(function(){
      $('[name=sort_by]').val('all').trigger('change');
    });

    $('[data-average-week-worktime-toggle]').click(() => {
      const a = $('[data-average-week-worktime-toggle]');
      const container = $('[data-average-week-worktime-container]');

      container.toggle(300);
      a.rotate(180);

    })
    $('[name=sort_by]').change(function(){

        const container = $('[data-sort-option-container]');
        container.find('.select_search-value').remove();
        container.find('.select_search-input').val('').prop('disabled', false);

      if(this.value == 'all'){
        initUren()
        container.find('.select_search-input').val('').prop('disabled', true);
        return;
      }

      if(this.value == 'user'){
        for(const user of users){
          const { id, name, lastname} = user;
          $('[data-sort-option-container]').find('.select_search-box').append(`<span class="select_search-value" data-value="${id}" data-name="${name} ${lastname}">${name} ${lastname}</span>`)
        }
      }
      if(this.value == 'bv'){
        for(const bv of bvs){
          const { id, name} = bv;
          $('[data-sort-option-container]').find('.select_search-box').append(`<span class="select_search-value" data-value="${id}" data-name="${name}">${name}</span>`)
        }
      }


    });
    $('[data-average-week-worktime-sub]').change(averageWeekWorktime);
    $('[name=sort_option], [name=date_start], [name=date_end]').change(initUren)

    function initUren(){
      loader();

      const data = {
        relations: ['project', 'user'],
        start: _base.start(),
        end: _base.end(),
      }

      if(_base.sort() && _base.sort_option()){
        data[_base.sort()] = _base.sort_option();
      }

      if(_base.request){
        _base.request.abort();
        _base.request = null;
      }

      _base.request = ajax('api/urenregistratie/get', data);
      _base.request
        .always(() => {
          _base.request = null;
        })
        .then(response => {
          uren = response.uren;
          verlof = response.verlof;
          initGraphs();
        })
        .catch(err => {
          if(err.status === 0){ return }
          errorLoader()
        });


    }
    function initGraphs(){
      if(!_base.show){
        _base.show = true;
        $('[data-stats]').toggle(300);
      }

      $('[data-chart-explanation]').empty();

      try{ initTotalUren(); }catch (e) { handleCatchError(e) }
      try{ userWorktime(); }catch (e) { handleCatchError(e) }
      try{ workHoursRatio(); }catch (e) { handleCatchError(e) }
      try{ averageProjectTime(); }catch (e) { handleCatchError(e) }
      try{ topFiveProjects(); }catch (e) { handleCatchError(e) }
      try{ averageWeekWorktime(); }catch (e) { handleCatchError(e) }
      try{ hoursOtherBv(); }catch (e) { handleCatchError(e) }
      try{ urenZonderProject(); }catch (e) { handleCatchError(e) }
      try{ verlofPerMedewerkerChart(); }catch (e) {handleCatchError(e)}
      successLoader();
    }
    function initTotalUren(){

      const data = mapUren(uren);

      $('[data-uren-count]').html(`${data.total.toFixed(1)} UREN`)
    }

    function mapUren(uren){
      const data = {
        werk: 0,
        pauze: 0,
        reis: 0,
        verlof: 0,
        bijzonderverlof: 0,
        feest: 0,
        ziekteuren: 0,
        total: 0,
        users: {},
      }

      for(const uur of uren){
        const { totaaluren100, pauze, verlof, bijzonderverlof, reisuren, feesturen, ziekteuren, user} = uur;
        if (user == null){ continue; }

        data.werk += Number(totaaluren100 || 0);
        data.pauze += Number(pauze || 0);
        data.verlof += Number(verlof || 0);
        data.bijzonderverlof += Number(bijzonderverlof || 0);
        data.reis += Number(reisuren || 0);
        data.feest += Number(feesturen || 0)
        data.ziekteuren += Number(ziekteuren || 0);
        data.total += Number(totaaluren100) + Number(pauze) + Number(verlof) + Number(bijzonderverlof) + Number(reisuren) + Number(feesturen) + Number(ziekteuren);
        data.users[user.id] = user
      }

      data.users = resetIndex(data.users);

      return data;
    }

    function userWorktime(){
      if(_base.showSet?.user_worktime){ return; }

      let users = [];
      for(const uur of uren){
        const { totaaluren100, pauze, verlof, bijzonderverlof, reisuren, feesturen, user, ziekteuren} = uur;

        if(!user){ continue }

        let row = users.find(x => x.user_id == user.id);
        if(!row){
          users.push({
            user: user,
            user_id: user.id,
            total: 0,
            werk: 0,
            pauze: 0,
            verlof: 0,
            bijzonderverlof: 0,
            reis: 0,
            feest: 0,
            ziekteuren: 0,
          });
          row = users.find(x => x.user_id == user.id);
        }

        row.total += Number(totaaluren100 || 0) + Number(pauze || 0) + Number(verlof || 0) + Number(bijzonderverlof || 0) + Number(reisuren || 0) + Number(feesturen || 0);
        row.werk += Number(totaaluren100 || 0);
        row.pauze += Number(pauze || 0);
        row.verlof += Number(verlof || 0);
        row.bijzonderverlof += Number(bijzonderverlof || 0);
        row.reis += Number(reisuren || 0);
        row.feest += Number(feesturen || 0)
        row.ziekteuren += Number(ziekteuren || 0)
      }

      users.sort((a, b) => {
        return (a.total >= b.total) ? -1 : 1;
      });


      let labels = [];
      let data = {
        werk: [],
        pauze: [],
        reis: [],
        verlof: [],
        bijzonderverlof: [],
        feest: [],
        ziekteuren: [],
      }

      for(const row of users){
        if(!row.total){ continue; }

        labels.push(`${row.user.name} ${row.user.lastname}`);
        data.werk.push(row.werk);
        data.pauze.push(row.pauze);
        data.reis.push(row.reis);
        data.verlof.push(row.verlof);
        data.bijzonderverlof.push(row.bijzonderverlof);
        data.feest.push(row.feest);
        data.ziekteuren.push(row.ziekteuren);
      }

      if(_charts.userWorktime){
        _charts.userWorktime.data.labels = labels;
        _charts.userWorktime.data.datasets[0].data = data.werk;
        _charts.userWorktime.data.datasets[1].data = data.pauze;
        _charts.userWorktime.data.datasets[2].data = data.reis;
        _charts.userWorktime.data.datasets[3].data = data.verlof;
        _charts.userWorktime.data.datasets[4].data = data.bijzonderverlof;
        _charts.userWorktime.data.datasets[5].data = data.feest;
        _charts.userWorktime.data.datasets[6].data = data.ziekteuren;
        _charts.userWorktime.update();
        return;
      }

      let options =  {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Werkuren',
              backgroundColor: "#8ABB21BF",
              data: data.werk,
              borderRadius: 3,
            },
            {
              label: 'Pauze',
              backgroundColor: "#ffa600BF",
              data: data.pauze,
              borderRadius: 3,
            },
            {
              label: 'Reis',
              backgroundColor: "#2D95ECBF",
              data: data.reis,
              borderRadius: 3,
            },
            {
              label: 'Verlof',
              backgroundColor: "#F64D2ABF",
              data: data.verlof,
              borderRadius: 3,
            },
            {
              label: 'Bijzonderverlof',
              backgroundColor: "#c02323BF",
              data: data.bijzonderverlof,
              borderRadius: 3,
            },
            {
              label: 'Feest',
              backgroundColor: "#7d3ac1BF",
              data: data.feest,
              borderRadius: 3,
            },
            {
              label: 'Ziekteuren',
              backgroundColor: "#72a7a7BF",
              data: data.ziekteuren,
              borderRadius: 3,
            }
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            legend: {
              display: true,
            }
          },
          scales: {
            x: {
              stacked: true
            },
            y: {
              stacked: true
            }
          },
        }
      };
      _charts.userWorktime = new Chart(document.getElementById('user_worktime'), options);
    }
    function workHoursRatio(){
      if(_base.showSet?.work_hours_ratio){ return; }

      const generateTr = (data, is_total = false) => {
        const {users, werk, pauze, reis, verlof, bijzonderverlof, feest, ziekteuren, total} = data;

        if(!total && !is_total){ return ''; }

        let td_th = 'th';
        let label = 'Totaal';

        if(!is_total){
          const user = first(users);
          label = `${user.name || ''} ${user.lastname || ''}`
          td_th = `td`;
        }

        return `
          <tr>
            <${td_th}>${label}</${td_th}>
            <${td_th}>${ werk ? `${werk}<div class="text-muted font-size-075" >${(werk / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ pauze ? `${pauze}<div class="text-muted font-size-075" >${(pauze / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ reis ? `${reis}<div class="text-muted font-size-075" >${(reis / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ verlof ? `${verlof}<div class="text-muted font-size-075" >${(verlof / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ bijzonderverlof ? `${bijzonderverlof}<div class="text-muted font-size-075" >${(bijzonderverlof / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ feest ? `${feest}<div class="text-muted font-size-075" >${(feest / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ ziekteuren ? `${ziekteuren}<div class="text-muted font-size-075" >${(ziekteuren / total * 100).toFixed(1)}%</div>` : '<span class="text-muted" >0</span>' }</${td_th}>
            <${td_th}>${ total ? total : '<span class="text-muted" >0</span>'  }</${td_th}>
          </tr>
        `

      }

      const data = mapUren(uren);
      const tbody = $(`[data-chart-explanation="work_hours_ratio"]`);

      let uren_by_user = resetIndex(groupBy(uren, 'medewerker_id'));
      uren_by_user = uren_by_user.map(uren => { return mapUren(uren); })
      uren_by_user = uren_by_user.sort((a, b) => { return b.total - a.total; })
      uren_by_user.map(user_uren => tbody.append(generateTr(user_uren)) );
      tbody.append(generateTr(data, true));

      if(_charts.workHoursRatio){
        _charts.workHoursRatio.data.datasets[0].data = [data.werk, data.pauze, data.reis, data.verlof, data.bijzonderverlof, data.feest, data.ziekteuren];
        _charts.workHoursRatio.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          labels: ['Werkuren', 'Pauze', 'Reis', 'Verlof', "Bijzonder verlof", "Feest", "Ziekteuren"],
          datasets: [
            {
              backgroundColor: ["#8ABB21BF", "#ffa600BF", "#2D95ECBF", "#F64D2ABF", "#c02323BF", "#7d3ac1BF", "#72a7a7BF"],
              data: [data.werk, data.pauze, data.reis, data.verlof, data.bijzonderverlof, data.feest, data.ziekteuren],
              borderRadius: 5,
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: (chart) => {
                  let tot = 0;
                  for(const v of chart[0].dataset.data ){ tot += v; }
                  return `${(chart[0].parsed / tot * 100).toFixed(1)}%`
                }
              }
            }
          }
        }
      };
      _charts.workHoursRatio = new Chart(document.getElementById('work_hours_ratio'), options);
    }
    function averageProjectTime(){
      if(_base.showSet?.average_project_time){ return; }

      let projects = []
      for(const uur of uren){
        let { totaaluren100, projectnummer, project} = uur;

        if(!project){ continue }

        let row = projects.find(x => x.projectnummer == projectnummer);
        if(!row){
          projects.push({
            project: project,
            projectnummer: projectnummer,
            uren: 0,
          });
          row = projects.find(x => x.projectnummer == projectnummer);
        }
        row.uren += Number(totaaluren100 || 0);

      }
      projects.sort((a, b) => { return (a.uren >= b.uren) ? -1 : 1; });

      let max = null
      let min = null;
      let hours = [];
      for(const row of projects){
        if(!max || row.uren > max.uren){ max = row; }
        if(!min || row.uren < min.uren){ min = row; }
        hours.push(row.uren);
      }

      let avg = Number(average(hours).toFixed(1));
      if(!max){ max = {project: {opdrachtgever: null, projectnaam: 'Onbekend', projectnr: 'Onbekend'}, uren: 0}  }
      if(!min){ min = {project: {opdrachtgever: null, projectnaam: 'Onbekend', projectnr: 'Onbekend'}, uren: 0}  }

      let colors = ["#00995f66", "#4ba74d66", "#91b73669", "#c6be2366", "#ecbd0066", "#feab0066", "#ff8c0066", "#fc611366"];
      let termijn = max.uren

      const perc = avg / termijn * 100;
      const iteration = 100 / colors.length;

      for(const i in colors){
        if(perc >= 100){
          colors[0] = colors[0].slice(0, -2)
          break;
        }

        const current = Number(i) * iteration;
        const next = (Number(i) + 1) * iteration;

        if(perc >= current && perc < next){
          colors[i] = colors[i].slice(0, -2);
        }
      }

      $('[data-average-project-time]').html(avg)
      $('[data-average-project-time-scale]').html(`${ min.uren.toFixed(1) } - ${max.uren.toFixed(1)}`);

      if(_charts.averageProjectTime){
        _charts.averageProjectTime.data.datasets[0].backgroundColor = colors;
        _charts.averageProjectTime.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          datasets: [
            {
              backgroundColor: colors,
              data: [1, 1, 1, 1, 1, 1, 1, 1],
              borderWidth: 6,
              borderRadius: 8
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            tooltip: {
              enabled: false
            }
          },
          rotation: 270,
          circumference: 180,
          cutout: 90,
        }
      };
      _charts.averageProjectTime = new Chart(document.getElementById('average_project_time'), options);
    }
    function topFiveProjects(){
      if(_base.showSet?.top_5_projects){ return; }

      $('[data-top-five-projects-list]').empty();
      $('[data-chart-explanation="top_5_projects"]').empty();

      let projects = []
      for(const uur of uren){
        let { totaaluren100, projectnummer, project} = uur;

        if(!project){ continue }

        let row = projects.find(x => x.projectnummer == projectnummer);
        if(!row){
          projects.push({
            project: project,
            projectnummer: projectnummer,
            uren: 0,
          });
          row = projects.find(x => x.projectnummer == projectnummer);
        }
        row.uren += Number(totaaluren100 || 0);

      }
      projects.sort((a, b) => { return (a.uren >= b.uren) ? -1 : 1; });

      let data = [];
      let labels = [];
      let colors = [];
      for(const row of projects.slice(0, 5)){
        const { project, uren } = row;
        const { projectnr, projectnaam, opdrachtgever } = project;

        data.push(uren);
        labels.push(projectnr);
        colors.push(stringToHexColor(projectnr));

        $('[data-top-five-projects-list]').append(`
          <div class="pb-1 d-flex mx--2 my-3 tippy" data-tippy-content="${ opdrachtgever ? `${opdrachtgever}: ` : '' } ${projectnaam || ''}">
            <div class="v-center">
              <div class="h-px-10 ratio-1-1 rounded-circle mx-2" style="background-color: ${stringToHexColor(projectnr)}" ></div>
            </div>
            <div class="flex-between w-100 border-bottom border-black mx-2" >
                <span class="nobr" >${projectnr}</span>
                <span class="nobr" >${uren.toFixed(1)}</span>
            </div>
          </div>
        `)
      }
      for(const row of projects){
        const { project, uren } = row;
        const { projectnr, projectnaam, opdrachtgever, id } = project;
        $(`[data-chart-explanation="top_5_projects"]`).append(`
          <tr>
            <td><a href="${url}/uren/projecturen/${id}" target="_blank">${ projectnr } | ${projectnaam}</a></td>
            <td>${ uren.toFixed(1) }</td>
          </tr>
        `)
      }
      tippyInit();

      if(_charts.topFiveProjects){
        _charts.topFiveProjects.data.datasets[0].data = data;
        _charts.topFiveProjects.data.datasets[0].backgroundColor = colors;
        _charts.topFiveProjects.data.labels = labels;
        _charts.topFiveProjects.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [
            {
              backgroundColor: colors,
              data: data,
              borderRadius: 5,
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: (chart) => {
                  let tot = 0;
                  for(const v of chart[0].dataset.data ){ tot += v; }
                  return `${(chart[0].parsed / tot * 100).toFixed(1)}%`
                }
              }
            },
          }
        }
      };
      _charts.topFiveProjects = new Chart(document.getElementById('top_5_projects'), options);
    }
    function averageWeekWorktime(){
      if(_base.showSet?.average_week_worktime){ return; }

      $('[data-average-week-worktime-list], [data-chart-explanation="average_week_worktime"]').empty();

      const data = {
        labels: [],
        hours: [],
        colors: []
      }
      let users = [];

      for(const uur of uren){
        const { user } = uur;

        if(!user){ continue }

        let row = users.find(x => x.user_id == user.id);
        if(!row){
          users.push({
            user: user,
            user_id: user.id,
            hours: 0,
          });
          row = users.find(x => x.user_id == user.id);
        }

        $('[data-average-week-worktime-sub]:checked').each(function(){
          const target = $(this).data('average-week-worktime-sub');

          if(!isNumber(uur[target])){ return true; }

          row.hours += Number(uur[target] || 0);
        })
      }

      users = users.filter( user => user.hours );
      users.sort((a, b) => {
        return (a.hours >= b.hours) ? -1 : 1;
      });

      let week = Math.ceil((dateDiffInDays(_base.start(), _base.end()) || 1) / 7);
      if(week < 1){ week = 1 }

      for(let row of users){
        const { user, hours } = row;
        const name = `${user.name} ${user.lastname}`

        data.labels.push(name);
        data.hours.push(Number((hours / week).toFixed(1)));
        data.colors.push(stringToHexColor(name));

        $('[data-average-week-worktime-list]').append(`
          <div class="pb-1 d-flex mx--2 my-3">
            <div class="v-center">
              <div class="h-px-10 ratio-1-1 rounded-circle mx-2" style="background-color: ${stringToHexColor(name)}" ></div>
            </div>
            <div class="flex-between w-100 border-bottom border-black mx-2" >
                <span class="nobr" >${name}</span>
                <span class="nobr" >${(row.hours / week).toFixed(1)}</span>
            </div>
          </div>
        `)
        $(`[data-chart-explanation="average_week_worktime"]`).append(`
          <tr>
            <td>${ name }</td>
            <td>${ hours.toFixed(1) }</td>
            <td>${ week }</td>
            <td>${ (hours / week).toFixed(1) }</td>
          </tr>
        `)
      }
      $(`[data-chart-explanation="average_week_worktime"]`).append(`
          <tr>
            <th>Totale gemiddelde</th>
            <th>${ sum(users.map(user => user.hours)).toFixed(1) }</th>
            <th>${ week }</th>
            <th>${ average(users.map(user => user.hours / week)).toFixed(1) }</th>
          </tr>
        `)


      if(_charts.averageWeekWorktime){
        _charts.averageWeekWorktime.data.labels = data.labels;
        _charts.averageWeekWorktime.data.datasets[0].data = data.hours;
        _charts.averageWeekWorktime.data.datasets[0].backgroundColor = data.colors;
        _charts.averageWeekWorktime.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          labels: data.labels,
          datasets: [
            {
              backgroundColor: data.colors,
              data: data.hours,
              borderRadius: 5,
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: (chart) => {
                  let tot = 0;
                  for(const v of chart[0].dataset.data ){ tot += v; }
                  return `${(chart[0].parsed / tot * 100).toFixed(1)}%`
                }
              }
            }
          }
        }
      };
      _charts.averageWeekWorktime = new Chart(document.getElementById('average_week_worktime'), options);
    }

    function hoursOtherBv(){
      if(!_base.showSet?.uren_op_andere_bv){return; }
      $('[data-hours-other-bv-list], [data-chart-explanation="uren_op_andere_bv"]').empty();

      let users = [];

      for(const uur of uren){
        const { user, project} = uur;

        if(!user || !project || (user.bv_id == project.bv)){ continue }

        let row = users.find(x => x.user_id == user.id);
        if(!row){
          users.push({
            user: user,
            user_id: user.id,
            hours: 0,
            project: project,
          });
          row = users.find(x => x.user_id == user.id);
        }

        row.hours += Number(uur.totaaluren100 || 0);
      }

      users = users.filter( user => user.hours );
      users.sort((a, b) => {
        return (a.hours >= b.hours) ? -1 : 1;
      });

      let data = [];
      let labels = [];
      let colors = [];
      for(const row of users){
        const { user, hours } = row;
        const name = `${user.name} ${user.lastname}`

        data.push(hours);
        labels.push(name);
        colors.push(stringToHexColor(name));

        $('[data-hours-other-bv-list]').append(`
          <div class="pb-1 d-flex mx--2 my-3">
            <div class="v-center">
              <div class="h-px-10 ratio-1-1 rounded-circle mx-2" style="background-color: ${stringToHexColor(name)}" ></div>
            </div>
            <div class="flex-between w-100 border-bottom border-black mx-2" >
                <span class="nobr" >${name}</span>
                <span class="nobr" >${hours.toFixed(1)}</span>
            </div>
          </div>
        `)
        let userbv = bvs.find(x => x.id == user.bv_id);
        let projectbv = bvs.find(x => x.id == row.project.bv);
        $(`[data-chart-explanation="uren_op_andere_bv"]`).append(`
          <tr>
            <td>${ name }</td>
            <td>${ userbv ? userbv.name : 'Onbekend' }</td>
            <td>${ hours.toFixed(1) }</td>
            <td>${ row.project.projectnaam }</td>
            <td>${ projectbv ? projectbv.name : 'Onbekend' }</td>
          </tr>
        `)
      }

      if(_charts.hoursOtherBv){
        _charts.hoursOtherBv.data.datasets[0].data = data;
        _charts.hoursOtherBv.data.datasets[0].backgroundColor = colors;
        _charts.hoursOtherBv.data.labels = labels;
        _charts.hoursOtherBv.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [
            {
              backgroundColor: colors,
              data: data,
              borderRadius: 5,
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: (chart) => {
                  let tot = 0;
                  for(const v of chart[0].dataset.data ){ tot += v; }
                  return `${(chart[0].parsed / tot * 100).toFixed(1)}%`
                }
              }
            }
          }
        }
      };
      _charts.hoursOtherBv = new Chart(document.getElementById('uren_op_andere_bv'), options);
    }

    function urenZonderProject(){
      if(!_base.showSet?.uren_zonder_project){ return; }

      $('[data-uren-zonder-project-list]').empty();

      const data = {
        labels: [],
        hours: [],
        colors: []
      }
      let users = [];

      for(const uur of uren){
        const { user, projectnummer, totaaluren100} = uur;
        if(!user || (projectnummer != '-' && totaaluren100 > 0)){ continue }

        let row = users.find(x => x.user_id == user.id);
        if(!row){
          users.push({
            user: user,
            user_id: user.id,
            hours: 0,
          });
          row = users.find(x => x.user_id == user.id);
        }

        row.hours += Number(uur['totaaluren100'] || 0);
      }

      users = users.filter( user => user.hours );
      users.sort((a, b) => {
        return (a.hours >= b.hours) ? -1 : 1;
      });

      for(let row of users){
        const { user, hours } = row;
        const name = `${user.name} ${user.lastname}`

        data.labels.push(name);
        data.hours.push(Number((hours).toFixed(1)));
        data.colors.push(stringToHexColor(name));

        $('[data-uren-zonder-project-list]').append(`
          <div class="pb-1 d-flex mx--2 my-3">
            <div class="v-center">
              <div class="h-px-10 ratio-1-1 rounded-circle mx-2" style="background-color: ${stringToHexColor(name)}" ></div>
            </div>
            <div class="flex-between w-100 border-bottom border-black mx-2" >
                <span class="nobr" >${name}</span>
                <span class="nobr" >${(row.hours).toFixed(1)}</span>
            </div>
          </div>
        `)
      }

      if(_charts.urenZonderProject){
        _charts.urenZonderProject.data.labels = data.labels;
        _charts.urenZonderProject.data.datasets[0].data = data.hours;
        _charts.urenZonderProject.data.datasets[0].backgroundColor = data.colors;
        _charts.urenZonderProject.update();
        return;
      }

      let options =  {
        type: 'doughnut',
        data: {
          labels: data.labels,
          datasets: [
            {
              backgroundColor: data.colors,
              data: data.hours,
              borderRadius: 5,
            },
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: false,
            },
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: (chart) => {
                  let tot = 0;
                  for(const v of chart[0].dataset.data ){ tot += v; }
                  return `${(chart[0].parsed / tot * 100).toFixed(1)}%`
                }
              }
            }
          }
        }
      };
      _charts.urenZonderProject = new Chart($('#uren_zonder_project'), options);
    }

    function verlofPerMedewerkerChart() {
      if (!_base.showSet?.verlof_per_medewerker) return;

      let sortedVerlof = [...verlof];
      if (verlofSortType === 'meest_gebruikt') {
        sortedVerlof.sort((a, b) => Number(b.gebruiktverlofsaldo) - Number(a.gebruiktverlofsaldo));
      } else if (verlofSortType === 'alfabetisch') {
        sortedVerlof.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      }

      const totalItems = sortedVerlof.length;
      const totalPages = Math.ceil(totalItems / verlofPageSize);
      if (verlofPage >= totalPages) verlofPage = totalPages - 1;
      if (verlofPage < 0) verlofPage = 0;

      const start = verlofPage * verlofPageSize;
      const end = start + verlofPageSize;

      const medewerkers = sortedVerlof.slice(start, end).map(v => v.name);
      const gebruiktVerlof = sortedVerlof.slice(start, end).map(v => Number(v.gebruiktverlofsaldo));
      const totaalVerlof = sortedVerlof.slice(start, end).map(v => Number(v.verlofsaldo));

      const ctx = document.getElementById('verlof_medewerkers');

      if (_charts.verlofPerMedewerker) {
        _charts.verlofPerMedewerker.destroy();
      }

      const options = {
        type: 'bar',
        data: {
          labels: medewerkers,
          datasets: [
            {
              label: 'Gebruikt',
              data: gebruiktVerlof,
              backgroundColor: '#2D95ECBF',
              barThickness: 15,
              order: 2
            },
            {
              label: 'Totaal',
              data: totaalVerlof,
              backgroundColor: '#2D95EC3F',
              barThickness: 15,
              order: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          indexAxis: 'y',
          plugins: {
            legend: { display: false },
            title: { display: false }
          },
          scales: {
            x: {
              stacked: false,
            },
            y: {
              stacked: false,
            }
          }
        }
      };

      _charts.verlofPerMedewerker = new Chart(ctx, options);

      // Update page info (optional)
      if (document.getElementById('verlof_page_info')) {
        document.getElementById('verlof_page_info').textContent = `Pagina ${verlofPage + 1} van ${totalPages}`;
      }
      // Disable/enable buttons
      if (document.getElementById('verlof_prev_page')) {
        document.getElementById('verlof_prev_page').disabled = verlofPage === 0;
      }
      if (document.getElementById('verlof_next_page')) {
        document.getElementById('verlof_next_page').disabled = verlofPage >= totalPages - 1;
      }
    }

    $(document).on('click', '#verlof_prev_page', function() {
      verlofPage--;
      verlofPerMedewerkerChart();
    });
    $(document).on('click', '#verlof_next_page', function() {
      verlofPage++;
      verlofPerMedewerkerChart();
    });

    $(document).on('change', '#verlof_sort_select', function() {
      verlofSortType = $(this).val();
      verlofPage = 0;
      verlofPerMedewerkerChart();
    });
  </script>
@endsection