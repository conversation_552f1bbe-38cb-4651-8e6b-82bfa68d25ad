@extends('layouts.app')

@section('title', 'Invoeren')

@section('content')

  <div class="card">

    <form method="POST" onsubmit="return onPost()" class="card-body">
    @csrf
      <div class="float-right">
        <a class="btn btn-danger btn-sm text-white" onclick="removeProject()">Blok verwijderen</a>
        <a class="btn btn-success btn-sm text-white" onclick="addProject()">Blok toevoegen</a>
      </div>

        <h4>{{ \Carbon\Carbon::parse($date)->format('d-m-Y') }} {{ $user->name . ' ' . $user->lastname }}</h4>

      <div class="row append-loader" id="projecten">

        @php $i = 0; @endphp
        @foreach($projecten as $project)
        @php $i++; @endphp

        <div class="col-md-6 project-container my-2" id="project{{$i}}">

          <input type="hidden" name="projecten[{{$i}}][type]" value="insert" />
          <input type="hidden" value="{{ isset($project->id) ? $project->id : '' }}" name="projecten[{{$i}}][pid]" class="form-control">

          @if(hasAccessTo('urenregistratie_projectnummer'))
            <div class="select_search-container mb-4">
              <label>Projectnummer</label>
              <input type="hidden" name="projecten[{{$i}}][projectnummer]" class="select_search-hidden-input projectnummers" data-placeholder="Projectnummer" data-required="required">
              <div class="select_search-values" >
                <div class="select_search-box" >
                  <span @if((isset($project->projectnummer) && $project->projectnummer == '-') || (getSettingValue('standaard_project_algemeen_invoer') == '-')) data-selected="true" @endif class="select_search-value" data-value="-" data-name="Geen project" >Geen project</span>
                  @foreach($projectnummers as $p)
                    @php
                      $name =   $p->projectnr." ".$p->opdrachtgever." ".$p->woonplaats." ".$p->projectnaam
                    @endphp
                    <span class="select_search-value" data-value="{{ $p->projectnr }}" data-name="{{$name}}" @if(isset($project->projectnummer) && $project->projectnummer == $p->projectnr) data-selected="true" @elseif(getSettingValue('standaard_project_algemeen_invoer') == $p->projectnr) data-selected="true" @endif >{{$name}}</span>
                  @endforeach
                  @if(getSettingValue('urenregistratie_invoeren_offertenummers') == 'aan')
                    @foreach($offertenummers as $o)
                      @php
                        $explodedoffertenummer = explode('.', $o->offertenummer);
                        if(!isset($offertenummer) || $explodedoffertenummer[0] != $offertenummer){
                          $new = true;
                        }else{
                          $new = false;
                        }
                        $offertenummer = $explodedoffertenummer[0];
                        $name =   $offertenummer." ".$o->klant->naam." ".$o->klant->plaats." ".$o->naam
                      @endphp
                      @if ($new)
                        <span class="select_search-value" data-value="{{ $offertenummer }}" data-name="{{$name}}" @if(isset($project->projectnummer) && $project->projectnummer == $offertenummer) data-selected="true" @endif >{{$name}}</span>
                      @endif
                    @endforeach
                  @endif
                </div>
              </div>
            </div>
          @endif

          @if(getSettingCheckbox('urenregistratie_taken'))
            <div class="mb-4">
              <label>Project taken</label>
              <div class="select_multiple-container projecttaken-container" data-name="projecten[{{$i}}][project_taken][]" data-placeholder="Taken"></div>
            </div>
          @endif

          @if(getSettingValue('urenregistratie_invoeren_uursoorten') == 'aan' || getSettingValue('urenregistratie_invoeren_uursoorten') == 'bv')
            <div class="mb-4 select_search-container">
              <label>Uursoort</label>
              <input type="hidden" name="projecten[{{$i}}][uursoort]" class="select_search-hidden-input" data-placeholder="Uursoort">
              <div class="select_search-values" >
                <div class="select_search-box" >
                  @foreach($uursoorten as $row)
                    <span class="select_search-value" data-value="{{$row->id}}" data-name="{{$row->name}}">{{$row->code}}. {{$row->name}}</span>
                  @endforeach
                </div>
              </div>
            </div>
          @endif

          @if(!$urengetal)
            @if(hasAccessTo('urenregistratie_begintijd'))
            <div class="mb-4">
                <label>Begintijd</label> <br>
                <input data-begintijd type="time" value="{{ isset($project->begintijd) ? \Carbon\Carbon::parse($project->begintijd)->format('H:i') : '' }}" name="projecten[{{$i}}][begintijd]" class="form-control date" placeholder="Begintijd" required>
            </div>
            @endif

          @if(hasAccessTo('urenregistratie_eindtijd'))
          <div class="mb-4">
              <label>Eindtijd</label> <br>
              <input data-eindtijd type="time" value="{{ isset($project->eindtijd) ? \Carbon\Carbon::parse($project->eindtijd)->format('H:i') : '' }}" name="projecten[{{$i}}][eindtijd]" class="form-control" placeholder="Eindtijd" required>
          </div>
        @endif

          @else
            <div class="mb-4">
                <label>Totaal aantal uren</label> <br>
                <input type="number" pattern="[0-9]+([\.,][0-9]+)?" step="0.01" title="Ingevoerde waarde niet correct" @if(isset($project->standaarduren)) value="{{ ($project->standaarduren+($project->pauze ?? 0)) }}"@endif @if(isset($project->gewerkte_uren))value="{{ $project->gewerkte_uren }}"@endif name="projecten[{{$i}}][gewerkteuren]" class="form-control" placeholder="Totaal aantal uren" required>
            </div>
          @endif

          @if(hasAccessTo('urenregistratie_pauzetijd'))
          <div class="mb-4">
            <label>Pauze</label> <br>
            <select name="projecten[{{$i}}][pauze]" class="form-select pauze-select" id="">
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 0) selected @endif value="0">Geen pauze</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 0.25) selected @endif value=".25">15 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 0.5) selected @endif value=".5">30 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 0.75) selected @endif value=".75">45 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 1.0) selected @endif value="1">1 uur</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 1.25) selected @endif value="1.25">1 uur 15 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 1.5) selected @endif value="1.5">1 uur 30 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 1.75) selected @endif value="1.75">1 uur 45 min</option>
              <option @if((floatval($project->pauze ?? 0) ?? 0) == 2.0) selected @endif value="2">2 uur</option>
            </select>
          </div>
          @endif

          @if(getSettingValue('urenregistratie_invoeren_machines') == 'aan')
            <div class="bg-inverse-secondary rounded border p-2 mb-4" >
              <div class="flex-between">
                <span>Machines</span>
                <a class="btn btn-inverse-primary text-white" onclick="addMachine({{$i}})">@icon_plus</a>
              </div>
              <div class="machines"></div>
            </div>
          @endif

          @if(hasAccessTo('urenregistratie_km'))
          <div class="mb-4">
            <label>Kilometers</label> <br>
            <input type="number" step="1" value="{{ isset($project->kilometers) ? $project->kilometers : '' }}" name="projecten[{{ $i }}][kilometers]" class="form-control" placeholder="Kilometers">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_ladenlossen'))
          <div class="mb-4">
            <label>laden/lossen begintijd</label> <br>
            <input type="time" value="{{ isset($project->ladenlossen_begintijd) ? $project->ladenlossen_begintijd : '' }}" name="projecten[{{$i}}][ladenlossen_begintijd]" class="form-control" placeholder="Laden/lossen begintijd">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_ladenlossen'))
          <div class="mb-4">
            <label>Laden/lossen eintijd</label> <br>
            <input type="time" value="{{ isset($project->ladenlossen_eindtijd) ? $project->ladenlossen_eindtijd : '' }}" name="projecten[{{$i}}][ladenlossen_eindtijd]" class="form-control" placeholder="Laden/lossen eindtijd">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_ladenlossen_middag'))
          <div class="mb-4">
            <label>Laden/lossen middag begintijd</label> <br>
            <input type="time" value="{{ isset($project->ladenlossen_middag_begintijd) ? $project->ladenlossen_middag_begintijd : '' }}" name="projecten[{{$i}}][ladenlossen_middag_begintijd]" class="form-control" placeholder="Laden/lossen middag begintijd">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_ladenlossen_middag'))
          <div class="mb-4">
            <label>Laden/lossen middag eindtijd</label> <br>
            <input type="time" value="{{ isset($project->ladenlossen_middag_eindtijd) ? $project->ladenlossen_middag_eindtijd : '' }}" name="projecten[{{$i}}][ladenlossen_middag_eindtijd]" class="form-control" placeholder="Laden/lossen middag eindtijd">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_reisuren'))
          <div class="mb-4">
            <label>Reistijd</label> <br>
            <input type="text" value="{{ isset($project->reisuren) ? $project->reisuren : '' }}" name="projecten[{{$i}}][reisuren]" class="form-control" placeholder="Reistijd">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_heenreis_woonwerk'))
          <div class="mb-4">
            <label>Heenreis woon/werk</label> <br>
            <input type="text" value="{{ isset($project->heenreis_woonwerk) ? $project->heenreis_woonwerk : '' }}" name="projecten[{{$i}}][heenreis_woonwerk]" class="form-control" placeholder="Heenreis woon/werk">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_heenreis_woonwerk'))
          <div class="mb-4">
            <label>Terugreis woon/werk</label> <br>
            <input type="text" value="{{ isset($project->terugreis_woonwerk) ? $project->terugreis_woonwerk : '' }}" name="projecten[{{$i}}][terugreis_woonwerk]" class="form-control" placeholder="Terugreis woon/werk">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_heenreis_huiswerk'))
          <div class="mb-4">
            <label>Heenreis woon/project</label> <br>
            <input type="text" value="{{ isset($project->heenreis_huisproject) ? $project->heenreis_huisproject : '' }}" name="projecten[{{$i}}][heenreis_huisproject]" class="form-control" placeholder="Heenreis woon/project">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_heenreis_huiswerk'))
          <div class="mb-4">
            <label>Terugreis woon/project</label> <br>
            <input type="text" value="{{ isset($project->terugreis_huisproject) ? $project->terugreis_huisproject : '' }}" name="projecten[{{$i}}][terugreis_huisproject]" class="form-control" placeholder="Terugreis woon/project">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_bestuurder'))
          <div class="mb-4">
            <label>Bestuurder</label> <br>
            <select name="projecten[{{$i}}][bestuurder]" class="form-control">
              <option @if(isset($project->bestuurder) && $project->bestuurder == 1) selected @endif value="1">Ja</option>
              <option @if(isset($project->bestuurder) && $project->bestuurder == 0) selected @endif value="0">Nee</option>
              <option @if(!isset($project->bestuurder))selected @endif disabled value="null">Maak een keuze</option>
            </select>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_bijrijder'))
          <div class="mb-4">
            <label>Bijrijder</label> <br>
            <input type="text" value="{{ isset($project->bijrijder) ? $project->bijrijder : '' }}" name="projecten[{{$i}}][bijrijder]" class="form-control" placeholder="Bijrijder">
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_bestuurder'))
          <div class="mb-4">
            <label>Kenteken</label> <br>
            <select name="projecten[{{$i}}][kenteken]" class="form-control">
              @foreach($kentekens as $kenteken)<option @if(isset($project->kenteken_id) && $project->kenteken_id == $kenteken->id)selected @endif value="{{ $kenteken->id }}">{{ $kenteken->kenteken }}</option>@endforeach
              <option @if(!isset($project->kenteken_id))selected @endif value="">Maak een keuze</option>
            </select>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_naca'))
          <div class="mb-4">
              <label>Naca</label> <br>
            <select name="projecten[{{$i}}][naca]" class="form-control" required>
              @foreach($naca as $n)
              <option @if(isset($project->naca_id) && $project->naca_id == $n->id)selected @endif value="{{ $n->id }}">{{ $n->code }} ({{ $n->omschrijving}})</option>
              @endforeach
              <option @if(!isset($project->naca_id))selected @endif value="default">Maak een keuze</option>
            </select>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_tijdvoortijd'))
          <div class="mb-4">
            <label>
            <input type="checkbox" @if(isset($project->tijdvoortijd) && $project->tijdvoortijd > 0)checked @endif name="projecten[{{$i}}][tijdvoortijd]"> Tijd voor tijd</label>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_facturabel'))
          <div class="mb-4">
            <label>Facturabel</label> <br>
            <select name="projecten[{{$i}}][facturabel]" class="form-control">
              <option selected value=""></option>
              @foreach($facturabelopties as $optie)<option @if($project->facturabel_id ?? null == $optie->id) selected @endif value="{{ $optie->id }}">{{ $optie->optie }}</option>@endforeach
            </select>
          </div>
         @endif

          @if(hasAccessTo('urenregistratie_overnachting'))
          <div class="mb-4">
            <label>Overnachting</label>
            <select name="projecten[{{$i}}][overnachting]" class="form-control" placeholder="Maak een keuze">
              @if(isset($project->overnachting) && $project->overnachting != null) <option selected value="{{ $project->overnachting }}">{{ $project->overnachting }}</option>@endif
              <option @if(!isset($project->overnachting))selected @endif></option>
              <option value="Ja">Ja</option>
              <option value="Ja enkele reis">Ja, met heen/terug reis</option>
            </select>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_verlof'))
          <div class="mb-4">
              <label>Verlof</label>
              <select name="projecten[{{$i}}][verlof]" class="form-control" placeholder="Maak een keuze">
                @if(isset($project->verlof) && $project->verlof > 0 || isset($project->ziekteuren) && $project->ziekteuren > 0 || isset($project->feesturen) && $project->feesturen> 0 || isset($project->bijzonderverlof) && $project->bijzonderverlof> 0 || isset($project->roostervrij) && $project->roostervrij > 0) {{ $verloftotaal = $project->verlof + $project->ziekteuren + $project->feesturen + $project->bijzonderverlof + $project->roostervrij}} <option selected value="{{ $verloftotaal }}">{{ $verloftotaal }}</option>@endif
                <option @if(!isset($project->verlof))selected @endif></option>
                <option value="8">8 (hele dag)</option>
                <option value="7.75">7,75</option>
                <option value="7.5">7,5</option>
                <option value="7.25">7,25</option>
                <option value="7">7</option>
                <option value="6.75">6,75</option>
                <option value="6.5">6,5</option>
                <option value="6.25">6,25</option>
                <option value="6">6</option>
                <option value="5.75">5,75</option>
                <option value="5.5">5,5</option>
                <option value="5.25">5,25</option>
                <option value="5">5</option>
                <option value="4.75">4,75</option>
                <option value="4.5">4,5</option>
                <option value="4.25">4,25</option>
                <option value="4">4</option>
                <option value="3.75">3,75</option>
                <option value="3.5">3,5</option>
                <option value="3.25">3,25</option>
                <option value="3">3</option>
                <option value="2.75">2,75</option>
                <option value="2.5">2,5</option>
                <option value="2.25">2,25</option>
                <option value="2">2</option>
                <option value="1.75">1,75</option>
                <option value="1.5">1,5</option>
                <option value="1.25">1,25</option>
                <option value="1">1</option>
                <option value="0.75">0,75</option>
                <option value="0.5">0,5</option>
                <option value="0.25">0,25</option>
              </select>
          </div>

          <div class="mb-4">
            <label>Verlofreden</label> <br>
            <select name="projecten[{{$i}}][verlofreden]" class="form-control">
              <option selected value=""></option>
              @foreach(getVerlofredenen() as $reden)
                @if($reden->active && (($reden->role_id == 0 || Auth::user()->role_id == $reden->role_id) || Auth::user()->hasPermissionTo('Verlof afhandelen')))
                  <option @if(isset($project->verlofreden_id) && $project->verlofreden_id == $reden->id)selected @endif value="{{ $reden->id }}">{{ $reden->reden }}</option>
                @endif
              @endforeach
            </select>
          </div>
          @endif

          @if(hasAccessTo('urenregistratie_opmerkingen'))
          <div class="mb-4">
            <label>Opmerkingen</label> <br>
            <textarea type="text" name="projecten[{{$i}}][opmerkingen]" class="form-control" placeholder="Opmerkingen">{{ isset($project->opmerkingen) ? $project->opmerkingen : '' }}</textarea>
          </div>
          @endif
        </div>
      @endforeach
      </div>

        <div class="d-flex justify-content-between align-items-center" >
          <div data-submit-btns >
            <input type="hidden" name="prevurl" value="{{url()->previous() ?? '/'}}">
            <button class="btn btn-primary" type="submit" name="action" value="permanent">Opslaan</button>
            <button class="btn btn-primary" type="submit" name="action" value="tijdelijk">Tijdelijk Opslaan</button>
          </div>
          <div>
            <a class="btn btn-danger btn-sm text-white" onclick="removeProject()">Blok verwijderen</a>
            <a class="btn btn-success btn-sm text-white" onclick="addProject()">Blok toevoegen</a>
          </div>
        </div>
    </form>
  </div>

@endsection
@section('script')

<script>
  var aantal = @json($i);
  var projectnummersgezet = @json(!empty($projectnummers));
  var projectnummers = resetIndex(@json($projectnummers));
  var urenregistratie_invoeren_offertenummers = @json(getSettingValue('urenregistratie_invoeren_offertenummers'));
  var offertenummers = @json($offertenummers);
  var urenregistratie_projectnummer = @json(hasAccessTo('urenregistratie_projectnummer'));
  var urengetal = @json($urengetal);
  var urenregistratie_begintijd = @json(hasAccessTo('urenregistratie_begintijd'));
  var urenregistratie_eindtijd = @json(hasAccessTo('urenregistratie_eindtijd'));
  var urenregistratie_pauzetijd = @json(hasAccessTo('urenregistratie_pauzetijd'));
  var urenregistratie_kilometers = @json(hasAccessTo('urenregistratie_km'));
  var urenregistratie_ladenlossen = @json(hasAccessTo('urenregistratie_ladenlossen'));
  var urenregistratie_ladenlossen_middag = @json(hasAccessTo('urenregistratie_ladenlossen_middag'));
  var urenregistratie_reisuren = @json(hasAccessTo('urenregistratie_reisuren'));
  var urenregistratie_heenreis_woonwerk = @json(hasAccessTo('urenregistratie_heenreis_woonwerk'));
  var urenregistratie_heenreis_huiswerk = @json(hasAccessTo('urenregistratie_heenreis_huiswerk'));
  var urenregistratie_bestuurder = @json(hasAccessTo('urenregistratie_bestuurder'));
  var urenregistratie_bijrijder = @json(hasAccessTo('urenregistratie_bijrijder'));
  var urenregistratie_naca = @json(hasAccessTo('urenregistratie_naca'));
  var nacagezet = @json(!empty($naca));
  var naca = @json($naca);
  var urenregistratie_tijdvoortijd = @json(hasAccessTo('urenregistratie_tijdvoortijd'));
  var urenregistratie_overnachting = @json(hasAccessTo('urenregistratie_overnachting'));
  var urenregistratie_facturabel = @json(hasAccessTo('urenregistratie_facturabel'));
  var urenregistratie_verlof = @json(hasAccessTo('urenregistratie_verlof'));
  var redenengezet = @json(!empty($redenen));
  var redenen = @json($redenen);
  var facturabelopties = @json($facturabelopties);
  var urenregistratie_opmerkingen = @json(hasAccessTo('urenregistratie_opmerkingen'));
  var urenregistratie_taken = @json(getSettingCheckbox('urenregistratie_taken'));
  var default_project = @json(getSettingValue('standaard_project_algemeen_invoer'));

  const urenMachines = @json(getSettingValue('urenregistratie_invoeren_machines'));
  const urenUursoorten = @json(getSettingValue('urenregistratie_invoeren_uursoorten'));
  const planningPrefill = @json(getSettingValue('urenregistratie_invoeren_planning'));
  const planningmachinessetting = @json(getSettingValue('planning_machines'));
  const projectOvernemen = @json(getSettingValue('urenregistratie_invoeren_project_overnemen'));
  const machines = @json($machines);
  const uursoorten = @json($uursoorten);
  const planning = @json($planning);

  const werkbonPrefill = @json(getSettingValue('urenregistratie_invoeren_werkbonnen'));
  const werkbonnen = @json($werkbonnen);

  const projecten = @json($projecten);

  $(document).ready(function(){
    planningInit();
    werkbonInit();
    setDefaultPauzes();
  });

  $(document).on('focusout', '.date', function(){
    const scrlId = findContainer('project-container', this).attr('id');
    const projectContainers = $('.project-container');
    let swapped = true;
    if (projectContainers.length <= 1) {swapped = false;}
    while (swapped) {
      swapped = false;
      for (let x = 0; x < projectContainers.length - 1; x++) {
        const thisT = $(projectContainers[x]).find('.date').val();
        const nextT = $(projectContainers[x + 1]).find('.date').val();
        if (isTimeBigger(thisT, nextT)) {
          const temp = projectContainers[x];
          projectContainers[x] = projectContainers[x + 1];
          projectContainers[x + 1] = temp;
          swapped = true;
        }
        if (thisT == nextT){swapped = false;}
      }
    }

    // Reorder the containers in the DOM
    const containerWrapper = $('#projecten'); // Change this to your actual wrapper element
    projectContainers.detach().appendTo(containerWrapper);
    setDefaultPauzes();
    scrl(`#${scrlId}`);
  });
  $(document).on('change', '.projectnummers', function(){
    const container = findContainer('project-container', this);
    const taken_container = container.find('.projecttaken-container');
    const box = taken_container.find('.select_multiple-box');
    const name = taken_container.data('name')

    box.find('.select_multiple-value').remove();
    const project = projectnummers.find(row => row.projectnr == this.value);
    if(project?.taken?.length){
      for(const taak of project.taken){
        box.append(`
          <label class="select_multiple-value">
            <div class="select_multiple-value-indicator text-success v-center">@icon_check</div>
            ${taak.name}
            <input type="checkbox" class="d-none" name="${name}" data-name="${taak.name}" value="${taak.id}">
          </label>
        `)
      }
    }
    refreshSelectMultipleIndicators();
  });
  $(document).on('change', '[data-eindtijd]', function(){
    if(this.value == '00:00'){ this.value = '23:59'; }
  })

  function onPost(){
    let post = true;

    $('.projectnummers').each(function(){
      if(!this.value){
        notification('Projectnummer is verplicht', 'warning');
        post = false;
      }
    });


    if(post){
      $('[data-submit-btns]').addClass('d-none').append(`@spinner_success`);
    }

    return post;
  }

  function machinesSelect(i, machineid, plan){
    let options = '';
    for(var machine of machines){
      if(machine.id == machineid){var selected = 'data-selected="selected"';}else{var selected = '';}
      options += `<span class="select_search-value" data-value="${machine.id}" ${selected} data-name="${machine.name}">
                  <div class="d-flex">
                    <img width="50" height="50" class="mr-2" src="${url}/client/public/img/machines/${machine.icon}.png">
                    <div>
                      <div>${machine.name}</div>
                      <div class="text-muted">${machine.group.name}</div>
                    </div>
                  </div>
                </span>`
    }
    if(plan){var begintijd = plan.begin; var eindtijd = plan.eind;}else{var begintijd = ""; var eindtijd = "";}
    if(machineid){
      return `<div id="${randomString()}">
              <div class="my-2 d-flex">
              <div class="select_search-container w-100">
                <input type="hidden" id="select${lastString()}" onchange="showTijden(${i}, '${lastString()}', '${begintijd}', '${eindtijd}')" name="projecten[${i}][machines][]" class="select_search-hidden-input" data-placeholder="Selecteer een machine">
                <div class="select_search-values" >
                  <div class="select_search-box" >${options}</div>
                </div>
              </div>
              <a class="btn btn-inverse-danger ml-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
            </div>
            <div id="tijden${lastString()}" class="mt-2 mb-4 flex-between">
              <label>Van</label>
              <input type="time" name="projecten[${i}][machinetijden][${machineid}][begintijd]" placeholder="Begintijd" value="${begintijd}" class="form-control mx-1">
              <label>Tot</label>
              <input type="time" name="projecten[${i}][machinetijden][${machineid}][eindtijd]" placeholder="Eindtijd" value="${eindtijd}" class="form-control mx-1">
            </div>
            </div>`
    }else{
      return `<div id="${randomString()}">
              <div class="my-2 d-flex">
              <div class="select_search-container w-100">
                <input type="hidden" id="select${lastString()}" onchange="showTijden(${i}, '${lastString()}', '${begintijd}', '${eindtijd}')" name="projecten[${i}][machines][]" class="select_search-hidden-input" data-placeholder="Selecteer een machine">
                <div class="select_search-values" >
                  <div class="select_search-box" >${options}</div>
                </div>
              </div>
              <a class="btn btn-inverse-danger ml-1" onclick="deleteDiv('#${lastString()}')" >@icon_close</a>
            </div>
            <div id="tijden${lastString()}" class="mt-2 mb-4 flex-between">

            </div>
            </div>`
    }
  }

  function showTijden(i, divId, begintijd, eindtijd){
    var machineid = $('#select'+divId).val();
    if(planningmachinessetting == 'pp'){
      $('#tijden'+divId).empty();
      $('#tijden'+divId).append(
        `<label>Van</label>
        <input type="time" name="projecten[${i}][machinetijden][${machineid}][begintijd]" placeholder="Begintijd" value="${begintijd}" class="form-control mx-1">
        <label>Tot</label>
        <input type="time" name="projecten[${i}][machinetijden][${machineid}][eindtijd]" placeholder="Eindtijd" value="${eindtijd}" class="form-control mx-1">`
      );
    }
  }

  function addProject(projectnummer = null){
    aantal = aantal + 1;

    $("#projecten").append('<div class="col-md-6 project-container my-2" id="project'+aantal+'"></div>');
    const container = $("#project"+aantal);

    container.append('<input type="hidden" name="projecten['+aantal+'][type]" value="insert" />');
    container.append('<input type="hidden" value="" name="projecten['+aantal+'][pid]" class="form-control">');


    if(urenregistratie_projectnummer){
      container.append(
        `<div class="select_search-container mb-4" data-project-container="${aantal}" >
          <label data-projectnummer-label="${aantal}" >Projectnummer</label>
          <input type="hidden" name="projecten[${aantal}][projectnummer]" class="select_search-hidden-input projectnummers" data-placeholder="Projectnummer" data-required="required">
          <div class="select_search-values" >
            <div class="select_search-box" id="projectnummers${aantal}">
              <span class="select_search-value" data-value="-" data-name="Geen project" ${default_project ? 'data-selected="true"' : ''}>Geen project</span>
            </div>
          </div>
        </div>`
      );

      let preSelected = false;
      for(const project of projectnummers){
        let selected = '';
        if(projectnummer && project.projectnr == projectnummer){
          selected = 'data-selected="selected"';
          preSelected = true;
        }

        if(projectOvernemen == 'aan' && $(`[name='projecten[${aantal-1}][projectnummer]']`).val() == project.projectnr){
          selected = 'data-selected="selected"';
          preSelected = true;
        }

        if(!preSelected && project.projectnr == default_project){
          selected = 'data-selected="selected"';
          preSelected = true;
        }else if(projectOvernemen != 'aan' && project.projectnr == default_project){
          selected = 'data-selected="selected"';
          preSelected = true;
        }

        const name = (project.projectnr || '')+" "+(project.opdrachtgever || '')+" "+(project.woonplaats || '')+" "+(project.projectnaam || '');
        $('#projectnummers'+aantal).append(
          `<span class="select_search-value" ${selected} data-value="${project.projectnr || ''}" data-name="${name}" >${name}</span>`
        )
      }

      if(projectnummer && !preSelected){
        $('#projectnummers'+aantal).append(
          `<span class="select_search-value" data-selected="selected" data-value="${projectnummer}" data-name="${projectnummer}" >${projectnummer}</span>`
        )
      }

      if(urenregistratie_invoeren_offertenummers){
        let offertenummer = '';
        for(const offerte of offertenummers){
          let selected = '';
          let splitted = offerte.offertenummer.split('.')
          if(offertenummer != splitted[0]){
            offertenummer = splitted[0];

            const name = (offertenummer || '')+" "+(offerte.klant.naam || '')+" "+(offerte.klant.plaats || '')+" "+(offerte.naam || '');
            $('#projectnummers'+aantal).append(
              `<span class="select_search-value" data-value="${offertenummer || ''}" data-name="${name}" >${name}</span>`
            )
          }
        }
      }
    }

    if(urenregistratie_taken){
      container.append(`
        <div class="mb-4">
          <label>Project taken</label>
          <div class="select_multiple-container projecttaken-container" data-name="projecten[${aantal}][project_taken][]" data-placeholder="Taken"></div>
        </div>
      `);
    }

    if(urenUursoorten === 'aan' || urenUursoorten === 'bv'){
      let options = ''
      for(const index in uursoorten){
        options += `<span class="select_search-value" data-value="${uursoorten[index].id}" data-name="${uursoorten[index].name}">${uursoorten[index].code}. ${uursoorten[index].name}</span>`
      }

      container.append(
        `<div class="mb-4 select_search-container">
          <label>Uursoort</label>
          <input type="hidden" name="projecten[${aantal}][uursoort]" class="select_search-hidden-input" data-placeholder="Uursoort">
          <div class="select_search-values" >
            <div class="select_search-box" >${options}</div>
          </div>
        </div>`
      );
      initSelectSearch();

    }

    if(!urengetal){
      if(urenregistratie_begintijd){
        container.append(`<div class="mb-4"><label>Begintijd</label> <br><input data-begintijd type="time" name="projecten[${aantal}][begintijd]" class="form-control date" value="${$(`[name='projecten[${aantal-1}][eindtijd]']`).val()}" placeholder="Begintijd" required ></div>`);
      }
      if(urenregistratie_eindtijd){
        let standaardEind = '';
        if(projecten.length == 1 && projecten[0].standaarduren){
          standaardEind = projecten[0].eindtijd;
        }
        container.append(`<div class="mb-4"><label>Eindtijd</label> <br><input data-eindtijd type="time" name="projecten[${aantal}][eindtijd]" value="${standaardEind ?? ''}" class="form-control" placeholder="Eindtijd" required ></div>`);
      }
    } else{
      container.append('<div class="mb-4"><label>Totaal aantal uren</label> <br><input type="number" pattern="[0-9]+([\.,][0-9]+)?" step="0.01" title="Ingevoerde waarde niet correct" value="" name="projecten['+aantal+'][gewerkteuren]" class="form-control" placeholder="Totaal aantal uren" required></div>');
    }

    if(urenregistratie_pauzetijd){
      container.append(
        `<div class="mb-4" >
          <label>Pauze</label>
          <select name="projecten[${aantal}][pauze]" class="form-select">
            <option value="0">Geen pauze</option>
            <option value=".25">15 min</option>
            <option value=".5">30 min</option>
            <option value=".75">45 min</option>
            <option value="1">1 uur</option>
            <option value="1.25">1 uur 15 min</option>
            <option value="1.5">1 uur 30 min</option>
            <option value="1.75">1 uur 45 min</option>
            <option value="2">2 uur</option>
          </select>
        </div>`);
    }

    if(urenMachines === 'aan'){
      container.append(
        `<div class="bg-inverse-secondary rounded border p-2 mb-4" >
            <div class="flex-between">
              <span>Machines</span>
              <a class="btn btn-inverse-primary text-white" onclick="addMachine(${aantal})">@icon_plus</a>
            </div>
            <div class="machines"></div>
          </div>`
      );
    }

    if(urenregistratie_kilometers){
      container.append('<div class="mb-4"><label>Kilometers</label> <br><input type="number" step="1" value="" name="projecten['+aantal+'][kilometers]" class="form-control" placeholder="Kilometers"></div>');
    }

    if(urenregistratie_ladenlossen){
      container.append('<div class="mb-4"><label>laden/lossen begintijd</label> <br><input type="time" value="" name="projecten['+aantal+'][ladenlossen_begintijd]" class="form-control" placeholder="Laden/lossen begintijd"></div>');
    }

    if(urenregistratie_ladenlossen){
      container.append('<div class="mb-4"><label>Laden/lossen eintijd</label> <br><input type="time" value="" name="projecten['+aantal+'][ladenlossen_eindtijd]" class="form-control" placeholder="Laden/lossen eindtijd"></div>');
    }

    if(urenregistratie_ladenlossen_middag){
      container.append('<div class="mb-4"><label>Laden/lossen middag begintijd</label> <br><input type="time" value="" name="projecten['+aantal+'][ladenlossen_middag_begintijd]" class="form-control" placeholder="Laden/lossen middag begintijd"></div>');
    }

    if(urenregistratie_ladenlossen_middag){
      container.append('<div class="mb-4"><label>Laden/lossen middag eindtijd</label> <br><input type="time" value="" name="projecten['+aantal+'][ladenlossen_middag_eindtijd]" class="form-control" placeholder="Laden/lossen middag eindtijd"></div>');
    }

    if(urenregistratie_reisuren){
      container.append('<div class="mb-4"><label>Reistijd</label> <br><input type="text" value="" name="projecten['+aantal+'][reisuren]" class="form-control" placeholder="Reistijd"></div>');
    }

    if(urenregistratie_heenreis_woonwerk){
      container.append('<div class="mb-4"><label>Heenreis woon/werk (privé)</label> <br><input type="text" value="" name="projecten['+aantal+'][heenreis_woonwerk]" class="form-control" placeholder="Heenreis woon/werk"></div>');
    }

    if(urenregistratie_heenreis_woonwerk){
      container.append('<div class="mb-4"><label>Terugreis woon/werk (privé)</label> <br><input type="text" value="" name="projecten['+aantal+'][terugreis_woonwerk]" class="form-control" placeholder="Terugreis woon/werk"></div>');
    }

    if(urenregistratie_heenreis_huiswerk){
      container.append('<div class="mb-4"><label>Heenreis woon/project</label> <br><input type="text" value="" name="projecten['+aantal+'][heenreis_huisproject]" class="form-control" placeholder="Heenreis woon/project"></div>');
    }

    if(urenregistratie_heenreis_huiswerk){
      container.append('<div class="mb-4"><label>Terugreis woon/project</label> <br><input type="text" value="" name="projecten['+aantal+'][terugreis_huisproject]" class="form-control" placeholder="Terugreis woon/project"></div>');
    }

    if(urenregistratie_bestuurder){
      container.append('<div class="mb-4"><label>Bestuurder</label> <br><select name="projecten[{{$i}}][bestuurder]" class="form-control"><option value="1">Ja</option><option value="0">Nee</option><option value="null">Maak een keuze</option></select></div>');
    }

    if(urenregistratie_bijrijder){
      container.append('<div class="mb-4"><label>Bijrijder</label> <br><input type="text" value="" name="projecten['+aantal+'][bijrijder]" class="form-control" placeholder="Bijrijder"></div>');
    }

    if(urenregistratie_naca){
      if(nacagezet == true){
        container.append('<div class="mb-4"><label>Naca</label> <br><select name="projecten['+aantal+'][naca]" class="form-control" id="naca'+aantal+'" required></select></div>');
        for(i = 0; i < naca.length; i++){
          $('#naca'+aantal).append('<option value="'+naca[i]['id']+'">'+naca[i]['code']+' '+naca[i]['omschrijving']+'</option>');
        }
      }
    }

    if(urenregistratie_tijdvoortijd){
      container.append('<div class="mb-4"><label><input type="checkbox" name="projecten['+aantal+'][tijdvoortijd]"> Tijd voor tijd</label></div>');
    }

    if(urenregistratie_overnachting){
      container.append('<div class="mb-4"><label>Overnachting</label><select name="projecten['+aantal+'][overnachting]" class="form-control" placeholder="Maak een keuze"><option></option><option value="Ja">Ja</option><option value="Ja enkele reis">Ja, met heen/terug reis</option></select></div>');
    }

    if(urenregistratie_facturabel){
      container.append('<div class="mb-4"><label>Facturabel</label><br><select name="projecten['+aantal+'][facturabel]" id="facturabel'+aantal+'" class="form-control"><option selected value=""></option></select></div>');
      for(i = 0; i < facturabelopties.length; i++){
        $('#facturabel'+aantal).append('<option value="'+facturabelopties[i]['id']+'">'+facturabelopties[i]['optie']+'</option>');
      }
    }

    if(urenregistratie_verlof){
      if(redenengezet == true){
        container.append('<div class="mb-4"><label>Verlof</label><select name="projecten['+aantal+'][verlof]" class="form-control" placeholder="Maak een keuze"><option></option><option value="8">8 (hele dag)</option><option value="7.75">7,75</option><option value="7.5">7,5</option> <option value="7.25">7,25</option><option value="7">7</option> <option value="6.75">6,75</option><option value="6.5">6,5</option> <option value="6.25">6,25</option><option value="6">6</option> <option value="5.75">5,75</option><option value="5.5">5,5</option> <option value="5.25">5,25</option><option value="5">5</option> <option value="4.75">4,75</option><option value="4.5">4,5</option> <option value="4.25">4,25</option><option value="4">4</option> <option value="3.75">3,75</option><option value="3.5">3,5</option> <option value="3.25">3,25</option><option value="3">3</option> <option value="2.75">2,75</option><option value="2.5">2,5</option> <option value="2.25">2,25</option><option value="2">2</option> <option value="1.75">1,75</option><option value="1.5">1,5</option> <option value="1.25">1,25</option><option value="1">1</option> <option value="0.75">0,75</option><option value="0.5">0,5</option> <option value="0.25">0,25</option></select></div><div class="mb-4"><label>Verlofreden</label><br><select name="projecten['+aantal+'][verlofreden]" id="redenen'+aantal+'" class="form-control"><option selected value=""></option></select></div>');
        for(i = 0; i < redenen.length; i++){
          $('#redenen'+aantal).append('<option value="'+redenen[i]['id']+'">'+redenen[i]['reden']+'</option>');
        }
      }
    }

    if(urenregistratie_opmerkingen){
      container.append('<div class="mb-4"><label>Opmerkingen</label> <br><textarea type="text" name="projecten['+aantal+'][opmerkingen]" class="form-control" placeholder="Opmerkingen"></textarea></div>');
    }


    initSelectSearch();
    initSelectMultiple();

    container.find('.projectnummers').trigger('change');
    scrl(`#project${aantal}`);
  }
  function addMachine(i, machine, plan){
    if(machine && plan){
      $(`#project${i}`).find('.machines').append(`${machinesSelect(i, machine.id, plan)}`);
    }else{
      $(`#project${i}`).find('.machines').append(`${machinesSelect(i)}`);
    }


    initSelectSearch();
  }
  function werkbonInit(){
    if(werkbonPrefill != 'aan' || !werkbonnen.length || @json(isset($correction))){return}
    $('#projecten').empty();
    for(const werkbon of werkbonnen){
      let projectnummer = '';
      if(werkbon.project){projectnummer = werkbon.project.projectnr;}

      const project = projectnummers.find(row => row.projectnr == projectnummer);

      addProject(projectnummer);

      let gewerkteUren = werkbon.values.find(row => row.keyword == 'gewerkteuren');

      $(`[data-projectnummer-label=${aantal}]`).html('Projectnummer <small class="text-muted" >( werkbon )</small>')

      if(typeof gewerkteUren === "undefined"){
        let beginTijd = werkbon.values.find(row => row.keyword == 'begintijd');
        let eindTijd = werkbon.values.find(row => row.keyword == 'eindtijd');

        $(`input[name='projecten[${aantal}][begintijd]']`).val(beginTijd.value).trigger('change');
        $(`input[name='projecten[${aantal}][eindtijd]']`).val(eindTijd.value);
        $(`input[name='projecten[${aantal}][projectnummer]']`).trigger('change');
      }else{
        $(`input[name='projecten[${aantal}][gewerkteuren]']`).val(gewerkteUren.value)
      }

      if(werkbon.machines){
        for(const machine of werkbon.machines){
          addMachine(aantal, machine, werkbon)
        }
      }
    }
  }
  function planningInit(){
    if((planningPrefill != 'aan' && planningPrefill != 'first_last') || !planning.length || @json(isset($correction)) || aantal > 0){return}
    if(planningPrefill == 'aan'){
      $('#projecten').empty();
      for(const row of planning){
        let projectnummer = '';
        if(row.offerte){projectnummer = row.offerte.offertenummer;}
        if(row.project){projectnummer = row.project.projectnr;}

        const project = projectnummers.find(row => row.projectnr == projectnummer);

        addProject(projectnummer);

        $(`[data-projectnummer-label=${aantal}]`).html('Projectnummer <small class="text-muted" >( planning )</small>')
        $(`input[name='projecten[${aantal}][begintijd]']`).val(row.begin).trigger('change');
        $(`input[name='projecten[${aantal}][eindtijd]']`).val(row.eind);
        $(`input[name='projecten[${aantal}][projectnummer]']`).trigger('change');


        if(row.machines && planningmachinessetting == 'pp'){
          for(const machine of row.machines){
            addMachine(aantal, machine, row)
          }
        }

        for(const taak of row.taken){
          $(`[name='projecten[${aantal}][project_taken][]'][value='${taak.id}']`).prop('checked', true);
        }
        refreshSelectMultipleIndicators();
      }
    }else if(planningPrefill == 'first_last'){
      $('#projecten').empty();
      let first = planning[0];
      let last = planning[planning.length - 1];

      let projectnummer = @json(getSettingValue('standaard_project_algemeen_invoer')) ?? '';

      addProject(projectnummer);

      $(`[data-projectnummer-label=${aantal}]`).html('Projectnummer <small class="text-muted" >( planning )</small>')
      $(`input[name='projecten[${aantal}][begintijd]']`).val(first.begin).trigger('change');
      $(`input[name='projecten[${aantal}][eindtijd]']`).val(last.eind);
      $(`input[name='projecten[${aantal}][projectnummer]']`).trigger('change');
      refreshSelectMultipleIndicators();
    }
  }

  function removeProject(){
    var element = document.getElementById('project'+aantal);
    element.parentNode.removeChild(element);
    aantal = aantal - 1;
  }

  function setDefaultPauzes(){

    if (projecten.length == 1 && projecten[0].standaardpauzes) {
      const standaardPauzes = JSON.parse(projecten[0].standaardpauzes);
      $('.pauze-select').each(function(){
        const container = findContainer('project-container', this);
        const begintijd = container.find('[data-begintijd]').val();
        const eindtijd = container.find('[data-eindtijd]').val();
        let pauzeduur = 0;
        for (let pauze in standaardPauzes) {
          pauze = standaardPauzes[pauze];
          if (pauze.begin >= begintijd && pauze.eind <= eindtijd) {
            const begin = new Date('2021-01-01 ' + pauze.begin);
            const eind = new Date('2021-01-01 ' + pauze.eind);
            pauzeduur += Math.round(dateDiffInHours(begin, eind) * 4) / 4;
          }
        }
        $(this).val(pauzeduur);
      });

    }
  }
</script>

@endsection
