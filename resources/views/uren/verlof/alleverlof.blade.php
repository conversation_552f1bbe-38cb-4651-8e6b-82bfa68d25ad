@extends('layouts.app')
@section('title', 'Verlof inzien')

@section('content')
    <div class="card">
        <h4 class="mt-3 mb-2 mx-2">Alle verlof</h4>
        <div class="mx-2">
            <h5 class="d-flex align-items-center">
                <a href="{{ url('/uren/verlof/inzien/' . ($year - 1)) }}"
                   class="btn btn-outline-secondary btn-sm d-inline-flex align-items-center justify-content-center mx-1"
                   style="width: 2em; height: 2em; border-radius: 0;"
                   title="Vorig jaar">
                    <span aria-hidden="true">&larr;</span>
                </a>
                <span class="mx-2">Verlofoverzicht {{ $year }}</span>
                @if($year < date('Y'))
                    <a href="{{ url('/uren/verlof/inzien/' . ($year + 1)) }}"
                       class="btn btn-outline-secondary btn-sm d-inline-flex align-items-center justify-content-center mx-1"
                       style="width: 2em; height: 2em; border-radius: 0;"
                       title="Volgend jaar">
                        <span aria-hidden="true">&rarr;</span>
                    </a>
                @endif
            </h5>
        </div>
    
        <h4 class="mt-4 mb-2 mx-2">Goedgekeurd</h4>
        <div class="mx-2">
            <table class="table table-bordered mb-4">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Medewerker</th>
                        <th>Opmerking</th>
                        <th>Beoordeeld door</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($finalGroupsGoedgekeurd as $group)
                        @php
                            $first = $group[0];
                            $last = $group[count($group) - 1];
                        @endphp
                        <tr data-start="{{ $first->datum }}" data-end="{{ $last->datum }}">
                            <td>
                                @if(CarbonDmy($first->datum) == CarbonDmy($last->datum))
                                    {{ CarbonDmy($first->datum) }}
                                @else
                                    {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                                @endif
                                @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                            </td>
                            <td>
                                <a href="{{ url('/uren/verlof/aanvragen/inzien/' . ($first->user->id ?? $first->medewerker_id)) . '/' . date('Y')  }}">
                                    {{ $first->user->name ?? '' }} {{ $first->user->lastname ?? '' }}
                                </a>
                            </td>
                            <td>{{ $first->opmerkingen }}</td>
                            <td>{{ $first->door->name ?? '' }} {{ $first->door->lastname ?? '' }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4">Geen goedgekeurde verlofaanvragen gevonden.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <h4 class="mt-4 mb-2 mx-2">Afgewezen</h4>
        <div class="mx-2">
            <table class="table table-bordered mb-4">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Medewerker</th>
                        <th>Opmerking</th>
                        <th>Opmerking beoordelaar</th>
                        <th>Beoordeeld door</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($finalGroupsAfgewezen as $group)
                        @php
                            $first = $group[0];
                            $last = $group[count($group) - 1];
                        @endphp
                        <tr data-start="{{ $first->datum }}" data-end="{{ $last->datum }}">
                            <td>
                                @if(CarbonDmy($first->datum) == CarbonDmy($last->datum))
                                    {{ CarbonDmy($first->datum) }}
                                @else
                                    {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                                @endif
                                @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                            </td>
                            <td>
                                <a href="{{ url('/uren/verlof/aanvragen/inzien/' . ($first->user->id ?? $first->medewerker_id)) . '/' . date('Y')  }}">
                                    {{ $first->user->name ?? '' }} {{ $first->user->lastname ?? '' }}
                                </a>
                            </td>
                            <td>{{ $first->opmerkingen }}</td>
                            <td>{{ $first->beoordeel_opmerking }}</td>
                            <td>{{ $first->door->name ?? '' }} {{ $first->door->lastname ?? '' }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4">Geen afgewezen verlofaanvragen gevonden.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
@endsection

@section('script')
@endsection