@extends('layouts.app')
@section('title', 'Verlofredenen')

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <form id="verlofredenen-form">
        <div id="verlofredenen-container">
            @foreach($verlofredenen as $index => $verlofreden)
                <div class="card m-2 p-2 reden-item" style="width: 450px;" data-index="{{ $index }}">
                    <input type="hidden" name="verlofredenen[{{ $index }}][id]" value="{{ $verlofreden->id }}">
                    <input type="hidden" name="verlofredenen[{{ $index }}][active]" value="1">

                    <label>Reden:</label>
                    <input type="text" name="verlofredenen[{{ $index }}][reden]" value="{{ $verlofreden->reden }}" class="form-control">

                    <label>Omschrijving:</label>
                    <input type="text" name="verlofredenen[{{ $index }}][omschrijving]" value="{{ $verlofreden->omschrijving }}" class="form-control">

                    <label>Uren berekening:</label>
                    
                    <select name="verlofredenen[{{ $index }}][standaard_reden]" class="form-control my-2">
                        @foreach(getHoofdVerlofRedenen() as $reden)
                            <option value="{{ $reden->id }}"
                                @if($verlofreden->standaard_reden == $reden->id) selected @endif>
                                {{ \Illuminate\Support\Str::endsWith($reden->reden, 'uren') ? $reden->reden : $reden->reden . ' uren' }}
                            </option>
                        @endforeach
                    </select>

                    <label>Voor rol:</label>
                    <select name="verlofredenen[{{ $index }}][role_id]" class="form-control my-2">
                        <option value="0" @if($verlofreden->role_id == 0) selected @endif>Alle Rollen</option>
                        @foreach(getRoles() ?? [] as $role)
                            <option value="{{ $role->id }}" @if($role->id == $verlofreden->role_id) selected @endif>{{ $role->name }}</option>
                        @endforeach
                    </select>

                    <label>Kleur:</label>
                    <div class="input-group align-items-center mb-2">
                        <input type="hidden" name="verlofredenen[{{ $index }}][kleur]" value="{{ $verlofreden->kleur ?? '#000000' }}" class="color-input">
                        <div class="color-block cursor-pointer mx-1"
                             onclick="selectVerlofRedenColor('{{ $index }}', this)"
                             style="background: {{ $verlofreden->kleur ?? '#000000' }}; width: 3rem; height: 2.2rem; min-width: 3rem; border: 1px solid #ccc;"></div>
                    </div>

                    <button type="button" class="btn btn-danger mt-2 w-100" onclick="removeReden(this)">Verwijder</button>
                </div>
            @endforeach
        </div>
    </form>

    <button onclick="addReden()" class="btn btn-secondary mt-3">Nieuwe reden toevoegen</button>
    <button onclick="saveRedenen()" class="btn btn-primary mt-3">Alles opslaan</button>
@endsection

@section('script')
    <script>
        let redenIndex = {{ count($verlofredenen) }};
        const roles = @json(getRoles() ?? []);

        function selectVerlofRedenColor(index, el) {
            let input = $(`input[name='verlofredenen[${index}][kleur]']`);
            let block = $(el);
            getHexColor({color: input.val()})
                .then((color) => {
                    input.val(color);
                    block.css('background', color);
                })
                .catch(() => {});
        }

        function addReden() {
            let rolesOptions = '<option value="0" selected>Alle Rollen</option>';
            roles.forEach(role => {
                rolesOptions += `<option value="${role.id}">${role.name}</option>`;
            });
            const html = `
                <div class="card m-2 p-2 reden-item" style="width: 450px;" data-index="${redenIndex}"> 
                    <input type="hidden" name="verlofredenen[${redenIndex}][active]" value="1">

                    <label>Reden:</label>
                    <input type="text" name="verlofredenen[${redenIndex}][reden]" class="form-control">

                    <label>Omschrijving:</label>
                    <input type="text" name="verlofredenen[${redenIndex}][omschrijving]" class="form-control">

                    <label>Uren berekening:</label>
                    <select name="verlofredenen[${redenIndex}][standaard_reden]" class="form-control my-2">
                        <option value="verlof" selected>Verlof</option>
                        <option value="bijzonder verlof">Bijzonder verlof</option>
                        <option value="ziekte">Ziekte</option>
                        <option value="feestdag">Feestdag</option>
                    </select>

                    <label>Voor rol:</label>
                    <select name="verlofredenen[${redenIndex}][role_id]" class="form-control my-2">
                        ${rolesOptions}
                    </select>

                    <label>Kleur:</label>
                    <div class="input-group align-items-center mb-2" style="max-width: 180px;">
                        <input type="hidden" name="verlofredenen[${redenIndex}][kleur]" value="#000000" class="color-input">
                        <div class="color-block cursor-pointer mx-1"
                             onclick="selectVerlofRedenColor('${redenIndex}', this)"
                             style="background: #000000; width: 3rem; height: 2.2rem; min-width: 3rem; border: 1px solid #ccc;"></div>
                    </div>

                    <button type="button" class="btn btn-danger mt-2 w-100" onclick="removeReden(this)">Verwijder</button>
                </div>
            `;

            $('#verlofredenen-container').append(html);
            redenIndex++;
        }

        function removeReden(btn) {
            const card = $(btn).closest('.reden-item');
            const index = card.data('index');
            const idInput = card.find('input[name^="verlofredenen"][name$="[id]"]');

            card.find('input, select').remove();
            
            if (idInput.length > 0) {
                card.append(`<input type="hidden" name="verlofredenen[${index}][id]" value="${idInput.val()}">`);
            }
            card.append(`<input type="hidden" name="verlofredenen[${index}][active]" value="0">`);
            card.hide();
        }

        function saveRedenen() {
            loader();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                type: "POST",
                url: "{{ url('uren/verlof/updateRedenen') }}",
                data: $('#verlofredenen-form').serialize(),
                success: function (response) {
                    successLoader();
                    window.location.reload();
                },
                error: function (err) {
                    actError(err);
                    errorLoader();
                }
            });
        }
    </script>
@endsection