@extends('layouts.app')
@section('title', 'Verlof afhandelen')

@section('content')
<div class="card">
    <h4 class="mt-3 mb-0 mx-2"><PERSON><PERSON><PERSON><PERSON> van {{ $medewerker->name }} {{ $medewerker->lastname }}</h4>
    <div class="mb-2 mx-2">
        <h5 class="d-flex align-items-center mb-3">
            <a href="{{ url('/uren/verlof/aanvragen/inzien/' . $medewerker->id . '/' . ($year - 1)) }}"
               class="btn btn-outline-secondary btn-sm d-inline-flex align-items-center justify-content-center mx-1"
               style="width: 2em; height: 2em; border-radius: 0;"
               title="Vorig jaar">
                <span aria-hidden="true">&larr;</span>
            </a>
            <span class="mx-2">Verlofoverzicht {{ $year }}</span>
            @if($year < date('Y'))
                <a href="{{ url('/uren/verlof/aanvragen/inzien/' . $medewerker->id . '/' . ($year + 1)) }}"
                   class="btn btn-outline-secondary btn-sm d-inline-flex align-items-center justify-content-center mx-1"
                   style="width: 2em; height: 2em; border-radius: 0;"
                   title="Volgend jaar">
                    <span aria-hidden="true">&rarr;</span>
                </a>
            @endif
        </h5>
        <ul class="list-unstyled">
            <li><strong>Contractuele verlofuren</strong>: {{ number_format($contractueelSaldo, 2, ',', '.') }} uur</li>
            <li><strong>Aanvullende verlofuren</strong>: {{ number_format($aanvullendSaldo, 2, ',', '.') }} uur</li>
            <li class="border-top my-2 w-25"></li>
            <li><strong>Gebruikte verlofuren</strong>: {{ number_format($gebruiktSaldo, 2, ',', '.') }} uur</li>
            <li class="border-top my-2 w-25"></li>
            <li><strong>Resterend verlofsaldo</strong>: {{ number_format($resterendSaldo, 2, ',', '.') }} uur</li>
        </ul>
    </div>
    
    <h4 class="mb-2 mx-2">Aangevraagd</h4>
    <div class="mx-2">
        <table class="table table-bordered mb-4">
            <thead>
                <tr>
                    <th>Datum</th>
                    <th>Opmerking</th>
                    <th>Acties</th>
                </tr>
            </thead>
            <tbody>
                @forelse($finalGroups as $group)
                    @php
                        $first = $group[0];
                        $last = $group[count($group)-1];
                    @endphp
                    <tr>
                        <td>
                            @if($first->datum == $last->datum)
                                {{ CarbonDmy($first->datum) }}
                            @else
                                {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                            @endif
                            @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                        </td>
                        <td>{{ $first->opmerkingen }}</td>
                        <td>
                            <a href="{{ url('/uren/verlof/aanvragen/' . $first->medewerker_id) }}" class="btn btn-primary btn-sm">Beoordelen</a>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="3">Geen aangevraagde verlofaanvragen gevonden.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <h4 class="mt-4 mb-2 mx-2">Goedgekeurd</h4>
    <div class="mx-2">
        <table class="table table-bordered mb-4">
            <thead>
                <tr>
                    <th>Datum</th>
                    <th>Opmerking</th>
                    <th>Beoordeeld door</th>
                </tr>
            </thead>
            <tbody>
                @forelse($finalGroupsGoedgekeurd as $group)
                    @php
                        $first = $group[0];
                        $last = $group[count($group)-1];
                    @endphp
                    <tr>
                        <td>
                            @if($first->datum == $last->datum)
                                {{ CarbonDmy($first->datum) }}
                            @else
                                {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                            @endif
                            @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                        </td>
                        <td>{{ $first->opmerkingen }}</td>
                        <td>{{ $first->door->name ?? '' }} {{ $first->door->lastname ?? '' }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="3">Geen goedgekeurde verlofaanvragen gevonden.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <h4 class="mt-4 mb-2 mx-2">Afgewezen</h4>
    <div class="mx-2">
        <table class="table table-bordered mb-4">
            <thead>
                <tr>
                    <th>Datum</th>
                    <th>Opmerking</th>
                    <th>Opmerking beoordelaar</th>
                    <th>Beoordeeld door</th>
                </tr>
            </thead>
            <tbody>
                @forelse($finalGroupsAfgewezen as $group)
                    @php
                        $first = $group[0];
                        $last = $group[count($group)-1];
                    @endphp
                    <tr>
                        <td>
                            @if($first->datum == $last->datum)
                                {{ CarbonDmy($first->datum) }}
                            @else
                                {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                            @endif
                            @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                        </td>
                        <td>{{ $first->opmerkingen }}</td>
                        <td>{{ $first->beoordeel_opmerking }}</td>
                        <td>{{ $first->door->name ?? '' }} {{ $first->door->lastname ?? '' }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="3">Geen afgewezen verlofaanvragen gevonden.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@endsection

@section('script')

@endsection