@extends('layouts.app')
@section('title', 'Verlof aanvragen')

@section('content')
    <form action="{{ url('uren/verlof/invoeren') }}" method="POST" id="verlof-invoeren-form">
        @csrf
        <div class="card m-2 p-2 reden-item">
            @if(Auth::user()->hasPermissionTo('Verlof afhandelen'))
                <div class="mb-3">
                    <label>Werknemer(s): *</label>
                    <div class="select_multiple-container" data-placeholder="Werknemer">
                        @foreach(getUsers() as $user)
                            <label class="select_multiple-value user-select-none">{{$user->name}} {{$user->lastname}} <input type="checkbox"
                                    class="d-none" name="medewerker_id[]" data-name="{{$user->name}} {{$user->lastname}}"
                                    value="{{$user->id}}" @if($user->id == Auth::user()->id) checked @endif></label>
                        @endforeach
                    </div>
                </div>
            @else
                <input type="hidden" name="medewerker_id[]" value="{{ Auth::user()->id }}">
                <div id="verlofsaldo-info" class="mb-3 small">
                    Verlof saldo: <span id="saldo-totaal">{{ Auth::user()->verlofsaldo(date('Y')) }}</span> uur,
                    Gebruikt: <span
                        id="saldo-gebruikt">{{ Auth::user()->verlofsaldo(date('Y')) - Auth::user()->verlofsaldoExclusief(date('Y')) }}</span>
                    uur,
                </div>
            @endif

            <div class="mb-3">
                <label>Reden: *</label>
                <div class="select_search-container">
                    <input type="hidden" name="reden_id" class="select_search-hidden-input" data-placeholder="Reden">
                    <div class="select_search-values">
                        <div class="select_search-box">
                            @foreach(getVerlofredenen() as $reden)
                                @if($reden->active && (($reden->role_id == 0 || Auth::user()->role_id == $reden->role_id) || Auth::user()->hasPermissionTo('Verlof afhandelen')))
                                    <span class="select_search-value" data-value="{{$reden->id}}" data-name="{{$reden->reden}}">{{$reden->reden}}</span>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label>Datum: *</label>
                <div class="select_search-container">
                    <input type="text" name="daterange" class="form-control" placeholder="Kies een datum" required>
                </div>
            </div>

            <div id="time-inputs" style="display: none;" class="mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <label>Begintijd:</label>
                        <input type="time" name="start_time" class="form-control">
                    </div>
                    <div class="col-md-6">
                        <label>Eindtijd:</label>
                        <input type="time" name="end_time" class="form-control">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="mt-0">Opmerking:</label>
                <div class="select_search-container">
                    <textarea name="opmerking" class="form-control" rows="3" @if(getSettingCheckbox('verlof_opmerking_verplicht'))
                    required @endif></textarea>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-sm mt-3 float-left w-25">Verlof aanvragen</button>
        </div>
    </form>
    <hr class="my-4">
    <h5>Mijn verlof <span class="small">(Verlofsaldo over dit jaar: {{ Auth::user()->verlofsaldoExclusief(date('Y')) }} uur)</span></h5>
    <table class="table table-bordered mb-4 bg-white">
        <thead>
            <tr>
                <th>Datum</th>
                <th>Reden</th>
                <th>Opmerking</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @forelse($finalGroupsMijnVerlof as $group)
                @php
                    $first = $group[0];
                    $last = $group[count($group) - 1];
                    $reden = getVerlofredenen()->where('id', $first->reden_id)->first()->reden ?? '-';
                @endphp
                <tr>
                    <td>
                        {{ CarbonDmy($first->datum) }}@if($first->datum != $last->datum) -
                        {{ CarbonDmy($last->datum) }}@endif
                        @if($first->van && $first->tot)
                            ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }})
                        @endif
                    </td>
                    <td>{{ $reden }}</td>
                    <td>{{ $first->opmerkingen }}</td>
                    <td>
                        @if($first->beoordeeld == 0)
                            <span class="badge badge-warning">In behandeling</span>
                        @elseif($first->akkoord == 1)
                            <span class="badge badge-success">Goedgekeurd</span>
                        @else
                            <span class="badge badge-danger">Afgewezen</span>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">Geen verlof(aanvragen) gevonden.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    <div id="other-users-verlof-container">
        @if(isset($allUsersVerlofGrouped) && count($allUsersVerlofGrouped))
            @foreach($allUsersVerlofGrouped as $userId => $data)
                <div class="other-user-verlof-table" id="verlof-user-{{ $userId }}" data-user-id="{{ $userId }}" style="display:none;">
                    <h5>Verlof van {{ $data['user']->name }} {{ $data['user']->lastname }} <span class="small">(Verlofsaldo over dit jaar: {{ $data['user']->verlofsaldoExclusief(date('Y')) }} uur)</span></h5>
                    <table class="table table-bordered mb-4 bg-white">
                        <thead>
                            <tr>
                                <th>Datum</th>
                                <th>Reden</th>
                                <th>Opmerking</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($data['groups'] as $group)
                                @php
                                    $first = $group[0];
                                    $last = $group[count($group) - 1];
                                    $reden = getVerlofredenen()->where('id', $first->reden_id)->first()->reden ?? '-';
                                @endphp
                                <tr>
                                    <td>
                                        {{ CarbonDmy($first->datum) }}@if($first->datum != $last->datum) -
                                        {{ CarbonDmy($last->datum) }}@endif
                                        @if($first->van && $first->tot)
                                            ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }})
                                        @endif
                                    </td>
                                    <td>{{ $reden }}</td>
                                    <td>{{ $first->opmerkingen }}</td>
                                    <td>
                                        @if($first->beoordeeld == 0)
                                            <span class="badge badge-warning">In behandeling</span>
                                        @elseif($first->akkoord == 1)
                                            <span class="badge badge-success">Goedgekeurd</span>
                                        @else
                                            <span class="badge badge-danger">Afgewezen</span>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">Geen verlof(aanvragen) gevonden.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            @endforeach
        @endif
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function () {
            $('input[name="daterange"]').daterangepicker({
                locale: {
                    format: 'DD-MM-YYYY',
                    applyLabel: 'Toepassen',
                    cancelLabel: 'Annuleren',
                    fromLabel: 'Van',
                    toLabel: 'Tot',
                    customRangeLabel: 'Aangepast',
                    daysOfWeek: ['Zo', 'Ma', 'Di', 'Wo', 'Do', 'Vr', 'Za'],
                    monthNames: ['Januari', 'Februari', 'Maart', 'April', 'Mei', 'Juni', 'Juli', 'Augustus', 'September', 'Oktober', 'November', 'December']
                },
                minDate: moment().startOf('day'),
                autoUpdateInput: false
            });

            $('input[name="daterange"]').on('apply.daterangepicker', function (ev, picker) {
                $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format('DD-MM-YYYY'));

                if (picker.startDate.format('YYYY-MM-DD') === picker.endDate.format('YYYY-MM-DD')) {
                    $('#time-inputs').show();
                    $('input[name="start_time"]').prop('required', true);
                    $('input[name="end_time"]').prop('required', true);
                } else {
                    $('#time-inputs').hide();
                    $('input[name="start_time"]').prop('required', false);
                    $('input[name="end_time"]').prop('required', false);
                }
            });

            $('input[name="daterange"]').on('cancel.daterangepicker', function (ev, picker) {
                $(this).val('');
                $('#time-inputs').hide();
                $('input[name="start_time"]').prop('required', false);
                $('input[name="end_time"]').prop('required', false);
            });

            @if(Auth::user()->hasPermissionTo('Verlof afhandelen'))
            $(document).on('change', 'input[name="medewerker_id[]"]', function() {
                var checked = $('input[name="medewerker_id[]"]:checked');
                $('.other-user-verlof-table').hide();
                checked.each(function() {
                    var userId = $(this).val();
                    $('#verlof-user-' + userId).show();
                });
            });
            $('input[name="medewerker_id[]"]').trigger('change');
            @endif
        });
    </script>
@endsection