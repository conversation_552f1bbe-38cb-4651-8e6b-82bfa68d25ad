@extends('layouts.app')
@section('title', '<PERSON><PERSON><PERSON><PERSON> be<PERSON>')
@section('content')
<div class="container-fluid">
    <div class="col-md-7 col-lg-5">
        <h3 class="mt-2">
            Verlofaanvragen
            @if(!$verlof->isEmpty())
                van {{ $verlof->first()->user->name }} {{ $verlof->first()->user->lastname }}
            @endif
        </h3>
        <p class="fs-5"><PERSON><PERSON><PERSON><PERSON>aldo over dit jaar: {{ $verlofsaldo }}</p>
        @if($verlof->isEmpty())
            <div class="bg-white border rounded p-3">Geen openstaande verlofaanvragen.</div>
        @else
        <form method="POST" action="{{ url('uren/verlof/aanvragen') }}">
            @csrf
            <div class="d-flex flex-column">
                <div class="mb-2">
                    <input type="checkbox" id="select-all-verlof" class="me-2">
                    <label for="select-all-verlof" class="user-select-none" style="cursor: pointer;">Alles selecteren</label>
                </div>
                @foreach($verlof as $aanvraag)
                    <div class="card rounded mb-2">
                        <div class="card-body py-2 px-3">
                            @if(isset($ingepland[$aanvraag->id]) && $ingepland[$aanvraag->id])
                                <div class="alert alert-warning p-2 mb-2">
                                    Let op! Deze gebruiker is op deze datum ingepland!
                                </div>
                            @endif
                            <label class="mb-0 w-100 user-select-none" style="cursor: pointer;">
                                <input type="checkbox" name="verlof[]" value="{{ $aanvraag->id }}" id="verlof-{{ $aanvraag->id }}" class="me-2">
                                {{ CarbonDmy($aanvraag->datum) }}
                                @if($aanvraag->van && $aanvraag->tot)
                                    <span class="ms-2">
                                        {{ \Carbon\Carbon::parse($aanvraag->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($aanvraag->tot)->format('H:i') }}
                                    </span>
                                @else
                                    <span class="ms-2">(Hele dag)</span>
                                @endif
                                @if(!empty($aanvraag->opmerkingen))
                                    <div class="mt-1 text-muted">Opmerking: {{ $aanvraag->opmerkingen }}</div>
                                @endif
                            </label>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="mb-3">
                <label for="opmerking" class="form-label">Opmerking</label>
                <textarea class="form-control" id="opmerking" name="opmerking" rows="2" placeholder="Opmerking (alleen voor afkeuren)"></textarea>
            </div>
            <div class="mt-2 d-flex gap-2">
                <button type="submit" name="status" value="1" class="btn btn-success" id="btn-goedkeuren" disabled>Goedkeuren</button>
                <button type="submit" name="status" value="0" class="btn btn-danger" id="btn-afkeuren" disabled>Afkeuren</button>
                <a href="{{ url()->previous() }}" class="btn btn-secondary">Terug</a>
            </div>
        </form>
        @endif
    </div>
</div>
@endsection
@section('script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAll = document.getElementById('select-all-verlof');
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="verlof[]"]');
        const btnGoedkeuren = document.getElementById('btn-goedkeuren');
        const btnAfkeuren = document.getElementById('btn-afkeuren');

        function updateButtons() {
            const anyChecked = Array.from(checkboxes).some(cb => cb.checked);
            btnGoedkeuren.disabled = !anyChecked;
            btnAfkeuren.disabled = !anyChecked;
        }

        selectAll.addEventListener('change', function() {
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });
            updateButtons();
        });
        
        checkboxes.forEach(cb => {
            cb.addEventListener('change', function() {
                if (!cb.checked) {
                    selectAll.checked = false;
                } else if (Array.from(checkboxes).every(c => c.checked)) {
                    selectAll.checked = true;
                }
                updateButtons();
            });
        });

        updateButtons();
    });
</script>
@endsection