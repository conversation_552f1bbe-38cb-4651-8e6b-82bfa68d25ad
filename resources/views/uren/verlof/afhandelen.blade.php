@extends('layouts.app')
@section('title', 'Verlof afhandelen')
@section('content')
    <div class="card mt-3">
        <h4 class="mt-3 mb-2 mx-2">Verlofaanvragen</h4>
        <div class="mx-2">
            <table class="table table-bordered mb-4">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Medewerker</th>
                        <th>Opmerking</th>
                        <th>Acties</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($finalGroups as $group)
                        @php
                            $first = $group[0];
                            $last = $group[count($group) - 1];
                        @endphp
                        <tr data-start="{{ $first->datum }}" data-end="{{ $last->datum }}">
                            <td>
                                @if($first->datum == $last->datum)
                                    {{ CarbonDmy($first->datum) }}
                                @else
                                    {{ CarbonDmy($first->datum) }} - {{ CarbonDmy($last->datum) }}
                                @endif
                                @if($first->van) ({{ \Carbon\Carbon::parse($first->van)->format('H:i') }} - {{ \Carbon\Carbon::parse($first->tot)->format('H:i') }}) @endif
                            </td>
                            <td>
                                <a href="{{ url('/uren/verlof/aanvragen/inzien/' . ($first->user->id ?? $first->medewerker_id))  . '/' . date('Y') }}">
                                    {{ $first->user->name ?? '' }} {{ $first->user->lastname ?? '' }}
                                </a>
                            </td>
                            <td>{{ $first->opmerkingen }}</td>
                            <td>
                                <a href="{{ url('/uren/verlof/aanvragen/' . $first->medewerker_id) }}"
                                    class="btn btn-primary btn-sm">Beoordelen</a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4">Geen aangevraagde verlofaanvragen gevonden.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
@endsection
@section('script')
@endsection