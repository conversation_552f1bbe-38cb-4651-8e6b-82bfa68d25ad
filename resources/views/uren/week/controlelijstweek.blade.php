@extends('layouts.app')

@section('title', 'Weekoverzichten alle medewerkers')

@section('content')
<div class="flex-between my-3">
  <div class="ds-container bg-white" data-display-select-container="bvs" >
    <a class="ds-node" data-value="alle" >Alle</a>
    @foreach (getBvs() as $bv)
      <a class="ds-node" data-value="{{ $bv->id }}" >{{ $bv->name }}</a>
    @endforeach
  </div>
  <div>
    <button class="btn btn-dark" data-display="alles" onclick="display('alles')">Alles</button>
    <button class="btn btn-inverse-dark" data-display="kantoor" onclick="display('kantoor')">Kantoor</button>
    <button class="btn btn-inverse-dark" data-display="buitendienst" onclick="display('buitendienst')">Buitendienst</button>
    @if (getSettingValue('urenregistratie_invoeren_machines') == 'aan')
    <button class="btn btn-inverse-dark" data-display="machines" onclick="display('machines')">Machines</button>
    @endif
  </div>
  <div class="flex-between" >
    <a href="{{ url('/uren/controlelijst/' . $jaar . '/' . $weeknummer . '/export') }}" id="exportButton" class="btn btn-inverse-primary mx-1">Exporteren Excel</a>
    <a href="{{ url('/uren/controlelijst/' . $jaar . '/' . $weeknummer . '/exportxml') }}" id="exportButton" class="btn btn-inverse-primary mx-1">Exporteren Xml</a>
    <div class="toggle-btn"></div>
  </div>
</div>
<section class="append-loader" >
@foreach($controlelijsten as $week)
  @if (!$week[0]->user->hasPermissionTo('uren invoeren') && !$week[0]->user->hasPermissionTo('uren o.b.v. getal'))
    @continue
  @endif
  <div class="card {{ $week[0]->jobtype }}" data-bv-id="{{ $week[0]->user->bv_id }}">
    <div class="card-content">
      <div class="mb-4" style="padding: 10px;">
        <table>
          <tr><td style="width: 15%;">Personeelsnummer</td><td>{{ $week[0]->pnumber }}</td></tr>
          <tr><td>Naam</td><td>{{ $week[0]->name }} {{ $week[0]->lastname }}</td></tr>
          <tr><td>Functie</td><td>{{ $week[0]->jobtype }}</td></tr>
        </table>
      </div>
      @php
      $data = json_decode(getSettingValue(('urenregistratie_labels') ?? '[]'), true);
      @endphp
      <div class="table-responsive">
        <table class="table">
          <tr>
            <th>{{ isset($data["Datum"]) ? $data["Datum"] : "Datum" }}</th>
            @if(hasAccessTo('urenregistratie_projectnummer'))
              <th>@if(getSettingValue("urenregistratie_controlelijst_projectweergave") == "naam") {{ isset($data["Projectnaam"]) ? $data["Projectnaam"] : "Projectnaam" }}@else{{ isset($data["Projectnummer"]) ? $data["Projectnummer"] : "Projectnummer" }}@endif</th>
            @endif
            @if(hasAccessTo('urenregistratie_naca'))
              <th>Naca</th>
            @endif
            @if($week[0]->user->hasPermissionTo('uren o.b.v. getal'))
              <th>{{ isset($data["Ingevulde uren"]) ? $data["Ingevulde uren"] : "Ingevulde uren" }}</th>
            @else
              <th>{{ isset($data["Begintijd"]) ? $data["Begintijd"] : "Begintijd" }}</th>
              <th>{{ isset($data["Eindtijd"]) ? $data["Eindtijd"] : "Eindtijd" }}</th>
            @endif

            @if(hasAccessTo('urenregistratie_pauzetijd'))
              <th class="td-toggle td-pauze">{{ isset($data["Pauze"]) ? $data["Pauze"] : "Pauze" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_ladenlossen'))
              <th>{{ isset($data["Laden/lossen van"]) ? $data["Laden/lossen van"] : "Laden/lossen van" }}</th>
              <th>{{ isset($data[" Laden/lossen tot"]) ? $data[" Laden/lossen tot"] : " Laden/lossen tot" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_ladenlossen_middag'))
              <th>{{ isset($data["Laden/lossen middag van"]) ? $data["Laden/lossen middag van"] : "Laden/lossen middag van" }}</th>
              <th>{{ isset($data["Laden/lossen middag tot"]) ? $data["Laden/lossen middag tot"] : "Laden/lossen middag tot" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_bestuurder'))
              <th>{{ isset($data["Bestuurder"]) ? $data["Bestuurder"] : "Bestuurder" }}</th>
              <th>{{ isset($data["Kenteken"]) ? $data["Kenteken"] : "Kenteken" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_bijrijder'))
              <th>{{ isset($data["Bijrijder"]) ? $data["Bijrijder"] : "Bijrijder" }}</th>
            @endif
            <th>{{ isset($data["100%"]) ? $data["100%"] : "100%" }}</th>
            <th class="td-toggle td-125" >{{ isset($data["125%"]) ? $data["125%"] : "125%" }}</th>
            <th class="td-toggle td-128" >{{ isset($data["128,44%"]) ? $data["128,44%"] : "128,44%" }}</th>
            <th class="td-toggle td-150" >{{ isset($data["150%"]) ? $data["150%"] : "150%" }}</th>
            <th class="td-toggle td-165" >{{ isset($data["165%"]) ? $data["165%"] : "165%" }}</th>
            <th class="td-toggle td-200" >{{ isset($data["200%"]) ? $data["200%"] : "200%" }}</th>
            <th class="td-toggle td-minuren" >{{ isset($data["Minuren"]) ? $data["Minuren"] : "Minuren" }}</th>
            @if(hasAccessTo('urenregistratie_tijdvoortijd'))
              <th>{{ isset($data["Tijd voor Tijd"]) ? $data["Tijd voor Tijd"] : "Tijd voor Tijd" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_reisuren'))
              <th class="td-toggle td-reisuren">{{ isset($data["Reisuren"]) ? $data["Reisuren"] : "Reisuren" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_km'))
              <th class="td-toggle td-kilometers">{{ isset($data["Kilometers"]) ? $data["Kilometers"] : "Kilometers" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_woonwerkKm'))
              <th class="td-toggle td-woonwerkKm">{{ isset($data["woonwerkKm"]) ? $data["woonwerkKm"] : "Woon-werk" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_huisProjectKm'))
              <th class="td-toggle td-huisprojectKm">{{ isset($data["huisprojectKm"]) ? $data["huisprojectKm"] : "Huis-project" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_overnachting'))
              <th class="td-toggle td-overnachting">{{ isset($data["Overnachting"]) ? $data["Overnachting"] : "Overnachting" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_verlof'))
              <th class="td-toggle td-bijzonderverlofuren">{{ isset($data["Bijzonderverlofuren"]) ? $data["Bijzonderverlofuren"] : "Bijzonderverlofuren" }}</th>
              <th class="td-toggle td-roostervrij">{{ isset($data["Roostervrij"]) ? $data["Roostervrij"] : "Roostervrij" }}</th>
              <th class="td-toggle td-verlofuren">{{ isset($data["Verlofuren"]) ? $data["Verlofuren"] : "Verlofuren" }}</th>
              <th class="td-toggle td-verlofreden">{{ isset($data["Verlofreden"]) ? $data["Verlofreden"] : "Verlofreden" }}</th>
              <th class="td-toggle td-feesturen">{{ isset($data["Feesturen"]) ? $data["Feesturen"] : "Feesturen" }}</th>
              <th class="td-toggle td-ziekteuren">{{ isset($data["Ziekteuren"]) ? $data["Ziekteuren"] : "Ziekteuren" }}</th>
            @endif
            @if(hasAccessTo('urenregistratie_opmerkingen'))
              <th class="td-toggle td-opmerkingen">{{ isset($data["Opmerkingen"]) ? $data["Opmerkingen"] : "Opmerkingen" }}</th>
            @endif
          </tr>
  @php
    $medewerker_id = 0;
    $hide = json_decode(getSettingValue(('uren_hide_column') ?? '[]'), true);

    $week_totaal_ingevuld = 0;
    $week_totaal_gewerkte_uren = 0;
    $week_totaal_pauze = 0;
    $week_totaal_overuren125 = 0;
    $week_totaal_overuren128 = 0;
    $week_totaal_overuren150 = 0;
    $week_totaal_overuren165 = 0;
    $week_totaal_overuren200 = 0;
    $week_tijdvoortijd = 0;
    $week_reisuren = 0;
    $week_kilometers = 0;
    $week_woonwerkKmHeen = 0;
    $week_woonwerkKmTerug = 0;
    $week_huisProjectKmHeen = 0;
    $week_huisProjectKmTerug = 0;
    $week_bijzonderverlof = 0;
    $week_roostervrij = 0;
    $week_verlof = 0;
    $week_feesturen = 0;
    $week_ziekteuren = 0;
    $week_minuren = 0;
    $weektotaal = 0;
    $newweek = [];

    $startOfWeek = Carbon()->setISODate($jaar, $weeknummer)->startOfWeek();
    $endOfWeek = Carbon()->setISODate($jaar, $weeknummer)->endOfWeek();

    $period = \Carbon\CarbonPeriod::create($startOfWeek, '1 day', $endOfWeek);
    foreach($period as $day){
      $found = false;
      foreach($week as $dag){
        if($day->format('Y-m-d') == $dag->datum){
          $newweek[] = $dag;
          $found = true;
        }
      }
      if(!$found){
        $newweek[] = (object)[
          'datum' => $day->format('Y-m-d'),
          'medewerker_id' => $week[0]->medewerker_id,
          'projectnummer' => null,
          'code' => null,
          'gewerkte_uren' => '0.00',
          'begintijd' => null,
          'eindtijd' => null,
          'pauze' => 0,
          'ladenlossen_begintijd' => null,
          'ladenlossen_eindtijd' => null,
          'ladenlossen_middag_begintijd' => null,
          'ladenlossen_middag_eindtijd' => null,
          'bestuurder' => null,
          'kenteken_id' => null,
          'bijrijder' => null,
          'totaaluren100' => 0,
          'totaaldaguren100' => 0,
          'totaal_overuren125' => 0,
          'totaal_overuren128' => 0,
          'totaal_overuren150' => 0,
          'totaal_overuren165' => 0,
          'totaal_overuren200' => 0,
          'tijdvoortijd' => 0,
          'reisuren' => 0,
          'kilometers' => 0,
          'heenreis_woonwerk' => 0,
          'terugreis_woonwerk' => 0,
          'heenreis_huisproject' => 0,
          'terugreis_huisproject' => 0,
          'heenreis_huisprojectKm' => 0,
          'terugreis_huisprojectKm' => 0,
          'overnachting' => null,
          'bijzonderverlof' => 0,
          'roostervrij' => 0,
          'verlof' => 0,
          'reden' => null,
          'feesturen' => 0,
          'ziekteuren' => 0,
          'opmerkingen' => null,
          'minuren' => 0,
          'user' => $week[0]->user,
          'project' => null
        ];
      }
    }
  @endphp
  @foreach ($newweek as $dag)
  @php
  $dayofweek = \Carbon\Carbon::parse($dag->datum)->dayOfWeek + 1;
  @endphp
    @if (getSettingCheckbox('ignore_day_'.$dayofweek) && $dag->gewerkte_uren == '0.00' && $dag->begintijd == null)
      @continue
    @endif
    @php
      $week_totaal_ingevuld += $dag->totaaluren100;
      $week_totaal_gewerkte_uren += $dag->totaaldaguren100;
      $week_totaal_pauze += $dag->pauze;
      $week_totaal_overuren125 += $dag->totaal_overuren125;
      $week_totaal_overuren128 += $dag->totaal_overuren128;
      $week_totaal_overuren150 += $dag->totaal_overuren150;
      $week_totaal_overuren165 += $dag->totaal_overuren165;
      $week_totaal_overuren200 += $dag->totaal_overuren200;
      $week_tijdvoortijd += $dag->tijdvoortijd;
      $week_reisuren += $dag->reisuren;
      $week_kilometers += $dag->kilometers;
      $week_woonwerkKmHeen += $dag->heenreis_woonwerk;
      $week_woonwerkKmTerug += $dag->terugreis_woonwerk;
      $week_huisProjectKmHeen += $dag->heenreis_huisproject;
      $week_huisProjectKmTerug += $dag->terugreis_huisprojectKm;
      $week_bijzonderverlof += $dag->bijzonderverlof;
      $week_roostervrij += $dag->roostervrij;
      $week_verlof += $dag->verlof;
      $week_feesturen += $dag->feesturen;
      $week_ziekteuren += $dag->ziekteuren;
      $week_minuren += $dag->minuren;
      $weektotaal += $dag->totaaldaguren100+$dag->totaal_overuren125+$dag->totaal_overuren128+$dag->totaal_overuren150+$dag->totaal_overuren165+$dag->totaal_overuren200+$dag->reisuren+$dag->bijzonderverlof+$dag->verlof+$dag->feesturen+$dag->ziekteuren;
    @endphp
    <tr>
      <td><a href="{{ url('/uren/correction/'.\Carbon\Carbon::parse($dag->datum)->format('d-m-Y').'/user/'.$dag->medewerker_id) }}">{{ \Carbon\Carbon::parse($dag->datum)->format('d-m-Y') }}</a></td>
      @if(hasAccessTo('urenregistratie_projectnummer'))
        @if(getSettingValue("urenregistratie_controlelijst_projectweergave") == "naam")
          <td><span class="tippy" data-tippy-content="{{ $dag->projectnummer }}">{{ $dag->project ? $dag->project->projectnaam : '-'}}</span></td>
        @else
          <td><span class="tippy" data-tippy-content="{{ $dag->project ? $dag->project->projectnaam : '-'}}">{{ $dag->projectnummer }}</span></td>
        @endif
      @endif

      @if(hasAccessTo('urenregistratie_naca'))
        <td>{{ $dag->code }}</td>
      @endif
      @if($dag->user->hasPermissionTo('uren o.b.v. getal'))
        <td>{{$dag->gewerkte_uren}}</td>
      @else
        <td>{{Carbon()->parse($dag->begintijd ?? '00:00')->format("H:i")}}</td>
        <td>{{Carbon()->parse($dag->eindtijd ?? '00:00')->format("H:i")}}</td>
      @endif

      @if(hasAccessTo('urenregistratie_pauzetijd'))
        <td class="td-toggle td-pauze">{{ $dag->pauze }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_ladenlossen'))
        <td>{{ $dag->ladenlossen_begintijd }}</td>
        <td>{{ $dag->ladenlossen_eindtijd }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_ladenlossen_middag'))
        <td>{{ $dag->ladenlossen_middag_begintijd }}</td>
        <td>{{ $dag->ladenlossen_middag_eindtijd }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_bestuurder'))
        <td>{{ $dag->bestuurder ? "Ja" : "Nee" }}</td>
        <td>{{ $dag->kenteken_id ? $dag->kenteken->kenteken : '-' }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_bijrijder'))
        <td>{{ $dag->bijrijder }}</td>
      @endif
      <td>@if ($dag->totaaldaguren100 == 0) @else {{ $dag->totaaldaguren100 }}  @endif</td>
      <td class="td-toggle td-125">@if ($dag->totaal_overuren125 == 0) @else {{ $dag->totaal_overuren125 }}  @endif</td>
      <td class="td-toggle td-128">@if ($dag->totaal_overuren128 == 0) @else {{ $dag->totaal_overuren128 }}  @endif</td>
      <td class="td-toggle td-150">@if ($dag->totaal_overuren150 == 0) @else {{ $dag->totaal_overuren150 }}  @endif</td>
      <td class="td-toggle td-165">@if ($dag->totaal_overuren165 == 0) @else {{ $dag->totaal_overuren165 }}  @endif</td>
      <td class="td-toggle td-200">@if ($dag->totaal_overuren200 == 0) @else {{ $dag->totaal_overuren200 }}  @endif</td>
      <td class="td-toggle td-minuren">@if ($dag->minuren == 0) @else {{ $dag->minuren }}  @endif</td>
      @if(hasAccessTo('urenregistratie_tijdvoortijd'))
        <td>@if ($dag->tijdvoortijd == 0) @else {{ $dag->tijdvoortijd }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_reisuren'))
        <td class="td-toggle td-reisuren">@if ($dag->reisuren == 0) @else {{ $dag->reisuren }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_km'))
        <td class="td-toggle td-kilometers">@if ($dag->kilometers == 0) @else {{ $dag->kilometers }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_woonwerkKm'))
        <td class="td-toggle td-woonwerkKm">@if ($dag->heenreis_woonwerk + $dag->terugreis_woonwerk == 0) @else {{ $dag->heenreis_woonwerk + $dag->terugreis_woonwerk }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_huisProjectKm'))
        <td class="td-toggle td-huisprojectKm">@if ($dag->heenreis_huisproject + $dag->terugreis_huisproject == 0) @else {{ $dag->heenreis_huisproject + $dag->terugreis_huisproject }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_overnachting'))
        <td class="td-toggle td-overnachting">@if ($dag->overnachting == null) @else {{ "Ja" }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_verlof'))
        <td class="td-toggle td-bijzonderverlofuren">@if ($dag->bijzonderverlof == 0) @else {{ $dag->bijzonderverlof }}  @endif</td>
        <td class="td-toggle td-roostervrij">@if ($dag->roostervrij == 0) @else {{ $dag->roostervrij }}  @endif</td>
        <td class="td-toggle td-verlofuren">@if ($dag->verlof == 0) @else {{ $dag->verlof }}  @endif</td>
        <td class="td-toggle td-verlofreden">{{ $dag->reden }}</td>
        <td class="td-toggle td-feesturen" >@if ($dag->feesturen == 0) @else {{ $dag->feesturen }}  @endif</td>
        <td class="td-toggle td-ziekteuren" >@if ($dag->ziekteuren == 0) @else {{ $dag->ziekteuren }}  @endif</td>
      @endif
      @if(hasAccessTo('urenregistratie_opmerkingen'))
        <td class="td-toggle td-opmerkingen">{{ $dag->opmerkingen }}</td>
      @endif
    </tr>
    @endforeach
    <tr>
      <td>Totaal : </td>
      <td>{{$weektotaal}}</td>
      @if(hasAccessTo('urenregistratie_naca'))
        <td></td>
      @endif
      <td @if(!$dag->user->hasPermissionTo('uren o.b.v. getal')) colspan="2" @endif>{{$week_totaal_ingevuld}}</td>

      @if(hasAccessTo('urenregistratie_pauzetijd'))
        <td class="td-toggle td-pauze">{{ $week_totaal_pauze }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_ladenlossen'))
        <td></td>
        <td></td>
      @endif
      @if(hasAccessTo('urenregistratie_ladenlossen_middag'))
        <td></td>
        <td></td>
      @endif
      @if(hasAccessTo('urenregistratie_bestuurder'))
        <td></td>
        <td></td>
      @endif
      @if(hasAccessTo('urenregistratie_bijrijder'))
        <td></td>
      @endif
      <td>{{$week_totaal_gewerkte_uren}}</td>
      <td class="td-toggle td-125">{{ $week_totaal_overuren125 }}</td>
      <td class="td-toggle td-128">{{ $week_totaal_overuren128 }}</td>
      <td class="td-toggle td-150">{{ $week_totaal_overuren150 }}</td>
      <td class="td-toggle td-165">{{ $week_totaal_overuren165 }}</td>
      <td class="td-toggle td-200">{{ $week_totaal_overuren200 }}</td>
      <td class="td-toggle td-minuren">{{ $week_minuren }}</td>
      @if(hasAccessTo('urenregistratie_tijdvoortijd'))
        <td>{{$week_tijdvoortijd}}</td>
      @endif
      @if(hasAccessTo('urenregistratie_reisuren'))
        <td class="td-toggle td-reisuren">{{$week_reisuren}}</td>
      @endif
      @if(hasAccessTo('urenregistratie_km'))
        <td class="td-toggle td-kilometers">{{$week_kilometers}}</td>
      @endif
      @if(hasAccessTo('urenregistratie_woonwerkKm'))
        <td class="td-toggle td-woonwerkKm">{{$week_woonwerkKmHeen + $week_woonwerkKmTerug}}</td>
      @endif
      @if(hasAccessTo('urenregistratie_huisProjectKm'))
        <td class="td-toggle td-huisProjectKm">{{$week_huisProjectKmHeen + $week_huisProjectKmTerug}}</td>
      @endif
      @if(hasAccessTo('urenregistratie_overnachting'))
        <td></td>
      @endif
      @if(hasAccessTo('urenregistratie_verlof'))
        <td class="td-toggle td-bijzonderverlofuren">{{ $week_bijzonderverlof }}</td>
        <td class="td-toggle td-roostervrij">{{ $week_roostervrij }}</td>
        <td class="td-toggle td-verlofuren">{{ $week_verlof }}</td>
        <td></td>
        <td class="td-toggle td-feesturen" >{{ $week_feesturen }}</td>
        <td class="td-toggle td-ziekteuren" >{{ $week_ziekteuren }}</td>
      @endif
      @if(hasAccessTo('urenregistratie_opmerkingen'))
        <td></td>
      @endif
    </tr>
  </table>
</div>
</div>
</div>
@endforeach
  @if (getSettingValue('urenregistratie_invoeren_machines') == 'aan')
  @foreach ($machines as $machine)
  <div class="card machines">
    <div class="card-content">
      <div class="mb-4" style="padding: 10px;">
        <table>
          <tr><td>Machine :&nbsp;</td><td>{{ $machine->name }}</td></tr>
          <tr><td><img width="50" height="50" class="mr-2" src="{{url("/client/public/img/machines/".$machine->icon.".png")}}"></td><td></td></tr>
        </table>
      </div>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Datum</th>
              <th>Medewerker</th>
              <th>Begintijd</th>
              <th>Eindtijd</th>
              <th style="width: 1000px;"></th>
            </tr>
          </thead>
          <tbody>
            @foreach ($controlemachines as $row)
            @foreach ($row->machineuren as $machineuur)
            @if ($machineuur->machine_id == $machine->id)
            @php
              if($machineuur->begintijd != null || $machineuur->eindtijd != null){
                $begintijd = $machineuur->begintijd;
                $eindtijd = $machineuur->eindtijd;
              }else{
                $begintijd = $row->begintijd;
                $eindtijd = $row->eindtijd;
              }
            @endphp
            <tr>
              <td><a href="{{ url('/uren/correction/'.\Carbon\Carbon::parse($row->datum)->format('d-m-Y').'/user/'.$row->medewerker_id) }}">{{ \Carbon\Carbon::parse($row->datum)->format('d-m-Y') }}</a></td>
              <td>{{$row->user->name}} {{$row->user->lastname}}</td>
              <td>{{\Carbon\Carbon::parse($begintijd)->format('H:i')}}</td>
              <td>{{\Carbon\Carbon::parse($eindtijd)->format('H:i')}}</td>
              <td></td>
            </tr>
            @endif
            @endforeach
            @endforeach
          </tbody>
        </table>
      </div>
    </div>
  </div>
  @endforeach
  @endif
</section>


@endsection
@section('script')
<script>
  var jaar = @json($jaar);
  var weeknummer = @json($weeknummer);
  var hide = @json($hide);

  $(document).ready(() => {
    _displaySelect['bvs'].onchange = selectBv;
    hideInit(true);
  })

  function hideInit(state){
    if(!Object.keys(hide).length){return;}

    for(const key in hide){
      $(`.td-${key}`).toggleClass('d-none', state);
    }

    $('.toggle-btn').html(`<a class="btn btn-inverse-success tippy" data-tippy-content="Verborgen items weergeven" onclick="hideInit(false)" >@icon_show</a>`)
    if(!state){
      $('.toggle-btn').html(`<a class="btn btn-inverse-danger tippy" data-tippy-content="Verborgen items verbergen" onclick="hideInit(true)" >@icon_hide</a>`)
    }
    tippyInit();
  }
  function display(jobtype) {
    $(`[data-display]`).removeClass('btn-dark').addClass('btn-inverse-dark');
    $(`[data-display='${jobtype}']`).removeClass('btn-inverse-dark').addClass('btn-dark');



    if(jobtype == 'alles'){

    }

    if(jobtype == 'kantoor'){
      $('#exportButton').attr('href', `${url}/uren/controlelijst/${jaar}/${weeknummer}/export/kantoor`)
      $('.kantoor').css({display: 'block'});
      $('.machines').css({display: 'none'});
      $('.buitendienst').css({display: 'none'});
    }
    else if (jobtype == 'buitendienst') {
      $('#exportButton').attr('href', `${url}/uren/controlelijst/${jaar}/${weeknummer}/export/buitendienst`)
      $('.kantoor').css({display: 'none'});
      $('.machines').css({display: 'none'});
      $('.buitendienst').css({display: 'block'});
    }
    else if (jobtype == 'machines') {
      $('#exportButton').attr('href', ``)
      $('.kantoor').css({display: 'none'});
      $('.buitendienst').css({display: 'none'});
      $('.machines').css({display: 'block'});
    }
    else {
      $('#exportButton').attr('href', `${url}/uren/controlelijst/${jaar}/${weeknummer}/export`)
      $('.kantoor').css({display: 'block'});
      $('.buitendienst').css({display: 'block'});
      $('.machines').css({display: 'block'});
    }
  }
  function selectBv(id){
    if(id == 'alle'){
      $(`.card`).css({display: 'block'});
    }else{
      $(`.card`).css({display: 'none'});
      $(`.card[data-bv-id='${id}']`).css({display: 'block'});
    }
  }
</script>
@endsection
