
@extends('layouts.app')

@section('title', 'Offertes')

@section('content')
@php
  $view = json_decode(getSettingValue('offertes_index_view'), true);
  $overzicht = json_decode((getSettingValue("custom_offertes_overzicht") ?? '[]'), true);
@endphp

<section class="append-loader" >

  <div style="display: none" data-batch-init-container class="w-100" >
    <div class="py-2">
      <div class="card p-2 m-0">
        <div class="flex-between" >
          <span>Aantal geselecteerde offertes: <b class="badge badge-lg badge-inverse-secondary font-size-09 text-dark" data-batch-amount >0</b></span>
          <div>
            <a class="btn btn-inverse-success" data-batch-preview-btn > @icon_check </a>
            <a class="btn btn-inverse-danger" data-batch-init > @icon_close </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <section class="my-3 h-px-100" data-stats-navigation data-rounded="true" >
    <div class="py-2 bg-modal z-index-999" data-content >
      <div class="card m-0 p-2">
        <div class="row">
          <div class="col select_search-container" >
            <div class="w-100" data-label >Groeperen op</div>
            <input type="hidden" name="sort_by" class="select_search-hidden-input" data-placeholder="Groeperen op">
            <div class="select_search-values" >
              <div class="select_search-box rounded-9 font-size-085">
                <span class="select_search-value nobr" data-value="all" data-name="Alles" data-selected="true" >Alles</span>
                <span class="select_search-value nobr" data-value="bv" data-name="bv">B.V.</span>
                <span class="select_search-value nobr" data-value="klant" data-name="klant">Klant</span>
                <span class="select_search-value nobr" data-value="vestiging" data-name="vestiging">Vestiging</span>
              </div>
            </div>
          </div>
          <div class="col select_search-container" data-sort-option-container >
            <div class="w-100" data-label >Optie</div>
            <input type="hidden" name="sort_option" class="select_search-hidden-input" data-placeholder="Selecteer optie">
            <div class="select_search-values" >
              <div class="select_search-box rounded-9 font-size-085">
                <span class="select_search-value nobr" data-option="" data-value="" data-name=""></span>
                @foreach(getBvs() as $bv)
                  <span class="select_search-value nobr" data-option="bv" data-value="{{$bv->id}}" data-name="{{$bv->name}}">{{$bv->name}}</span>
                @endforeach
                @foreach(getKlanten() as $klant)
                  <span class="select_search-value nobr" data-option="klant" data-value="{{$klant->id}}" data-name="{{$klant->title()}}">{{$klant->title()}}</span>
                @endforeach
                @foreach(getVestigingen() as $vestiging)
                  <span class="select_search-value nobr" data-option="vestiging" data-value="{{$vestiging->id}}" data-name="{{$vestiging->naam ?? $vestiging->plaats}}">{{$vestiging->naam ?? $vestiging->plaats}}</span>
                @endforeach
              </div>
            </div>
          </div>
          <div class="col select_search-container" >
            <div class="w-100" data-label >Template</div>
            <input type="hidden" name="sort_template" class="select_search-hidden-input" data-placeholder="Selecteer template">
            <div class="select_search-values" >
              <div class="select_search-box rounded-9 font-size-085">
                <span class="select_search-value nobr" data-value="" data-name="">Alle templates</span>
                  @foreach($templates as $template)
                    <span class="select_search-value nobr" data-value="{{$template->id}}" data-name="{{$template->naam}}">{{$template->naam}}</span>
                  @endforeach
              </div>
            </div>
          </div>
          <div class="col select_search-container" >
            <div class="w-100" data-label >Stadium</div>
            <input type="hidden" name="sort_stadium" class="select_search-hidden-input" data-placeholder="Selecteer stadium">
            <div class="select_search-values" >
              <div class="select_search-box rounded-9 font-size-085">
                  <span class="select_search-value nobr" data-value="" data-name="">Alle stadiums</span>
                  <span class="select_search-value nobr" data-value="Open" data-name="Open">Open</span>
                  <span class="select_search-value nobr" data-value="Afgerond" data-name="Afgerond">Afgerond</span>
                  <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
              </div>
            </div>
          </div>
          <div class="col select_search-container" >
            <div class="w-100" data-label >status</div>
            <input type="hidden" name="sort_status" class="select_search-hidden-input" data-placeholder="Selecteer status">
            <div class="select_search-values" >
              <div class="select_search-box rounded-9 font-size-085">
                  <span class="select_search-value nobr" data-value="" data-name="">Alle statussen</span>
                  <span class="select_search-value nobr" data-value="In aanmaak" data-name="In aanmaak">In aanmaak</span>
                  <span class="select_search-value nobr" data-value="Uitgebracht" data-name="Uitgebracht">Uitgebracht</span>
                  @if(hasModule('Accorderen'))
                    <span class="select_search-value nobr" data-value="Accorderen" data-name="Accorderen">Accorderen</span>
                  @endif
                  <span class="select_search-value nobr" data-value="Akkoord" data-name="Akkoord">Akkoord</span>
                  <span class="select_search-value nobr" data-value="Afgewezen" data-name="Afgewezen">Afgewezen</span>
                  <span class="select_search-value nobr" data-value="Verwijderd" data-name="Verwijderd">Verwijderd</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="my-3" >
    <div class="flex-between">
      <div class="w-100 mx-3 dropdown-divider "></div>
      <h4 class="nobr m-0" data-offertes-count >
        <div class="text-placeholder-md" ></div>
      </h4>
      @if(isIframe() && hasPermission('Offertes aanmaken') && getSettingValue('offertes_create_via_projects_only') != 'ja')
        <div class="w-50 mx-3 dropdown-divider "></div>
        <a class="btn text-primary btn-sm nobr font-size-12" href="{{url('iframe/offertes/select')}}" >Nieuwe offerte</a>
      @endif
      <div class="w-100 mx-3 dropdown-divider"></div>
    </div>
  </section>

  <section class="d-none mt-3" data-offertes-table-container>

    <div class="card rounded-pill rounded-m-unset">
      <div class="row flex-md-nowrap">
        <div class="col-md-8 col-12 flex-align flex-md-nowrap flex-wrap">
          <div class="flex-align mx-2 min-w-200 py-2" >
            <select class="form-select mr-2 rounded-pill" name="order_by">
              <option value="created_at">Offertenummer</option>
              <option value="naam">Offertenaam</option>
              <option value="sent_at">Verzenddatum</option>
              <option value="sent_to">Verzonden aan</option>
              <option value="mail_geopend">Mail geopend</option>
            </select>
            <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_offertes', 'DESC') == 'DESC') d-none @endif" data-order-direction="ASC" >@icon_asc</a>
            <a class="btn btn-light rounded-pill @if(_COOKIE('order_direction_offertes', 'DESC') == 'ASC') d-none @endif" data-order-direction="DESC" >@icon_desc</a>
            <input type="hidden" name="order_direction" value="{{_COOKIE('order_direction_offertes') ?? 'DESC'}}" >
          </div>
          <div class="form-control-divider mx-2"></div>
          <div class="ds-container flex-align bg-white shadow-none mx-2 px-0 py-2" data-display-select-container="pagination" data-can-be-empty >
            <select class="form-select mx-1 rounded-pill min-w-100" name="per_page">
              <option @if(($_COOKIE['offertes_per_page'] ?? null) === "25") selected @endif value="25" >25</option>
              <option @if(($_COOKIE['offertes_per_page'] ?? null) === "50") selected @endif value="50" >50</option>
              <option @if(($_COOKIE['offertes_per_page'] ?? null) === "100") selected @endif value="100" >100</option>
              <option @if(($_COOKIE['offertes_per_page'] ?? null) === "250") selected @endif value="250" >250</option>
            </select>
          </div>
        </div>
        <div class="col-md-4 col-12 flex-align">
          <div class="form-control-divider mx-2 d-md-block d-none"></div>
          <a class="btn btn-light rounded-pill" onclick="initOffertes()" >@icon_redo</a>
          <div class="w-100 overflow-visible mx-2 py-2">
            <div class="w-100" >
              <input type="text" class="form-control-custom rounded-pill" placeholder="Zoeken..." name="search" >
              <div class="position-relative">
                <div class="rounded-9 bg-white border font-size-085 p-2 position-absolute shadow w-100 z-index-9 d-none" data-search-results ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card my-2">
      <div class="overflow-auto py-2 my-2">
        <table class="offertes-table" >
          <thead>
          <tr>
            @isset($view['offertenummer'])
              <th>Offertenummer</th>
            @endisset
            @isset($view['offertenaam'])
              <th>Offertenaam</th>
            @endisset
            @isset($view['offertebedrag'])
              <th>Offertebedrag</th>
            @endisset
            @isset($view['klant'])
              <th>Klant</th>
            @endisset
            @isset($view['contactpersoon'])
              <th>Contactpersoon</th>
            @endisset
            @isset($view['locatie'])
              <th>Locatie</th>
            @endisset
            {{--                  @if($template->id != 'all' && isset($overzicht[$template->id]))--}}
            {{--                  @foreach($overzicht[$template->id] as $naam)--}}
            {{--                    <th>{{$naam}}</th>--}}
            {{--                  @endforeach--}}
            {{--                @endif--}}
            @isset($view['user'])
              <th>Uitgebracht door</th>
            @endisset
            @isset($view['slagingskans'])
              <th>Kans op Akkoord</th>
            @endisset
            @isset($view['status'])
              <th>Status</th>
            @endisset
            @if(hasModule('Accorderen') && getSettingValue('accorderen_flow_offertes'))
              <th>Akkoord</th>
            @endif
            @isset($view['sent_to'])
              <th>Verzonden aan</th>
            @endisset
            @isset($view['mail'])
              <th>Mail geopend</th>
            @endisset
            @if(hasModule('Actielijst') && getSettingValue("offertes_show_actie_opmerkingen") == 1)
              <th>Actielijst opmerkingen</th>
            @endif
            <th></th>
          </tr>
          </thead>
          <tbody data-offertes-tbody >
{{--            @if($template->id != 'all' && isset($overzicht[$template->id]))--}}
{{--              @foreach($overzicht[$template->id] as $keyword => $naam)--}}
{{--                @php $value = $offerte->keyword($keyword)->tekst ?? ''; @endphp--}}
{{--                <td @if(strtotime($value) != null && strtotime($value) != "") data-sort="{{\Carbon\Carbon::parse($value)->format('Y-m-d')}}" @endif>{{$value}}</td>--}}
{{--              @endforeach--}}
{{--            @endif--}}
          </tbody>
          <tfoot>
          <tr>
            @isset($view['offertenummer'])
              <th></th>
            @endisset
            @isset($view['offertenaam'])
              <th></th>
            @endisset
            @isset($view['offertebedrag'])
              <th>
                <div class="flex-between form-control-custom my-1" data-dt-resize-force >
                  <span class="w-px-50">Pag:</span>
                  <span class="w-100"><div class="flex-between"> <span class="mx-1">€</span> <span data-offerte-bedrag="page" ></span> </div></span>
                </div>
                <div class="flex-between form-control-custom my-1" data-dt-resize-force >
                  <span class="w-px-50">Tot:</span>
                  <span class="w-100"><div class="flex-between"> <span class="mx-1">€</span> <span data-offerte-bedrag="all" ></span> </div></span>
                </div>
              </th>
            @endisset
            @isset($view['klant'])
              <th></th>
            @endisset
            @isset($view['contactpersoon'])
              <th></th>
            @endisset
            @isset($view['locatie'])
              <th></th>
            @endisset
            {{--                @if($template->id != 'all' && isset($overzicht[$template->id]))--}}
            {{--                  @foreach($overzicht[$template->id] as $naam)--}}
            {{--                    <th></th>--}}
            {{--                  @endforeach--}}
            {{--                @endif--}}
            @isset($view['user'])
              <th></th>
            @endisset
            @isset($view['slagingskans'])
              <th></th>
            @endisset
            @isset($view['status'])
              <th></th>
            @endisset
            @if(hasModule('Accorderen') && getSettingValue('accorderen_flow_offertes'))
              <th></th>
            @endif
            @isset($view['sent_to'])
              <th></th>
            @endisset
            @isset($view['mail'])
              <th></th>
            @endisset
            @if(hasModule('Actielijst') && getSettingValue("offertes_show_actie_opmerkingen") == 1)
              <th></th>
            @endif
            <th></th>
          </tr>
          </tfoot>
        </table>
      </div>
    </div>

  </section>

</section>

{{--Modals--}}
<section>
  <!--Email Modal  -->
  <div class="modal fade" id="emailModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content bg-light">
        <form data-email-form method="post" enctype="multipart/form-data">
          <div class="modal-body">
            @csrf
            <div class="mb-2" >
              <div class="flex-between">
                <div>
                  @if(hasModule("Rapporten"))
                    <a class="btn btn-inverse-dark rounded-pill text-white tippy" data-tippy-content="Rapporten" data-email-attach-btn="rapporten"><i class="menu-icon fa fa-book-open mx-0"></i></a>
                  @endif
                  @if(hasModule('Bestanden'))
                    <a class="btn btn-inverse-dark rounded-pill text-white tippy" data-tippy-content="Bestanden" data-email-attach-btn="explorer"><i class="fas fa-folder-open m-0"></i></a>
                  @endif
                </div>
                <div>
                  <a class="btn btn-inverse-dark rounded-pill text-white" data-email-attach-btn="CC" >CC</a>
                  <a class="btn btn-inverse-dark rounded-pill text-white" data-email-attach-btn="BCC" >BCC</a>
                </div>
              </div>
              <div>
                <div data-email-attach-container="rapporten" class="d-none bg-white border my-2 overflow-auto p-2 rounded overflow-auto" >
                  <label>Rapporten</label>
                  <div class="my-2" >
                    <select name="verslagen[]" class="form-select" >
                      <option value="" disabled selected>Selecteer een rapport</option>
                      @foreach($verslagen as $verslag)
                        <option  value="{{$verslag->id}}">{{$verslag->naam}} @if(isset($verslag->klant)) | <small>{{$verslag->klant->naam}}</small>@endif</option>
                      @endforeach
                    </select>
                  </div>
                  <div id="mailRapporten"></div>
                  <div class="text-center my-2" >
                    <a class="btn btn-inverse-primary" onclick="addRapport()"><i class="fas fa-plus"></i></a>
                  </div>
                </div>
                <div data-email-attach-container="explorer" class="d-none bg-white border my-2 overflow-auto p-2 rounded" >
                  <label>Bestanden</label>
                  <div data-email-attach-content="explorer" ></div>
                  <div class="text-center my-2">
                    <a class="btn btn-inverse-primary" onclick="openExplorer()">@icon_cloud</a>
                    <a class="btn btn-inverse-primary tippy" data-tippy-content="Uploaden" onclick="openUploadExplorer()">@icon_upload</a>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="row">
                <div class="col-12 my-2">
                  <label>Afzender</label>
                  <input name="sender" placeholder="Afzender"  type="text" class="form-control-custom" required>
                </div>
                <div class="col-12 my-2 select_edit-container">
                  <label>Aan</label>
                  <input type="text" autocomplete="off" name="email" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>" required>
                  <div class="select_edit-values">
                    <div class="select_edit-box" id="offerte-emails"></div>
                  </div>
                </div>
                <div class="col-12 my-2 select_edit-container d-none" data-email-attach-container="CC">
                  <label>Cc</label>
                  <input type="text" autocomplete="off" name="ccemail" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>">
                  <div class="select_edit-values">
                    <div class="select_edit-box" id="offerte-ccs"></div>
                  </div>
                </div>
                <div class="col-12 my-2 select_edit-container d-none" data-email-attach-container="BCC">
                  <label>Bcc</label>
                  <input type="text" autocomplete="off" name="bccemail" class="select_edit-input form-select" placeholder="<EMAIL>, <EMAIL>">
                  <div class="select_edit-values">
                    <div class="select_edit-box" id="offerte-bccs"></div>
                  </div>
                </div>
                <div class="col-12 my-2">
                  <label>Onderwerp</label>
                  <input name="subject" id="subjectModal" placeholder="onderwerp" class="form-control-custom">
                </div>
                <div class="col-12 my-2">
                  <label class="flex-between my-1">
                    <span>Inhoud</span>
                    @php $emailTemplates = json_decode(getSettingValue('offertes_emails_templates') ?? '[]') @endphp
                    @if(countObject($emailTemplates))
                      <select class="form-select w-auto" data-email-content-select >
                        <option value="main" >Algemeen</option>
                        @foreach($emailTemplates as $string => $emailTemplate)
                          <option value="{{$string}}">{{$emailTemplate->name}}</option>
                        @endforeach
                      </select>
                    @endif
                  </label>
                  <textarea name="mess" id="contentModal" class="editor"></textarea>
                </div>
                @foreach($checkboxen as $key => $row)
                  <div class="col-12 my-2">
                    <input type="checkbox" name="checkboxen[]" class="cursor-pointer" value="{{$key}}"><span>&nbsp;{!! $row["inhoud"] !!}</span>
                  </div>
                @endforeach
                <div class="col-12 my-2">
                  <input type="checkbox" name="klantNAW" class="cursor-pointer" value="on" ><span>&nbsp;Vraag klant N.A.W.</span>
                </div>
                @if(hasModule('Facturatie (InforDB)') || hasModule('Facturatie'))
                  @if(getSettingValue("offertes_automatische_incasso") == "Aan")
                    <div class="col-12 my-2">
                      <input onchange="enableIncassant()" class="cursor-pointer" type="checkbox" name="incasso" id="incasso" value="on"><span>&nbsp;Automatische incasso</span>
                      <div id="incassantDiv" class="d-none row">
                        <div class="col-12 my-2">
                          <label>Kenmerk machtiging</label>
                          <input name="kenmerk" placeholder="Kenmerk" type="text" class="form-control">
                        </div>
                      </div>
                    </div>
                  @endif
                @endif
              </div>
              <div data-sent-emails ></div>
            </div>

          </div>
          <div class="modal-footer">
            <input type="submit" id="sendModal" class="btn btn-success" value="Versturen">
            <input type="hidden" id="offerteIdModal" name="offerteId">
          </div>
        </form>
      </div>
    </div>
  </div>
  {{--  Files modal--}}
  <div class="modal fade bd-example-modal-lg" id="filesModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-body">
          <div data-files-container ></div>
        </div>
        <div class="modal-footer" ></div>
      </div>
    </div>
  </div>
</section>


@endsection

@section('script')
    <script>
        var offertes = [];
        var templates = @json(resetIndex($templates));
        var verslagen = @json($verslagen);
        var vestigingen = @json(getVestigingen());
        var explorer_listener;
        let noload = getParameter('filter');
        var editors = {
          email: null,
        }

        const modules = {
          bestanden: @json(hasModule('Bestanden')),
          accorderen: @json(hasModule('Accorderen')),
          actielijst: @json(hasModule('Actielijst')),
        }
        const settings = {
          offertes_show_actie_opmerkingen: @json(getSettingValue("offertes_show_actie_opmerkingen") === '1'),
          offertes_afwijzing_reden: @json(getSettingCheckbox('offertes_afwijzing_reden')),
        }
        const data = {
          view: @json($view),
        }

        const _base = {
          request: null,
          table: null,
          total_count: 0,
          tbody: $('[data-offertes-tbody]'),
          dsi: null,
          all_ids: [],
          current_ids: [],
          per_page: () => { return $('[name=per_page]').val() },
          page: () => { return _displaySelect['pagination'].value },
          sort: () => { return $('[name=sort_by]').val() },
          order_by: () => { return $('[name=order_by]').val(); },
          order_direction: () => { return $('[name=order_direction]').val(); },
          sort_option: () => { return $('[name=sort_option]').val() },
          sort_status: () => { return $('[name=sort_status]').val() },
          sort_template: () => { return $('[name=sort_template]').val() },
          sort_stadium: () => { return $('[name=sort_stadium]').val() },
        }
        const _search = {
          request: null,
          container: $('[data-search-results]'),
          input: $('[name=search]'),
          timeout: null,
          results: [],
          ids: [],
          is_loading: () => { return !!($('[data-search-results]').find('[data-spinner]').length) }
        }
        const _this = {
          offerte: null,
          klant_data: {
            name: '',
            titel: '',
            achternaam: '',
          }
        }
        const _copy = {
          klanten: null,
          project: null,
          request: null,
          container: $('[data-copy-search-results]'),
          input: $('[name=copysearch]'),
          timeout: null,
          results: [],
          is_loading: () => { return !!($('[data-copy-search-results]').find('[data-spinner]').length) }
        }
        const _batch = {
          state: false,
          selected: {},
          editors: {},
        }


        pageInteractive(() => {
          editorInit('#contentModal')
            .then(editor => editors.email = editor);

          _base.dsi = _displaySelect['pagination'];
          _base.dsi.onchange = page => { initOffertes({page: page}) }
        })
        pageComplete(prefillGetValues)

        $(".templateIcons").click(function () {
          $(".templateIcons").each(function (){
            $(this).css({"transform":""})
          })
          $(this).css({"transform":"translateY(-10px)"})
        })

        $('[data-email-attach-btn]').click(function(){
          const btn = $(this);
          const target = btn.data('email-attach-btn');
          const container = $(`[data-email-attach-container='${target}']`);

          btn.toggleClass(['btn-inverse-dark', 'btn-dark']);
          container.toggleClass('d-none');

          if((target == 'BCC' || target == 'CC')){
            container.find('.select_edit-input').val('');
          }
        })
        $('[data-email-content-select]').change(function(){
          try{
            const { offerte } = _this;

            const klant = offerte.klanten;
            const main = @json(getSettingValue('offerte_email_content'));
            const select = @json(json_decode(getSettingValue('offertes_emails_templates') ?? '[]'));

            const value = $(this).val();

            let content = main;
            if(value != 'main'){
              if(!select[value]){
                notification('Er is iets foutgegaan!');
                return;
              }
              content = select[value].content;
            }

            editors.email.setData(content || '');
            emailContentKeywords();
          }
          catch (e) { handleCatchError(e); }
        });
        $('[data-email-form]').submit(function(e){
          const data = $(this).serializeArray();
          const id = data.find(row => row.name == 'offerteId').value;
          const offerte = offertes.find(row => row.id == id);

          if(!id || !offerte){
            notification('Er is iets foutgegaan!');
            return false;
          }

          if(!editors.email.getData()){notification('Inhoud is verplicht!', 'warning'); return false;}
          data.find(row => row.name == 'mess').value = editors.email.getData();

          loader();
          hideModal('emailModal');

          ajax('/api/offertes/send', data)
            .then(response => {
              try{
                successLoader();

                offerte.status = 'Verzonden';

                $(`.status-${id}`).html(`<span class="badge badge-primary" >Uitgebracht</span>`)
                $(`.sent-${id}`).html(`
                  <small class="d-block">${now().date.eu} ${now().time}</small>
                  <small class="d-block" >${response.emails.join(', ')}</small>
                `)
                $(`.sent-status-${id}`).html(`<i class="bi bi-hourglass-split h4 mx-0"></i>`);
                notification(`Offerte ${offerte.offertenummer} verstuurd aan ${response.emails.join(', ')}!`, 'success', 5);
              }
              catch (e) {
                actError(e);
                notification('Er is iets foutgegaan, offerte is wel verstuurd!', 'warning');
              }
            })
            .catch(handleCatchError);

          return false;
        });

        $(document).on('click', '[data-batch-init]', function(){
          _batch.state = !_batch.state;

          $(`[data-batch-init='main']`).toggleClass(['btn-light', 'btn-dark'])
          $('[data-batch-init-container]').toggle(250);

          if(!_batch.state){
            _batch.selected = {};
            $('[data-batch-checkbox-toggle]').toggle(250);
            $('[data-batch-amount]').html(0);

            setTimeout(() => {
              $('[data-batch-checkbox-container]').remove();
            }, 250)

            return
          }

          $('.offertes-table').find('tr').each(function(){
            const id = $(this).attr('data-offerte-tr');
            const offerte = offertes.find(offerte => offerte.id == id);

            //Append checkbox when: It's select all, !Accorderen module or Has accorderen and succeeded
            let checkbox = '<input type="checkbox" class="form-check-custom mx-3" disabled >'
            if(!id){
              checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="all" >`
            }
            else if(!offerte?.accorderen?.isActive || offerte?.accorderen?.succeeded){
              checkbox = `<input type="checkbox" class="form-check-custom mx-3" data-batch-checkbox="${id}" >`
            }

            $(this).prepend(`<td data-batch-checkbox-container class="p-0 w-0" > <div data-batch-checkbox-toggle style="display: none"> ${checkbox} </div> </td>`)
          })

          $('[data-batch-checkbox-toggle]').toggle(250);

        });
        $(document).on('click', '[data-batch-preview-btn]', function(){

          if(!Object.keys(_batch.selected).length){
            notification('Geen offertes geselecteerd!', 'warning');
            return;
          }

          let tabs = '';
          for(const id in _batch.selected){
            const offerte = offertes.find(offerte => offerte.id == id);

            if(!offerte){ continue; }

            const { contactpersoon, klanten, token, offertenummer, files } = offerte;

            let klant_data;
            if(contactpersoon){
              klant_data = {
                name:`${contactpersoon.voornaam || ''} ${contactpersoon.achternaam || ''}`,
                titel: contactpersoon.titel,
                achternaam: contactpersoon.achternaam,
              }
            }
            else{
              klant_data = {
                name:`${klanten.contactpersoon_voornaam || ''} ${klanten.contactpersoon_achternaam || ''}`,
                titel: klanten.contactpersoon_titel,
                achternaam: klanten.contactpersoon_achternaam,
              }
            }

            let content =  @json(getSettingValue('offerte_email_content') ?? '');
            let sender =  @json(getSettingValue('offerte_afzender'));
            let subject = getEmailSubject(offerte);
            let receiver = getEmailReceiver(offerte);

            content = emailContentKeywordsReplace(content, offerte, klant_data)
            tabs += `<div class="py-1 form-tab" data-batch-tab="${offerte.id}" >
                    <div class="border rounded bg-white" >

                      <div class="flex-between px-2" >
                        <div class="d-flex align-items-center " >
                            <div data-batch-tab-indicator></div>
                            <span>${offertenummer}</span>
                        </div>
                        <div class="flex-align">
                          <a href="${url}/offertes/token/${token}" target="_blank" class="btn btn-inverse-primary btn-sm py-1 rounded-pill" >PDF</a>
                          <a class="btn" data-preview-tab-toggle >@icon_down</a>
                        </div>
                      </div>

                      <form data-preview-form style="display: none" class="w-100">
                        <div class="row m-0" style="background-color: rgba(0, 0, 0, 0.03)">

                          <div class="col-12">
                            ${ files.map(file => {
                              return `<div class="flex-align my-2 text-muted" >
                                        <img src="${url}/client/public/img/explorer/files/${file.icon}" height="25" >
                                        <span>${file.display_name}</span>
                                        <input type="hidden" name="explorer_files[]" value="${file.id}" >
                                      </div>`
                            }).join('') }
                          </div>

                          <div class="col-md-6 col-12 my-2">
                            <label>Afzender*</label>
                            <input data-sender name="sender" class="form-control-custom" placeholder="Afzender" value="${sender || ''}" >
                          </div>
                          <div class="col-md-6 col-12 my-2">
                            <label>Aan*</label>
                            <input data-email name="email" class="form-control-custom" placeholder="<EMAIL>, <EMAIL>" value="${receiver}" >
                          </div>
                          <div class="col-12 my-2">
                            <label>Onderwerp*</label>
                            <input data-subject name="subject" class="form-control-custom" placeholder="Onderwerp" value="${subject}" >
                          </div>
                          <div class="col-12 my-2">
                            <label>Inhoud</label>
                            <textarea data-content data-id="${id}" name="mess" class='batch-editor-${id}' placeholder="Inhoud" >${content || ''}</textarea>
                          </div>

                          <input type="hidden" name="offerteId" value="${id}" >

                        </div>
                      </form>

                    </div>
                </div>`
          }

          tabs += '<div class="my-2 text-right" data-batch-action  > <a class="btn btn-success text-white" data-batch-send> Versturen </a> </div>'

          confirmModal({
            text: tabs,
            hideFooter: true,
            large: true,
          })
            .then(() => {
              $(`[data-batch-init='main']`).trigger('click');
            })

          $('[data-preview-form] [data-content]').each(function(){
            const id = $(this).data('id');
            editorInit(`.batch-editor-${id}`)
                    .then(editor => _batch.editors[id] = editor);
          })
        })
        $(document).on('click', '[data-preview-tab-toggle]', function(){
          const container = findContainer('form-tab', this);

          container.find('[data-preview-tab-toggle]').rotate(180)
          container.find('[data-preview-form]').toggle(250);
        });
        $(document).on('click', '[data-batch-send]', async function(){

          let ready = true;
          const forms = {};
          $('[data-batch-tab-indicator]').empty();

          for(const id in _batch.selected){
            const offerte =  offertes.find(offerte => offerte.id == id);
            const container = $(`[data-batch-tab="${offerte.id}"]`);

            let data = container.find('[data-preview-form]').serializeArray();

            data.push({
              name: '_token',
              value: csrf
            })
            data.find(row => row.name == 'mess').value =_batch.editors[id].getData();

            const requiredSet = {
              email: false,
              sender: false,
              subject: false,
              mess: false,
            }
            for(const input of data){
              if(input.name == 'email' && input.value != ''){ requiredSet.email = true; }
              if(input.name == 'sender' && input.value != ''){ requiredSet.sender = true; }
              if(input.name == 'subject' && input.value != ''){ requiredSet.subject = true; }
              if(input.name == 'mess' && input.value != ''){ requiredSet.mess = true; }
            }

            if(!requiredSet.email || !requiredSet.sender || !requiredSet.subject || !requiredSet.mess){
              ready = false;
              container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-danger" > @icon_close </div>')
            }

            forms[offerte.id] = data;
          }

          if(!ready){
            notification('Vul alle verplichte velden in!', 'warning', 5);
            return;
          }

          for(const id in _batch.selected){
            if(_batch.selected[id] == 'sending'){ continue }

            _batch.selected[id] = 'sending'

            const container = $(`[data-batch-tab="${id}"]`);
            const offerte = offertes.find(offerte => offerte.id == id);
            const data = forms[id];

            container.find('[data-batch-tab-indicator]').html('<div class="mx-2" > @spinner_small </div>')

            try{
              const response = await ajax('api/offertes/send', data);
              delete _batch.selected[id];
              $(`[data-batch-tab=${id}]`).remove();
              notification(`Offerte <b>${offerte.offertenummer}</b> verstuurd aan ${response.emails.join(', ')}!`, 'success', 5);
              initOffertes();
            }
            catch(err){
              _batch.selected[id] = 'true'
              container.find('[data-batch-tab-indicator]').html('<div class="mx-2 text-warning" > @icon_exclamation </div>')
            }

          }
        });
        $(document).on('change', '[data-batch-checkbox]', function(){
          const id = $(this).data('batch-checkbox');
          const checked = $(this).prop('checked');

          if(id == 'all'){

            //No need to verify status when unchecking, uncheck all and return
            if(!checked){
              $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').prop('checked', false).trigger('change');
              return;
            }

            $(`[data-batch-checkbox]`).not('[data-batch-checkbox=all]').each(function(){
              const id = $(this).data('batch-checkbox');
              const offerte = offertes.find(offerte => offerte.id == id);

              if(['Afgerond', 'Verwijderd'].includes(offerte?.status) || offerte?.status == 'Uitgebracht'){ return true; }

              $(this).prop('checked', checked).trigger('change')
            });

            return;
          }

          delete _batch.selected[id];
          if(checked){
            _batch.selected[id] = true
          }

          $('[data-batch-amount]').html(Object.keys(_batch.selected).length);
        });

        $(document).on('change', '[data-copy-form] [name=project]', function(){
          const form = $('[data-copy-form]');

          const meerwerk_container = form.find('[data-copy-meerwerk-container]')
          const meerwerk_switch = form.find('[name=meerwerk]');

          meerwerk_container.removeClass('opacity-50').find('[data-copy-forced-meerwerk-label]').remove();
          meerwerk_switch.attr('onclick', '');

          if (!this.value){ return; }
          const project = _copy.project;
          if(!project){
            notification('Er is iets foutgegaan!');
            return;
          }

          if(project.klant_id){
            $('[data-copy-klanten-container]').find(`[data-value=${project.klant_id}]`).click();
          }
          if(project.offerte){
            meerwerk_switch.prop('checked', true).attr('onclick', 'return false');
            meerwerk_container.addClass('opacity-50').append(`<div data-copy-forced-meerwerk-label class="font-size-075 my-2" > Project <b>${project.projectnr}</b> heeft al een geaccordeerde offerte <b>${project.offerte.offertenummer}</b>, u kunt alleen meerwerk offertes toevoegen. </div>`)
          }

        });
        $(document).on('change', '[data-copy-form] [name=klant]', function(){
          const form = $('[data-copy-form]');
          const cp = form.find('[name=contactpersoon]');
          const locatie = form.find('[name=locatie]');
          const btn =

          cp.empty().attr('disabled', true);
          locatie.empty().attr('disabled', true);

          if (!this.value){ return; }

          const klant = _copy.klanten.find(row => row.id == this.value);

          const { contactpersoon_voornaam, contactpersoon_achternaam, contactpersonen } = klant;
          if(contactpersoon_voornaam || contactpersoon_achternaam){
            cp.append(`<option value="0" >${contactpersoon_voornaam} ${contactpersoon_achternaam}</option>`)
          }
          for(const contact of contactpersonen){
            cp.append(`<option value="${contact.id}" >${contact.voornaam || ''} ${contact.achternaam || ''}</option>`)
          }

          const { straat, postadres_straat, huisnummer, postadres_huisnummer, toevoeging, postadres_toevoeging, plaats, postadres_plaats, locaties } = klant;
          if(straat || plaats){
            locatie.append(`<option value="bezoekadres" >${straat || ''} ${huisnummer || ''}${toevoeging || ''}, ${plaats}</option>`)
          }
          if(postadres_straat || postadres_plaats){
            locatie.append(`<option value="postadres" >${postadres_straat || ''} ${postadres_huisnummer || ''}${postadres_toevoeging || ''}, ${postadres_plaats || ''}</option>`);
          }
          for(const adres of locaties){
            locatie.append(`<option value="${adres.id}" >${adres.straat || ''} ${adres.huisnummer || ''}${adres.toevoeging || ''}, ${adres.plaats || ''}</option>`);
          }

          cp.attr('disabled', cp.is(':empty'));
          locatie.attr('disabled', locatie.is(':empty'));
        });
        $(document).on('submit', '[data-copy-form]', function(e){
          e.preventDefault();

          const form = $(this);

          const data = serializeArrayToObject(form.serializeArray());

          if (!data.klant){
            notification('Selecteer een klant!', 'warning');
            return;
          }

          loader();
          form.find('[type=submit]').attr('disabled', true);

          ajax('api/offertes/copy', data)
            .then(response => {
              successLoader();
              notification(`Offerte ${response.offertenummer} aangemaakt! `, 'success');
              hideModal('dynamic-confirm-modal');
              initOffertes();
            })
            .catch(err =>{
              errorLoader();

              form.find('[type=submit]').attr('disabled', false);
              if (err?.responseJSON?.message){
                notification(err.responseJSON.message);
              }
            });

        })

        $(document).on('click', '[data-copy-project-id]', function(){
          const targetid = $(this).data('copy-project-id');
          const targetname = $(this).data('copy-project-nr');
          getProjecten({ids: [targetid]}).then(response => {
            _copy.project = response.projecten[0];
            _copy.input.val(targetname);
            $('[data-copy-form] [name=project]').val(targetid);
            $('[data-copy-form] [name=project]').trigger('change');
          });
        });
        $(document).on('input', '[name=copysearch]', async function(){
          _copy.input = $(this);
          _copy.container = $('[data-copy-search-results]');
          _copy.is_loading = () => { return !!($('[data-copy-search-results]').find('[data-spinner]').length) }
          let { container, timeout, is_loading } = _copy;
          if (timeout){
            clearTimeout(timeout);
            timeout = null;
          }

          if(!is_loading()){
            container.removeClass('d-none').html(`<div class="text-center py-2" data-spinner >@spinner</div>`);
          }

          if(!this.value){
            container.addClass('d-none').empty();
            _copy.input.val('');
            return;
          }


          _copy.timeout = setTimeout(findCopyProjecten, 100);
        })

        $(document).on('click', function(){
          _search.container.addClass('d-none').empty();
          _copy.container.addClass('d-none').empty();
        });
        $(document).on('click', '[data-search-by-results]', function(){
          const target = $(this).data('search-by-results');

          $('[name=sort_by]').val('all');
          $('[data-sort-option-container]').find('.select_search-input').val('').prop('disabled', true);
          $('[data-sort-option-container]').find('.select_search-hidden-input').val('');

          $('[name=sort_template]').val('')
          $('[name=sort_stadium]').val('')
          $('[name=sort_status]').val('')

          correctSelectSearchDisplay();

          _search.ids = [];
          if (target == 'all'){
            _search.results.map(project => _search.ids.push(Number(project.id)));
          }
          else{
            _search.ids.push(target);
          }

          initOffertes();
        });
        $(document).on('input', '.select_search-hidden-input', correctSelectSearchDisplay);
        $('[data-order-direction]').click(function(){
          const selected = $(this).data('order-direction');
          const target = selected == 'ASC' ? 'DESC' : 'ASC';

          $('[data-order-direction]').addClass('d-none');
          $(`[data-order-direction=${target}]`).removeClass('d-none');
          $('[name=order_direction]').val(target).trigger('change');

          setCookie('order_direction_offertes', target)
        });
        $('[name=search]').on('input', async function(){
          let { container, timeout, is_loading } = _search;

          if (timeout){
            clearTimeout(timeout);
            timeout = null;
          }

          if(!is_loading()){
            container.removeClass('d-none').html(`<div class="text-center py-2" data-spinner >@spinner</div>`);
          }

          if(!this.value){
            container.addClass('d-none').empty();
            _search.ids = null;
            _search.input.val('');
            prefillGetValues();
            return;
          }


          _search.timeout = setTimeout(findOffertes, 100);
        })
        $('[name=sort_by]').change(function(){
          const value = this.value || 'all';

          _search.ids = null;
          _search.input.val('');


          const container = $('[data-sort-option-container]');
          container.find('.select_search-input').val('').prop('disabled', false);
          container.find('.select_search-value').addClass('d-none');

          if(value == 'all'){
            initOffertes()
            container.find('.select_search-input').val('').prop('disabled', true);
            return;
          }

          container.find(`[data-option=${value}]`).removeClass('d-none');
        });
        $('[name=per_page], [name=order_by], [name=order_direction]').change(function(){
          setCookie('offertes_per_page', _base.per_page());
          initOffertes()
        });
        $('[name=sort_option], [name=sort_status], [name=sort_template], [name=sort_stadium]').change(() => {
          _search.ids = null;
          _search.input.val('');

          setCookie('offertes_sort_template', _base.sort_template());

          initOffertes()
        })


        function prefillGetValues(){
          if (getParameter('sort_by') && getParameter('sort_option')){
            $('[name=sort_by]').val(getParameter('sort_by')).trigger('change');
            $(`[data-option=${getParameter('sort_by')}][data-value=${getParameter('sort_option')}]`).click();
          }
          if (getParameter('status')){
            $('[name=sort_status]').val(getParameter('status'));
          }
          if (getParameter('stadium')){
            $('[name=sort_stadium]').val(getParameter('stadium'));
          }
          if(getCookie('offertes_sort_template')){
            $('[name=sort_template]').val(getCookie('offertes_sort_template'));
          }

          correctSelectSearchDisplay();
          initOffertes();
        }
        function initOffertes(options = {}){
          if (noload){
            $('[data-offertes-table-container]').removeClass('d-none');
            noload = false;
            return;
          }
          loader();

          const page = options?.page || 1;

          const data = {
            ids: _search.ids,
            page: page,
            paginate: _base.per_page(),
            status: _base.sort_status(),
            template: _base.sort_template(),
            stadium: _base.sort_stadium(),
            sort_by: _base.order_by(),
            sort_type: _base.order_direction(),
            budget: true,
            relations: ['projectTemp.manager', "klanten", "template.roles", "template.subTemplate.roles", "user", "editor", "contactpersoon", "locatie", "actie_opmerkingen", 'project', 'all_emails'],
            relations_lite: {
              project: null,
            }
          }

          if(_base.sort() && _base.sort_option()){
            data[_base.sort()] = _base.sort_option();
          }
          if(_base.request){
            _base.request.abort();
            _base.request = null;
          }

          _base.request = ajax('/api/offertes/get', data);
          _base.request
            .always(() => {
              _base.request = null;
            })
            .then(response => {
              offertes = response.offertes;
              _base.total_count = response.total_count;
              _base.all_ids = response.all_ids;
              _base.current_ids = response.current_ids;

              fillOffertes();
              refreshPagination(options?.page);
              totOfferteAmount();
              clearLoader();
            })
            .catch(handleCatchError);
        }
        function fillOffertes(){
          const { table, tbody } = _base;
          const { view } = data;

          if(table){ table.destroy(); }

          tbody.empty();

          for (const offerte of offertes) {
            const {id, created_at, offertenummer, naam, klanten, contactpersoon, _locatie, user, slagingskans, status, color, template_id,
                   accorderen, sent_at, sent_to, mail_geopend, actie_opmerkingen, stadium, versions, beeing_edited_till, editor, token, beslisdatum, afwijzing_reden, has_budget
                  } = offerte;
            const template = templates.find(template => template.id == template_id);

            let templateHTML = '';
            if(template.roles.some(role => role.id == _user.role_id)){
              templateHTML = `<a class="dropdown-item" href="${url}/offertes/token/${token}" target="_blank">Offerte</a>`
            }

            const filteredSubTemplates = template.sub_template.filter(sub_template =>
              sub_template.roles.some(role => role.id == _user.role_id)
            );

            let details_export = '';
            template.details.forEach(detail => {
              if(!Number(detail.excel_export)){ return true; }
              details_export += `<a class="dropdown-item cursor-pointer" href="${url}/offertes/details/export/excel/${detail.id}/${token}" target="_blank">${detail.name} Excel</a>`
            });

            const subTemplatesHTML = filteredSubTemplates.map(sub_template =>
              `<a class="dropdown-item d-flex align-items-center" href="${url}/offertes/token/${token}?subtemplate=${sub_template.id}" target="_blank">${sub_template.naam}</a>`
            ).join('');

            tbody.append(`
              <tr data-offerte-tr="${id}" >
                ${ view['offertenummer']
                  ? `<td data-sort="${now(created_at).timestamp}" class="nobr" > ${offertenummer}</td>`
                  : ''
                }
                ${ view['offertenaam']
                  ? `<td  class="nobr" > ${naam}</td>`
                  : ''
                }
                ${ view['offertebedrag']
                  ? `<td>
                        <a data-offerte-amount="${id}" >  <div class="text-placeholder" ></div> </a>
                    </td>`
                  : ''
                }
                ${ view['klant']
                  ? `<td>
                      <a class="text-primary cursor-pointer" onclick="showKlant(${klanten.id})" >${klanten.naam || ''}</a>
                      <span class="d-block text-muted">${klanten.telefoonnummer || ''}</span>
                    </td>`
                  : ''
                }
                ${ view['contactpersoon']
                  ? `<td>
                      ${ contactpersoon
                      ? `<a class="text-primary cursor-pointer" onclick="showKlant(${klanten.id})" >${contactpersoon.voornaam || ''} ${contactpersoon.achternaam || ''}</a>
                        <span class="d-block text-muted">${contactpersoon.telefoon || ''}</span>
                        <span class="d-block text-muted">${contactpersoon.mobiel || ''}</span>`
                      : `<a class="text-primary cursor-pointer" onclick="showKlant(${klanten.id})" >${klanten.contactpersoon_voornaam || ''} ${klanten.contactpersoon_achternaam || ''}</a>
                        <span class="d-block text-muted">${klanten.contactpersoon_telefoon || ''}</span>
                        <span class="d-block text-muted">${klanten.contactpersoon_mobiel || ''}</span>`
                      }
                    </td>`
                  : ''
                }
                ${ view['locatie']
                  ? `<td>
                      <nobr class="d-block" >${_locatie.straat || ''} ${_locatie.huisnummer || ''}${_locatie.toevoeging || ''}</nobr>
                      <nobr class="d-block" >${_locatie.postcode || ''} ${_locatie.plaats || ''}</nobr>
                    </td>`
                  : ''
                }
                ${ view['user']
                  ? `<td>${user?.name || ''} ${user?.lastname || ''}</td>`
                  : ''
                }
                ${ view['slagingskans']
                  ? `<td>${slagingskans || '0'}</td>`
                  : ''
                }
                ${ view['status']
                  ? `<td class="status-${id}" >
                      <nobr class="badge badge-${color} ${beslisdatum ? 'tippy' : ''}" ${beslisdatum ? `data-tippy-content="${now(beslisdatum).date.eu} ${now(beslisdatum).time} ${afwijzing_reden ? `<br>Reden: ${afwijzing_reden}` : ''}"` : ''}>${status}</nobr>
                    </td>`
                  : ''
                }
                ${ accorderen.isActive
                  ? `<td data-sort="${now(accorderen?.updated_at).timestamp}" data-accorderen-nodes="${id}" > ${accorderen?.nodes || ''} </td>`
                  : ''
                }
                ${ view['sent_to']
                  ? `<td data-sort="${sent_at}" class="sent-${id}" >
                      ${ sent_to
                        ? `<small class="d-block">${ now(sent_at).date.eu } ${ now(sent_at).time }</small>
                          <small class="d-block" >${ sent_to }</small>`
                        : `<i class="bi bi-dash h4 mx-0"></i>`
                      }
                    </td>`
                  : ''
                }
                ${ view['mail']
                  ? `<td class="sent-status-${id} text-center" >
                      ${ sent_to
                        ? `
                          ${ mail_geopend
                            ? `<i data-tippy-content="${now(mail_geopend).date.eu} ${now(mail_geopend).time}" class="tippy bi bi-check2 h3 mx-0"></i>`
                            : `<i class="bi bi-hourglass-split h4 mx-0"></i>`
                          }
                        `
                        : `<i class="bi bi-dash h4 mx-0"></i >`
                      }
                    </td>`
                  : ''
                }
                ${ settings.offertes_show_actie_opmerkingen
                  ? `<td>
                      <ul>
                        ${ actie_opmerkingen.map(opmerking => {
                          return `<li>${opmerking.opmerking || ''}</li>`
                        }).join('') }
                      </ul>
                    </td>`
                  : ''
                }
                <td>

                  ${ stadium == 'Open'
                    ? `<div class="btn-group d-block">
                          <button type="button" class="btn btn-success dropdown-toggle btn-block" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Acties
                          </button>
                          <div class="dropdown-menu">
                          ${template.sub_template.length > 0
                                ?
                                `
                                  <a class="dropdown-item-toggle" data-toggle-sub-dropdown="pdf_offertes" data-dropdown-target="${id}"><span>PDF's</span> @icon_down</a>
                                  <div data-sub-dropdown="pdf_offertes" data-sub-dropdown-id="${id}" style="display: none">
                                    <div class="py-2">
                                      <div class="cursor-pointer">
                                        ${templateHTML}
                                        ${subTemplatesHTML}
                                      </div>
                                    </div>
                                  </div>
                                `
                                : `<a class="dropdown-item cursor-pointer" href="${url}/offertes/token/${token}" target="_blank">PDF</a>`
                              }
                            ${details_export}
                            ${ !beeing_edited_till || now(beeing_edited_till).timestamp < now().timestamp
                              ? `<a class="dropdown-item cursor-pointer" href="${url}${_global.is_iframe ? '/iframe' : ''}/offertes/edit/${id}" >Wijzigen</a>`
                              : `<a class="dropdown-item cursor-pointer text-muted tippy" data-tippy-content="<div>Offerte wordt momenteel gewijzigd door ${editor?.name || ''} ${editor?.lastname || ''}</div><div><small>Volgende check: ${now(beeing_edited_till).time}</small></div>" >Wijzigen</a>`
                            }
                            ${ !accorderen.isActive || accorderen.succeeded || has_budget
                              ? `<a class="dropdown-item cursor-pointer" onclick="emailModal(${id})" data-toggle="modal" data-target="#emailModal">Versturen</a>`
                              : ``
                            }
                            ${modules.bestanden ? `<a class="dropdown-item cursor-pointer" onclick="openExplorerPath({path: '/Offertes/(${id})', callback: initOffertes})">Bijlagen</a>` : ''}
                            <a class="dropdown-item" onclick="copyOfferte(${id})">Kopiëren</a>
                            <a class="dropdown-item" onclick="confirmStatus(${id}, 'Verwijderd')">Verwijderen</a>
                          </div>
                        </div>
                        ${versions.length
                          ? `<div class="btn-group d-block">
                              <button type="button" class="btn btn-primary dropdown-toggle btn-block" data-toggle="dropdown">Versies</button>
                              <div class="dropdown-menu">
                                <a class="dropdown-item text-success" href="${url}/offertes/token/${token}" target="_blank" >${offertenummer}</a>
                                ${versions.map(version => {
                                  return `<div class="dropdown-item flex-between mx--2" >
                                            <a class="text-black text-decoration-none mx-2" href="${url}/offertes/token/${version.token}" target="_blank" >${version.offertenummer}</a>
                                            <a class="text-black text-decoration-none mx-2 tippy" data-tippy-content="Instellen als huidige versie" onclick="setAsCurrentVersion(${version.id})" > <i class="fa-solid fa-code-branch m-0"></i> </a>
                                          </div>`
                                }).join('')}
                              </div>
                            </div>`
                          : ``
                        }
                        ${!accorderen.isActive || accorderen.failed || accorderen.succeeded || !accorderen.inProcess
                          ? `<div class="btn-group d-block" data-status-btn="${id}">
                              <button type="button" class="btn btn-warning dropdown-toggle btn-block" data-toggle="dropdown">Status</button>
                              <div class="dropdown-menu">
                                ${!accorderen.isActive || accorderen.succeeded || has_budget
                                  ? `${status == 'In aanmaak' ? `<a class="dropdown-item cursor-pointer" onclick="uitgebracht(${id})" >Uitgebracht</a>` : ''}
                                    <a class="dropdown-item" onclick="confirmStatus(${id}, 'Akkoord')">Akkoord</a>
                                    <a class="dropdown-item" onclick="confirmStatus(${id}, 'Afgewezen')">Afgewezen</a>`
                                  : `<a class="dropdown-item" onclick="confirmStatus(${id}, 'Accorderen')" data-accorderen-btn="${id}">Accorderen</a>`
                                }
                              </div>
                            </div>`
                          : ``
                        }`
                    : ''
                  }
                  ${ stadium == 'Afgerond'
                     ? `<div class="btn-group d-block">
                          <button type="button" class="btn btn-success dropdown-toggle btn-block" data-toggle="dropdown">Acties</button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item cursor-pointer" href="${url}${_global.is_iframe ? '/iframe' : ''}/offertes/edit/${id}/true" >Inzien</a>
                             ${template.sub_template.length > 0
                                ?
                                `
                                  <a class="dropdown-item-toggle" data-toggle-sub-dropdown="pdf_offertes" data-dropdown-target="${id}"><span>PDF's</span> @icon_down</a>
                                  <div data-sub-dropdown="pdf_offertes" data-sub-dropdown-id="${id}" style="display: none">
                                    <div class="py-2">
                                      <div class="cursor-pointer">
                                        ${templateHTML}
                                        ${subTemplatesHTML}
                                      </div>
                                    </div>
                                  </div>
                                `
                                : `<a class="dropdown-item cursor-pointer" href="${url}/offertes/token/${token}" target="_blank">PDF</a>`
                             }
                            ${modules.bestanden ? `<a class="dropdown-item cursor-pointer" onclick="openExplorerPath({path: '/Offertes/(${id})'})">Bijlagen</a>` : ''}
                            <a class="dropdown-item" onclick="copyOfferte(${id})">Kopiëren</a>
                            <a class="dropdown-item" href="${url}/offertes/reset/${token}">Terugzetten</a>
                          </div>
                        </div>
                        ${versions.length
                          ? `<div class="btn-group d-block">
                                        <button type="button" class="btn btn-primary dropdown-toggle btn-block" data-toggle="dropdown">Versies</button>
                                        <div class="dropdown-menu">
                                          <a class="dropdown-item text-success" href="${url}/offertes/token/${token}" target="_blank" >${offertenummer}</a>
                                          ${versions.map(version => {
                                            return `<div class="dropdown-item flex-between" >
                                                      <a class="text-black text-decoration-none" href="${url}/offertes/token/${version.token}" target="_blank" >${version.offertenummer}</a>
                                                    </div>`
                                          }).join('')}
                                        </div>
                                      </div>`
                          : ``
                        }`
                     : ``
                  }
                  ${ stadium == 'Verwijderd'
                     ? `<div class="btn-group d-block ">
                          <button type="button" class="btn btn-success dropdown-toggle btn-block" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Acties</button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="${url}/offertes/token/${token}" target="_blank">PDF</a>
                            ${modules.bestanden ? `<a class="dropdown-item cursor-pointer" onclick="openExplorerPath({path: '/Offertes/(${id})'})">Bijlagen</a>` : ''}
                            <a class="dropdown-item" href="${url}/offertes/enable/${id}" >Activeer</a>
                          </div>
                        </div>
                        ${versions.length
                          ? `<div class="btn-group d-block">
                                        <button type="button" class="btn btn-primary dropdown-toggle btn-block" data-toggle="dropdown">Versies</button>
                                        <div class="dropdown-menu">
                                          <a class="dropdown-item text-success" href="${url}/offertes/token/${token}" target="_blank" >${offertenummer}</a>
                                          ${versions.map(version => {
                                            return `<div class="dropdown-item flex-between" >
                                                      <a class="text-black text-decoration-none" href="${url}/offertes/token/${version.token}" target="_blank" >${version.offertenummer}</a>
                                                    </div>`
                                          }).join('')}
                                        </div>
                                      </div>`
                          : ``
                        }`
                     : ``
                  }


                </td>
              </tr>
            `);
          }

          initTable();
          appendDTButton();
          appendBatchButton();
          tippyInit();
        }
        function initTable(){
          $('[data-offertes-table-container]').removeClass('d-none');
          _base.table = tableInit('.offertes-table', {
            paging: false,
            ordering: false,
            searching: false,
            info: false,
            columnDefs: [{orderable: false, targets: [-1]}],
          })
        }
        function refreshPagination(page){

          const { dsi, total_count } = _base;
          const pages_count = Math.ceil((_base.total_count || 1) / (_base.per_page() || (_base.total_count || 1))) // if per_page == '' (no pagination) the pages_count must be 1, also when empty;

          if(!page){
            for(const node of dsi.values()){
              $(`[data-display-select-container='pagination']`).find(`.ds-node[data-value=${node.value}]`).remove();
            }
            for(let i = 1; i <= pages_count; i++){
              dsi.addValue({name: i, value: i});
            }
            dsi.silentSelect(1);
          }
          $('[data-offertes-count]').html(`${total_count} ${(total_count !== 1) ? 'OFFERTES' : 'OFFERTE'}`);
        }
        async function findOffertes(){
          let { container } = _search;
          const value = $('[name=search]').val()

          if (_search.request){
            _search.request.abort();
            _search.request = null;
          }

          _search.request = searchOffertes(value);
          _search.request
            .then(offertes => {

              _search.results = offertes;
              _search.timeout = null;
              if (!offertes.length){
                container.html(`<div class='text-center text-muted py-2' >Geen resultaten gevonden</div>`)
                return;
              }

              container.empty();
              for(let i = 0; i <5; i++){
                if(!offertes[i]){ break; }

                const offerte = offertes[i];
                const { id, offertenummer, naam } = offerte;

                container.append(`
                  <div class="cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5" data-search-by-results='${id}' >
                    <div class="font-size-09" >${offertenummer || ''}</div>
                    <div class='text-muted font-size-075' >${naam || ''}</div>
                  </div>
                `);
              }
              container.append(`<div class='cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5 text-center' data-search-by-results='all' >Alle resultaten weergeven ( ${offertes.length} )</div>`);
            })
            .catch(err => {
              if(err?.status === 0){ console.log('aborted'); return }
              if (err?.responseJSON?.message){ notification(err?.responseJSON?.message); }
              actError(err);
            })
        }

        function totOfferteAmount(){
          $('[data-offerte-bedrag]').html(`<div class="text-placeholder" ></div>`);

          ajax('api/offertes/total', {ids: _base.current_ids})
            .then(response => {
              $('[data-offerte-bedrag="page"]').html(toPrice(response?.total?.excl));

              for(const id in response.offertes){
                const total = response.offertes[id];
                const offerte = findOfferte(id);
                const element =  $(`[data-offerte-amount=${id}]`);

                const tippy_content = `<table>
                                        <tr>
                                          <td class='pr-3 text-left' >Excl. BTW:</td>
                                          <td class='pr-1' >€</td>
                                          <td class='text-right' >${toPrice(total.excl)}</td>
                                        </tr>
                                        <tr>
                                          <td class='pr-3 text-left' >BTW:</td>
                                          <td class='pr-1' >€</td>
                                          <td class='text-right' >${toPrice(total.btw)}</td>
                                        </tr>
                                        <tr>
                                          <th class='pr-3 text-left border-top' >Incl. BTW:</th>
                                          <th class='pr-1 border-top' >€</th>
                                          <td class='text-right border-top' >${toPrice(total.incl)}</td>
                                        </tr>
                                      </table>`

                element.attr('data-tippy-content', tippy_content).html(`€ ${toPrice(total.excl)}`);
                tippyInit();
              }
            })
            .catch(err => {
              if (err?.responseJSON?.message){ notification(err?.responseJSON?.message); }
              // actError(err);
            })

          ajax('api/offertes/total', {ids: _base.all_ids})
            .then(response => {
              $('[data-offerte-bedrag="all"]').html(toPrice(response?.total?.excl));
              tippyInit();
            })
            .catch(err => {
              if (err?.responseJSON?.message){ notification(err?.responseJSON?.message); }
              actError(err);
            })
        }

        function uitgebracht(id){
          confirmModal({
            text: `<div>
                    <label>Verzonden aan</label>
                    <input name="uitgebracht_verzonden_aan" class="form-control-custom my-1" placeholder="E-mail">
                    <div class="flex-between my-1 mx--1">
                      <input name="uitgebracht_verzonden_date" class="form-control-custom mx-1" type="date" value="${now().date.us}">
                      <input name="uitgebracht_verzonden_time" class="form-control-custom mx-1" type="time" value="${now().time}" >
                    </div>
                  </div>`
          }).then((res) => {
            if(!res.status){return}

            loader();

            const to = $('[name=uitgebracht_verzonden_aan]').val();
            const date = $('[name=uitgebracht_verzonden_date]').val();
            const time = $('[name=uitgebracht_verzonden_time]').val();

            $.ajax({
              type: "POST",
              url: url+'/api/offertes/status',
              data: {
                status: 'Uitgebracht',
                sent_to: to,
                date: date,
                time: time,
                offerte: id,
                _token: "{{csrf_token()}}",
              },
              success: function () {
                successLoader();
                $(`.sent-${id}`).html(
                  `<small class="d-block">${convert(date)} ${time || ''}</small>
                  <small class="d-block" >${to}</small>`
                )
                $(`.status-${id}`).html(
                  `<nobr class="badge badge-primary" >
                    Uitgebracht
                  </nobr>`);
              },
              error: function () {
                errorLoader();
              }
            });
          })
        }
        function confirmStatus(id, status){
          const offerte = offertes.find(row => row.id == id);

          if(!offerte){
            notification('Er is iets foutgegaan!');
            return;
          }
          let html = `Weet je zeker dat je de status van <b>${offerte.offertenummer}</b> naar <b>${status}</b> wilt veranderen?`;
          if(status == 'Afgewezen' && settings.offertes_afwijzing_reden){
            html += `<div class="mt-2">
                      <label>Geef de reden aan waarom de offerte is afgewezen:</label>
                      <textarea name="afwijzing_reden" class="form-control-custom" placeholder="Reden"></textarea>
                    </div>`
          }
          confirmModal({
            text: html
          })
            .then(response => {
              if(!response.status){ return; }
              const afwijzing_reden = $('[name=afwijzing_reden]').val();
              setStatus(offerte, status, afwijzing_reden);
            })

        }
        function setStatus(offerte, status, afwijzing_reden = null){
          loader();
          ajax('/api/offertes/status', {
            offerte: offerte.id,
            status: status,
            afwijzing_reden: afwijzing_reden,
          })
            .then(response => {
              initOffertes();
            })
            .catch(err => {
             errorLoader();
             actError(err);
            })
        }

        function selectVerslag(id){
          if(selectedVersalgen[id]){
            delete selectedVersalgen[id];
            $("#verslag"+id).removeClass("bg-inverse-success")
          }
          else{
            selectedVersalgen[id] = true;
            $("#verslag"+id).addClass("bg-inverse-success")
          }
        }
        function emailModal(id){
          try{
            let offerte = findOfferte(id);

            $('input[name=email]').val('');
            $("#mailRapporten").html('');
            $(`[name='verslagen[]']`).val('')
            $('[data-email-content-select]').val('main')
            $("#bijlagenRow").html('<div class="col-md-6 col-12 my-2"><input type="file" name="files[]" class="dropify" data-max-file-size="10M"></div>');
            $(`[data-email-attach-content='explorer']`).empty();

            $('[data-email-attach-btn]').removeClass('btn-dark').addClass('btn-inverse-dark');
            $('[data-email-attach-container]').addClass('d-none');

            $('#offerteIdModal').val(offerte.id);
            $('#subjectModal').val(getEmailSubject(offerte));
            $('[data-sent-emails]').removeClass('d-none');
            $('[data-sent-emails]').addClass('d-inline');
            let options = '';
            let ccoptions = '';
            let optionalEmail;
            if(offerte.klanten.naam || offerte.klanten.email){
              if(offerte.klanten.email){optionalEmail = offerte.klanten.email;}
              options += `<span onclick="changeContactpersoon(0)" class="select_edit-value d-flex justify-content-between align-items-center" data-value="${offerte.klanten.email || ''}">
                          <span>${offerte.klanten.naam || ''}</span>
                          <small class="text-muted" >${offerte.klanten.email}</small>
                        </span>`
              ccoptions += `<span class="select_edit-value d-flex justify-content-between align-items-center" data-value="${offerte.klanten.email}">
                          <span>${offerte.klanten.naam || ''}</span>
                          <small class="text-muted" >${offerte.klanten.email}</small>
                        </span>`
            }
            if(offerte.klanten.contactpersoon_email || offerte.klanten.contactpersoon_voornaam || offerte.klanten.contactpersoon_achternaam){
              if(offerte.klanten.contactpersoon_email){optionalEmail = offerte.klanten.contactpersoon_email;}
              options += `<span onclick="changeContactpersoon(0)" class="select_edit-value d-flex justify-content-between align-items-center" data-value="${offerte.klanten.contactpersoon_email || ''}">
                          <span>${offerte.klanten.contactpersoon_voornaam || ''} ${offerte.klanten.contactpersoon_achternaam || ''}</span>
                          <small class="text-muted" >${offerte.klanten.contactpersoon_email || ''}</small>
                        </span>`
              ccoptions += `<span class="select_edit-value d-flex justify-content-between align-items-center" data-value="${offerte.klanten.contactpersoon_email}">
                          <span>${offerte.klanten.contactpersoon_voornaam || ''} ${offerte.klanten.contactpersoon_achternaam || ''}</span>
                          <small class="text-muted" >${offerte.klanten.contactpersoon_email || ''}</small>
                        </span>`
            }
            for(const contactpersoon of offerte.klanten.contactpersonen){
              if(!contactpersoon.email && !contactpersoon.voornaam && !contactpersoon.achternaam){continue;}
              if(contactpersoon.email){optionalEmail = contactpersoon.email;}

              _this.klant_data = {
                name: contactpersoon.voornaam+" "+contactpersoon.achternaam,
                titel: contactpersoon.titel,
                achternaam: contactpersoon.achternaam,
              }

              options += `<span onclick="changeContactpersoon(${contactpersoon.id})" class="select_edit-value d-flex justify-content-between align-items-center" data-value="${contactpersoon.email || ''}">
                          <span>${contactpersoon.voornaam || ''} ${contactpersoon.achternaam || ''}</span>
                          <small class="text-muted">${contactpersoon.email || ''}</small>
                        </span>`
              ccoptions += `<span class="select_edit-value d-flex justify-content-between align-items-center" data-value="${contactpersoon.email}">
                          <span>${contactpersoon.voornaam || ''} ${contactpersoon.achternaam || ''}</span>
                          <small class="text-muted">${contactpersoon.email || ''}</small>
                        </span>`
            }

            if(offerte.contactpersoon_id == null || offerte.contactpersoon_id == '0'){
              _this.klant_data = {
                name: (offerte.klanten.contactpersoon_voornaam || '')+" "+(offerte.klanten.contactpersoon_achternaam || ''),
                titel: offerte.klanten.contactpersoon_titel,
                achternaam: offerte.klanten.contactpersoon_achternaam,
              }
            }
            else{
              for(const contactpersoon of offerte.klanten.contactpersonen){
                if(contactpersoon.id == offerte.contactpersoon_id){
                  _this.klant_data = {
                    name: (contactpersoon.voornaam || '')+" "+(contactpersoon.achternaam || ''),
                    titel: contactpersoon.titel,
                    achternaam: contactpersoon.achternaam,
                  }
                  break;
                }
              }
            }

            $('[name=ccemail]').val('');
            $('[name=bccemail]').val('');
            $("input[name=email]").val(getEmailReceiver(offerte));

            $("#offerte-emails").html(options);
            $("#offerte-ccs").html(ccoptions);
            $("#offerte-bccs").html(ccoptions);

            for(const file of offerte.files){
              insertFile(file);
            }
            if(offerte.files.length){
              $(`[data-email-attach-btn='explorer']`).click();
            }

            let content =  @json(getSettingValue('offerte_email_content') ?? '');
            let sender = @json(getSettingValue('offerte_afzender'));

            $(`[name=sender]`).val(sender || '');
            if(!$(`[name=email]`).val()){
              $(`[name=email]`).val(optionalEmail || '');
            }

            _this.offerte = offerte;

            $('[data-sent-emails]').html(`
              <div class="flex-between my-2" >
                  <span>Verzonden emails</span>
                  <a class="btn btn-sm btn-light" onclick="$('[data-sent-emails-container]').toggle(300); $('[data-sent-emails-icons]').toggleClass('rotate-180')" >
                      <i class="fa fa-chevron-down m-0 transition-03" data-sent-emails-icons ></i>
                  </a>
              </div>
              <div class="w-100" style="display: none" data-sent-emails-container >
                  <div data-sent-emails-container-content ></div>
              </div>
            `);

            if(!offerte.all_emails?.length){ $('[data-sent-emails-container-content]').html('<div class="text-muted font-italic" >Geen emails gevonden!</div>') }
            for(const mail of offerte.all_emails){
              const string = randomString();
              $('[data-sent-emails-container-content]').append(`
            <div class="bg-inverse-secondary rounded py-1 px-2 my-1" >
              <div class="flex-between" >
                <div class="d-flex align-items-center" >
                  <span>${mail.subject}</span>
                </div>
                <a class="btn btn-sm" onclick="$('[data-sent-email-container=${string}]').toggle(300); $('[data-sent-email-icons=${string}]').toggleClass('rotate-180')" >
                    <i class="fa fa-chevron-down m-0 transition-03" data-sent-email-icons="${string}" ></i>
                </a>
            </div>
            <div class="w-100" style="display: none" data-sent-email-container="${string}" >
                <div class="font-size-09" data-sent-email-container-content="${string}" >
                  <div><b>Datum:</b> ${convert(mail.date)} ${mail.time.slice(0, 5)} </div>
                  <div><b>Afzender:</b> ${mail.sender || ''} </div>
                  <div><b>Aan:</b> ${JSON.parse(mail.emails || '[]').join(', ')} </div>
                  <div class="mt-2" >${mail.message}</div>
                </div>
            </div>
          </div>
          `)
            }

            editors.email.setData(content);
            dropifyInit();
            emailContentKeywords();
          }
          catch (e) {
            notification(`Er is iets foutegegaan! ${e.message}`);
            actError(e);
          }
        }
        function getEmailSubject(offerte){
          let subjProjName = @json(getSettingCheckbox('offerte_projnaam_in_subj'));
          return `Offerte: ${offerte.offertenummer} ${subjProjName ? (offerte.project_temp ? ' - '+offerte.project_temp.projectnaam : '') : ''}`;
        }
        function getEmailReceiver(offerte){
          let email = '';

          try{

            if(offerte.contactpersoon_id == null){
              email =  offerte.klanten.email;
            }
            else if(offerte.contactpersoon_id == '0'){
              email =  offerte.klanten.contactpersoon_email
            }
            else{
              for(const contactpersoon of offerte.klanten.contactpersonen){
                if(contactpersoon.id == offerte.contactpersoon_id){
                  email =  contactpersoon.email;
                }
              }
            }

            if(!email){
              if(offerte.klanten.email){
                email = offerte.klanten.email
              }
              else if(offerte.klanten.contactpersoon_email){
                email = offerte.klanten.contactpersoon_email
              }
              else {
                for(const contact of offerte.klanten.contactpersonen){
                  if(contact.email){ email = contact.email; }
                }
              }
            }

            return email;
          }
          catch (e) {
            actError(e);
            return '';
          }
        }

        function emailContentKeywords(){
          const { offerte, klant_data } = _this;

          let content = emailContentKeywordsReplace(editors.email.getData(), offerte, klant_data);
          editors.email.setData( content );
        }
        function emailContentKeywordsReplace(content, offerte, klant_data){
          if(!content){ return ''; }

          const klant = offerte.klanten;
          const project = offerte.project_temp;

          content = content.replaceAll("&lt;{klant}&gt;", klant_data.name || '');
          content = content.replaceAll("&lt;{klant.titel}&gt;", klant_data.titel || '');
          content = content.replaceAll("&lt;{klant.achternaam}&gt;", klant_data.achternaam || '');
          content = content.replaceAll("&lt;{bv}&gt;", offerte.template._bv.name)
          content = content.replaceAll("&lt;{user}&gt;", _user.name || '');
          content = content.replaceAll("&lt;{user-phone}&gt;", _user.phone || '');
          content = content.replaceAll("&lt;{user-email}&gt;", _user.email);
          content = content.replaceAll("&lt;{user-function}&gt;", _user.functie || '');

          const  klant_vestiging = {
            adres: '',
            phone: '',
          }
          if(klant?.vestiging_id){
            const vestiging = vestigingen.find(row => row.id == klant.vestiging_id);
            if(vestiging){
              const { straat, huisnummer, toevoeging, postcode, plaats, telefoon} = vestiging;

              klant_vestiging.adres = `${straat || ''} ${huisnummer || ''}${toevoeging || ''},<br>${postcode || ''} ${plaats || ''}`
              klant_vestiging.phone = telefoon || '';
            }
          }
          content = content.replaceAll("&lt;{klant-vestiging-adres}&gt;", klant_vestiging.adres);
          content = content.replaceAll("&lt;{klant-vestiging-phone}&gt;", klant_vestiging.phone);

          const project_vestiging = {
            adres: '',
            phone: ''
          }
          if(project?.vestiging_id){
            const vestiging = vestigingen.find(row => row.id == project.vestiging_id);
            if(vestiging){
              const { straat, huisnummer, toevoeging, postcode, plaats, telefoon} = vestiging;
              project_vestiging.adres = `${straat || ''} ${huisnummer || ''}${toevoeging || ''},<br>${postcode || ''} ${plaats || ''}`
              project_vestiging.phone = telefoon || '';
            }
          }
          content = content.replaceAll("&lt;{project-vestiging-adres}&gt;", project_vestiging.adres);
          content = content.replaceAll("&lt;{project-vestiging-phone}&gt;", project_vestiging.phone);

          const project_manager = {
            full_name: '',
            phone: '',
            email: '',
            functie: '',
          }
          if(project?.manager){
            const { name, lastname, functie, phone, email } = project.manager;

            project_manager.full_name = `${name || ''} ${lastname || ''}`;
            project_manager.phone = phone || '';
            project_manager.email = email || '';
            project_manager.functie = functie || '';
          }
          content = content.replaceAll("&lt;{project-manager}&gt;", project_manager.full_name);
          content = content.replaceAll("&lt;{project-manager-phone}&gt;", project_manager.phone);
          content = content.replaceAll("&lt;{project-manager-email}&gt;", project_manager.email);
          content = content.replaceAll("&lt;{project-manager-function}&gt;", project_manager.functie);

          return content
        }
        function changeContactpersoon(contact_id){
          const klant = _this.offerte.klanten;

          if(!contact_id){
            _this.klant_data = {
              name: `${klant.contactpersoon_voornaam || ''} ${klant.contactpersoon_achternaam}`,
              titel: klant.contactpersoon_titel,
              achternaam: klant.contactpersoon_achternaam,
            }
          }
          else{
            const contact = klant.contactpersonen.find(contact => contact.id == contact_id);
            _this.klant_data = {
              name: `${contact.voornaam || ''} ${contact.achternaam}`,
              titel: contact.titel,
              achternaam: contact.achternaam,
            }
          }

          emailContentKeywords();
        }
        function enableIncassant(){
          $("#incassantDiv").toggleClass("d-none");
        }
        function addRapport(){
          let string = randomString(15)
          $("#mailRapporten").append(
            '<select class="form-select mb-2" name="verslagen[]" id="rapporten'+string+'" >' +
              '<option disabled selected >Selecteer een rapport</option>' +
            '</select>'
          );
          for(let r in verslagen){
            let rapport = verslagen[r];
            let name = rapport.naam;
            if(rapport.klant){
              name += " | " + rapport.klant.naam ? rapport.klant.naam : rapport.klant.contactpersoon_voornaam + " " + rapport.klant.contactpersoon_achternaam
            }
            $("#rapporten"+string).append(
              '<option value="'+rapport.id+'">'+name+'</option>'
            )
          }
        }

        function openExplorer(){
          getExplorerFile()
            .then((response) => {
              if(!response.status){ return }
              insertFile(response.file);
            })
        }
        async function openUploadExplorer(){
          hideModal('emailModal');
          const response = await uploadExplorerFile({ path: `/Offertes/(${_this.offerte.id})/bijlagen` });
          showModal('emailModal');

          if(!response.status){ return; }
          insertFile(response.file);
        }
        function insertFile(file){
          appendExplorerFile({
            file: file,
            id: `[data-email-attach-content='explorer']`,
            name: 'explorer_files[]'
          })

        }
        function findOfferte(id){
          for(let offerte of offertes){
            if(offerte.id == id){return offerte;}
          }
          return false;
        }

        function setAsCurrentVersion(id){
          let current = null;
          let version = null;



          for(const offerte of offertes ){
            version = offerte.versions.find(row => row.id == id);
            if(version){
              current = offerte;
              break;
            }
          }

          if(!version || !current){
            notification('Er is iets foutgegaan!', 'danger');
            return;
          }

          let nummer = current.offertenummer.split('.')[0];

          confirmModal({
            text: `<div>Weet je zeker dat je de offerte <b>${nummer}</b> wilt terugzetten naar versie <b>${version.versie}</b>?</div>
                   <div class="mt-2 text-center" > <small class="text-muted" >Alle bovenliggende versies zullen vervallen.</small> </div>`
          })
            .then(res => {
              if(!res.status){ return; }

              loader()
              ajax('api/offertes/set-version-as-current', {id: version.id})
                .then(response => {
                  successLoader();
                  location.reload();
                })
                .catch(err => errorLoader());

            })

        }

        async function copyOfferte(id){
          const offerte = findOfferte(id);

          if(!_copy.klanten){
            confirmModal({
              text: `<div class="text-center my-5" >
                    <div>@spinner</div>
                    <div class="my-1 text-primary" >Klanten worden opgehaald</div>
                  </div>`,
              hideFooter: true
            })
            _copy.klanten = resetIndex((await getKlanten()).klanten);
          }

          const { klanten, projecten } = _copy;
          confirmModal({
            text: `<form data-copy-form data-main-form >
                    <div class="mb-2" >
                      <label>Offerte</label>
                      <input type="text" class="form-control-custom" value="${offerte.offertenummer}" readonly >
                      <input type="hidden" name="offerte" value="${id}" >
                    </div>
                    <div class="my-2" >
                      <label>Project</label>
                      <input type="text" class="form-control-custom" placeholder="Zoeken..." name="copysearch" >
                      <input type="hidden" class="form-control-custom" placeholder="Zoeken..." name="project" >
                      <div class="position-relative">
                        <div class="rounded-9 bg-white border font-size-085 p-2 position-absolute shadow w-100 z-index-9 d-none" data-copy-search-results ></div>
                      </div>
                    </div>
                    <div class="select_search-container my-2" data-copy-klanten-container >
                      <label>Klant*</label>
                      <input type="hidden" name="klant" class="select_search-hidden-input" data-placeholder="Klant" data-required="required">
                      <div class="select_search-values" >
                        <div class="select_search-box" >
                            ${klanten.map(klant => `<span class="select_search-value" data-value="${klant.id}" data-name="${klant.titel}">${klant.titel}</span>`).join('')}
                        </div>
                      </div>
                    </div>
                    <div class="my-2" data-copy-cp-container>
                        <label>Contactpersoon</label>
                        <select name="contactpersoon" class="form-select" disabled ></select>
                    </div>
                    <div class="my-2" data-copy-locatie-container >
                        <label>Locatie</label>
                        <select name="locatie" class="form-select" disabled ></select>
                    </div>
                    <div class="my-3" data-copy-meerwerk-container >
                        <label class="m-0 cursor-pointer" > <input type="checkbox" class="form-switch-custom" name="meerwerk" > Meerwerk offerte </label>
                    </div>
                    <div class="mt-4 text-center"> <input type="submit" class="btn btn-success" value="Kopiëren" > </div>
                  </form>`,
            hideFooter: true
          });

          initSelectSearch();
        }
        async function findCopyProjecten(){
          let { container } = _copy;
          const value = $('[name=copysearch]').val()

          if (_copy.request){
            _copy.request.abort();
            _copy.request = null;
          }

          _copy.request = searchProjecten(value);
          _copy.request
                  .then(projecten => {

                    _copy.results = projecten;
                    _copy.timeout = null;
                    if (!projecten.length){
                      container.html(`<div class='text-center text-muted py-2' >Geen resultaten gevonden</div>`)
                      return;
                    }

                    container.empty();
                    for(let i = 0; i <5; i++){
                      if(!projecten[i]){ break; }
                      const project = projecten[i];

                      container.append(`
            <div class="cursor-pointer hover-bg-inverse-secondary px-2 py-1 rounded-5" data-copy-project-id='${project.id}' data-copy-project-nr='${project.projectnr}' >
                <div class='font-size-09' >${project.projectnr || ''}</div>
              <div class='text-muted font-size-075' >${project.projectnaam || ''}</div>
            </div>
          `);
                    }
                  })
                  .catch(err => {
                    if(err?.status === 0){ console.log('aborted'); return }
                    if (err?.responseJSON?.message){ notification(err?.responseJSON?.message); }
                    actError(err, 'findProjecten()');
                  })
        }

        function appendBatchButton(){
          $('.dataTables_filter').append('<a class="btn btn-light ml-1" data-batch-init="main" ><i class="fa-solid fa-paper-plane m-0"></i></a>');
        }

    </script>
@endsection

