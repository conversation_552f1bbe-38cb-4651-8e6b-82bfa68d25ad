  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" href="https://infordb.ikbentessa.nl/client/public/img/favicon32x32.ico" sizes="32x32"
        type="image/x-icon">
  <link rel="icon" href="https://infordb.ikbentessa.nl/client/public/img/favicon16x16.ico" sizes="16x16"
        type="image/x-icon">
  <title>@yield('title') &middot; {{ env('APP_NAME') }}</title>
  <link rel="stylesheet" type="text/css"
        href="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.11.3/b-2.0.1/b-colvis-2.0.1/b-html5-2.0.1/b-print-2.0.1/datatables.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.0/croppie.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-BmbxuPwQa2lc/FVzBcNJ7UAyJxM6wuqIj61tLrc4wSX0szH/Ev+nYRRuWlolflfl" crossorigin="anonymous">
  <link rel="stylesheet"
        href="{{ asset('client/admin/assets/vendors/iconfonts/mdi/css/materialdesignicons.min.css') }}">
  <link rel="stylesheet" href="{{ asset('client/admin/assets/vendors/iconfonts/puse-icons-feather/feather.css') }}">
  <link rel="stylesheet" href="{{ asset('client/admin/assets/vendors/css/vendor.bundle.base.css') }}">
  <link rel="stylesheet" href="{{ asset('client/admin/assets/vendors/css/vendor.bundle.addons.css') }}">
  <link rel="stylesheet" href="{{ asset('client/admin/assets/css/shared/style.css') }}">
  <link rel="stylesheet" href="{{ asset('client/admin/assets/css/demo_1/style.css') }}">
  <link rel="stylesheet" href="{{ asset('client/css/app.css') }}{{cacheClear()}}">
  <link rel="stylesheet" href="{{ url('client/public/css/yearpicker.css') }}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Dropify/0.2.2/css/dropify.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/codemirror.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/addon/hint/show-hint.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jsonview@1.2.0/dist/jquery.jsonview.min.css">
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />

  <link href='https://inforcdn.com/library/cdn/minify/<EMAIL>' rel='stylesheet' crossorigin='anonymous' integrity='sha256-66Fo/1zMXw9pyODWbe0Fy0Z34wkJYLdxB1z4lowsRDA='>
  <link href='https://inforcdn.com/library/cdn/minify/<EMAIL>' rel='stylesheet' crossorigin='anonymous' integrity='sha256-4HTteT4aMHp7y0ET+qemHpFxLbobxS2MUBC76NU+4NA='>
  <link href='https://inforcdn.com/library/cdn/minify/<EMAIL>' rel='stylesheet' crossorigin='anonymous' integrity='sha256-P0CAHvSKAD2sqkNnOYOlraxEs0qH0RZXTI86cvhjVr4='>
  <link href='https://inforcdn.com/library/cdn/minify/<EMAIL>' rel='stylesheet' crossorigin='anonymous' integrity='sha256-3FZCVS47olcpmXE5BgRpDC2WxKYx5uix2h+9E6vLyxw='>
  <link href='https://inforcdn.com/library/cdn/minify/<EMAIL>' rel='stylesheet' crossorigin='anonymous' integrity='sha256-kleEiIWJZLgjknL/rLrv49oN4IFPrkwRBbdpK1eOOlc='>  {{--  Dynamic CSS--}}
  @if(stringContains(currentRoute(), '/offertes/edit/') || stringContains(currentRoute(), '/offertes/create/'))
    <link class="css-dynamic" rel="stylesheet" data-css="offertes.css"
          href="@if(isset($_COOKIE['dynamic-css-offertes'])){{ asset('client/css/offertes.css') }}@endif">
  @endif

  {{--  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.23/css/jquery.dataTables.css">--}}
  <script>
    var api_token = `{{_get('api_token')}}`;
    var csrf = `{{csrf_token()}}`;
    var url = `{{url('/')}}`;
    var ENV = {
      GOOGLE_MAPS: '{{env("GOOGLE_MAPS")}}'
    }
    var _global = {
      route: @json(currentRoute()),
      auth: @json(Auth::check()),
      subdomain: @json(getSubdomein()),
      exact_globe_connected: @json(exactGlobe(['no_curl' => true])->connected),
      exact_online_connected: @json(exactOnlineLocal()->connected),
      king_connected: @json(kingConnected()),
      zenvoices_connected: @json(zenvoicesConnected()),
      business_central_connected: @json(businessCentralLocal()->connected),
      eboekhouden_connected: @json(isEboekhouden()),
      tweeba_connected: @json(tweebaLocal()->connected),
      is_iframe: @json(routePrefix() == 'iframe'),
      regex_ignore: /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/g,
    }
    var _settings = {
      route: @json(currentRoute()),
      offerte_btw_perc: @json(getSettingValue('offerte_btw_perc') ?? 0),
      factuur_standaard_incl_btw: @json(getSettingValue('factuur_standaard_incl_btw')),
      factuur_btw_percs: @json(json_decode(getSettingValue("factuur_btw_percs") ?? '[]')),
      factuur_btw_perc: @json(getSettingValue('factuur_btw_perc')),
      vooruit_planning: @json(getSettingValue('vooruit_planning')),
      custom_offertes_overzicht: @json(json_decode(getSettingValue("custom_offertes_overzicht") ?? '{}')),
      projecten_taken_hide_name: @json(getSettingValue("projecten_taken_hide_name")),
      dagoverzicht_active_uren: @json(resetIndex(json_decode(getSettingValue("dagoverzicht_active_uren") ?? '{}'))),
      planning_taken_inplannen_prefill: @json(json_decode(getSettingValue("planning_taken_inplannen_prefill") ?? '{}')),
      factuur_betalingstermijn: @json(getSettingValue("factuur_betalingstermijn") ?? 30),
      urenregistratie_invoeren_planning: @json(getSettingValue("urenregistratie_invoeren_planning")),
      environment_signature_default_color: @json(getSettingValue("environment_signature_default_color")),
      environment_signature_default_width: @json(getSettingValue("environment_signature_default_width")),
      facturatie_send_default_attach_proforma: @json(getSettingValue("facturatie_send_default_attach_proforma")),
      planning_project_taken_hide_project_btn: @json(getSettingValue("planning_project_taken_hide_project_btn")),
      planning_google_hide: @json(getSettingValue("planning_google_hide")),
      planning_taken_inplannen_prefill: @json(json_decode(getSettingValue('planning_taken_inplannen_prefill') ?? '[]')),
      leveranciers_tarieven_hide_btw: @json(getSettingValue('leveranciers_tarieven_hide_btw')),
      inkoopbonnen_hide_btw: @json(getSettingValue('inkoopbonnen_hide_btw')),
      offertes_details_sort: @json(getSettingValue('offertes_details_sort')),
      offertes_subtotaal_hide: @json(getSettingValue('offertes_subtotaal_hide')),
      offerte_wijzigen_accorderen_verbergen: @json(getSettingValue("offertes_wijzigen_accorderen_verbergen") == 'aan'),
    }
    var _asp = {
      spinner: `@spinner`,
      spinner_small: `@spinner_small`,
      spinner_large: `@spinner_large`,
      spinner_success: `@spinner_success`,
    }

    @if(Auth::check())
    @php
      $user = Auth::user();
      $user->firstname = $user->name;
      $user->name = $user->name.' '.$user->lastname;

      unset($user->password);
      unset($user->google_info);
      unset($user->google_token);
    @endphp
    var _user = @json($user);
    @endif

    function initMap() {}
  </script>
  <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.13.0-next/umd/popper.min.js"></script>
  <script src="https://code.iconify.design/1/1.0.7/iconify.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.0/croppie.min.js" type="text/javascript"></script>
  <script src="https://cdn.jsdelivr.net/npm/decimal.js@10.3.1/decimal.min.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

  @if(Auth::check() && routePrefix() != "iframe" && !stringContains(currentRoute(), '/uren/projectnummers/kastvakken'))
    <style>
      .bg-primary, .navbar.default-layout {
        background: {{ permissionValue('color_1') }};
      }

      .sidebar .nav .nav-item.active > .nav-link {
        color: {{ permissionValue('color_1') }};
      }

      .brand-logo, .brand-logo-mini {
        height: 75px !important;
        width: 75px !important;
      }
    </style>
  @endif
