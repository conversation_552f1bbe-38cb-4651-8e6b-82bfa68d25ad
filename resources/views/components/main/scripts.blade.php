<script crossorigin='anonymous' integrity='sha256-6Hwfx2mTN7eX1Xx4D8TV+jeJOIbkgEGRC5820F/ESCg=' src='https://inforcdn.com/library/cdn/minify/<EMAIL>'></script>
<script crossorigin='anonymous' integrity='sha256-RWC/abnO38EmHzNKB1MOlboyMvZr6Wz1zQlNHjI5Uk8=' src='https://inforcdn.com/library/cdn/minify/<EMAIL>'></script>
<script crossorigin='anonymous' integrity='sha256-Gx6BwbqRn2kXC9SO6CYiSyafCjs3+55lP2C25ZmrIrU=' src='https://inforcdn.com/library/cdn/minify/<EMAIL>'></script>
<script crossorigin='anonymous' integrity='sha256-iH3iFhhAsA6K9fneDE+gTzCmV7XjgaKkHqIYDLhhsEI=' src='https://inforcdn.com/library/cdn/minify/<EMAIL>'></script>
<script crossorigin='anonymous' integrity='sha256-lVM7tFzb13m5EGhmlgiiQ3KT0xPCECUOepQ09IA2MjA=' src='https://inforcdn.com/library/cdn/minify/<EMAIL>'></script>

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"
        integrity="sha384-B4gt1jrGC7Jh4AgTPSdUtOBvfO8shuf57BaghqFfPlYxofvL8/KUEfYiJOMMV+rV"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/color-thief/2.3.0/color-thief.umd.js"></script>
{{--  <script src="{{ asset('client/admin/assets/vendors/js/vendor.bundle.base.js') }}"></script>--}}
<script src="{{ asset('client/public/js/classes/CKEditorImageUploadAdapter.js')}}"></script>

@if(hasModule('WhatsApp')) <script src="{{ asset('client/public/js/whatsapp.js')}}"></script> @endif
@if(TweebaLocal()->connected) <script src="{{ asset('client/public/js/tweeba.js')}}"></script> @endif
<script src="{{ asset('client/public/js/konva.js')}}"></script>
<script src="{{ asset('client/public/js/moment.js')}}"></script>
<script src="{{ asset('client/public/js/tippy.js')}}"></script>
<script src="{{ asset('client/public/js/main.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/classes/BusinessCentral.js')}}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/classes/ExactOnline.js')}}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/classes/ExactGlobe.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/classes/King.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/classes/Zenvoices.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('client/public/js/datatablesCustomize.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('client/admin/assets/js/shared/off-canvas.js') }}"></script>
<script src="{{ asset('client/admin/assets/js/shared/hoverable-collapse.js') }}"></script>
<script src="{{ asset('client/admin/assets/js/shared/misc.js') }}"></script>
<script src="{{ asset('client/admin/assets/js/shared/settings.js') }}"></script>
<script src="{{ asset('client/admin/assets/js/shared/todolist.js') }}"></script>
<script src="{{ asset('client/admin/assets/js/demo_2/dashboard.js') }}"></script>
<script src="{{ url('client/public/js/yearpicker.js') }}"></script>
<script src="{{ url('client/public/js/chart.js') }}"></script>
<script src="{{url('client/public/js/statistiekenNavigation.js')}}" ></script>
<script src="{{url('client/public/js/nacalculatie.js')}}" ></script>
<script src="https://cdn.ckeditor.com/ckeditor5/35.4.0/classic/ckeditor.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@jaames/iro"></script>
<script src="https://jeremyfagis.github.io/dropify/dist/js/dropify.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.5.1/dist/chart.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.11.3/b-2.0.1/b-colvis-2.0.1/b-html5-2.0.1/b-print-2.0.1/datatables.min.js"></script>
<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAOo20gZOetftvgtbu9NYFKt3-e1h7xajs&libraries=places&callback=initMap" async defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/addon/hint/show-hint.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/mode/css/css.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsonview@1.2.0/dist/jquery.jsonview.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" integrity="sha512-XMVd28F1oH/O71fzwBnV7HucLxVwtxf26XV8P4wPk26EDxuGZ91N8bsOttmnomcCD3CS5ZMRL50H0GgOHvegtg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
  $(document).ready(() => {
    tippyInit();
    dropifyInit();
    hideSessionAlert()
    $('[data-ckeditor]').each(function () {
      $(this).addClass(randomString(10));
      editorInit(`.${lastString()}`);
    })
  })
  $('[data-navbar-toggle]').click(() => {
    setTimeout(() => {
      setCookie('tessa_navbar_state', $('body').hasClass('sidebar-icon-only') ? 'off' : 'on');
    }, 100)
  })

  function hideSessionAlert() {
    setTimeout(() => {
      $('[data-session-alert]').toggle(300);
    }, 2500);
  }
</script>
@if (Auth::check() && routePrefix() != 'iframe' && !stringContains(currentRoute(), '/uren/projectnummers/kastvakken'))
  <script>
    var authInterval;
    var loginInterval;

    $(document).ready(function () {
      authInit();
    })

    function authInit() {
      if (authInterval) {
        clearInterval(authInterval);
      }

      authValidate();
      authInterval = setInterval(authValidate, 120000);
    }

    function authValidate() {
      validateSession()
        .then((response) => {
          if (response.auth) {
            return;
          }

          clearInterval(authInterval);
          showModal('session_modal');
          listenForToken()
        })
        .catch((error) => {
          console.log("Auth can't be validated");
          console.log(error);
        });
    }

    function loginRequest() {
      window.open('{{url("iframe/login")}}', 'newwindow', 'width=515,height=800');
    }

    function listenForToken() {
      loginInterval = setInterval(function () {
        const csrf = localStorage.getItem('login-attempt');
        const time = Number(localStorage.getItem('login-attempt-timestamp') || null);

        if (!csrf || !time) {
          return
        }

        const current_time = new Date().getTime();
        if (!isBetween(time, current_time - 20000, current_time + 20000)) {
          return
        }

        clearInterval(loginInterval);
        handleCsrf();
      }, 150)
    }

    function handleCsrf() {
      csrf = localStorage.getItem('login-attempt');

      $('[name="csrf-token"]').attr('content', csrf);
      $('[name="_token"]').val(csrf);

      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': csrf
        }
      });
      hideModal('session_modal');
      authInit();
    }

  </script>
@endif
