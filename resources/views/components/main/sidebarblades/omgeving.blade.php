@if(Auth::user()->hasPermissionTo('medewerkers beheren') || Auth::user()->hasPermissionTo('rollen beheren') || Auth::user()->hasPermissionTo('Machines beheren') || Auth::user()->hasPermissionTo('Vestigingen beheren') || Auth::user()->hasPermissionTo('Tarieven beheren') || Auth::user()->hasPermissionTo('Uursoorten beheren'))
  <li class="nav-item">
    <a class="nav-link" data-toggle="collapse" href="#omgeving-dropdown" aria-expanded="false" aria-controls="users-dropdown">
      <i class="menu-icon fa fa-desktop"></i>
      <span class="menu-title" data-menu-title>Omgeving</span>
      <i class="menu-arrow"></i>
    </a>
    <div class="collapse" id="omgeving-dropdown">
      <ul class="nav flex-column sub-menu">

        @if(Auth::user()->hasPermissionTo('medewerkers beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon mdi mdi-account"></i>
              <span class="menu-title">Personeel</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/users/create') }}">Nieuwe medewerker</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/users') }}">Alle medewerkers</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/users?inactive=true') }}">Inactieve medewerkers</a></div>
              </div>
            </div>
          </li>
        @endif

          @if(Auth::user()->hasPermissionTo('medewerkers beheren') && getSettingCheckbox('users_intake'))
            <li class="nav-item sub-nav-item">
              <a class="nav-link">
                <i class="menu-icon fa fa-id-card"></i>
                <span class="menu-title">Intake</span>
                <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
              </a>
              <div class="sub-nav-content" style="display: none">
                <div class="flex-column sub-menu">
                  <div class="nav-item"><a class="nav-link" href="{{ url('/users/intake/create') }}">Nieuwe intake</a></div>
                  <div class="nav-item"><a class="nav-link" href="{{ url('/users/intake?active=1') }}">Openstaande intakes</a></div>
                </div>
              </div>
            </li>
          @endif

        @if(Auth::user()->hasPermissionTo('rollen beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon mdi mdi-account-key"></i>
              <span class="menu-title">Rollen</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/roles/create') }}">Nieuwe rol</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/roles') }}">Alle rollen</a></div>
              </div>
            </div>
          </li>
        @endif

        @if(Auth::user()->hasPermissionTo('Machines beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon bi bi-printer-fill"></i>
              <span class="menu-title">Machines</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/machines/create') }}">Nieuwe machine</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/machines') }}">Actieve machines</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/machines?active=0') }}">Verwijderde machines</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/machines/groepen') }}">Groepen</a></div>
              </div>
            </div>
          </li>
      @endif

        @if(Auth::user()->hasPermissionTo('Vestigingen beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon bi bi-house-fill"></i>
              <span class="menu-title">Vestigingen</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/vestigingen/create') }}">Nieuwe vestiging</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/vestigingen') }}">Alle vestigingen</a></div>
              </div>
            </div>
          </li>
        @endif

        @if(Auth::user()->hasPermissionTo('Tarieven beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon fa-solid fa-coins"></i>
              <span class="menu-title">Tarieven</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/tarieven/create') }}">Nieuwe tarief</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/tarieven') }}">Alle tarieven</a></div>
              </div>
            </div>
          </li>
        @endif

        @if(Auth::user()->hasPermissionTo('Eenheden beheren'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon fa-solid fa-coins"></i>
              <span class="menu-title">Eenheden</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('/eenheden/create') }}">Nieuwe eenheid</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('/eenheden') }}">Alle eenheden</a></div>
              </div>
            </div>
          </li>
        @endif

        @if(Auth::user()->hasPermissionTo('Uursoorten beheren') && hasModule('Urenregistratie'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <i class="menu-icon fa-solid fa-business-time"></i>
              <span class="menu-title">Uursoorten</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                <div class="nav-item"><a class="nav-link" href="{{ url('uren/uursoorten/create') }}">Uursoort toevoegen</a></div>
                <div class="nav-item"><a class="nav-link" href="{{ url('uren/uursoorten') }}">Alle uursoorten</a></div>
              </div>
            </div>
          </li>
        @endif

      </ul>
    </div>
  </li>
@endif
