@if(Auth::user()->hasPermissionTo('Werkbonnen beheren') || Auth::user()->hasPermissionTo('Eigen werkbonnen beheren') || Auth::user()->hasPermissionTo('Werkbonnen aanmaken'))
  <li class="nav-item">
    <a class="nav-link" data-toggle="collapse" href="#werkbonnen-dropdown" aria-expanded="false" aria-controls="werkbonnen-dropdown">
      <i class="menu-icon fa-solid fa-file-lines"></i>
      <span class="menu-title" data-menu-title>Werkbonnen</span>
      <i class="menu-arrow"></i>
    </a>
    <div class="collapse" id="werkbonnen-dropdown">
      <ul class="nav flex-column sub-menu">
        @if(Auth::user()->hasPermissionTo('Werkbonnen aanmaken'))
          <li class="nav-item sub-nav-item">
            <a class="nav-link">
              <span class="menu-title">Nieuwe werkbon</span>
              <i class="menu-icon fa fa-chevron-right sub-nav-icon" style="font-size: 10px" ></i>
            </a>
            <div class="sub-nav-content" style="display: none">
              <div class="flex-column sub-menu">
                @foreach(\App\Werkbonnen::templates() as $template)
                  <div class="nav-item ml-3"><a class="nav-link" href="{{url("werkbonnen/new/".$template->id)}}">{{$template->naam}}</a></div>
                @endforeach
              </div>
            </div>
          </li>
        @endif
        @if (!getSettingCheckbox('menu_hide_all_but_filter'))
          <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen?stadium=Open') }}">Open werkbonnen</a></li>
          @if (!empty(getSettingJson('werkbon_opvolgstappen')))
            <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen?stadium=Opvolgen') }}">Opvolg werkbonnen</a></li>
          @endif
          <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen?stadium=Afgerond') }}">Afgeronde werkbonnen</a></li>
          <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen?stadium=Verwijderd') }}">Verwijderde werkbonnen</a></li>
          <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen') }}">Alle werkbonnen</a></li>
        @endif
        <li class="nav-item"><a class="nav-link" href="{{ url('werkbonnen?filter=1') }}">Werkbonnen filteren</a></li>
      </ul>
    </div>
  </li>
@endif
