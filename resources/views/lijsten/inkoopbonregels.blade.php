@extends("layouts.app")

@section("title", "Inkoopbonnen")

@section("content")
  <section class="append-loader" >
    <section class="my-4 h-px-100" data-stats-navigation >
      <div class="py-3 bg-modal z-index-999" data-content >
        <div class="card m-0 p-2">
          <div class="row">
            <div class="col my-1">
              <div class="w-100" data-label >Groeperen op</div>
              <select name="sort_by" class="form-select" >
                <option value="all" >Alles</option>
                <option value="bv" >B.V.</option>
                <option value="leverancier" >Leverancier</option>
                <option value="project" >Project</option>
                <option value="vestiging" >Vestiging</option>
              </select>
            </div>
            <div class="col my-1 select_search-container" data-sort-option-container >
              <div class="w-100" data-label >Optie</div>
              <input type="hidden" name="sort_option" class="select_search-hidden-input" data-placeholder="Selecteer optie">
              <div class="select_search-values" >
                <div class="select_search-box">
                  @foreach(getBvs() as $bv)
                    <span class="select_search-value nobr" data-option="bv" data-value="{{$bv->id}}" data-name="{{$bv->name}}">{{$bv->name}}</span>
                  @endforeach
                  @foreach($leveranciers as $leverancier)
                    <span class="select_search-value nobr" data-option="leverancier" data-value="{{$leverancier->id}}" data-name="{{$leverancier->naam}}">{{$leverancier->naam}}</span>
                  @endforeach
                  @foreach($projecten as $project)
                    <span class="select_search-value nobr" data-option="project" data-value="{{$project->id}}" data-name="{{$project->projectnr}}">
                    <div>{{$project->projectnr}}</div>
                    <div class="font-size-08 text-muted" >{{$project->projectnaam}}</div>
                  </span>
                  @endforeach
                  @foreach(getVestigingen() as $vestiging)
                    <span class="select_search-value nobr" data-option="vestiging" data-value="{{$vestiging->id}}" data-name="{{$vestiging->naam ?? $vestiging->plaats}}">{{$vestiging->naam ?? $vestiging->plaats}}</span>
                  @endforeach
                </div>
              </div>
            </div>
            <div class="col my-1">
              <div class="w-100" data-label >Status geselecteerde periode</div>
              <div class="select_multiple-container" data-placeholder="Selecteer status" >
                <label class="select_multiple-value" >Open <input type="checkbox" class="d-none" name="status_select" data-name="Open" value="Open" checked ></label>
                <label class="select_multiple-value" >Accorderen <input type="checkbox" class="d-none" name="status_select" data-name="Accorderen" value="Accorderen" checked ></label>
                <label class="select_multiple-value" >Afgerond <input type="checkbox" class="d-none" name="status_select" data-name="Afgerond" value="Afgerond" checked ></label>
                <label class="select_multiple-value" >Verwijderd <input type="checkbox" class="d-none" name="status_select" data-name="Verwijderd" value="Verwijderd" ></label>
              </div>
            </div>
            <div class="col my-1">
              <div class="w-100" data-label >Huidige status</div>
              <div class="select_multiple-container" data-placeholder="Selecteer status" >
                <label class="select_multiple-value" >Open <input type="checkbox" class="d-none" name="status" data-name="Open" value="Open" checked ></label>
                <label class="select_multiple-value" >Accorderen <input type="checkbox" class="d-none" name="status" data-name="Accorderen" value="Accorderen" checked ></label>
                <label class="select_multiple-value" >Afgerond <input type="checkbox" class="d-none" name="status" data-name="Afgerond" value="Afgerond" checked ></label>
                <label class="select_multiple-value" >Verwijderd <input type="checkbox" class="d-none" name="status" data-name="Verwijderd" value="Verwijderd" ></label>
              </div>
            </div>
            @if(businessCentralLocal()->connected)
              <div class="col my-1">
                <div class="w-100" data-label >Grootboek</div>
                <div class="select_multiple-container" data-placeholder="Selecteer grootboeknummer" >
                  @foreach(businessCentralLocal()->getStoredAccounts('75') as $account)
                    <label class="select_multiple-value" > <span class="badge badge-sm badge-inverse-primary mr-1" >{{$account->number}}</span> {{$account->name}} <input type="checkbox" class="d-none" name="bc_accounts[]" data-name="{{$account->number}}" value="{{$account->number}}" ></label>
                  @endforeach
                </div>
              </div>
            @endif
          </div>
          <div class="row">
            <div class="col my-1">
              <div class="w-100" data-label >Datum</div>
              <select name="target" class="form-select" >
                <option value="">Selecteer datum</option>
                <option value="created_at">Uitgebracht</option>
                <option value="reporting_date">Rapportagedatum</option>
                <option value="sent_at">Verzonden</option>
                <option value="completed_at">Afgerond</option>
                <option value="gefactureerd_op">Gefactureerd</option>
              </select>
            </div>
            <div class="col my-1" data-target-dates >
              <div class="w-100" data-label >Van</div>
              <input type="date" class="form-control-custom" name="target_date_start" value="{{Carbon()->now()->addDays(-30)->format('Y-m-d')}}" >
            </div>
            <div class="col my-1" data-target-dates >
              <div class="w-100" data-label >Tot</div>
              <input type="date" class="form-control-custom" name="target_date_end" value="{{Carbon()->now()->format('Y-m-d')}}" >
            </div>
            <div class="col my-1 w-100">
              <div class="w-100" data-label >Export</div>
              <form class="w-100" action="{{url("/lijsten/inkoopbonregels/export/excel")}}" method="post" target="_blank">
                <input type="submit" class="btn btn-light border w-100 form-control-custom" value="Excel" />
                @csrf
                <div data-excel-ids>
                </div>
              </form>
            </div>

          </div>
        </div>
      </div>
    </section>

    <section class="my-2" >
      <div class="flex-between">
        <div class="w-100 mx-3 dropdown-divider "></div>
        <h4 class="nobr" data-inkoopbonnen-count>0 INKOOPBONREGELS</h4>
        <div class="w-px-100 mx-3 dropdown-divider" ></div>
        <h4 class="nobr" data-facturen-excl ><span class="mr-2" >€</span> <span>0,00</span></h4>
        <div class="w-100 mx-3 dropdown-divider"></div>
      </div>
    </section>

    <section class="card my-2">
      <div class="overflow-auto">
        <table data-table >
          <thead>
          <tr>
            <th>Bonnummer</th>
            <th>Regel</th>
            @if(businessCentralLocal()->connected)
              <th>Grootboek</th>
            @endif
            <th>Datum</th>
            <th>Project</th>
            <th>Leverancier</th>
            <th>Vestiging</th>
            <th>Status(sen) geselecteerde periode</th>
            <th>Huidige status</th>
            <th>Verzonden aan</th>
            <th>Verzonden op</th>
            <th>Afgerond op</th>
            <th>Gefactureerd op</th>
            <th>Rapportagedatum</th>
            <th>Totaal Excl.</th>
            <th>Totaal Incl.</th>
            <th>Btw</th>
            <th></th>
          </tr>
          </thead>
          <tbody>

          </tbody>
        </table>
      </div>
    </section>



  </section>
@endsection
@section("script")
  <script >

    const _data = {
      regels: [],
    }
    const _base = {
      request: null,
      table: null,
      tbody: $('[data-table]').find('tbody'),
      excel_ids: $('[data-excel-ids]'),
      sort: () => { return $('[name=sort_by]').val() },
      sort_option: () => { return $('[name=sort_option]').val() },
      target: () => { return $('[name=target]').val() },
      status: () => { return  $('[name=status]:checked').map(function() { return this.value; }).get();},
      target_status: () => { return  $('[name=status_select]:checked').map(function() { return this.value; }).get();},
      target_start: () => { return $('[name=target_date_start]').val() },
      target_end: () => { return $('[name=target_date_end]').val() },
      bc_accounts: () => { return  $('[name="bc_accounts[]"]:checked').map(function() { return this.value; }).get();},
    }
    const _bc = {
      instance: new BusinessCentral(),
    }

    $(document).ready(function(){
      initTable();
      $('[name=sort_by]').val('all').trigger('change');
      initSelectMultiple();
    });

    $('[name=sort_by]').change(function(){

      const container = $('[data-sort-option-container]');
      container.find('.select_search-input').val('').prop('disabled', false);
      container.find('.select_search-value').addClass('d-none');

      if(this.value == 'all'){
        initInkoopbonnen()
        container.find('.select_search-input').val('').prop('disabled', true);
        return;
      }

      container.find(`[data-option=${this.value}]`).removeClass('d-none');
    });
    $('[name=sort_option], [name=target_date_start], [name=target_date_end], [name=target], [name=status_select], [name=status], [name="bc_accounts[]"]').change(function() {
      initInkoopbonnen();
    })

    function initInkoopbonnen(){
      loader();

      const data = {
        status: _base.status(),
        target: _base.target(),
        target_start: _base.target_start(),
        target_end: _base.target_end(),
        target_status: _base.target_status(),
        bc_accounts: _base.bc_accounts(),
        relations: ['inkoopbon.project.vestiging', 'inkoopbon.leverancier', 'bc_account'],
      }

      if(_base.sort() && _base.sort_option()){
        data[_base.sort()] = _base.sort_option();
      }

      if(_base.request){
        _base.request.abort();
        _base.request = null;
      }

      _base.request = ajax('api/inkoopbonnen/regels/get', data);
      _base.request
        .always(() => {
          _base.request = null;
        })
        .then(response => {
          _data.ids = response.regels.map(regel => regel.id);
          _data.regels = response.regels;
          fillInkoopbonregels();
          fillExcel();
        })
        .catch(err => {
          console.log(err)
          if(err.status === 0){ return }
          errorLoader()
        });

    }

    function fillExcel(){
      const { excel_ids } = _base;
      excel_ids.empty();
      excel_ids.html(
        _data.ids.map(id => {
          return `<input type="hidden" value="${id}" name="ids[]" />`
        })
      )
    }
    function fillInkoopbonregels(){
      const { table, tbody } = _base;
      const { regels } = _data;

      if(table){ table.destroy(); }

      tbody.empty();

      let total_excl = 0;

      for(const regel of regels){
        total_excl += Number(regel.total.excl);
        tbody.append( createInkoopbonregelTR(regel) )
      }

      $('[data-inkoopbonnen-count]').html(`${regels.length} ${regels.length === 1 ? 'INKOOPBONREGEL' : 'INKOOPBONREGELS'}`)
      $('[data-facturen-excl]').html(`<span class="mr-2" >€</span> <span>${toPrice(total_excl)}</span>`)

      tippyInit();
      initTable();
      successLoader();
    }

    function createInkoopbonregelTR(regel){
      let { id, reporting_date, naam, inkoopbon, total, business_central_account, bc_account } = regel
      const { bonnummer, project, leverancier, color, status, sent_to, sent_at, token, statussen, date, completed_at, gefactureerd_op } = inkoopbon;

        return `
          <tr >
            <td>${bonnummer}</td>
            <td>${naam}</td>
            ${ _bc.instance.connected ? `<td class="nobr" > <span class="badge badge-sm badge-inverse-primary mr-1" >${business_central_account}</span> ${bc_account?.name || ''}</td>` : ''}
            <td>${convert(date)}</td>
            <td>${project?.projectnr || ''}</td>
            <td>${leverancier?.naam || ''}</td>
            <td>${project?.vestiging?.naam || ''}</td>
            <td>
              <div class="accorderen-flow-container font-size-09">
                ${statussen.map(status_badge => { return `<div data-tippy-content="<div class='pb-1 mb-1 border-bottom text-center' >${status_badge.status}</div> ${status_badge.start} <span class='mx-2' >t/m</span> ${status_badge.end}" class="tippy accorderen-flow-node text-${status_badge.color} "> <i class="fa-regular fa-circle-check"></i> ${status_badge.status}</div>` }).join('')}
              </div>
            </td>
            <td> <span class="badge badge-${color} badge-lg" >${status}</span> </td>
            <td>
                <span class="d-block">${sent_to || ''}</span>
            </td>
            <td>
                <span class="d-block">${sent_at ? convert(sent_at) : ''}</span>
            </td>
            <td>
                <span class="d-block">${completed_at ? convert(completed_at) : ''}</span>
            </td>
            <td>
                <span class="d-block">${gefactureerd_op ? convert(gefactureerd_op) : ''}</span>
            </td>
            <td class="nobr" >
              ${reporting_date || ''}
            </td>
            <td>
                ${toPrice(total.excl)}
            </td>
            <td>
                ${toPrice(total.incl)}
            </td>
            <td>
                ${toPrice(total.btw)}
            </td>
            <td class="text-right nobr" >
              <a href="${url}/inkoopbonnen/token/${token}" target="_blank" class="btn btn-inverse-primary" >PDF</a>
            </td>
          </tr>
        `
    }

    function initTable(){
      _base.table = tableInit('[data-table]', {
        pageLength: 100,
        columnDefs: [ { orderable: false, targets: [-1] }],
        order: [[0, 'desc']],
      });
    }

  </script>
@endsection
