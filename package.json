{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"cross-env": "^5.2.1", "laravel-mix": "^4.0.7", "resolve-url-loader": "^2.3.1", "sass": "^1.32.7", "sass-loader": "^7.1.0"}, "dependencies": {"@popperjs/core": "^2.9.1", "bootstrap": "^4.6.0", "cropperjs": "^1.5.12", "responsive-sketchpad": "^1.2.1"}}