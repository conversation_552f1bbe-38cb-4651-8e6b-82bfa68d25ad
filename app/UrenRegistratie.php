<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class UrenRegistratie extends Model {
  protected $table = 'urenregistratie';

  protected $fillable = [
    'medewerker_id',
    'datum',
    'projectnummer',
    'gewerkte_uren',
    'pauze',
    'begintijd',
    'eindtijd',
    'reisuren',
    'overnachting',
    'heenreis_huisproject',
    'terugreis_huisproject',
    'heenreis_woonwerk',
    'terugreis_woonwerk',
    'ladenlossen_begintijd',
    'ladenlossen_eindtijd',
    'ladenlossen_middag_begintijd',
    'ladenlossen_middag_eindtijd',
    'bestuurder',
    'bijrijder',
    'kenteken_id',
    'verlof',
    'ziekteuren',
    'feesturen',
    'verlofreden_id',
    'naca_id',
    'opmerkingen',
    'totaaluren100',
    'totaal_overuren150',
    'totaal_overuren200',
    'tijdvoortijd',
    'totaaldaguren100',
    'tijdelijk'
  ];

  public function naca() {
    return $this->hasOne('App\Naca', 'id', 'naca_id');
  }

  public function verlof() {
    return $this->hasOne('App\Verlofreden', 'id', 'verlofreden_id');
  }

  public function verlofRtl() { // same function, other name.
    return $this->hasOne('App\Verlofreden', 'id', 'verlofreden_id');
  }

  public function facturabelopties() { // same function, other name.
    return $this->hasOne('App\Facturabel', 'id', 'facturabel_id');
  }

  public function facturabel() { // same function, other name.
    return $this->hasOne('App\Facturabel', 'id', 'facturabel_id');
  }

  public function kenteken() {
    return $this->hasOne('App\Kenteken', 'id', 'kenteken_id');
  }

  public function project() {
    return $this->hasOne(Project::class, 'projectnr', 'projectnummer')->with('taken');
  }

  public function user(){
    return $this->hasOne(User::class, 'id', 'medewerker_id');
  }

  public function projecttaken(){
    return $this->hasManyThrough(ProjectTaken::class, UrenRegistratieProjectTaken::class, 'urenregistratie_id', 'id', 'id', 'taak_id');
  }
  public function machines(){
    return $this->hasManyThrough(Machines::class, UrenRegistratieMachines::class, 'urenregistratie_id', 'id', 'id', 'machine_id')->with("group");
  }
  public function machineuren(){
    return $this->hasMany(UrenRegistratieMachines::class, 'urenregistratie_id', 'id')->with('machine');
    }
  public function uursoort(){
    return $this->hasOne(Uursoorten::class, 'id', 'uursoort_id');
  }


  public function hasTaak($id){
    $check = UrenRegistratieProjectTaken::where(['urenregistratie_id' => $this->id, 'taak_id' => $id])->first();
    return $check != null;
  }

  public function sumMachineUren(){
      foreach($this->machineuren as $i => $row){
          $this->machineuren[$i]->totaal = 0;
          if(!$row->begintijd || !$row->eindtijd){ continue; }

          $diffinminutes = Carbon::parse($row->eindtijd)->diffInMinutes(Carbon::parse($row->begintijd)) / 60;
          $this->machineuren[$i]->totaal = round($diffinminutes, 2);
      }
  }
  public static function total($uur){
    $pauze = $uur->pauze;

    if ($uur->gewerkte_uren){
      return $uur->gewerkte_uren - $pauze;
    }
    elseif($uur->begintijd && $uur->eindtijd){
      return Carbon::parse($uur->datum.' '.$uur->begintijd)->diffInHours(Carbon::parse($uur->datum.' '.$uur->eindtijd)) - $pauze;
    }

    return 0;
  }
  public static function calcAdvancedOveruren($request){
    $projects = $request->projecten;
    if (isset($request->projects)){
      $projects = json_decode($request->projects, true);
    }

    $settings = json_decode((getSettingValue('urenregistratie_advanced_overuren_data') ?? '[]'), true);
    $overuren = [
      '100' => 0,
      '125' => 0,
      '128' => 0,
      '150' => 0,
      '165' => 0,
      '200' => 0,
      'tijdvoortijd' => 0,
    ];
    $times = [];

    try{
      $date = Carbon::parse($request->date);
      $dow = $date->dayOfWeek;

      // filter unnecessary settings out ( based on dow )
      foreach ($settings as $s => $setting){
        if(!isset($setting['dow'][$dow])){
          unset($settings[$s]);
          continue;
        }

        $settings[$s]['_first'] = 0;
        $settings[$s]['_remaining'] = 0;
      }

      // Create an easy accessible array of times and total hours
      foreach ($projects as $project){
        $hours = round((Carbon::parse($project['begintijd'])->diffInMinutes(Carbon::parse($project['eindtijd'])) / 60), 2);
        $verlof = $project['verlof'];

        if ($verlof){
          if ($verlof > $hours){ $verlof = $hours; }
          $hours = $hours - $verlof;
        }

        $times[] = [
          'start' => $project['begintijd'],
          'end' => $project['eindtijd'],
          'hours' => $hours
        ];
      }

      //All 100% if no setting found
      if(!count($settings)){
        foreach ($times as $time){ $overuren['100'] += $time['hours']; }
        return $overuren;
      }

      //Check for hours overlapping and put it into corresponding setting
      foreach($settings as $s => $setting){
        foreach ($times as $t =>$time){
          if(!$time['hours']){ continue; }

          $overlap = getOverlapHours($setting['start'], $setting['end'], $time['start'], $time['end']);
          if ($overlap > $time['hours']){ $overlap = $time['hours']; }

          $times[$t]['hours'] = $time['hours'] - $overlap;
          $settings[$s]['_first'] += $overlap;
        }
      }

      //Place hours into remaining category
      foreach($settings as $s => $setting){
        if(!$setting['first_hours']['hours']){ continue; }
        if($setting['_first'] <= $setting['first_hours']['hours']){ continue; }

        $exceed = $setting['_first'] - $setting['first_hours']['hours'];

        $settings[$s]['_first'] = $setting['_first'] - $exceed;
        $settings[$s]['_remaining'] = $exceed;
      }

      //Place all hours into $overuren
      foreach($settings as $s => $setting){
        $overuren[$setting['first_hours']['percentage']] += $setting['_first'];
        $overuren[$setting['remaining_hours']['percentage']] += $setting['_remaining'];
      }

      //Check for leftovers and place them into 100%
      foreach ($times as $time){
        if(!$time['hours']){ continue; }
        $overuren['100'] += $time['hours'];
      }

      return $overuren;
    }
    catch (\Exception $e){

      // Put everything into 100% if an error occurs
      actError($e);
      foreach ($projects ?? [] as $project){
        $overuren['100'] += round((Carbon::parse($project['begintijd'])->diffInMinutes(Carbon::parse($project['eindtijd'])) / 60), 2);
      }
      return $overuren;
    }
  }

}
