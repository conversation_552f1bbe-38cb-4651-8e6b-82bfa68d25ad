<?php

namespace App\Http\Controllers;

use App\Exports\FacturenExport;
use App\Exports\InkoopbonnenExport;
use App\Exports\InkoopbonregelsExport;
use App\Exports\InkoopfacturenExport;
use App\Exports\ProformaExport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;


class LijstenController extends Controller
{
    public function inkoopbonnen(){
        return view('lijsten.inkoopbonnen', [
            'leveranciers' => getLeveranciers(['select' => ['id', 'naam']]),
            'projecten' => getProjecten(['select' => ['id', 'projectnr', 'projectnaam']]),
        ]);
    }
    public function inkoopbonregels(){
        return view('lijsten.inkoopbonregels', [
            'leveranciers' => getLeveranciers(['select' => ['id', 'naam']]),
            'projecten' => getProjecten(['select' => ['id', 'projectnr', 'projectnaam']]),
        ]);
    }
    public function inkoopfacturen(){
      return view('lijsten.inkoopfacturen', [
        'leveranciers' => getLeveranciers(['select' => ['id', 'naam']]),
        'projecten' => getProjecten(['select' => ['id', 'projectnr', 'projectnaam']]),
      ]);
    }
    public function facturen(){
      return view('lijsten.facturen', [
        'klanten' => getKlanten(['select' => ['id', 'naam', 'contactpersoon_achternaam', 'contactpersoon_voornaam']]),
        'projecten' => getProjecten(['select' => ['id', 'projectnr', 'projectnaam'], 'has' => 'facturen']),
      ]);
    }
    public function proformas(){
      return view('lijsten.proformas', [
        'klanten' => getKlanten(['select' => ['id', 'naam', 'contactpersoon_achternaam', 'contactpersoon_voornaam']]),
        'projecten' => getProjecten(['select' => ['id', 'projectnr', 'projectnaam'], 'has' => 'facturen']),
      ]);
    }
    public function downloadProformaExcel($subdomain, Request $request) {
      return Excel::download(new ProformaExport(['ids' => $request->ids]), 'Pro forma\'s ' . Carbon::now()->format('Y-m-d') .'.xlsx');
    }
    public function downloadFacturenExcel($subdomain, Request $request) {
      return Excel::download(new FacturenExport(['ids' => $request->ids]), 'Facturen ' . Carbon::now()->format('Y-m-d') .'.xlsx');
    }
    public function downloadInkoopfacturenExcel($subdomain, Request $request) {
      return Excel::download(new InkoopfacturenExport(['ids' => $request->ids]), 'Inkoopfacturen ' . Carbon::now()->format('Y-m-d') .'.xlsx');
    }
    public function downloadInkoopbonnenExcel($subdomain, Request $request) {
      return Excel::download(new InkoopbonnenExport(['ids' => $request->ids]), 'Inkoopbonnen ' . Carbon::now()->format('Y-m-d') .'.xlsx');
    }
    public function downloadInkoopbonregelsExcel($subdomain, Request $request) {
      return Excel::download(new InkoopbonregelsExport(['ids' => $request->ids]), 'Inkoopbonregels ' . Carbon::now()->format('Y-m-d') .'.xlsx');
    }

}
