<?php

namespace App\Http\Controllers;

use App\BV;
use App\Checklists;
use App\Leveranciers;
use App\Klanten;
use App\Offertes;
use App\Offerteteksten;
use App\StatistiekenFacturatieGroepen;
use App\Templates;
use App\Vestigingen;
use App\Werkbonnen;
use App\WerkbonnenTemplates;
use App\WerkbonTeksten;
use App\Zenvoices\Kostendragers as zenvoice_kostendragers;
use DB;
use App\Verlof;
use App\Verlofreden;
use Carbon\Carbon;

class StatistiekenController extends Controller
{
    private function base(){
      $klanten = Klanten::where('status', 1)->orderBy('naam', 'ASC')->get();
      $leveranciers = Leveranciers::where('active', 1)->orderBy('naam', 'ASC')->get();
      $vestigingen = Vestigingen::where('active', 1)->orderBy('naam', 'ASC')->get();

      if(zenvoicesConnected()){
          $kostendragers = zenvoice_kostendragers::orderBy('naam', 'ASC')->get();
      }

      return [
        'klanten' => $klanten,
        'leveranciers' => $leveranciers,
        'vestigingen' => $vestigingen,
        'zenvoice_kostendragers' => $kostendragers ?? null,
      ];
    }

    public function urenregistratie(){
    $data['users'] = getUsers();

    $year = date('Y');
    $verlofRedenen = Verlofreden::where('active', 1)->get();
    $verzuimVerhoudingData = [];
    $verlofUsedByUserAndReason = [];
    foreach ($data['users'] as $user) {
        foreach ($verlofRedenen as $reden) {
            $total = Verlof::where('beoordeeld', 1)
                ->where('akkoord', 1)
                ->where('reden_id', $reden->id)
                ->where('medewerker_id', $user->id)
                ->whereYear('datum', $year)
                ->get()
                ->reduce(function($carry, $v) {
                    if ($v->van && $v->tot) {
                        $start = Carbon::parse($v->datum . ' ' . $v->van);
                        $end = Carbon::parse($v->datum . ' ' . $v->tot);
                        $hours = $start->floatDiffInHours($end);
                    } else {
                        $hours = 0;
                        $standaarduren = $v->user && $v->user->standaarduren ? $v->user->standaarduren : null;
                        if ($standaarduren && count($standaarduren)) {
                            $dagNamen = [
                                'monday' => 'maandag',
                                'tuesday' => 'dinsdag',
                                'wednesday' => 'woensdag',
                                'thursday' => 'donderdag',
                                'friday' => 'vrijdag',
                                'saturday' => 'zaterdag',
                                'sunday' => 'zondag',
                            ];
                            $englishDay = strtolower(Carbon::parse($v->datum)->format('l'));
                            $dagNaam = $dagNamen[$englishDay] ?? $englishDay;
                            $std = $standaarduren->where('dag', $dagNaam)->first();
                            if ($std) {
                                $hours = $std->standaarduren;
                            }
                        }
                    }
                    return $carry + $hours;
                }, 0);
            $verlofUsedByUserAndReason[$user->id][$reden->id] = $total;
        }
    }
    foreach ($verlofRedenen as $reden) {
        $total = Verlof::where('beoordeeld', 1)
            ->where('akkoord', 1)
            ->where('reden_id', $reden->id)
            ->whereYear('datum', $year)
            ->get()
            ->reduce(function($carry, $v) {
                if ($v->van && $v->tot) {
                    $start = Carbon::parse($v->datum . ' ' . $v->van);
                    $end = Carbon::parse($v->datum . ' ' . $v->tot);
                    $hours = $start->floatDiffInHours($end);
                } else {
                    $hours = 0;
                    $standaarduren = $v->user && $v->user->standaarduren ? $v->user->standaarduren : null;
                    if ($standaarduren && count($standaarduren)) {
                        $dagNamen = [
                            'monday' => 'maandag',
                            'tuesday' => 'dinsdag',
                            'wednesday' => 'woensdag',
                            'thursday' => 'donderdag',
                            'friday' => 'vrijdag',
                            'saturday' => 'zaterdag',
                            'sunday' => 'zondag',
                        ];
                        $englishDay = strtolower(Carbon::parse($v->datum)->format('l'));
                        $dagNaam = $dagNamen[$englishDay] ?? $englishDay;
                        $std = $standaarduren->where('dag', $dagNaam)->first();
                        if ($std) {
                            $hours = $std->standaarduren;
                        }
                    }
                }
                return $carry + $hours;
            }, 0);
        $verzuimVerhoudingData[$reden->id] = $total;
    }
    $data['verlofRedenen'] = $verlofRedenen;
    $data['verzuimVerhoudingData'] = $verzuimVerhoudingData;
    $data['verlofUsedByUserAndReason'] = $verlofUsedByUserAndReason;

    return view("statistieken.urenregistratie", $data);
    }

    public function offertes(){
      $data = $this->base();

      $data['templates'] = Templates::orderBy('naam', "ASC")->get();

      return view("statistieken.offertes", $data);
    }
    public function facturatie(){
      $data = $this->base();

      $data['groepen'] = StatistiekenFacturatieGroepen::with("items")->get()->keyBy("id");

      return view("statistieken.facturatie", $data);
    }
    public function proformas(){
      $data = $this->base();

      return view("statistieken.proformas", $data);
    }
    public function inkoopbonnen(){
      $data = $this->base();

      return view("statistieken.inkoopbonnen", $data);
    }

    public function inkoopfacturen(){
      $data = $this->base();

      return view("statistieken.inkoopfacturen", $data);
    }

    public function projecten(){
      $data = $this->base();
      return view("statistieken.projecten", $data);
    }

    public function werkbonnen(){
      $data = $this->base();

      return view("statistieken.werkbonnen", $data);
    }
    public function checklists(){
      $checklists = Checklists::with('template', 'klant', 'user', '_bv', 'keywords', 'details')->get();
      return view("statistieken.checklists",[
        "checklists" => $checklists,
      ]);
    }

}

