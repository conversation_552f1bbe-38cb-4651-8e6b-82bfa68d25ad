<?php

namespace App\Http\Controllers;

use App\ActivityLog;
use App\BV;
use App\ExplorerFiles;
use App\Facturen;
use App\Mail\NewWerkbon;
use App\Planning;
use App\ProjectTaken;
use App\TemplateKeywords;
use App\WerkbonnenProjectTaken;
use App\WerkbonnenTemplateKeywords;
use App\WerkbonRegels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use PDF;
use DB;
use Auth;
use Storage;
use App\Klanten;
use App\User;
use App\Offertes;
use App\Project;
use App\Werkbonnen;
use App\WerkbonTeksten;
use App\OffertePlanning;
use App\OffertesDatasetsItems;
use App\Offerteteksten;
use App\WerkbonnenTemplates;
use Illuminate\Http\Request;
use function foo\func;
use App\Http\Controllers\Api\WerkbonnenController as apiWerkbonnenController;

class WerkbonnenController extends Controller
{

  private $relations = ['project', 'offerte', 'klant', 'user', 'template', 'vestiging'];
  private $select_finish = false;

  public function index($subdomains){
    $templates = WerkbonnenTemplates::get();
    $werkbonOverzichtTonen = $this->overzichtVelden();

    return view('werkbonnen.index',[
      "templates" => $templates,
      "werkbonOverzichtTonen" => $werkbonOverzichtTonen,
    ]);
  }

  public function viewPdf($subdomein, $id){
    $werkbon = Werkbonnen::where("id", $id)->firstOrFail();
    $pdf = $this->pdf($id);
    return $pdf->stream( $werkbon->werkbonnummer.".pdf");
  }
  public function viewPdfToken($subdomein, $token){
    $token = str_replace('.pdf', '', $token);

    $werkbon = Werkbonnen::where("token", $token)->firstOrFail();
    $pdf = $this->pdf($werkbon->id);
    return $pdf->stream( $werkbon->werkbonnummer.".pdf");
  }
  public function pdf($id){
    $werkbon = Werkbonnen::where("id", $id)->with("project.locatie", "project.custom", "_bv", 'planning', 'klant', 'project_taken', 'template')->firstOrFail();
    $keywords = WerkbonTeksten::where("werkbon_id", $id)->get()->keyBy("keyword");
    $opties = WerkbonTeksten::where("werkbon_id", $id)->where("type", "optie")->get()->groupBy("keyword");
    $inputs = WerkbonTeksten::where("type", "input")->where("werkbon_id", $id)->orWhere("type", "input_slot")->where("werkbon_id", $id)->get()->keyBy("keyword");
    $defPersonen = WerkbonTeksten::where("type", "offerte_aantal_personen")->where("werkbon_id", $id)->first();
    $klant = Klanten::where("id", $werkbon->klant_id)->first();
    $users = User::get()->keyBy("id");
    $pregel = Planning::where("project_id", $werkbon->project_id)->whereNotNull("aantal_personen")->first();

    $project = $werkbon->project;
    if(isset($project)){
      $offerte = Offertes::where("offertenummer", $project->projectnr)->first();
    }
    if(isset($offerte)){
      $offerteteksten = Offerteteksten::where("offerte_id", $offerte->id)->get()->keyBy("keyword");
      $offerteplanning = OffertePlanning::where("offerte_id", $offerte->id)->get();
    }
    else{
      $offerte = false;
      $offerteteksten = false;
      $offerteplanning  = false;
    }
    $data = [
      "keywords" => $keywords,
      "pregel" => $pregel,
      "werkbon" => $werkbon,
      "opties" => $opties,
      "input" => $inputs,
      "klant" => $klant,
      "offerte" => $offerte,
      "offerteteksten" => $offerteteksten,
      "offerteplanning" => $offerteplanning,
      "users" => $users,
      "defPersonen" => isset($defPersonen) ? json_decode($defPersonen->value, true) ?? [] : [],
    ];
    return PDF::loadView("werkbonnen.pdf.".getClientId()."werkbon".$werkbon->template_id, $data);
  }

  public function new($subdomein, $id){
    $template = WerkbonnenTemplates::where("id", $id)->firstOrFail();
    $keywords = WerkbonnenTemplateKeywords::where('template_id', $template->id)->with("listInputTemplates")->orderBy('order_index', 'ASC')->get();

    $query = Project::where("active", 1)->with('offerte', 'klant', 'uncompleted_taken', 'planning')->orderBy('created_at', 'desc');

    if (getSettingValue('werkbon_projecten_alleen_opdracht') == 'ja') {
      $query->where('status', 'Opdracht');
    }

    if (hasPermission("Gekoppelde projecten inzien")) {
      $query->whereHas('custom', function ($query) {$query->where(['type' => 'user', 'value' => Auth::user()->id]);});
    }

    $projecten = $query->get();
    
    $opties = $keywords->where("type", "optie")->groupBy("index");

    //    Exclude keywords with own var
    $keywords = resetIndex($keywords->whereNotIn('type', ['optie']));


    return view("werkbonnen.create",[
      "template" => $template,
      "keywords" => $keywords,
      "projecten" => $projecten,
      "opties" => $opties,
    ]);
  }
  public function store(Request $request){
    $project = Project::where("id", $request->project)->with('_bv')->first();
    $klant = Klanten::where("id", $request->klant)->with('_bv')->first();
    $template = WerkbonnenTemplates::where('id', $request->template)->first();

    $werkbonnummer = Werkbonnen::werkbonnummer(($project->_bv ?? firstBvObject()), ($project->id ?? null));

    $images = $this->storeImages($request);

    if(($request->betalingsoptie ?? '') == 'Pin' || ($request->betalingsoptie ?? '') == 'Contant' || ($request->betalingsoptie ?? '') == 'Online betaald'){
      $gefactureerd = 1;
    }

    $werkbonId = Werkbonnen::insertGetId([
      "werkbonnummer" => $werkbonnummer,
      "template_id" => $request->template,
      "bv" => $project->bv ?? firstBv(),
      "user_id" => $request->user ?? Auth::user()->id,
      "klant_id" => $project->klant_id ?? ($klant->id ?? null),
      "contactpersoon_id" => $request->contactpersoon ?? null,
      "project_id" => $project->id ?? null,
      "vestiging_id" => $request->vestiging ?? null,
      "files" => json_encode($images ?? []),
      "betalingsoptie" => $request->betalingsoptie ?? null,
      "handtekeningen" => "[]",
      "datum" => Carbon::now()->format("d-m-Y"),
      "opmerking" => $request->opmerking ?? null,
      "gefactureerd" => $gefactureerd ?? 0,
      "token" => randomString(25),
      "created_at" => Carbon::now(),
    ]);
    $werkbon = Werkbonnen::where("id", $werkbonId)->with("project", "user", "template")->first();

    $this->storeKeywords($request, $werkbon->id);
    $this->storeTaken($request, $werkbon->id);
    $this->storeOpties($request, $werkbon->id);
    $werkbon->finishWerkbon($project, $this->select_finish);

    if(isset($request->offerteAantalPersonen)){
      WerkbonTeksten::insert([
        "werkbon_id" => $werkbonId,
        "input_id" => null,
        "keyword" => "offerteAantalPersonen",
        "titel" => null,
        "type" => "offerte_aantal_personen",
        "value" => json_encode($request->offerteAantalPersonen),
        "created_at" => Carbon::now(),
      ]);
    }
    if(isset($request->planningNaam)){
      WerkbonRegels::insert([
        "werkbon_id" => $werkbonId,
        "naam" => $request->planningNaam ?? "",
        "aantal" => $request->planningPrijs ?? "",
        "prijs" => $request->planningPrijs ?? "",
        "btw" => $request->planningBtw ?? 21,
        "created_at" => Carbon::now(),
      ]);
    }

    if(isset($project)){
      if(getSettingValue('werkbon_project_complete') != 'nee'){
        $project->active = 0;
        $project->save();
      }
    }

    $apiWerkbonnenController = new apiWerkbonnenController();
    $apiWerkbonnenController->sendmail($werkbon);

    $log = Auth::user()->name . " " . Auth::user()->lastname . " heeft de werkbon '" . $werkbon->werkbonnummer . "' (" . $werkbon->id . ") aangemakt.";
    actLog($log, Auth::user()->id, 5);

    return redirect('werkbonnen?stadium=Open')->with("status", "Werkbon aangemaakt!");
  }
  public function edit($subdomein, $id){
    $werkbon = Werkbonnen::where("id", $id)->with("klant", "project", "project_taken", "regels", "keywords", "vestiging")->firstOrFail();
    $template = WerkbonnenTemplates::where("id", $werkbon->template_id)->firstOrFail();
    $keywords = WerkbonnenTemplateKeywords::where('template_id', $template->id)->with('listInputTemplates')->orderBy('order_index', 'ASC')->get();

    $projecten = Project::where("active", 1)->with('offerte', 'klant', 'uncompleted_taken', 'planning')->orderBy('created_at', 'desc')->get();

    $opties = $keywords->where("type", "optie")->groupBy("index");

    //    Exclude keywords with own var
    $keywords = resetIndex($keywords->whereNotIn('type', ['optie']));

    return view("werkbonnen.create",[
      "werkbon" => $werkbon,
      "template" => $template,
      "keywords" => $keywords,
      "projecten" => $projecten,
      "opties" => $opties,
    ]);
  }
  public function update(Request $request){
    $project = Project::where("id", $request->project)->with('_bv')->first();
    $werkbon = Werkbonnen::where("id", $request->werkbon)->firstOrFail();

    $images = $this->storeImages($request);

    if(($request->betalingsoptie ?? '') == 'Pin' || ($request->betalingsoptie ?? '') == 'Contant' || ($request->betalingsoptie ?? '') == 'Online betaald'){
      $gefactureerd = 1;
    }

    Werkbonnen::where("id", $werkbon->id)->update([
      "user_id" => $request->user ?? $werkbon->user_id,
      "contactpersoon_id" => $request->contactpersoon ?? null,
      "gefactureerd" => $gefactureerd ?? 0,
      "files" => json_encode($images),
      "betalingsoptie" => $request->betalingsoptie,
      "opmerking" => $request->opmerking ?? null,
      "vestiging_id" => $request->vestiging ?? null,
    ]);

    if(isset($project)){
      Werkbonnen::where('id', $werkbon->id)->update([
        "klant_id" => $project->klant_id ?? null,
        "project_id" => $project->id ?? null,
      ]);
    }
    if(isset($request->klant)){
      Werkbonnen::where('id', $werkbon->id)->update([
        "klant_id" => $request->klant ?? null,
      ]);
    }

    $werkbon->refresh();

    $this->storeKeywords($request, $werkbon->id);
    $this->storeTaken($request, $werkbon->id);
    $this->storeOpties($request, $werkbon->id);
    $werkbon->finishWerkbon($project, $this->select_finish);

    if(isset($request->offerteAantalPersonen)){
      WerkbonTeksten::insert([
        "werkbon_id" => $werkbon->id,
        "input_id" => null,
        "keyword" => "offerteAantalPersonen",
        "titel" => null,
        "type" => "offerte_aantal_personen",
        "value" => json_encode($request->offerteAantalPersonen),
        "created_at" => Carbon::now(),
      ]);
    }
    if(isset($request->planningNaam)){
      WerkbonRegels::where("werkbon_id", $werkbon->id)->update([
        "werkbon_id" => $werkbon->id,
        "naam" => $request->planningNaam ?? "",
        "aantal" => $request->planningPrijs ?? "",
        "prijs" => $request->planningPrijs ?? "",
        "btw" => $request->planningBtw ?? 21,
        "created_at" => Carbon::now(),
      ]);
    }

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de werkbon '".$werkbon->werkbonnummer."' (".$werkbon->id.") gewijzigd.";
    actLog($log, Auth::user()->id, 5);
    return redirect('werkbonnen')->with("status", "Werkbon gewijzigd!");
  }

  private function attachFacturen($werkbonnen){
    foreach($werkbonnen as $werkbon){
      $facturen = Facturen::where("werkbonnen", "like", "%\"".$werkbon->id."\"%")->where("status", "!=", "Betaald")->get();
      $werkbon->facturen = $facturen;
    }
    return $werkbonnen;
  }

  private function storeImages($request){
    $images = [];
    foreach($request->file('image') ?? [] as $image){
      $file = ExplorerFiles::insertFromRequest([
        'file' => $image,
        'name' => $image->getClientOriginalName(),
        'path' => "/werkbonnen/temp/",
        'description' => null,
      ]);
      $images[] = $file->src;
    }
    foreach($request->image_hidden ?? [] as $src){
      if(!$src){continue;}
      $images[] = $src;
    }

    return $images;
  }
  private function storeKeywords($request, $werkbonId){
    $images = $request->file('items');
    WerkbonTeksten::where('werkbon_id', $werkbonId)->update(['werkbon_id' => '-'.$werkbonId]);
    WerkbonRegels::where('werkbon_id', $werkbonId)->update(['werkbon_id' => '-'.$werkbonId]);

    foreach ($request->items ?? [] as $id => $value){
      try{
        $keyword = WerkbonnenTemplateKeywords::where('id', $id)->first();
        $data = json_decode($keyword->data ?? '[]');

        if($keyword->type == 'list_select'){
          foreach($data->rows as $row){
            $row->value = $value[$row->name];
            unset($value[$row->name]);
          }
          foreach ($value ?? [] as $custom => $v){
            $data->rows[] = [
              'name' => $custom,
              'value' => $v,
            ];
          }
          $value = json_encode($data);
        }
        if($keyword->type == 'list_input'){
          $data->rows = [];
          foreach ($value ?? [] as $string => $v){
            if(isset($request->list_input_dataset_item_id[$string])){
              $datasetitem = OffertesDatasetsItems::where('id', $request->list_input_dataset_item_id[$string])->first();
              if($datasetitem){
                $datasetitem->value = json_decode($datasetitem->value);
              }
            }
            $data->rows[] = [
              'datasetitem' => $datasetitem ?? '',
              'name' => $request->list_input_name[$string] ?? '',
              'values' => $v,
            ];
          }
          $value = json_encode($data);
        }
        if($keyword->type == 'signature'){
          if(isset($value)){
            (substr($value, 0, 9) == "existing_") ? $value = str_replace('existing_', '', $value) : $value = storeBaseSignature($value);
          }
        }
        if($keyword->type == 'image'){
          if(isset($images[$id])){
            $img = $images[$id];
            $file = ExplorerFiles::insertFromRequest([
              'file' => $img,
              'name' => $img->getClientOriginalName(),
              'path' => "/werkbonnen/$werkbonId/keywords/",
              'description' => null,
            ]);
            $value = $file->src;
          }
        }
        if($keyword->type == 'images'){
          $value = json_encode($value);
        }

        if($keyword->type == 'select_finish'){
          if($value == $data->finishValue){
            $this->select_finish = "finish";
          }elseif ($value == $data->opvolgValue){
            $this->select_finish = "opvolgen";
          }
        }

        WerkbonTeksten::insert([
          "werkbon_id" => $werkbonId,
          "input_id" => $id,
          "keyword" => $keyword->keyword,
          "titel" => $keyword->naam,
          "type" => $keyword->type,
          "value" => $value,
          "created_at" => Carbon::now(),
        ]);
      }
      catch (\Exception $e){ actError($e); }

    }
    foreach ($request->items_hidden ?? [] as $id => $value){
      $keyword = WerkbonnenTemplateKeywords::where('id', $id)->first();
      $data = json_decode($keyword->data ?? '[]');

      WerkbonTeksten::insert([
        "werkbon_id" => $werkbonId,
        "input_id" => $id,
        "keyword" => $keyword->keyword,
        "titel" => $keyword->naam,
        "type" => $keyword->type,
        "value" => $value,
        "created_at" => Carbon::now(),
      ]);
    }
  }

  private function storeOpties($request, $werkbonId){
    foreach($request->opties ?? [] as $id => $optie){
      $keyword = WerkbonnenTemplateKeywords::where('id', $id)->first();

      for($i = 0; $i < count($optie[array_key_first($optie)]); $i++){
        $value = [];
        foreach(json_decode($keyword->rows) as $row){
          $value[$row->name] = $optie[$row->name][$i] ?? "";
        }
        if(!array_filter($value)){continue;}
        WerkbonTeksten::insert([
            "werkbon_id" => $werkbonId ?? null,
            "input_id" => $id,
            "keyword" => $keyword->keyword ?? null,
            "titel" => $keyword->titel ?? null,
            "type" => $keyword->type ?? null,
            "value" => json_encode($value) ?? null,
            "created_at" => Carbon::now(),
          ]);

      }
    }
  }
  private function storeTaken($request, $werkbonId){
    foreach($request->taken ?? [] as $id => $state){
      if(getSettingValue('werkbon_projecttaken_complete') != 'nee'){
        ProjectTaken::where('id', $id)->update(['completed' => 1]);
      }
      WerkbonnenProjectTaken::insert([
        'werkbon_id' => $werkbonId,
        'taak_id' => $id,
      ]);
    }
  }

  public function ondertekenen($subdomein, $token){
    $werkbon = Werkbonnen::where('token', $token)->with('_bv', 'template')->firstOrFail();

    $werkbon->mail_geopend = Carbon::now();
    $werkbon->save();

    if(!$werkbon->template->klant_signature){
      return $this->viewPdf(null, $werkbon->id);
    }

    return view('werkbonnen.ondertekenen', [
      'werkbon' => $werkbon,
    ]);
  }
  public function ondertekend(Request $request){
    $werkbon = Werkbonnen::where('token', $request->token)->firstOrFail();

    if(!isset($request->signature)){
      return redirect('werkbonnen/ondertekenen/'.$werkbon->token)->with('warning', 'Handtekening kan niet leeg zijn!');
    }

    $werkbon->klant_signature = storeBaseSignature($request->signature);
    $werkbon->signed_at = Carbon::now();
    $werkbon->status = 'Afgerond';
    $werkbon->save();

    $mail = getSettingValue('werkbon_email_new');
    if(isset($mail)){
      Mail::to($mail)->send(new NewWerkbon($werkbon, getSubdomein()));
    }

    $log = "Werkbon '".$werkbon->werkbonnummer."' is zojuist ondertekend.";
    actLog($log, null, 5);
    return redirect('werkbonnen/ondertekenen/'.$werkbon->token);
  }

  private function overzichtVelden(){
    $templateVelden = getSettingJson('werkbon_overzicht_tonen');
    $templateVelden["werkbonVelden"] = [];

    $cleanIndexTemplateVelden = resetIndex($templateVelden);
    if (is_array($cleanIndexTemplateVelden[0])) {
      $templateVelden["werkbonVelden"] = array_merge(...array_values($templateVelden));
    }

    return($templateVelden);
  }
}
