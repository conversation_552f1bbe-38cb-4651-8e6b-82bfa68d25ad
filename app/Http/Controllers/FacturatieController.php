<?php

namespace App\Http\Controllers;
use App\AccorderenFacturen;
use App\Http\Controllers\Api\AccorderenController as ApiAccorderenController;
use App\Classes\BladeDataFormatter;
use App\ExactGlobe\VAT;
use App\King\BtwCodes;
use App\FacturatieKlantAttachments;
use App\Facturen;
use App\FacturenAdres;
use App\FacturenProjectTaken;
use App\FacturenXml;
use App\Factuurregels;
use App\FactuurregelsDatasets;
use App\Http\Controllers\Api\FileController;
use App\OffertesDetailsTemplate;
use App\OffertesTemplatesDetailsTemplates;
use App\UploadedDatasetItems;
use App\Offertes;
use App\OffertesDatasets;
use App\Werkbonnen;
use App\EboekhoudenBtw;
use App\FacturenOffertes;
use App\FacturenProformas;
use App\FacturenProjecten;
use App\FacturenWerkbonnen;
use App\FactuurregelsCustomFields;
use App\Http\Controllers\Api\FacturatieController as ApiFacturatieController;
use App\JobQueue;
use PDF;
use Response;
use Auth;
use SoapBox\Formatter\Formatter;
use Storage;
use App\Project;
use App\Klanten;
use App\MandagenregistersRegels;
use App\Tarieven;
use App\UrenRegistratie;
use Illuminate\Http\Request;
use Carbon\Carbon;

class FacturatieController extends Controller
{
  public function openEmailFactuur($subdomein, $token){
    $token = str_replace('.pdf', '', $token);
    $factuur = Facturen::where('token', $token)->orderBy('id', 'desc')->firstOrFail();

    $factuur->mail_geopend = Carbon::now();
    $factuur->save();

    return $this->factuurPdf($subdomein, $factuur->id);
  }

  public static function generatePDF($id){
    $factuur = Facturen::where("id", $id)->with("regels", "klant", "BV", "user", "adres", "abonnement", "project_taken", "_projecten", "_werkbonnen", "_proformas", "_offertes")->firstOrFail();
    $klant = Klanten::where('id', $factuur->klant_id)->with('vestiging')->first();

    setcookie("downloadPdf", false, time()-1, "/");

    $blade = "facturatie.pdf".($factuur->is_proforma ? ".proforma.C" : ".C").getClientId()."factuur";
    $data = [
      "factuur" => $factuur,
      "klant" => $klant,
      "werkbonnen" => $factuur->_werkbonnen,
      "projecten" => $factuur->_projecten,
      "offertes" => $factuur->_offertes,
      "facturen" => $factuur->_proformas,
      "pages" => ''
    ];

    $bladeFormatter = new BladeDataFormatter($blade, $data);
    $data = $bladeFormatter->format();

    $pdf = PDF::loadView($blade, $data);
    $data['pages'] = getPDFPages($pdf);

    return PDF::loadView($blade, $data);
  }
  public function factuurPdf($subdomein, $id){
    $factuur = Facturen::where("id", $id)->with("regels", "klant", "BV", "user", "adres", "files", "abonnement", "project_taken")->firstOrFail();
//    $src = $factuur->storeAsFile();
//    if($src){
//      removeCookie('downloadPdf');
//      return FileController::displayStorageFile($src, $factuur->factuurnummer ?? "n.t.b");
//    }

    $pdf = self::generatePdf($factuur->id);
    removeCookie('downloadPdf');
    return $pdf->stream($factuur->klant->naam.' '.$factuur->factuurnummer ?? "n.t.b");
  }
  public function factuurPdfByToken($subdomein, $token){
    $factuur = Facturen::where('token', $token)->orderBy('id', 'desc')->firstOrFail();
    return $this->factuurPdf($subdomein, $factuur->id);
  }

  public function refresh(Request $request){
    $factuur = Facturen::where('token', $request->token)->orderBy('id', 'desc')->first();
    return response()->json(['factuur'=>$factuur], 201);
  }

  public function facturen(){
    return view("facturatie.index",[
        "datasets" => OffertesDatasets::orderBy("naam", "ASC")->get(),
        "snelstartGrootboeken" => SnelstartController::grootboeken("functie"),
    ]);
  }

  public function create(){
    return view("facturatie.create",[
      "details" => OffertesDetailsTemplate::with('templates')->get(),
    ]);
  }

  public function storeFactuur(Request $request){
    if(isset($request->klantId)){
      $klantId = $request->klantId;
      $klant = Klanten::where("id", $klantId)->first();
      $bv = $klant->bv ?? ($request->bv ?? null);
    }

    $factuurId = Facturen::insertGetId([
      'klant_id' => $klantId ?? null,
      'user_id' => Auth::user()->id,
      'bv' => $bv ?? firstBv(),
      'datum' => Carbon::now()->format('Y-m-d'),
      'betalingstermijn' => $request->betalingstermijn,
      'reporting_date' => $request->reporting_date ?? null,
      'periode' => $request->periode ?? null,
      'inleiding' => $request->inleiding ?? null,
      'slot' => $request->slot ?? null,
      'referentie' => $request->referentie ?? null,
      "posting_description" => $request->posting_description ?? null,
      'status' => 'Uitgebracht',
      'betaald_op' => null,
      'token' => randomString(25),
      'exact_globe_journal' => $request->journal ?? null,
      "business_central_kostenplaats" => $request->bc_kostenplaats ?? null,
      "business_central_kostendrager" => $request->bc_kostendrager ?? null,
      "king_dagboek" => $request->king_dagboek ?? null,
      "g_rekening_waarde" => $request->factuur_grekening ?? null,
      "g_rekening_nummer" => $request->factuur_grekeningNr ?? null,
      "btw_verlegd" => isset($request->btw_verlegd),
      "uren_van" => $request->uren_van ?? null,
      "uren_tot" => $request->uren_tot ?? null,
      'created_at' => Carbon::now(),
    ]);
    $factuur = Facturen::find($factuurId);
    $factuur->setFactuurnummer();
    FacturenAdres::insert([
      "factuur_id" => $factuurId,
      "klant" => $request->fa_klant,
      "tav" => $request->fa_tav,
      "straat" => $request->fa_straat,
      "huisnummer" => $request->fa_huisnummer,
      "toevoeging" => $request->fa_toevoeging,
      "postcode" => $request->fa_postcode,
      "plaats" => $request->fa_plaats,
      "land" => $request->fa_land,
    ]);

    $this->factuurWerkbonnen($factuurId, $request);
    $this->factuurOffertes($factuurId, $request);
    $this->factuurProjecten($factuurId, $request);
    $this->factuurProformas($factuurId, $request);
    $this->factuurUren($factuurId, $request);

    $this->storeRegels($request, $factuurId);
    $this->storeDatasetRegels($request, $factuurId);

    $this->storeMandagen($factuurId, $request);

    $factuurApi = new ApiFacturatieController();
    $factuurApi->explorerFiles($request,$factuurId);

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de factuur ".$factuur->factuurnummer." uitgebracht";
    actLog($log, Auth::user()->id, 27);

    JobQueue::addJob([
      'url' => 'api/facturatie/generatepdf',
      'instance_id' => $factuurId,
    ]);

    return redirect("facturatie/facturen?stadium=Open")->with("status", "Factuur uitgebracht");
  }
  public function editFactuur($subdomein, $id){
    $factuur = Facturen::where("id", $id)->with("regels", "klant", "adres", "project_taken", "project", "_werkbonnen", "_proformas", "_offertes")->firstOrFail();
    foreach($factuur->regels as $regel){
      $regel->exact_online_btw = $regel->exact_online_btw() ?? null;
      $regel->exact_online_gl = $regel->exact_online_gl() ?? null;
      $regel->exact_online_item = $regel->exact_online_item() ?? null;
    }

    return view("facturatie.create",[
      "details" => OffertesDetailsTemplate::with('templates')->get(),
      "factuur" => $factuur,
    ]);
  }
  public function updateFactuur(Request $request){
    $accorderen = isset($request->accorderen_edit);
    $factuur = Facturen::where("id", $request->factuur)->firstOrFail();

    $reset_accepted = false;
    if($factuur->is_proforma && $factuur->status == 'Afgewezen'){
      $reset_accepted = true;
    }

    Facturen::where('id', $request->factuur)->update([
      'inleiding' => $request->inleiding,
      'slot' => $request->slot,
      'betalingstermijn' => $request->betalingstermijn,
      'reporting_date' => $request->reporting_date ?? null,
      'periode' => $request->periode ?? null,
      'referentie' => $request->referentie,
      "posting_description" => $request->posting_description ?? null,
      'status' => 'Uitgebracht',
      'sent_to' => null,
      'sent_at' => null,
      'mail_geopend' => null,
      'exact_globe_journal' => $request->journal ?? null,
      'exact_globe_update_required' => 1,
      'proforma_accepted' => $reset_accepted ? null : $factuur->proforma_accepted,
      'proforma_accepted_at' => $reset_accepted ? null : $factuur->proforma_accepted_at,
      "business_central_kostenplaats" => $request->bc_kostenplaats ?? null,
      "business_central_kostendrager" => $request->bc_kostendrager ?? null,
      "king_dagboek" => $request->king_dagboek ?? null,
      "g_rekening_waarde" => $request->factuur_grekening ?? null,
      "g_rekening_nummer" => $request->factuur_grekeningNr ?? null,
      "btw_verlegd" => isset($request->btw_verlegd)
    ]);

    FacturenAdres::updateOrInsert([
      "factuur_id" => $factuur->id,
      ],[
      "klant" => $request->fa_klant,
      "tav" => $request->fa_tav,
      "straat" => $request->fa_straat,
      "huisnummer" => $request->fa_huisnummer,
      "toevoeging" => $request->fa_toevoeging,
      "postcode" => $request->fa_postcode,
      "plaats" => $request->fa_plaats,
      "land" => $request->fa_land,
    ]);

    if(!$accorderen){
      AccorderenFacturen::where('factuur_id', $factuur->id)->update(['factuur_id' => "-$factuur->id"]);
    }else{
      $factuur->accorderenStatus(['status' => '1']);
      Facturen::where('id', $factuur->id)->update(['status' => 'Accorderen']);
    }

    $this->clearFactuur($factuur->id);
    $this->storeRegels($request, $factuur->id);
    $this->storeDatasetRegels($request, $factuur->id);

    $this->storeMandagen($factuur->id, $request);

    $this->factuurWerkbonnen($factuur->id, $request);
    $this->factuurOffertes($factuur->id, $request);
    $this->factuurProformas($factuur->id, $request);
    $this->factuurUren($factuur->id, $request);

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft factuur ".$factuur->factuurnummer." gewijzigd";
    actLog($log, Auth::user()->id, 27);

  $factuurApi = new ApiFacturatieController();
  $factuurApi->explorerFiles($request,$factuur->id);

    JobQueue::addJob([
      'url' => 'api/facturatie/generatepdf',
      'instance_id' => $factuur->id,
    ]);

    if($accorderen){
      $request->status = $accorderen;
      $msg = 'De proforma '. $factuur->factuurnummer . ' Kan geaccordeerd worden.';
      ApiAccorderenController::sendAccordeerMail($factuur, 'proformas', 'Accorderen proforma', $msg, $request);
      return redirect('accorderen/proformas')->with("status", "Proforma gewijzigd en geaccordeerd");
    }

    return redirect($factuur->is_proforma ? 'facturatie/proforma?stadium=Open' : 'facturatie/facturen?stadium=Open')->with("status", "Factuur gewijzigd!");
  }

  private function factuurWerkbonnen($f, $request){
    FacturenWerkbonnen::where('factuur_id', $f)->delete();
    foreach ($request->werkbonnen ?? [] as $id){
      $werkbon = Werkbonnen::where("id", $id)->with('project')->first();
      Werkbonnen::where('id', $werkbon->id)->update([
        'gefactureerd' => 1,
        'status' => 'Afgerond',
      ]);
      FacturenWerkbonnen::insert([
        "factuur_id" => $f,
        "werkbon_id" => $id,
      ]);
    }
  }
  private function factuurOffertes($f, $request){
    FacturenOffertes::where('factuur_id', $f)->delete();
    foreach ($request->offertes ?? [] as $id){
      Offertes::where("id", $id)->update(["gefactureerd" => 1]);
      FacturenOffertes::insert([
        "factuur_id" => $f,
        "offerte_id" => $id,
      ]);
    }
  }
  private function factuurProjecten($f, $request){
    FacturenProjecten::where('factuur_id', $f)->delete();
    if(!isset($request->project)){return;}
    Project::where("id", $request->project)->update(["gefactureerd" => 1]);
    FacturenProjecten::insert([
      "factuur_id" => $f,
      "project_id" => $request->project,
    ]);

    foreach($request->taken[$request->project] ?? [] as $taakId){
      FacturenProjectTaken::insert([
        "factuur_id" => $f,
        "taak_id" => $taakId,
      ]);
    }
  }
  private function factuurProformas($f, $request){
    FacturenProformas::where('factuur_id', $f)->delete();
    foreach ($request->proformas ?? [] as $id){
      Facturen::where("id", $id)->update([
        "proforma_gefactureerd" => 1,
        "proforma_gefactureerd_op" => Carbon::now(),
        "status" => 'Gefactureerd',
      ]);
      FacturenProformas::insert([
        "factuur_id" => $f,
        "proforma_id" => $id,
      ]);
    }
  }

  private function factuurUren($f, $request){
    if(isset($request->uren_van) && isset($request->uren_tot) && isset($request->project)){
      $project = Project::where("id", $request->project)->first();
      UrenRegistratie::where('projectnummer', $project->projectnr)->whereBetween('datum', [$request->uren_van, $request->uren_tot])->update(['gefactureerd' => 1]);
    }
  }

  private function storeRegels($request, $id){
    $business_central_settings = json_decode(getSettingValue('business_central_settings', '[]'), true);
    $bc_default = $business_central_settings['default'] ?? null;

    foreach($request->naam ?? [] as $i => $row){
      if($row == null){ continue; }
      $order_indexes = array_keys($request->order_index);
      $order_index = array_search($i, $order_indexes);

      $btw = $request->btw[$i] ?? 0;
      $incl = isset($request->incl[$i]) ? 1 :0;
      $exact_vat = $request->exact_btw[$i] ?? null;
      $eboek_vat = $request->eb_btw[$i] ?? null;
      $king_vat = $request->king_btw_code[$i] ?? null;

      if(!isset($request->tekstregel[$i])){
        if($exact_vat){
            $code = VAT::where('code', $exact_vat)->first();
            $btw = $code->percentage ?? 0;
            $incl = $code->incl ?? 0;
        }
        if($eboek_vat){
            $code = EboekhoudenBtw::where('code', $eboek_vat)->first();
            $btw = $code->percentage ?? 0;
        }
        if(businessCentralLocal()->connected){
            $tax_group = businessCentralLocal()->getStoredTaxGroups()->where('id', $request->bc_tax_group[$i] ?? null)->first();
            $bc_tax_group = $tax_group->code ?? ($bc_default['tax_group'] ?? null);
            $bc_tax_area = $tax_group->tax_area_code ?? ($bc_default['tax_area'] ?? null);
            $bc_account = $request->bc_account[$i] ?? ($bc_default['sale_account'] ?? null);

            $btw = $tax_group->percentage ?? 21;
            $incl = 0;
        }
        if(exactOnlineLocal()->connected){
            $vat_code = $request->exact_online[$i]['vat'];
            $vat = exactOnlineLocal()->getStoredVat()->where('code', $vat_code)->first();

            $incl = $vat->incl ?? 0;
            $btw = $vat->percentage ?? 21;
        }
        if(kingConnected()){
            $vat = BtwCodes::where('king_id', $king_vat)->first();
            $btw = $vat->percentage ?? 21;
        }
      }

      $regel_id = Factuurregels::insertGetId([
        "factuur_id" => $id,
        "naam" => $request->naam[$i],
        "aantal" => isset($request->tekstregel[$i]) ? 0 : ($request->aantal[$i] ?? 1),
        "eenheid_id" => $request->eenheid[$i] ?? null,
        "prijs" => isset($request->tekstregel[$i]) ? 0 : ($request->prijs[$i] ?? 0),
        "btw" => $btw,
        "incl" => $incl,
        "tekstregel" => $request->tekstregel[$i] ?? 0,
        "detail_id" => $request->detail[$i] ?? null,
        "detail_based_on_id" => $request->detail_based_on[$i] ?? null,
        'eboek_btw' => $request->eb_btw[$i] ?? null,
        'exact_globe_vat' => $exact_vat,
        'exact_globe_gl' => $_POST['ledger'][$i] ?? null,
        'business_central_account' => $bc_account ?? null,
        'business_central_tax_group' => $bc_tax_group ?? null,
        'business_central_tax_area_code' => $bc_tax_area ?? null,
        'business_central_tax_id' => $request->bc_tax_group[$i] ?? null,
        'king_btw' => $king_vat,
        'king_grootboek' => $request->king_grootboek[$i] ?? null,
        'order_index' => $order_index,
        "created_at" => Carbon::now()
      ]);

      $this->storeCustomFields($request, $i, $regel_id);

      $regel = Factuurregels::find($regel_id);

      if(exactOnlineLocal()->connected && !isset($request->tekstregel[$i])){
        $regel->setExactOnlineMetadata('VAT', $request->exact_online[$i]['vat'] ?? null);
        $regel->setExactOnlineMetadata('GL', $request->exact_online[$i]['gl'] ?? null);
        $regel->setExactOnlineMetadata('item', $request->exact_online[$i]['item'] ?? null);
      }

      if(getSettingValue('exact_xml_export') == 'Aan'){
        FactuurregelsDatasets::insert([
          "regel_id" => $regel_id,
          "dataset_id" => 0,
          "item" => json_encode([
            'artikel' => strtoupper($request->naam[$i]) ?? null,
            'omschrijving' => $request->naam[$i] ?? null,
            'prijs' => $request->prijs[$i] ?? null,
            'kostprijs' => '0',
            'btw_code' => '03',
            'eenheid' => 'St.',
            'eenheidomschrijving' => 'Stuk',
            'assortimentcode' => '0007',
            'assortimentomschrijving' => 'Diverse'
          ]),
        ]);
      }
    }
  }
  private function storeCustomFields($request, $i, $regel_id){
    $custom_fields = getSettingJson('factuur_rows_custom_fields');
    if(!$custom_fields){ return; }
    foreach($request->custom[$i] ?? [] as $key => $value){
      FactuurregelsCustomFields::insert([
        'regel_id' => $regel_id,
        'keyword' => $key,
        'value' => $value,
      ]);
    }
  }
  private function storeDatasetRegels($request, $id){
    foreach($request->dataset_naam ?? [] as $i => $row){
      if($row == null){ continue; }
      $order_indexes = array_keys($request->order_index);
      $order_index = array_search($i, $order_indexes);

      $btw = $request->dataset_btw[$i] ?? 0;
      $incl =isset($request->dataset_incl[$i]) ? 1 :0;
      $exact_vat = $request->exact_btw[$i] ?? null;
      $eboek_vat = $request->eb_btw[$i] ?? null;
      $king_vat = $request->king_btw_code[$i] ?? null;

      if($exact_vat){
        $code = VAT::where('code', $exact_vat)->first();
        $btw = $code->percentage ?? 0;
        $incl = $code->incl ?? 0;
      }

      if($eboek_vat){
        $code = EboekhoudenBtw::where('code', $eboek_vat)->first();
        $btw = $code->percentage ?? 0;
      }

      if(kingConnected() && $king_vat){
        $vat = BtwCodes::where('king_id', $king_vat)->first();
        $btw = $vat->percentage ?? 21;
      }

      $fId = Factuurregels::insertGetId([
        "factuur_id" => $id,
        "naam" => $request->dataset_naam[$i],
        "aantal" => $request->dataset_aantal[$i] ?? 1,
        "prijs" => $request->dataset_prijs[$i] ?? 0,
        "btw" => $btw,
        "incl" => $incl,
        "detail_id" => $request->detail[$i] ?? null,
        "detail_based_on_id" => $request->detail_based_on[$i] ?? null,
        'exact_globe_vat' => $exact_vat,
        'exact_globe_gl' => $_POST['ledger'][$i] ?? null,
        'eboek_btw' => $eboek_vat,
        'king_btw' => $king_vat,
        'king_grootboek' => $request->king_grootboek[$i] ?? null,
        'order_index' => $order_index,
        "created_at" => Carbon::now()
      ]);

      FactuurregelsDatasets::insert([
        "regel_id" => $fId,
        "dataset_id" => $request->dataset_dataset[$i] ?? null,
        "item" => $request->dataset_item[$i] ?? null,
      ]);
    }
  }

  private function storeMandagen($factuurId, $request){
    try{
      if(isset($request->mandagen)){
        foreach($request->mandagen as $user){
          foreach($user['dates'] as $datum => $uren){
            MandagenregistersRegels::insert([
              'factuur_id' => $factuurId,
              'datum' => $datum,
              'user_id' => $user['user'],
              'uren' => $uren,
            ]);
          }
        }
      }
    }catch (\Exception $e){
      actError($e);
    }
  }

  // Proforma's
  public function indexProforma(){
    $data = $this->facturen()->getData();
    $data['isProforma'] = true;
    return view("facturatie.index", $data);
  }
  public function storeProforma(Request $request){
    try{
      $bv = $this->recogniseBv($request);
      $factuurnummer_instance = Facturen::proformanummer([
        'project' => $request->project,
        'offertes' => $request->offertes,
        'bv' => $bv,
      ]);

      $id = Facturen::insertGetId([
        "factuurnummer" => $factuurnummer_instance->factuurnummer,
        "factuurnummer_prefix" => $factuurnummer_instance->prefix,
        "factuurnummer_afterfix" => $factuurnummer_instance->afterfix,
        "factuurnummer_jaar" => $factuurnummer_instance->date,
        "klant_id" => $request->klantId,
        "user_id" => Auth::user()->id,
        "bv" => $bv,
        "datum" => Carbon::now()->format("Y-m-d"),
        "betalingstermijn" => $request->betalingstermijn,
        'periode' => $request->periode ?? null,
        "inleiding" => $request->inleiding ?? null,
        "slot" => $request->slot ?? null,
        "referentie" => $request->referentie ?? null,
        "posting_description" => $request->posting_description ?? null,
        "status" => 'Uitgebracht',
        "token" => randomString(25),
        "created_at" => Carbon::now(),
        "werkbonnen" => json_encode($request->werkbonnen ?? []),
        "offertes" => json_encode($request->offertes ?? []),
        "projecten" => json_encode([$request->project] ?? []),
        "business_central_kostenplaats" => $request->bc_kostenplaats ?? null,
        "business_central_kostendrager" => $request->bc_kostendrager ?? null,
        "is_proforma" => 1
      ]);

      $this->storeFactuuradres($id, $request);
      $this->storeRegels($request, $id);
      $this->completeWerkbonnen($id,$request);

      $this->factuurOffertes($id, $request);
      $this->factuurProjecten($id, $request);
      $this->factuurProformas($id, $request);
      $this->factuurUren($id, $request);

      $log = Auth::user()->name." ".Auth::user()->lastname." heeft de factuur ".$factuurnummer_instance->factuurnummer." uitgebracht";
      actLog($log, Auth::user()->id, 27);

      return redirect("facturatie/proforma")->with("status", "Pro forma uitgebracht");
    }
    catch (\Exception $e){
      actError($e);
      return redirect("facturatie/proforma")->with("warning", "Er is iets foutgegaan!");
    }
  }

  public function ondertekenenProforma($subdomain, $token){
    $factuur = Facturen::where(['token' => $token, 'is_proforma' => 1])->with('_bv')->orderBy('id', 'desc')->firstOrFail();
    Facturen::where('token', $token)->update(['mail_geopend' => Carbon::now()]);

    return view('facturatie.proforma.ondertekenen', [
      'factuur' => $factuur,
    ]);
  }
  public function ondertekendProforma(Request $request){
    $redirect = redirect('facturatie/proforma/ondertekenen/'.$request->token);

    try {
      $factuur = Facturen::where('token', $request->token)->orderBy('id', 'desc')->with('_bv', 'klant')->firstOrFail();

      if(!isset($request->signature)){ return $redirect; }

      if($request->signature == '_decline'){
        Facturen::where('id', $factuur->id)->update([
          'proforma_accepted' => 0,
          'proforma_accepted_at' => Carbon::now(),
          'status' => 'Afgewezen'
        ]);
        $factuur->refresh();
        $factuur->notifProforma();
        return $redirect;
      }

      Facturen::where('id', $factuur->id)->update([
        'proforma_accepted' => 1,
        'proforma_accepted_at' => Carbon::now(),
        'status' => 'Akkoord',
        'signature' => storeBaseSignature($request->signature),
      ]);
      $factuur->refresh();
      $factuur->notifProforma();

      if(getSettingCheckbox('proforma_accorderen_auto_factuur')){
        $factuur->cloneFactuur(hasModule('Accorderen') ? 'Accorderen' : 'Uitgebracht');
      }

      $this->storeKlantAttachments($request, $factuur->id);

      return $redirect;
    }
    catch (\Exception $e){
      actError($e);
      return $redirect->with('warning', 'Er is iets foutgegaan!');
    }
  }

  private function clearFactuur($id){
    Factuurregels::where("factuur_id", $id)->update(['factuur_id' => "-$id"]);
  }
  private function storeKlantAttachments($request, $id){
    try{
      $data = json_decode((getSettingValue('facturatie_proforma_sign_attachments') ?? '[]'), true);

      foreach ($request->attachments ?? [] as $string => $files){
        $name = $data[$string]['name'] ?? 'Onbekend';
        foreach ($files ?? [] as $file){
          FacturatieKlantAttachments::insert([
            'name' => $name,
            'factuur_id' => $id,
            'file_id' => $file,
          ]);
        }
      }
    }
    catch (\Exception $e){
      actError($e);
    }
  }

  public function downloadXml(){
    $wrapper = getSettingValue('facturen_export_xml_wrapper');
    $method = getSettingValue('facturen_export_xml_method');
    $regelsWrapper = getSettingValue('facturen_export_xml_regels');
    $klantWrapper = getSettingValue('facturen_export_xml_klant');
    $factuurregelsWrapper = getSettingValue('facturen_export_xml_factuurregels');
    $prijsWrapper = getSettingValue('facturen_export_xml_prijs');
    $layout = json_decode(getSettingValue('facturen_export_xml_data') ?? '[]', true);
    $key = getSettingValue('facturen_export_xml_key');
    $sets = json_decode(getSettingValue('facturen_export_xml_datasets') ?? '[]', true);

    $facturen = Facturen::where(function ($q){ $q->where('status', 'Uitgebracht')->orWhere('status', 'like', 'Verzonden%'); })->where('active', 1)->with('klant', 'regels', '_bv')->get();
    if($method == 'paid'){
      $facturen = Facturen::where('status', 'Betaald')->with('klant', 'regels', '_bv')->get();
    }
    if($method == 'all'){
      $facturen = Facturen::with('klant', 'regels','_bv')->get();
    }

    $data = [];
    foreach($facturen as $factuur){
      $i = 1;
      if(!isset($factuur->_bv)){
        $bvname = '';
      }else{
        $bvname = $factuur->_bv->name;
      }
      $row = [];
      $row['@attributes'] = ['code' => '05', 'type' => 'V'];
      $row['Description'] = $factuur->factuurnummer;
      $row['Order']['@attributes'] = ['type' => 'V'];
      $row['CalcIncludeVAT'] = 'N';
      $row['Delivery']['Date']= Carbon::now()->format('Y-m-d');

      foreach($layout as $db => $name){
        if(!$name){continue;}

        if($db == 'valuta'){
          $row[$name]['@attributes'] = ['code' => 'EUR'];
          continue;
        }
        elseif($db == 'klant' && isset($factuur->klant)){
          if(!isset($factuur->klant)){continue;}
          if(!isset($klantWrapper)){continue;}

          $klantRow = [];
          $kLayout = $layout['klant'];

          if(isset($kLayout['id'])){$klantRow[$kLayout['id']]['@attributes'] = ['code' => $factuur->klant->debiteurnummer];}
          if(isset($kLayout['datum'])){$klantRow[$kLayout['datum']] = $factuur->datum;}
          $row[$klantWrapper] = $klantRow;
          continue;
        }
        elseif($db == 'regels'){
          if(!isset($factuurregelsWrapper)){continue;}

          $regelsLayout = $layout['regels'];
          foreach($factuur->regels as $regel){
            $factuurregel = [];
            if(isset($regel->dataset) && isset($sets[$regel->dataset->dataset_id])){
              $temp = $sets[$regel->dataset->dataset_id];
              $json = json_decode($regel->dataset->item, true);
              $factuurregel[$temp['name'] ?? 'Item']['@attributes'] = ['code' => $json[$temp['key']] ?? ''];
            }
            elseif(isset($regel->dataset) && $regel->dataset->dataset_id == 0){
              $json = json_decode($regel->dataset->item, true);
              $factuurregel['Item']['@attributes'] = ['code' => $json['artikel'] ?? ''];
            }
            if(isset($regelsLayout['aantal'])){$factuurregel[$regelsLayout['aantal']] = $regel->aantal;}
            if(isset($prijsWrapper)){
              $prijs = [];
              if(isset($regelsLayout['valuta'])){$prijs[$regelsLayout['valuta']]['@attributes'] = ['code' => 'EUR'];}
              if(isset($regelsLayout['prijs'])){$prijs[$regelsLayout['prijs']] = $regel->prijs;}
              $factuurregel[$prijsWrapper] = $prijs;
              $factuurregel[$prijsWrapper]['@attributes'] = ['type' => 'S'];
            }
            $factuurregel['@name'] = $factuurregelsWrapper;
            $factuurregel['@attributes'] = ['lineNo' => $i];
            $i++;
            $row[] = $factuurregel;
          }
          continue;
        }

        $row[$name] = $factuur[$db];
      }
      if($row['YourRef'] == ''){
        $row['YourRef'] = 'Factuur Langen Grondverzet';
      }
      $data[] = $row;
    }
    $xml = Formatter::make([$regelsWrapper => $data], Formatter::ARR)->toXml($wrapper);
    $file = "temp.xml";
    file_put_contents($file, $xml);
    return Response::download($file, "facturen.xml", ["Content-type:" => "application/xhtml+xml"]);
  }


  public static function factuurTotaal($id){
    $incl = 0;
    $excl = 0;
    $btw = 0;
    $data = [
      "incl" => $incl,
      "excl" => $excl,
      "btw" => $btw,
    ];

    $factuur = Facturen::where("id", $id)->with("regels")->first();

    if(!isset($factuur)){return $data;}

    foreach($factuur->regels as $regel){
      $total = $regel->prijs * $regel->aantal;
      if($regel->incl){
        $incl += $total;
        $excl += $total / (100 + $regel->btw) * 100;
      }
      else{
        $incl += $total / 100 * (100 + $regel->btw);
        $excl += $total;
      }
    }

    $incl = round($incl, 2);
    $excl = round($excl, 2);

    return [
      "incl" => $incl,
      "excl" => $excl,
      "btw" => round($incl - $excl, 2),
    ];

  }
  public static function factuurToDisk($id){
    $factuur = Facturen::where('id', $id)->first();
    $pdf = FacturatieController::generatePDF($id);
    $content = $pdf->download()->getOriginalContent();
    Storage::disk("client")->put('/pdf/facturen/'.$factuur->factuurnummer.".pdf", $content);
  }

  public static function instance(){
    return new FacturatieController();
  }

  private function storeFactuuradres($id, $request){
    FacturenAdres::insert([
      "factuur_id" => $id,
      "klant" => $request->fa_klant,
      "tav" => $request->fa_tav,
      "straat" => $request->fa_straat,
      "huisnummer" => $request->fa_huisnummer,
      "toevoeging" => $request->fa_toevoeging,
      "postcode" => $request->fa_postcode,
      "plaats" => $request->fa_plaats,
      "land" => $request->fa_land,
    ]);
  }

  private function completeWerkbonnen($id, $request){
    foreach ($request->werkbonnen ?? [] as $id){
      Werkbonnen::where('id', $id)->update(['gefactureerd' => 1, 'status' => 'Afgerond']);
    }
  }
  private function recogniseBv($request){
    if(isset($request->klantId)){
      $klant = Klanten::find($request->klantId);
      if(isset($klant->bv)){
        return $klant->bv;
      }
    }
    if(isset($request->project) && $request->project != 0){
      $project = Project::where('id', $request->project)->first();
      if(isset($project) && isset($project->bv)){
        return $project->bv;
      }

    }
    foreach($request->offertes ?? [] as $o){
      $offerte = Offertes::where('id', $o)->first();
      if(isset($offerte) && isset($offerte->bv)){
        return $offerte->bv;
      }
    }
    foreach ($request->werkbonnen ?? [] as $id){
      $werkbon = Werkbonnen::where('id', $id)->first();
      if(isset($werkbon) && $werkbon->bv){
        return $werkbon->bv;
      }
    }

    return firstBv();
  }

  public function exactXmlExport(Request $request){
    FacturenXml::insert([
      'factuur_id' => $request->id,
    ]);
  }
  public function downloadExactXmlExport($subdomein, $van, $tot){
    try{
      $van = Carbon::parse($van)->format('Y-m-d H:i:s');
      $tot = Carbon::parse($tot)->addDay()->format('Y-m-d H:i:s');
      $wrapper = getSettingValue('facturen_export_xml_wrapper');
      $method = getSettingValue('facturen_export_xml_method');
      $regelsWrapper = getSettingValue('facturen_export_xml_regels');
      $klantWrapper = getSettingValue('facturen_export_xml_klant');
      $factuurregelsWrapper = getSettingValue('facturen_export_xml_factuurregels');
      $prijsWrapper = getSettingValue('facturen_export_xml_prijs');
      $layout = json_decode(getSettingValue('facturen_export_xml_data') ?? '[]', true);
      $key = getSettingValue('facturen_export_xml_key');
      $sets = json_decode(getSettingValue('facturen_export_xml_datasets') ?? '[]', true);

      $facturen = Facturen::where(function ($q){ $q->where('status', 'Uitgebracht')->orWhere('status', 'like', 'Verzonden%'); })->where('active', 1)->where('datum', '>=', $van)->where('datum', '<=', $tot)->whereHas('xml_export', function ($query) {return $query->where('factuur_id', '!=', '');})->with('klant', 'regels')->get();
      $data = [];
      foreach($facturen as $factuur){
        $i = 1;
        if(!isset($factuur->_bv)){
          $bvname = '';
        }else{
          $bvname = $factuur->_bv->name;
        }
        $row = [];
        $row['@attributes'] = ['code' => '05', 'type' => 'V'];
        $row['Description'] = $factuur->factuurnummer;
        $row['Order']['@attributes'] = ['type' => 'V'];
        $row['CalcIncludeVAT'] = 'N';
        $row['Delivery']['Date']= Carbon::now()->format('Y-m-d');

        foreach($layout as $db => $name){
          if(!$name){continue;}

          if($db == 'valuta'){
            $row[$name]['@attributes'] = ['code' => 'EUR'];
            continue;
          }
          elseif($db == 'klant' && isset($factuur->klant)){
            if(!isset($factuur->klant)){continue;}
            if(!isset($klantWrapper)){continue;}

            $klantRow = [];
            $kLayout = $layout['klant'];

            if(isset($kLayout['id'])){$klantRow[$kLayout['id']]['@attributes'] = ['code' => $factuur->klant->debiteurnummer];}
            if(isset($kLayout['datum'])){$klantRow[$kLayout['datum']] = $factuur->datum;}
            $row[$klantWrapper] = $klantRow;
            continue;
          }
          elseif($db == 'regels'){
            if(!isset($factuurregelsWrapper)){continue;}

            $regelsLayout = $layout['regels'];
            foreach($factuur->regels as $regel){
              $factuurregel = [];
              if(isset($regel->dataset) && isset($sets[$regel->dataset->dataset_id])){
                $temp = $sets[$regel->dataset->dataset_id];
                $json = json_decode($regel->dataset->item, true);
                $factuurregel[$temp['name'] ?? 'Item']['@attributes'] = ['code' => $json[$temp['key']] ?? ''];
              }
              elseif(isset($regel->dataset) && $regel->dataset->dataset_id == 0){
                $json = json_decode($regel->dataset->item, true);
                $factuurregel['Item']['@attributes'] = ['code' => substr($json['artikel'], 0, 30) ?? ''];
              }
              if(isset($regelsLayout['aantal'])){$factuurregel[$regelsLayout['aantal']] = $regel->aantal;}
              if(isset($prijsWrapper)){
                $prijs = [];
                if(isset($regelsLayout['valuta'])){$prijs[$regelsLayout['valuta']]['@attributes'] = ['code' => 'EUR'];}
                if(isset($regelsLayout['prijs'])){$prijs[$regelsLayout['prijs']] = $regel->prijs;}
                $factuurregel[$prijsWrapper] = $prijs;
                $factuurregel[$prijsWrapper]['@attributes'] = ['type' => 'S'];
              }
              $factuurregel['@name'] = $factuurregelsWrapper;
              $factuurregel['@attributes'] = ['lineNo' => $i];
              $i++;
              $row[] = $factuurregel;
            }
            continue;
          }

          $row[$name] = $factuur[$db];
        }
        if($row['YourRef'] == ''){
          $row['YourRef'] = 'Factuur Langen Grondverzet';
        }
        $data[] = $row;
      }
      $xml = Formatter::make([$regelsWrapper => $data], Formatter::ARR)->toXml($wrapper);
      $file = "temp.xml";
      file_put_contents($file, $xml);

      return Response::download($file, "facturen.xml", ["Content-type:" => "application/xhtml+xml"]);
    }catch(\Exception $e){
      actError($e);
    }
  }


  public function downloadKlantenExactXmlExport($subdomein, $van, $tot){
    try {
      $van = Carbon::parse($van)->format('Y-m-d H:i:s');
      $tot = Carbon::parse($tot)->addDay()->format('Y-m-d H:i:s');
      $facturen = Facturen::with('klant', 'regels')->where('datum', '>=', $van)->where('datum', '<=', $tot)->whereHas('xml_export', function ($query) {return $query->where('factuur_id', '!=', '');})->where('active', 1)->get();
      $factuurarr = [];
      foreach($facturen as $factuur){
        $factuurarr[] = $factuur->klant_id;
      }
      $klanten = Klanten::whereIn('id', $factuurarr)->where('status', 1)->where('xml_exported', 0)->get();

      $wrapper = getSettingValue('klanten_export_xml_wrapper');
      $regels = getSettingValue('klanten_export_xml_regels');
      $adres = getSettingValue('klanten_export_xml_adres');
      $cp = getSettingValue('klanten_export_xml_cp');
      $layout = json_decode(getSettingValue('klanten_export_xml_data') ?? '[]', true);
      $keys = ['1' => getSettingValue('klanten_export_xml_key1'), '2' => getSettingValue('klanten_export_xml_key2'), '3' => getSettingValue('klanten_export_xml_key3')];


      $data = [];
      foreach($klanten as $klant){
        $arr = [];
        $regelattributes = [];
        foreach($keys as $index => $key){
          if($key == ''){
            continue;
          }elseif($key == 'custom'){
            $regelattributes['regel'][getSettingValue('klanten_export_xml_key_custom_title'.$index)] = getSettingValue('klanten_export_xml_key_custom'.$index);
          }elseif($key == 'debiteur'){
            $regelattributes['regel']['code'] = $klant->debiteurnummer;
          }elseif($key == 'id'){
            $regelattributes['regel']['id'] = $klant->id;
          }
        }
        foreach($regelattributes as $r => $ra){
          $arr['@attributes'] = $ra;
        }


        $manager = [];
        foreach($layout as $db => $name){
          if($db == 'locatie' || $db == 'cp'){continue;}
          if(!$name){continue;}
          if($db == 'customtitle1'){$title1 = $name;}
          elseif($db == 'customtitle2'){$title2 = $name;}
          elseif($db == 'custommanagertitle1'){$managertitle1 = $name;}
          elseif($db == 'custommanagertitle2'){$managertitle2 = $name;}
          elseif($db == 'custom1' && isset($title1)){
            $arr[$title1]['@attributes'] = ['code' => $name];
          }
          elseif($db == 'custom2' && isset($title2)){
            $arr[$title2]['@attributes'] = ['code' => $name];
          }
          elseif($db == 'custommanager1' && isset($managertitle1)){
            $manager[$managertitle1] = $name;
          }
          elseif($db == 'custommanager2' && isset($managertitle2)){
            $manager[$managertitle2] = $name;
            $arr['Manager']['@attributes'] = $manager;
          }else{
            if(!isset($arr[$name])){$arr[$name] = '';}
            $arr[$name] .= $klant[$db].' ';
          }
        }

        if(isset($cp)){
          $aArr = [];
          if(isset($klant->straat) || isset($klant->postcode) || isset($klant->plaats)){
            for ($x = 1; $x <= 4; $x++) {
              $aTemp = [];
              if($x==1){$type='D';}elseif($x==2){$type='I';}elseif($x==3){$type='P';}elseif($x==4){$type='V';}
              foreach($layout["locatie"] ?? [] as $db => $name){
                if(!$name){continue;}
                if($db == 'customtitle1'){$title1 = $name;}
                elseif($db == 'customtitle2'){$title2 = $name;}
                elseif($db == 'custom1' && isset($title1)){
                  $aTemp[$title1]['@attributes'] = ['code' => $name];
                }
                elseif($db == 'custom2' && isset($title2)){
                  $aTemp[$title2]['@attributes'] = ['code' => $name];
                }else{
                  if(!isset($aTemp[$name])){$aTemp[$name] = '';}
                  $aTemp[$name] .= $klant[$db].' ';
                }
              }
              $adrattrs = [];
              $adrattrs['attrs']['type'] = $type;
                if(getSettingValue('klanten_export_xml_adres_title_attr1') != ''){
                  $adrattrs['attrs'][getSettingValue('klanten_export_xml_adres_title_attr1')] = getSettingValue('klanten_export_xml_adres_attr1');
                }
                if(getSettingValue('klanten_export_xml_adres_title_attr2') != ''){
                  $adrattrs['attrs'][getSettingValue('klanten_export_xml_adres_title_attr2')] = getSettingValue('klanten_export_xml_adres_attr2');
                }
                if(getSettingValue('klanten_export_xml_adres_title_attr3') != ''){
                  $adrattrs['attrs'][getSettingValue('klanten_export_xml_adres_title_attr3')] = getSettingValue('klanten_export_xml_adres_attr3');
                }

                if(count($adrattrs)){
                  foreach($adrattrs as $r => $ra){
                    $aTemp['@attributes'] = $ra;
                  }
                }
              $aArr[] = $aTemp;
            }
          }

          foreach($aArr as $i => $row){
            foreach ($row as $x => $value){
              if($x == '@attributes'){continue;}
              if(gettype($value) == 'array'){continue;}
              $aArr[$i][$x] = substr($value, 0, -1);
            }
          }

          $cArr = [];
          $cpLayout = $layout["cp"] ?? [];
          $cpArr = [];
          if(isset($cpLayout["titel"])) {
            if(!isset($cpArr[$cpLayout["titel"]])){$cpArr[$cpLayout["titel"]] = '';}
            $cpArr[$cpLayout["titel"]] .= $klant->contactpersoon_titel ? $klant->contactpersoon_titel." " : "  ";
          }
          if(isset($cpLayout["voornaam"])) {
            if(!isset($cpArr[$cpLayout["voornaam"]])){$cpArr[$cpLayout["voornaam"]] = '';}
            $cpArr[$cpLayout["voornaam"]] .= $klant->contactpersoon_voornaam ? $klant->contactpersoon_voornaam." " : "  ";
          }
          if(isset($cpLayout["achternaam"])) {
            if(!isset($cpArr[$cpLayout["achternaam"]])){$cpArr[$cpLayout["achternaam"]] = '';}
            $cpArr[$cpLayout["achternaam"]] .= $klant->contactpersoon_achternaam ? $klant->contactpersoon_achternaam." " : "  ";
          }
          if(isset($cpLayout["email"])) {
            if(!isset($cpArr[$cpLayout["email"]])){$cpArr[$cpLayout["email"]] = '';}
            $cpArr[$cpLayout["email"]] .= $klant->contactpersoon_email ? $klant->contactpersoon_email." " : "  ";
          }
          if(isset($cpLayout["functie"])) {
            if(!isset($cpArr[$cpLayout["functie"]])){$cpArr[$cpLayout["functie"]] = '';}
            $cpArr[$cpLayout["functie"]] .= $klant->contactpersoon_functie ? $klant->contactpersoon_functie." " : "  ";
          }
          if(isset($cpLayout["telefoon"])) {
            if(!isset($cpArr[$cpLayout["telefoon"]])){$cpArr[$cpLayout["telefoon"]] = '';}
            $cpArr[$cpLayout["telefoon"]] .= $klant->contactpersoon_telefoon ? $klant->contactpersoon_telefoon." " : "  ";
          }
          if(isset($cpLayout["mobiel"])) {
            if(!isset($cpArr[$cpLayout["mobiel"]])){$cpArr[$cpLayout["mobiel"]] = '';}
            $cpArr[$cpLayout["mobiel"]] .= $klant->contactpersoon_mobiel ? $klant->contactpersoon_mobiel." " : "  ";
          }
          if(isset($cpLayout["custom1"])) {
            $cpArr[$cpLayout["customtitle1"]]['@attributes'] = ['code' => $cpLayout["custom1"]];
          }
          if(isset($cpLayout["custom2"])) {
            $cpArr[$cpLayout["customtitle2"]]['@attributes'] = ['code' => $cpLayout["custom2"]];
          }
          if(isset($adres)){$cpArr[$adres] = $aArr;}
          $cpattrs = [];
            if(getSettingValue('klanten_export_xml_cp_title_attr1') != ''){
              $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr1')] = getSettingValue('klanten_export_xml_cp_attr1');
            }
            if(getSettingValue('klanten_export_xml_cp_title_attr2') != ''){
              $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr2')] = getSettingValue('klanten_export_xml_cp_attr2');
            }
            if(getSettingValue('klanten_export_xml_cp_title_attr3') != ''){
              $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr3')] = getSettingValue('klanten_export_xml_cp_attr3');
            }
            if(count($cpattrs)){
              foreach($cpattrs as $r => $ra){
                $cpArr['@attributes'] = $ra;
              }
            }
          $cArr[] = $cpArr;
          foreach($klant->contactpersonen as $row){
            if(isset($row->voornaam) || isset($row->achternaam) || isset($row->telefoon) || isset($row->mobiel)) {
              $cpArr = [];
              if(isset($cpLayout["titel"])){
                if(!isset($cpArr[$cpLayout["titel"]])){$cpArr[$cpLayout["titel"]] = '';}
                $cpArr[$cpLayout["titel"]] .= $row->titel.' ';
              }
              if(isset($cpLayout["voornaam"])){
                if(!isset($cpArr[$cpLayout["voornaam"]])){$cpArr[$cpLayout["voornaam"]] = '';}
                $cpArr[$cpLayout["voornaam"]] .= $row->voornaam.' ';
              }
              if(isset($cpLayout["achternaam"])){
                if(!isset($cpArr[$cpLayout["achternaam"]])){$cpArr[$cpLayout["achternaam"]] = '';}
                $cpArr[$cpLayout["achternaam"]] .= $row->achternaam.' ';
              }
              if(isset($cpLayout["email"])){
                if(!isset($cpArr[$cpLayout["email"]])){$cpArr[$cpLayout["email"]] = '';}
                $cpArr[$cpLayout["email"]] .= $row->email.' ';
              }
              if(isset($cpLayout["functie"])){
                if(!isset($cpArr[$cpLayout["functie"]])){$cpArr[$cpLayout["functie"]] = '';}
                $cpArr[$cpLayout["functie"]] .= $row->functie.' ';
              }
              if(isset($cpLayout["telefoon"])){
                if(!isset($cpArr[$cpLayout["telefoon"]])){$cpArr[$cpLayout["telefoon"]] = '';}
                $cpArr[$cpLayout["telefoon"]] .= $row->telefoon.' ';
              }
              if(isset($cpLayout["mobiel"])){
                if(!isset($cpArr[$cpLayout["mobiel"]])){$cpArr[$cpLayout["mobiel"]] = '';}
                $cpArr[$cpLayout["mobiel"]] .= $row->mobiel.' ';
              }
              if(isset($cpLayout["custom1"])) {
                $cpArr[$cpLayout["customtitle1"]]['@attributes'] = ['code' => $cpLayout["custom1"]];
              }
              if(isset($cpLayout["custom2"])) {
                $cpArr[$cpLayout["customtitle2"]]['@attributes'] = ['code' => $cpLayout["custom2"]];
              }
              if(isset($adres)){$cpArr[$adres] = $aArr;}
              $cpattrs = [];
              if(getSettingValue('klanten_export_xml_cp_title_attr1') != ''){
                $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr1')] = getSettingValue('klanten_export_xml_cp_attr1');
              }
              if(getSettingValue('klanten_export_xml_cp_title_attr2') != ''){
                $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr2')] = getSettingValue('klanten_export_xml_cp_attr2');
              }
              if(getSettingValue('klanten_export_xml_cp_title_attr3') != ''){
                $cpattrs['attrs'][getSettingValue('klanten_export_xml_cp_title_attr3')] = getSettingValue('klanten_export_xml_cp_attr3');
              }
              if(count($cpattrs)){
                foreach($cpattrs as $r => $ra){
                  $cpArr['@attributes'] = $ra;
                }
              }
              $cArr[] = $cpArr;
            }
          }
          foreach($cArr as $i => $row){
            foreach ($row as $x => $value){
              if($x == $adres){continue;}
              if($x == '@attributes'){continue;}
              if(gettype($value) == 'array'){continue;}
              $cArr[$i][$x] = substr($value, 0, -1);
            }
          }
          $arr[$cp] = $cArr;
          $arr['Debtor']['@attributes'] = ['number' => $klant->debiteurnummer, 'code' => $klant->debiteurnummer];
          $arr['PaymentCondition']['@attributes'] = ['code' => $klant->betalingstermijn ?? '30'];
        }
        $data[] = $arr;
      }

      $xml = Formatter::make([$regels => $data], Formatter::ARR)->toXml($wrapper);
      $file = "temp.xml";
      file_put_contents($file, $xml);
      Klanten::whereIn('id', $factuurarr)->where('status', 1)->where('xml_exported', 0)->update([
        'xml_exported' => 1,
      ]);
      $log = Auth::user()->name." ".Auth::user()->lastname." heeft klanten geexporteerd!";
      actLog($log, Auth::user()->id, 27);
      return Response::download($file, "klanten.xml", ["Content-type:" => "application/xhtml+xml"]);
    } catch (\Exception $e) {
      actError($e);
    }
  }

  public function datasetsExportXml($subdomein, $van, $tot, $id){
    try{
      $dataset = OffertesDatasets::where("id", $id)->with('items')->first();
      $van = Carbon::parse($van)->format('Y-m-d H:i:s');
      $tot = Carbon::parse($tot)->addDay()->format('Y-m-d H:i:s');
      $facturen = Facturen::with('klant', 'regels')->where('datum', '>=', $van)->where('datum', '<=', $tot)->whereHas('xml_export', function ($query) {return $query->where('factuur_id', '!=', '');})->get();
      $settings = json_decode(getSettingValue('datasets_export_xml') ?? '[]', true)[$id] ?? [];
      $exporteditems = UploadedDatasetItems::get();
      $tempexporteditems = [];
      $attrs = $settings['attrs'] ?? [];
      $regelattrs = $settings['regelattrs'] ?? [];
      $data = [];
      foreach($facturen as $factuur){
        foreach($factuur->regels as $item){
            $item = $item->dataset;
            if(isset($item->item)){
              if($exporteditems->contains('item', $item->item) || in_array($item->item, $tempexporteditems)){continue;}else{
                UploadedDatasetItems::insert([
                  'item' => $item->item,
                ]);
                $tempexporteditems[] = $item->item;
              }
            }else{continue;}
            $row = [];
        $json = json_decode($item->item ?? '[]', true);
        foreach($settings['values'] as $value){
          $v = $value['value'] ?? ($json[$value['index']] ?? '');
          $temp = [];
          $containerattributes = [];
          foreach($attrs as $attr){
            if($attr['parent'] == $value['name']){
              $a = $attr['value'] ?? ($json[$attr['index']] ?? '');
              $temp[$attr['name']] = $a;
            }elseif($attr['parent'] == $value['container']){
              $a = $attr['value'] ?? ($json[$attr['index']] ?? '');
              $parentarr = explode(',', $attr['parent']);
              $parent = end($parentarr);
              $containerattributes[$parent][$attr['name']] = $a;
            }elseif($attr['parent'] == $value['container'].','.$value['name']){
              $a = $attr['value'] ?? ($json[$attr['index']] ?? '');
              $parentarr = explode(',', $attr['parent']);
              $parent = end($parentarr);
              $containerattributes[$parent][$attr['name']] = $a;
            }
          }
          if(count($temp)){
            $v = ['@attributes' => $temp];
          }
          if(isset($value['container'])){
            $containers = explode(',', $value['container']);

            foreach($containers as $x => $container){

              if($x === 0){
                if(!isset( $row[$containers[0]] )){$row[$containers[0]] = [];}
              }
              elseif ($x === 1){
                if(!isset( $row[$containers[0]][$containers[1]] )){$row[$containers[0]][$containers[1]] = [];}
              }
              elseif ($x === 2){
                if(!isset( $row[$containers[0]][$containers[1]][$containers[2]] )){$row[$containers[0]][$containers[1]][$containers[2]] = [];}
              }
              elseif($x === 3){
                if(!isset( $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]] )){$row[$containers[0]][$containers[1]][$containers[2]][$containers[3]] = [];}
              }
              elseif($x === 4){
                if(!isset( $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]] )){$row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]] = [];}
              }

              if(count($containers) === 1){
                $row[$containers[0]][$value['name']] = $v;
                if(count($containerattributes)){
                  foreach($containerattributes as $c => $ca){
                    if($c != $containers[0]){
                      $row[$containers[0]][$c] = [];
                      $row[$containers[0]][$c]['@attributes'] = $ca;
                    }else{
                      $row[$containers[0]]['@attributes'] = $ca;
                    }
                  }
                }
              }
              elseif (count($containers) === 2){
                $row[$containers[0]][$containers[1]][$value['name']] = $v;
                if(count($containerattributes)){
                  foreach($containerattributes as $c => $ca){
                    if($c != $containers[1]){
                      $row[$containers[0]][$containers[1]][$c] = [];
                      $row[$containers[0]][$containers[1]][$c]['@attributes'] = $ca;
                    }else{
                      $row[$containers[0]][$containers[1]]['@attributes'] = $ca;
                    }
                  }
                }
              }
              elseif (count($containers) === 3){
                $row[$containers[0]][$containers[1]][$containers[2]][$value['name']] = $v;
                if(count($containerattributes)){
                  foreach($containerattributes as $c => $ca){
                    if($c != $containers[2]){
                      $row[$containers[0]][$containers[1]][$containers[2]][$c] = [];
                      $row[$containers[0]][$containers[1]][$containers[2]][$c]['@attributes'] = $ca;
                    }else{
                      $row[$containers[0]][$containers[1]][$containers[2]]['@attributes'] = $ca;
                    }
                  }
                }
              }
              elseif(count($containers) === 4){
                $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$value['name']] = $v;
                if(count($containerattributes)){
                  foreach($containerattributes as $c => $ca){
                    if($c != $containers[3]){
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$c] = [];
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$c]['@attributes'] = $ca;
                    }else{
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]]['@attributes'] = $ca;
                    }
                  }
                }
              }
              elseif(count($containers) === 5){
                $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]][$value['name']] = $v;
                if(count($containerattributes)){
                  foreach($containerattributes as $c => $ca){
                    if($c != $containers[4]){
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]][$c] = [];
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]][$c]['@attributes'] = $ca;
                    }else{
                      $row[$containers[0]][$containers[1]][$containers[2]][$containers[3]][$containers[4]]['@attributes'] = $ca;
                    }
                  }
                }
              }

            }


            continue;
          }

          $row[$value['name'] ?? 'temp'] = $v;
        }
        foreach($regelattrs as $regelattr){
          $a = $regelattr['value'] ?? ($json[$regelattr['index']] ?? '');
          if($regelattr['name'] == 'code'){$a = substr($a, 0, 30);}
          if($regelattr['name'] == 'searchcode'){$a = substr($a, 0, 13);}
          $regelattributes['regel'][$regelattr['name']] = $a;
        }
        foreach($regelattributes as $r => $ra){
          $row['@attributes'] = $ra;
        }
        $data[] = $row;
      }
    }
      $xml = Formatter::make([($settings['regels'] ?? 'Items') => $data], Formatter::ARR)->toXml($settings['wrapper'] ?? 'XML');
      $file = "temp.xml";
      file_put_contents($file, $xml);
      $log = Auth::user()->name." ".Auth::user()->lastname." heeft dataset ". $dataset->naam ."geexporteerd!";
      actLog($log, Auth::user()->id, 27);
      return Response::download($file, $dataset->naam.".xml", ["Content-type:" => "application/xhtml+xml"]);
    }catch(\Exception $e){
      actError($e);
    }
  }
}
