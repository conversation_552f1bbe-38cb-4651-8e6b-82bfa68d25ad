<?php

namespace App\Http\Controllers;

use App\ActivityLog;
use App\BeheerSettings;
use App\Module;
use PDF;
use Auth;
use DB;
use Route;
use Storage;
use App\User;
use App\BV;
use App\Clients;
use App\ClientSettings;
use App\ClientsModules;
use App\Databases;
use App\DBSyncSequence;
use App\Permission;
use App\Role;
use App\RolePermission;
use App\TemplateTypes;
use App\Verlofreden;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Response;


class DashboardController extends Controller
{
    public function livePreview(){

      return view('dashboard.preview');
    }
    public function template(){

      $clients = Clients::with("bvs")->get()->keyBy("id");
      $types = TemplateTypes::orderBy("type")->get();
      return view("dashboard.template",[
        "clients" => $clients,
        "types" => $types,

      ]);
    }
    public function storeTemplate(Request $request){

      $database = Databases::where("client_id", $request->client)->first();
      $ftp = DB::connection("tessa")->table("ftp_settings")->where("client_id", $request->client)->first();
      $connection = setConnection($database->driver,$database->host, $database->username, $database->password);
      $disk = setDisk($ftp->driver, $ftp->path,$ftp->host,  $ftp->username, $ftp->password);
      $string = randomString(7).".png";
      $disk->putFileAs('templatePreview/', $request->image, $string);

      $craeted = Carbon::now();
      $connection->table("templates")->insert([
        "naam" => isset($request->templateNaam) ? $request->templateNaam : " ",
        "preview" => $string,
        "bv" => isset($request->bv) ? $request->bv : 1,
        "status_email" => isset($request->email) ? $request->email : "<EMAIL>",
        "created_at" => $craeted,
      ]);
      $template = $connection->table("templates")->where("preview", $string)->where("created_at", $craeted)->first();
      if(!empty($request->type)){
        for($i=0;$i<count($request->type);$i++){
          $connection->table("template_keywords")->insert([
            "template_id" => $template->id,
            "titel" => isset($request->titel[$i]) ? $request->titel[$i] : "",
            "naam" => isset($request->naam[$i]) ? $request->naam[$i] : "",
            "keyword" => isset($request->keyword[$i]) ? $request->keyword[$i] : "",
            "type" => isset($request->type[$i]) ? $request->type[$i] : "",
            "tekst" => isset($request->tekst[$i]) ? $request->tekst[$i] : "",
          ]);
        }
      }
      $log = Auth::user()->name." ".Auth::user()->lastname." heeft de template '".$template->naam."' (".$template->id.") aangemaakt.";
      ActivityLog::insert([
        "client_id" => getClientId(),
        "user_id" => Auth::user()->id,
        "log" => $log,
        "created_at" => Carbon::now(),

      ]);
      return redirect("beheer")->with("status","Template toegevoegd");
    }
    public function index(){

        $clients = Clients::with('database')->get();
        $modules = Module::get();
        $stg = ClientSettings::get();
        $pms = Permission::get();
        $bv = BV::get();
        $settings=[];
        $permissions=[];
        $bvs = [];

        foreach($clients as $client){
          $users = DB::connection("tessa")->table("log_online")->where([
            "date" => date("Y-m-d"),
            "client_id" => $client->id
          ])->where("updated_at", ">=",  Carbon::now()->addMinutes(-10))->get();

          if (count($users)){
            $db = $client->database;
            $con = setConnection($db->driver, $db->host, $db->username, $db->password);
            foreach ($users as $user){
              $user->user = $con->table('users')->where('id', $user->user_id)->first();
            }
          }

          $client->online_users = $users;
        }
        foreach($stg as $row){
            $settings[$row->client_id][$row->setting_id] = $row->value;
        }
        foreach($pms as $row){
            $permissions[$row->module_id][] = $row->permission;
        }
        foreach($bv as $row){
            $bvs[$row->client_id][] = $row->name;
        }
        return view("dashboard.clients",[
            "clients" => $clients,
            "settings" => $settings,
            "modules" => $modules,
            "permissions" => $permissions,
            "bvs" => $bvs,
        ]);
    }
    public function client($subdomein, $id){

        $client = Clients::where("id", $id)->first();
        $modules = Module::get();
        $settings = BeheerSettings::get();
        $stg = ClientSettings::where("client_id", $id)->get();
        $cm = ClientsModules::where("client_id", $id)->get();
        $database = Databases::where('client_id', $client->id)->first();
        $perms = Permission::get()->keyBy('id');
        $connection = setConnection($database->driver, $database->host, $database->username, $database->password);
        $users = $connection->table('users')->whereNotNull('users.name')->join('roles', 'roles.id', '=', 'users.role_id')->where(['users.active'=> 1, "users.extern" => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->orderBy('role_id')->get(['users.*', 'roles.name as r_name'])->keyBy('id');
        $rolePermissions = $connection->table('roles_permissions')->get()->groupBy('role_id');
        $permcount = array();
        $i=0;
        foreach($perms as $perm){
          $roles = $connection->table('roles_permissions')->where('permission_id', $perm->id)->get();
          $usercount = 0;
          foreach($roles as $role){
            $usercount += $connection->table('users')->where('email', '!=', '<EMAIL>')->where(['role_id' => $role->role_id, 'active' => 1, 'extern' => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->count();
          }
          $permcount [$i]['permission'] = $perm->permission;
          $permcount [$i]['aantal'] = $usercount;
          $i++;
        }
        $clientSettings=[];
        $clientModules=[];
        foreach($stg as $row){
            $clientSettings[$row->setting_id] = $row->value;
        }
        foreach($cm as $row){
            $clientModules[$row->module_id] = $row->module_id;
        }
        return view("dashboard.clientsettings",[
            "client" => $client,
            "settings" => $settings,
            "clientSettings" => $clientSettings,
            "modules" => $modules,
            "clientModules" => $clientModules,
            "users" => $users,
            "perms" => $perms,
            "permcount" => $permcount,
            "rolePermissions" => $rolePermissions,
        ]);
    }
    public function updateSettings(Request $request){
      $client = Clients::where("id", $request->client)->first();
      ClientSettings::where("client_id", $request->client)->delete();
      foreach($request->settings as $key => $row){
        ClientSettings::where("client_id", $request->client)->where("setting_id", $key)->insert([
          "setting_id" => $key,
          "client_id" => $request->client,
          "value" => $row,
        ]);
      }
      $log = Auth::user()->name." ".Auth::user()->lastname." heeft de settings van '".$client->name."' (".$client->id.") gewijzigd.";
        ActivityLog::insert([
          "client_id" => getClientId(),
          "user_id" => Auth::user()->id,
          "log" => $log,
          "created_at" => Carbon::now(),
        ]);
        return redirect("beheer/client/".$request->client)->with("status","Setting gewijzigd");
    }
    public function updateModule($subdomein, $module, $client, $status){
        $module = Module::where("id", $module)->first();
        $client = Clients::where("id", $client)->first();

        if($status == 1){
            ClientsModules::insert([
                "client_id" => $client->id,
                "module_Id" => $module->id,
            ]);
        }
        else{
            ClientsModules::where(["client_id" => $client->id, "module_id" => $module->id])->delete();

            $database = Databeses::where("client_id", $client->id)->first();
            $permissions = Permission::where("module_id", $module->id)->pluck('id')->toArray();
            $connection = setConnection($database->driver, $database->host, $database->username, $database->password);
            $connection->table("roles_permissions")->where("permission_id", $permissions)->delete();
        }

        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de module '".$module->name."' van '".$client->name."' gewijzigd";
        ActivityLog::insert([
          "client_id" => getClientId(),
          "user_id" => Auth::user()->id,
          "log" => $log,
          "created_at" => Carbon::now(),

        ]);
        return redirect("beheer/client/".$client->id)->with("status", "Module gewijzigd");
    }
    public function createModule(){
        return view("dashboard.createmodule");
    }
    public function storeModule(Request $request){
        if(!empty(Module::where("name", $request->naam)->first())){
            return redirect("beheer/module/create")->with("warning", "Module met de gegeven naam bestaat al!");
        }
        Module::insert([
            "name" => $request->naam
        ]);
        $module = Module::where("name", $request->naam)->first();
        if(!empty($request->permission)){
          foreach($request->permission as $row){
            Permission::insert([
              "permission" => $row,
              "module_id" => $module->id
            ]);
          }
        }
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de module '".$module->name."' (".$module->id.") aangemaakt.";
        ActivityLog::insert([
          "client_id" => getClientId(),
          "user_id" => Auth::user()->id,
          "log" => $log,
          "created_at" => Carbon::now(),

        ]);
        return redirect("beheer")->with("status", "Module toegevoegd");

    }
    public function indexModule($subdomein, $id){

        $module = Module::where("id", $id)->first();
        $permissions = Permission::where("module_id", $id)->get();
        return view("dashboard.indexmodule",[
            "module" => $module,
            "permissions" => $permissions,

        ]);
    }
    public function deleteModule($subdomein, $id){
      $module = Module::where("id", $id)->first();
      Permission::where("module_id", $id)->delete();
        Module::where("id", $id)->delete();
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de module '".$module->name."' (".$module->id.") veriwjderd.";
        ActivityLog::insert([
          "client_id" => getClientId(),
          "user_id" => Auth::user()->id,
          "log" => $log,
          "created_at" => Carbon::now(),

        ]);
        return redirect("beheer")->with("status", "Module veriwjderd");
    }
    public function updateMoPer(Request $request){
      $module =  Module::where("id", $request->id)->first();
        Module::where("id", $request->id)->update([
            "name" => $request->naam,
        ]);
        if(!empty($request->permissions)){
            foreach($request->permissions as $key => $row){
                if(!empty($row)){
                    Permission::where("id", $key)->update([
                        "permission" => $row,
                    ]);
                }
                else{
                    Permission::where("id", $key)->delete();
                }
            }
        }
        if(!empty($request->newPermissions)){
            foreach($request->newPermissions as $row){
                if(!empty($row)){
                    Permission::insert([
                        "permission" => $row,
                        "module_id" => $request->id
                    ]);
                }
            }
        }
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de module '".$module->name."' (".$module->id.") gewijzigd.";
        ActivityLog::insert([
          "client_id" => getClientId(),
          "user_id" => Auth::user()->id,
          "log" => $log,
          "created_at" => Carbon::now(),
        ]);
        return redirect("beheer")->with("status", "Nodule gewijzigd");
    }
    public function createClient(){

        return view('dashboard.clientcreate',[
            "modules" => Module::get(),
            "settings" => BeheerSettings::get(),
            "subdomeins" => Clients::select("subdomain")->get(),
        ]);
    }
    public function storeClient(Request $request){
        $clientId = Clients::insertGetId([
            "name" => $request->clientNaam,
            "subdomain" => $request->clientDomein,
            "root" => $request->clientDomein,
        ]);

        Databases::insert([
            "client_id" => $clientId,
            "host" => $request->databaseHost,
            "driver" => $request->databaseDriver,
            "database" => $request->database,
            "username" => $request->databaseUsername,
            "port" => $request->databasePort,
            "password" => tes_encrypt($request->databasePassword),
        ]);
        if(!empty($request->settings)){
            foreach($request->settings as $skey => $row){
                if(!empty($row)){
                    ClientSettings::insert([
                        "client_id" => $clientId,
                        "setting_id" => $skey,
                        "value" => $row,
                    ]);
                }
            }
        }
        if(!empty($request->modules)){
            foreach($request->modules as $row){
                ClientsModules::insert([
                    "client_id" => $clientId,
                    "module_id" => $row,
                ]);
            }
        }

        $client = Clients::where("id", $clientId)->first();
        Artisan::call('syncSequence:Fetch test '.$client->database->id);
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de client '".$client->name."' (".$clientId.") toegevoegd.";
        actLog($log, Auth::user()->id, null);

        return redirect("beheer/firsttimesetup?clientid=$clientId");
    }

    public function encryption(){
      return view('dashboard.encryption');
    }
    public function encrypt(Request $request){
      if(($request->key ?? '') != env("DASHBOARD_MASTERKEY")){
        return redirect("beheer/encryption")->with("warning", "Onjuiste wachtwoord!");
      }

      return view('dashboard.encryption', [
        "data" => [
          "encrypt" => isset($request->encrypt) ? tes_encrypt($request->encrypt) : null,
          "decrypt" => isset($request->decrypt) ? tes_decrypt($request->decrypt) : null,
        ]
      ]);
    }

    public function sql(){
      return view('dashboard.sql');
    }
    public function sqlRun(Request $request){
      $databases = Databases::get();
      foreach($databases as $database) {
        try {
          $connection = setConnection($database->driver, $database->host, $database->username, $database->password);
          $connection->statement($request->sql);
        }
        catch(\Exception $e){

        }

      }
      return redirect('beheer/sql')->with('status', "Actie uitgevoerd");
    }

    public function queryLog($subdomein, $client = null, $date = null){
      $clients = Clients::get();
      $queries = [];

      if(isset($client) && isset($date)){
        $db = Databases::where('client_id', $client)->first();
        $con = setConnection($db->driver, $db->host, $db->username, $db->password);
        $queries = $con->table('query_log')->where('created_at', 'LIKE', $date."%")->orderBy("created_at", "DESC")->get();
      }

      return view('dashboard.queries', [
        'queries' => $queries,
        'clients' => $clients,
        'client' => $client,
        'date' => $date,
      ]);
    }

    public function forceLogin($subdomein, $url, $hash){
      $user = User::where(['password' => $hash])->firstOrFail();
      Auth::loginUsingId($user->id);

      $url = str_replace("0x7175", "?", $url);
      $url = str_replace("0x6571", "=", $url);
      $url = str_replace("0x736c", "/", $url);

      return redirect($url);
    }

    public function createUpdates($id){
      $modules = Module::active();
      $permissions = currentPermissions();
      $current_permissions = DB::table('roles_permissions')->where('role_id', $id)->pluck('permission_id')->toArray();
      $updates = DB::connection("tessa")->table("updates")->orderBy('created_at', 'desc')->get();
      return view("dashboard.updates",[
        'modules' => $modules,
        'current_permissions' => $current_permissions,
        'permissions' => $permissions,
        'updates' => $updates,
      ]);
    }
    public function storeUpdates(Request $request){
      $data = json_encode($request->permission);
      DB::connection("tessa")->table("updates")->insert([
        "titel" => $request->titel,
        "datum" => $request->datum,
        "versie" => $request->versie,
        "bericht" => $request->bericht,
        'created_at' => Carbon::now(),
        "modules" => $data,
      ]);
      return redirect("beheer/updates")->with("status","Bericht verzonden");
    }

    public function deleteUpdates() {
      $updates = DB::connection("tessa")->table("updates")->where('id', Route::current()->parameter('id'))->first();
      $delete = DB::connection("tessa")->table("updates")->where('id', Route::current()->parameter('id'))->delete();
      $log = Auth::user()->name." ".Auth::user()->lastname." heeft het artikel '".$updates->titel."' (".$updates->id.") verwijderd.";
      ActivityLog::insert([
        "client_id" => getClientId(),
        "user_id" => Auth::user()->id,
        "module_id" => 1,
        "log" => $log,
        "created_at" => Carbon::now(),
      ]);
      return redirect('/beheer/updates')->with('status', 'Bericht is verwijderd');
    }

    public function facturatie(){
      $databases = Databases::where("client_id", "!=", 1)->where("client_id", "!=", 4)->where("client_id", "!=", 22)->get();
      $clients = Clients::where("id", "!=", 1)->where("id", "!=", 4)->where("id", "!=", 22)->get()->keyBy('id');
      $perms = Permission::orderBy('module_id')->get()->keyBy('id');
      $permissions = [];
      foreach($databases as $database){
        $connection = setConnection($database->driver, $database->host, $database->username, $database->password);
        $permcount = [];
        foreach($perms as $perm){
          $permprice = DB::connection("tessa")->table("client_perm_prices")->where('client_id', $database->client_id)->where('perm_id', $perm->id)->first();
          $roles = $connection->table('roles_permissions')->where('permission_id', $perm->id)->get();
          $usercount = 0;
          foreach($roles as $role){
            $usercount += $connection->table('users')->where('email', '!=', '<EMAIL>')->where(['role_id' => $role->role_id, 'active' => 1, 'extern' => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->count();
          }
          if($usercount > 0){
            $permcount [$perm->id]['permission'] = $perm->permission;
            $permcount [$perm->id]['aantal'] = $usercount;
            $permcount [$perm->id]['prijs'] = $permprice->prijs ?? 0.00;
            $permcount [$perm->id]['aantalaanpassing'] = $permprice->aantal_aanpassing ?? 0;
            if($permprice && $permprice->prijs != 0.0){
              $permissions[$perm->id] = $perm->permission;
            }
          }
        }
        if($clients[$database->client_id]){
          $clients[$database->client_id]->permcount = $permcount;
        }
      }
      return view("dashboard.facturatie",[
        'permissions' => $permissions,
        'clients' => $clients,
      ]);
    }

    public function setPrice(Request $request){
      DB::connection("tessa")->table("client_perm_prices")->updateOrInsert([
        "client_id" => $request->clientid,
        "perm_id" => $request->permid,
      ],[
        "prijs" => $request->price,
        "aantal_aanpassing" => $request->aantalaanpassing,
      ]);

      return response()->json(null, 201);
    }

    public function facturatiePdf($subdomein, $id){
      $client = Clients::where('id', $id)->first();
      $client->setConfig();

      $users = User::where('email', '!=', '<EMAIL>')->where([ 'active' => 1, 'extern' => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->with('role')->orderBy('role_id')->orderBy('name', 'ASC')->get();
      $thead = [];
      foreach($users as $user){
        $perms = RolePermission::where('role_id', $user->role_id)->get()->keyBy('permission_id');
        $user->perms = $perms;
        foreach($user->perms as $perm){
          $permprice = DB::connection("tessa")->table("client_perm_prices")->where('client_id', $id)->where('perm_id', $perm->permission_id)->first();
          $permission = Permission::where('id', $perm->permission_id)->first();
          if($permprice && $permprice->prijs != '0.00'){
            $perm->prijs = $permprice->prijs;
            $perm->aantalaanpassing = $permprice->aantal_aanpassing;
            if(!isset($thead[$perm->permission_id])){
              $thead[$perm->permission_id] = $permission->permission;
            }
          }
        }
      }
      $pdf = PDF::loadView('dashboard.facturatiepdf', [
        'thead' => $thead,
        'users' => $users,
      ])->setPaper('a4', 'landscape');
      return $pdf->stream('dashboard.facturatiepdf');
    }

    public function dashboard(){
      return view("dashboard.dashboard");
    }

    public function firstTimeSetup(){
      try{
        $client = Clients::where('id', $_GET['clientid'])->first();
        if(!$client->setup){
          $syncsequence = DBSyncSequence::where('databases_to_sync', '["'. $client->database->id .'"]')->first();
          Artisan::call('syncSequence:Start ' . $client->id. ' ' . $syncsequence->token);
          $client->setup = 1;
          $client->save();
          return redirect("beheer/firsttimesetup?clientid=$client->id");
        }

        $client->setConfig();
        $rolId = Role::insertGetId([
          'name' => 'support',
          'verlof_mail' => '<EMAIL>',
          'verlof_mail2' => '<EMAIL>',
          'verlof_mail3' => '<EMAIL>',
          'correctie_mail' => '<EMAIL>',
          'correctie_mail2' => '<EMAIL>',
          'correctie_mail3' => '<EMAIL>',
          'created_at' => Carbon::now()
        ]);
        $clientModules = ClientsModules::where('client_id', $client->id)->get();
        $neededperms = Permission::whereIn('module_id', $clientModules->pluck('module_id'))->get();
        foreach($neededperms as $perm){
          RolePermission::insert([
            'role_id' => $rolId,
            'permission_id' => $perm->id,
          ]);
        }

        $userid = User::insertGetId([
          'name'                     => 'InforDB',
          'lastname'                 => 'Development',
          'email'                    => '<EMAIL>',
          'password'                 => '$2y$10$Rdx6rNNNGgPhZpnwE4ad7..KCMRzwYW5P.IW351J4n9dgRyDJk0N6',
          'role_id'                  => $rolId,
          'pnumber'                  => '999',
          'functie'                  => 'support',
          'created_at'               => Carbon::now(),
          'is_admin'                 => 1,
        ]);

        $redenen = [ // In de hoofd-database, 1 = Ziekte, 2 = Verlof, 3 = Bijzonder verlof, 4 = Feestdag
          'Ziekte' => 1,
          'Verlof' => 2,
          'Bijzonder verlof' => 3,
          'Feestdag' => 4,
          'Dokter' => 1,
          'Tandarts' => 1,
          'Ziekenhuis' => 1,
        ];
        foreach ($redenen as $reden => $id){
          Verlofreden::insert([
            'reden' => $reden,
            'standaard_reden' => $id,
          ]);
        }

        mkdir('../../378/'.$client->root, 0755, true);

        return redirect("beheer")->with("status", "Setup voltooid");

      }catch(\Exception $e){
        actError($e);
        dd($e->getMessage(), $e);
      }
    }


}
