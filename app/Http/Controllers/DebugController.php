<?php

namespace App\Http\Controllers;

use App\ApiPosts;

use App\ChecklistsKeywords;
use App\Classes\SnelstartApi;
use App\Classes\KingApi;
use App\Classes\whatsapp\WhatsAppChat;
use App\Classes\whatsapp\WhatsAppChatMessage;
use App\Clients;
use App\Device;
use App\Facturen;
use App\FacturenOffertes;
use App\FacturenProformas;
use App\FacturenProjecten;
use App\FacturenWerkbonnen;
use App\Http\Middleware\Requests;
use App\MainSettings;
use App\Role;
use App\RolePermission;
use App\Settings;
use App\User;
use App\Verlofreden;
use App\WhatsAppContacts;
use App\WhatsAppMessagesInbound;
use App\WhatsAppMessagesSent;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;

class DebugController extends Controller
{
    public function storePost(Request $request){
      foreach ($request->allFiles() as $file){
        $files[] = [
          "name" => $file->getClientOriginalName(),
          "path" => $file->getPathname(),
          "size" => $file->getSize(),
        ];
      }

      $post = $request->all();
      $data = [
        "headers" => $request->header(),
        "POST" => $post,
        "GET" => $_GET,
        "Files" => $files ?? [],
        "Json" => json_decode(file_get_contents('php://input') ?? "[]"),
      ];

      if(isset($post['app_error'])){ actErrorFront($post, '(APP)'); }
      if(isset($post['js_error'])){ actErrorFront($post, '(JS)'); }

      return response()->json($data, 201);
    }

    public function viewPost($subdomain, Requests $request){
      // if(Auth::user()->email != '<EMAIL>'){ return redirect('home'); }
      // $posts = ApiPosts::orderBy("created_at", "DESC")->take(100)->get();


      // //filter op date
      // if(isset($request->date)){
      //   $posts = ApiPosts::where("created_at", "like", $_GET['date']."%")->orderBy("created_at", "DESC")->get();
      // }

      // //filter op token
      // if(isset($_GET['token'])){
      //   $posts = ApiPosts::where("token", $_GET['token'])->orderBy("created_at", "DESC")->get();
      // }

      return view('debug.posts');
    }
    public function filterPosts(Request $request){
      if(isset($request->date) && $request->date != ""){
        $posts = ApiPosts::where("created_at", "LIKE", $request->date."%")->orderBy("created_at", "DESC")->get();
      }
      else{
        $posts = ApiPosts::orderBy("created_at", "DESC")->take(100)->get();
      }

      foreach ($posts ?? [] as $row){
        $dump[] =
        [
          'ROUTE' => $row->route ?? '',
          "DATE" => Carbon()->parse($row->created_at)->format('d-m-Y H:i'),
          "TOKEN" => $row->token,
          "REQUEST" => json_decode($row->post, true),
        ];
      }
      return response()->json([
          "posts"=>$dump
      ], 201);
    }

    public function verifyApiToken(Request $request){
      $device = Device::where('api_token', $request->api_token)->first();

      if(isset($device)){
        return response(null, 201);
      }

      $user = User::where('password', $request->password)->firstOrFail();
      $token = randomString(55);
      Device::insert([
        'user_id' => $user->id,
        'api_token' => $token,
        'device_id' => $request->token,
        'brand'=>$request->brand,
        'model'=>$request->model,
        'last_activity' => Carbon::now(),
        'os' => $request->os
      ]);
      debug_post([
        'Token verify' => false,
        'new token' => $token,
        'user' => $user->id,
        'brand'=>$request->brand,
        'model'=>$request->model,
      ]);

      return response(['token' => $token], 202);
    }

    public function whitelistIP($sub, $key){
      try {

        if ($key != env('DASHBOARD_MASTERKEY')){
          return redirect('home')->with('warning', 'Ongeldige masterkey!');
        }

        $whitelist = MainSettings::where('name', 'debug_ipv6_whitelist')->first();
        $ips = json_decode(($whitelist->value ?? '[]'), true);


        $ipv6 = $_SERVER['REMOTE_ADDR'];
        $ips[$ipv6] = true;

        MainSettings::updateOrInsert(["name" => 'debug_ipv6_whitelist'], ["value" => json_encode($ips)]);
        pushToDevelopers('IP Whitelist', "IPv6 $ipv6 is toegevoegd aan de whitelist!");

        return redirect('home')->with('status', "IPv6 $ipv6 toegevoegd aan de whitelist!");
      }
      catch (\Exception $e){
        actError($e);
        return redirect('home')->with('warning', 'Er is iets foutgegaan!');
      }
    }

    public function testGet(){
      return view('test');
    }
    public function testGet2(){
      //todo weghalen zodra ramudden klaar is
      return view('cp_edit_ramudden');
    }
    public function testPost(Request $request){
//      Excel::import(new LeveranciersImport, $request->file);
//     Excel::import(new KlantImport, $request->file);
    }
    public function testError(Request $request){
      try {
          foreach ($test as $test){}
      }
      catch (\Exception $e) {
        actError($e);
      }

      foreach ($test2 as $test2){}
    }
    public function testCronjob($sub, $command){
      Artisan::call($command);
    }

		public function runOnClients($fn){
			$clients = Clients::get();

			foreach($clients as $client){
				$client->setConfig();
        dump($client->subdomain);
				$fn($client);
				echo '<hr>';
			}

			//Revert to current domain settings
			getClient()->setConfig();
		}

    public function firstTimeSetup(){
      try{
        if(User::where('email', '<EMAIL>')->first()){dd('already done');}
        $db = DB::connection("tessa")->table("databases")->where('database', 'infores378_test')->first();
        $con = setConnection($db->driver, $db->host, $db->username, $db->password);
        $user = $con->table("users")->where('email', '<EMAIL>')->first();
        $role = $con->table("roles")->where('name', 'new domain setup')->first();
        $perms = $con->table("roles_permissions")->where('role_id', $role->id)->get();

        $id = Role::insertGetId([
          'name' => 'support',
          'verlof_mail' => $role->verlof_mail,
          'verlof_mail2' => $role->verlof_mail2,
          'verlof_mail3' => $role->verlof_mail3,
          'correctie_mail' => $role->correctie_mail,
          'correctie_mail2' => $role->correctie_mail2,
          'correctie_mail3' => $role->correctie_mail3,
          'created_at' => Carbon::now()
          ]);

        foreach ($perms as $perm){
          RolePermission::insert([
            'role_id' => $id,
            'permission_id' => $perm->permission_id,
          ]);
        }

        $userid = User::insertGetId([
          'name'                     => $user->name,
          'lastname'                 => $user->lastname,
          'email'                    => $user->email,
          'password'                 => $user->password,
          'role_id'                  => $id,
          'pnumber'                  => '999',
          'functie'                  => 'support',
          'phone'                    => $user->phone,
          'dateofbirth'              => $user->dateofbirth,
          'start_date'               => $user->start_date,
          'created_at'               => Carbon::now(),
          'straat'                   => $user->straat,
          'huisnummer'               => $user->huisnummer,
          'toevoeging'               => $user->toevoeging,
          'postcode'                 => $user->postcode,
          'woonplaats'               => $user->woonplaats,
          'is_admin'                 => $user->is_admin,
        ]);

        $redenen = [ // In de hoofd-database, 1 = Ziekte, 2 = Verlof, 3 = Bijzonder verlof, 4 = Feestdag
          'Ziekte' => 1,
          'Verlof' => 2,
          'Bijzonder verlof' => 3,
          'Feestdag' => 4,
          'Dokter' => 1,
          'Tandarts' => 1,
          'Ziekenhuis' => 1,
        ];
        foreach ($redenen as $reden => $id){
          Verlofreden::insert([
            'reden' => $reden,
            'standaard_reden' => $id,
          ]);
        }


        dd('done');
      }catch (\Exception $e){
        actError($e);
      }
    }
}
