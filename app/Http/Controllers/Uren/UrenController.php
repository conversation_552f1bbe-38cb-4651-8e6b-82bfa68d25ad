<?php

namespace App\Http\Controllers\Uren;

use App\ActivityLog;
use App\Classes\UrenregistratieClientFunctions;
use App\Machines;
use App\Planning;
use App\Standaarduren;
use App\UrenRegistratieMachines;
use App\UrenRegistratieProjectTaken;
use App\Uursoorten;
use App\MutatieLijst;
use DB;
use Auth;
use Route;
use App\User;
use App\Message;
use App\Project;
use App\Offertes;
use App\Werkbonnen;
use App\HoofdVerlofreden;
use App\UrenRegistratie;
use App\Facturabelopties;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class UrenController extends Controller {

  public function index() {
    $users = getUsers();
    $messages = Message::orderBy('date', 'DESC')->where('active', '1')->with('user')->get();
    return view('uren.users', ['users' => $users, 'messages' => $messages]);
  }
  public function messagereaded() {
    $messages = Message::where('id', Route::current()->parameter('id'))->update(['active' => '0']);
    return redirect('uren/correction')->with('status', 'Het bericht is gelezen');
  }

  public function edit() {
    $user = User::where('id', Route::current()->parameter('id'))->firstOrFail();
    $date = Carbon::createFromFormat('d-m-Y', Route::current()->parameter('date'))->format('Y-m-d');
    $projectnummers = Project::with('taken')->get();
    if(getSettingValue('urenregistratie_invoeren_projectnummers') == 'perbv'){
      $projectnummers = $projectnummers->where('bv', $user->bv_id);
    }
    if(getSettingValue('urenregistratie_invoeren_offertenummers') == 'aan'){
      $offertenummers = Offertes::where(['active' => 1, 'project_id' => null, 'type' => 1])->where(function ($query){$query->where("status", "In aanmaak")->orWhere("status", "LIKE", "Accorderen%")->orWhere("status", "Uitgebracht")->orWhere("status", "like", "verzonden%");})->with('klant')->get();
    }
    $kentekens = DB::table('kentekens')->get();
    $naca = DB::table('naca_codes')->get();
    $redenen = DB::table('verlofredenen')->get();
    $facturabelopties = DB::table('facturabelopties')->get();
    $type = "update";
    $projecten = UrenRegistratie::where('medewerker_id', $user->id)->where('datum', $date)->where('tijdelijk', 0)->where('goedkeuren', 0)->with("machines", "machineuren", "project", "projecttaken")->get();
    $machines = Machines::where('active', 1)->with("group")->get();
    $uursoorten = Uursoorten::where('active', 1)->get();
    $planning = Planning::where("user_id", $user->id)->where("datum", $date)->with("project", "offerte", "machines")->get();
    $werkbonnen = Werkbonnen::where("user_id", $user->id)->where("datum", Carbon::parse($date)->format('d-m-Y'))->where("active", 1)->with("project", "values")->get();


    if(count($projecten) == 0) {
      $aantal = 1;
    } else {
      $aantal = count($projecten);
    }

    if (count($projecten) <= 0) {
      $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
      $type = "insert";
      $projecten = Standaarduren::where('dag', $dagen[Carbon::parse($date)->dayOfWeek])->where('medewerkers_id', $user->id)->get();
    }

    $urengetal = false;
    foreach ($user->permissions() as $permission) {
      if ($permission->permission == "uren o.b.v. getal") {
        $urengetal = true;
      }
    }

    if (count($projecten) <= 0) {
      $type = "insert";
      $projecten = [new \StdClass() ];
    }

    if (isset($aantal)) {
      if (count($projecten) > $aantal) {
//        UrenRegistratie::where('id', $projecten[$aantal]->id)->delete();
        $projecten = $projecten->slice(0, $aantal);
      }
      elseif(count($projecten) < $aantal) {

        $p = [];
        for ($i = 0; $i < $aantal; $i++) {
          if (!empty($projecten[$i])) {
            $p[] = $projecten[$i];
          }
          else
          {
            $p[] = null;
          }
        }
        $projecten = $p;
      }
    }

    activity()->log(Auth::user()->name . ' ('.Auth::user()->id.') heeft de uren van '. $user->name .' ('.$user->id.') gewijzigd.');
    return view('uren.invoeren.invoeren', [
      'projecten' => $projecten,
      'kentekens' => $kentekens,
      'urengetal' => $urengetal,
      'aantal' => $aantal,
      'type' => $type,
      'redenen' => $redenen,
      'facturabelopties' => $facturabelopties,
      'naca' => $naca,
      'date' => $date,
      'user' => $user,
      'projectnummers' => $projectnummers,
      'offertenummers' => $offertenummers ?? [],
      "machines" => $machines,
      "uursoorten" => $uursoorten,
      "planning" => $planning,
      "werkbonnen" => $werkbonnen,
      "correction" => true,
    ]);
  }
  public function store($subdomain, $datum, $user_id, Request $request) {
    $totaalDagUren = 0;
    $totaalDagUren_incl_verlof = 0;
    $feestdaguren = false;
    $atvUren = false;

    $date = Carbon::parse(Route::current()->parameter('date'))->format('Y-m-d');
    $user_id = Route::current()->parameter('id');
    $tijdvoortijdgezet = 0;

    $user = User::where('id', $user_id)->firstOrFail();

    $convertDate = Carbon::parse($datum)->format("Y-m-d");
    DB::table('urenregistratie')->where('medewerker_id', $user_id)->where("datum", $convertDate)->delete();

    foreach ($request->projecten ?? [] as $i => $project) {

        $totaaluren = 0;
        $ziekteuren = 0;
        $tijdvoortijd = 0;
        $feesturen = 0;
        $atv = 0;
        $roostervrij = 0;
        $verlofuren = 0;
        $bijzonderverlofuren = 0;
        $overuren125 = 0;
        $overuren128 = 0;
        $overuren150 = 0;
        $overuren165 = 0;
        $totaaluren = 0;
        $overuren200 = 0;
        $pauze        = 0;
        $totaaluren_incl_verlof = 0;

        if (isset($project["tijdvoortijd"])) {
          $tijdvoortijdgezet = 1;
        }

        if (!empty($project["pauze"])) {
          $pauze = $project["pauze"];
        }

        if (isset($project["gewerkteuren"])) {
          $totaaluren = $project["gewerkteuren"] - $pauze;
          $totaaluren_incl_verlof = $totaaluren;
        }
        else {
          $totaaluren = strtotime($project["eindtijd"]) - strtotime($project["begintijd"]);
          $totaaluren = ($totaaluren / 3600) - $pauze;
          $totaaluren_incl_verlof = $totaaluren;
        }

        $hoofdReden = HoofdVerlofreden::getVerlofType($project["verlofreden"]);

        if (isset($project["verlof"]) && isset($project["verlofreden"])) {
          if ($hoofdReden == 'Ziekte') {
            $ziekteuren = $project["verlof"];
            $totaaluren = $totaaluren - $ziekteuren;

            if ($totaaluren < 0) {
              $ziekteuren = $ziekteuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Verlof') {
            $verlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $verlofuren;
            if ($totaaluren < 0) {
              $verlofuren = $verlofuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Bijzonder verlof') {
            $bijzonderverlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $bijzonderverlofuren;
            if ($totaaluren < 0) {
              $bijzonderverlofuren = $bijzonderverlofuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Roostervrije uren') {
            $roostervrij = $project["verlof"];
            $totaaluren = $totaaluren - $roostervrij;
            if ($totaaluren < 0) {
              $roostervrij = $roostervrij + $totaaluren;
              $totaaluren = 0;
            }
          }
          elseif ($hoofdReden == 'Feestdag') {
            $feestdaguren = true;
            $feesturen = $project["verlof"];
            $totaaluren = $totaaluren - $feesturen;
            if ($totaaluren < 0) {
              $feesturen = $feesturen + $totaaluren;
              $totaaluren = 0;
            }
          }
          elseif ($hoofdReden == 'ATV') {
            $atvUren = true;
            $atv = $project["verlof"];
            $totaaluren = $totaaluren - $atv;
            if ($totaaluren < 0) {
              $atv = $atv + $totaaluren;
              $totaaluren = 0;
            }
          } else {
            $verlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $verlofuren;
            if ($totaaluren < 0) {
              $verlofuren = $verlofuren + $totaaluren;
              $totaaluren = 0;
            }
          }
        }
        else {
          $verlofuren = 0;
        }

        $naca = null;
        if (!empty($project["naca"])) {
          if ($project["naca"] == "default") {
            $naca = null;
          } else {
            $naca = $project["naca"];
          }
        }



        $bestuurder = null;
        if(!empty($project)) {
          if(isset($project["bestuurder"]) && $project["bestuurder"] == "1") {
            $bestuurder = "1";
          } else {
            $bestuurder = "0";
          }
        }

        // Reisuren uitschakelen
        if($user->all_in === '1' || $user->disable_reisuren === '1'){
          $reisuren = 0;
        }

        // Custom berekeningen per klant
        $subdomein = getSubdomein();
        if (method_exists(UrenregistratieClientFunctions::class, $subdomein)) {
          $project = UrenregistratieClientFunctions::$subdomein($project, $request);
        }

        $insertId = DB::table('urenregistratie')->insertGetId([
          'medewerker_id' => $user_id,
          'datum'         => $date,
          'projectnummer' => !empty($project["projectnummer"]) ? $project["projectnummer"] : null,
          'pauze'         => !empty($project["pauze"])         ? $project["pauze"]         : 0,
          'begintijd'     => !empty($project["begintijd"])     ? Carbon::parse($project["begintijd"])->format('H:i') : null,
          'eindtijd'      => !empty($project["eindtijd"])      ? Carbon::parse($project["eindtijd"])->format('H:i')  : null,
          'gewerkte_uren' => !empty($project["gewerkteuren"])  ? $project["gewerkteuren"]  : 0,
          'kilometers'    => !empty($project["kilometers"])    ? $project["kilometers"]  : 0,
          'reisuren'      => !empty($project["reisuren"])      ? $project["reisuren"] : 0,
          'overnachting'  => !empty($project["overnachting"])  ? $project["overnachting"]  : null,

          'heenreis_huisproject'   => !empty($project["heenreis_huisproject"])  ? $project["heenreis_huisproject"]  : null,
          'terugreis_huisproject'  => !empty($project["terugreis_huisproject"]) ? $project["terugreis_huisproject"] : null,
          'heenreis_woonwerk'      => !empty($project["heenreis_woonwerk"])     ? $project["heenreis_woonwerk"]     : null,
          'terugreis_woonwerk'     => !empty($project["terugreis_woonwerk"])    ? $project["terugreis_woonwerk"]    : null,

          'ladenlossen_begintijd'        => !empty($project["ladenlossen_begintijd"])        ? Carbon::parse($project["ladenlossen_begintijd"])->format('H:i:s')        : null,
          'ladenlossen_eindtijd'         => !empty($project["ladenlossen_eindtijd"])         ? Carbon::parse($project["ladenlossen_eindtijd"])->format('H:i:s')         : null,
          'ladenlossen_middag_begintijd' => !empty($project["ladenlossen_middag_begintijd"]) ? Carbon::parse($project["ladenlossen_middag_begintijd"])->format('H:i:s') : null,
          'ladenlossen_middag_eindtijd'  => !empty($project["ladenlossen_middag_eindtijd"])  ? Carbon::parse($project["ladenlossen_middag_eindtijd"])->format('H:i:s')  : null,

          'bestuurder'      => $bestuurder,
          'bijrijder'       => !empty($project["bijrijder"])   ? $project["bijrijder"]   : null,
          'kenteken_id'     => !empty($project["kenteken"])    ? $project["kenteken"]    : null,
          'roostervrij'     => $roostervrij,
          'verlof'          => $verlofuren,
          'bijzonderverlof' => $bijzonderverlofuren,
          'ziekteuren'      => $ziekteuren,
          'feesturen'       => $feesturen,
          'atv_uren'        => $atv,
          'facturabel_id'   => !empty($project["facturabel"]) ? $project["facturabel"] : null,
          'verlofreden_id'  => !empty($project["verlofreden"]) ? $project["verlofreden"] : null,
          'naca_id'         => $naca,
          'opmerkingen'     => !empty($project["opmerkingen"]) ? $project["opmerkingen"] : null,

          'totaaluren100'      => $totaaluren,
          'totaal_overuren150' => 0,
          'totaal_overuren165' => 0,
          'totaal_overuren200' => 0,
          'tijdvoortijd' => 0,
          'totaaldaguren100' => 0,
          'tijdelijk' => 0,
          'uursoort_id' => $project['uursoort'] ?? null,

        ]);
        $totaalDagUren += $totaaluren;
        $totaalDagUren_incl_verlof += $totaaluren_incl_verlof;

        foreach($project["machines"] ?? [] as $machine){
          if(isset($project['machinetijden'])){
            foreach($project["machinetijden"] as $machineid => $tijden){
              if($machineid == $machine){
                $begintijd = $tijden['begintijd'];
                $eindtijd = $tijden['eindtijd'];
              }
            }
          }else{$begintijd = null; $eindtijd = null;}
          UrenRegistratieMachines::insert([
            'urenregistratie_id' => $insertId,
            'machine_id' => $machine,
            'begintijd' => $begintijd,
            'eindtijd' => $eindtijd,
          ]);
        }
        foreach($project["project_taken"] ?? [] as $taak_id){
          UrenRegistratieProjectTaken::insert([
            'urenregistratie_id' => $insertId,
            'taak_id' => $taak_id,
          ]);
        }

    }
    Cache::forget($request->fullUrl());

    $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
    $dag   = $dagen[Carbon::parse($request->date)->dayOfWeek];
    $standaarduren = DB::table('standaarduren')->where('dag', $dag)->where('medewerkers_id', $user_id)->first();
    $standaarduren = $standaarduren->standaarduren ?? 0;

    $overuren = [
      '100' => $totaalDagUren,
      '125' => 0,
      '128' => 0,
      '150' => 0,
      '165' => 0,
      '200' => 0,
      'tijdvoortijd' => 0,
    ];
    $sett = json_decode(getSettingValue('uren_overuren') ?? '[]', true);
    $minuren = 0;
    if($user->all_in == 1){
      $overuren['125'] = 0;
      $overuren['128'] = 0;
      $overuren['150'] = 0;
      $overuren['165'] = 0;
      $overuren['200'] = 0;
      DB::table('urenregistratie')->where('id', $insertId)->update(['reisuren' => 0]);
    }
    else {
      if (getSettingCheckbox('urenregistratie_advanced_overuren')){
        $overuren = UrenRegistratie::calcAdvancedOveruren($request);
      }
      else{
        if($user->nul_uren == 0 && !(Carbon::parse($request->date)->isWeekend())) {
          if ($overuren['100'] > $standaarduren) {
            $overuren[($sett['weekdagen'] ?? '150')] = $overuren['100'] - $standaarduren;
            $overuren['100'] = $standaarduren;
          }
          if($tijdvoortijdgezet == 1){
            if ($overuren['150'] > 0 || $overuren['125'] > 0 || $overuren[($sett['weekdagen'] ?? '150')] > 0) {
              $overuren['tijdvoortijd'] = $overuren[($sett['weekdagen'] ?? '150')];
              $overuren['125'] = 0;
              $overuren['128'] = 0;
              $overuren['150'] = 0;
              $overuren['165'] = 0;
              $overuren['200'] = 0;
            }
            elseif ($totaalDagUren_incl_verlof < $standaarduren) {
              $overuren['100'] = $standaarduren;
              $overuren['tijdvoortijd'] = $totaalDagUren_incl_verlof - $standaarduren;
            }
          }
        }
        elseif ($user->nul_uren == 0) {
          if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SATURDAY)) {
            $overuren[($sett['zaterdag'] ?? '165')] = $overuren['100'];
            $overuren['100'] = 0;
          }
          if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SUNDAY)) {
            $overuren[($sett['zondag'] ?? '200')] = $overuren['100'];
            $overuren['100'] = 0;
          }
          if($tijdvoortijdgezet == 1) {
            $overuren['tijdvoortijd'] = $overuren[($sett['zaterdag'] ?? '165')] + $overuren[($sett['zondag'] ?? '200')];
            $overuren['125'] = 0;
            $overuren['128'] = 0;
            $overuren['150'] = 0;
            $overuren['165'] = 0;
            $overuren['200'] = 0;
          }
        }
        if ($feestdaguren || $atvUren){
          $overuren['100'] = 0;
          $overuren['125'] = 0;
          $overuren['128'] = 0;
          $overuren['150'] = 0;
          $overuren['165'] = 0;
          $overuren['200'] = 0;
        }
      }
      if ($totaalDagUren_incl_verlof < $standaarduren && $tijdvoortijdgezet == 0) {
        $minuren = $standaarduren - $totaalDagUren_incl_verlof;
      }
    }

    DB::table('urenregistratie')->where('id', $insertId)->update([
      'totaaldaguren100' => $overuren['100'],
      'totaal_overuren125' => $overuren['125'],
      'totaal_overuren128' => $overuren['128'],
      'totaal_overuren150' => $overuren['150'],
      'totaal_overuren165' => $overuren['165'],
      'totaal_overuren200' => $overuren['200'],
      'minuren' => $minuren ?? 0,
      'tijdvoortijd' => $overuren['tijdvoortijd'],
    ]);

    $bericht = Message::where('user_id', $user_id)->where('date', $date)->update(['active' => '0']);

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de uren van '".$user->name." ".$user->lastname."' (".$user->id.") ingevoerd.";
    actLog($log, Auth::user()->id, 2);

    return redirect($request->prevurl)->with('status', 'De uren zijn ingevoerd.');
  }

  public function mutatie(Request $request) {
    try{
      $year = Route::current()->parameter('year');
      $month = Route::current()->parameter('month');
      $user_id = Route::current()->parameter('id');
      $maanden = ["", "januari", "februari", "maart", "april", "mei", "juni", "juli", "augustus", "september", "oktober", "november", "december"];
      $maand = null;
      $periode = null;
      if(is_numeric($month)){
        $periode = $month;
      }else{
        $maand = array_search($month, $maanden);
      }

      Mutatielijst::updateOrInsert([
        'medewerkers_id' => $user_id,
        'jaar' => $year,
        'maand' => $maand,
        'periode' => $periode,
        'bv_id' => User::where('id', $user_id)->first()->bv_id
      ],[
        'totaal_uren100' => $request->mutatieregel['totaaluren'] ?? 0,
        'totaal_uren125' => $request->mutatieregel['overuren125'] ?? 0,
        'totaal_uren128' => $request->mutatieregel['overuren128'] ?? 0,
        'totaal_uren150' => $request->mutatieregel['overuren150'] ?? 0,
        'totaal_uren165' => $request->mutatieregel['overuren165'] ?? 0,
        'totaal_uren200' => $request->mutatieregel['overuren200'] ?? 0,
        'totaal_reisuren' => $request->mutatieregel['reisuren'] ?? 0,
        'totaal_reis_km' => $request->mutatieregel['kilometers'] ?? 0,
        'roostervrij' => $request->mutatieregel['roostervrij'] ?? 0,
        'verlofuren' => $request->mutatieregel['verlofuren'] ?? 0,
        'bijzonderverlofuren' => $request->mutatieregel['bijzonderverlofuren'] ?? 0,
        'feesturen' => $request->mutatieregel['feesturen'] ?? 0,
        'ziekteuren80' => $request->mutatieregel['ziekteuren80'] ?? 0,
        'ziekteuren' => $request->mutatieregel['ziekteuren'] ?? 0,
        'meeruren' => !empty($request->mutatieregel["meeruren"])    ? $request->mutatieregel["meeruren"]    : 0,
        'opmerkingen' => $request->mutatieregel['opmerkingen_intern'] ?? '',
        'opmerkingen_accountant' => $request->mutatieregel['opmerkingen_accountant'] ?? '',
        'tijdspaarfonds' => $request->mutatieregel['tijdspaarfonds'] ?? 0,
        'tijdvoortijd_uren' => $request->mutatieregel['tvturen'] ?? 0,
        'overnachtingen' => $request->mutatieregel['overnachtingen'] ?? 'nee',
        'minuren' => $request->mutatieregel['minuren'] ?? 0,
        'totaal_woonwerk_km' => $request->mutatieregel['totaal_woonwerk_km'] ?? 0,
        'totaal_huisproject_km' => $request->mutatieregel['totaal_huisproject_km'] ?? 0,
      ]);
      if($periode != null){
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de mutatieregel van " . User::where('id', $user_id)->first()->name . " " . User::where('id', $user_id)->first()->lastname . " van periode " . $periode . " opgeslagen.";
      }else{
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de mutatieregel van " . User::where('id', $user_id)->first()->name . " " . User::where('id', $user_id)->first()->lastname . " van " . $maand . " opgeslagen.";
      }

      actLog($log, Auth::user()->id, 2);
      return redirect('/uren/weekoverzicht')->with('status', 'De mutatieregel is ingevoerd.');
    }
    catch (\Exception $e){
      actError($e);
      return redirect('/uren/weekoverzicht')->with('status', 'Er is iets misgegaan.');
    }
  }

  public function uursoortIndex(){
    $uursoorten = Uursoorten::where('active', 1)->with('BV')->get();
    return view('uren.uursoorten.index', [
      'uursoorten' => $uursoorten,
    ]);
  }
  public function uursoortCreate($subdomein, $id = null){
    $uursoort = null;
    if(isset($id)){
      $uursoort = Uursoorten::where("id", $id)->firstOrFail();
    }

    return view('uren.uursoorten.create', [
      'uursoort' => $uursoort,
    ]);
  }
  public function uursoortStore(Request $request){
    $data = [
      'code' => $request->code,
      'name' => $request->name,
      'bv' => $request->bv,
      'kostprijs' => $request->kostprijs,
      'verkoopprijs' => $request->verkoopprijs,
    ];

    if(isset($request->id)){
      Uursoorten::where("id", $request->id)->update($data);
    }
    else{
      Uursoorten::insert($data);
    }

    return redirect('uren/uursoorten');
  }

}
