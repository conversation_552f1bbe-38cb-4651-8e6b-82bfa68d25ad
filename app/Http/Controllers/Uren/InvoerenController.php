<?php

namespace App\Http\Controllers\Uren;

use App\ActivityLog;
use App\Classes\UrenregistratieClientFunctions;
use App\Machines;
use App\Planning;
use App\PlanningMachines;
use App\UrenRegistratieMachines;
use App\UrenRegistratieProjectTaken;
use App\Uursoorten;
use App\Werkbonnen;
use DB;
use Auth;
use Route;
use App\User;
use App\Message;
use App\Project;
use App\Offertes;
use App\Klanten;
use App\News;
use App\UrenRegistratie;
use App\HoofdVerlofreden;
use App\Facturabelopties;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewCorrection;

class InvoerenController extends Controller {

    public function index() {
        $id = Auth::user()->id;
        $user     = User::where('id', $id)->firstOrFail();
        $dagen    = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
        $response = [];

        $page = Request()->query('page');
        if(!isset($page) || $page == 1) {
          $min = 0;
          $max = 60;
        } else {
          $pageMinEen = $page - 1;
          $min = (60 * $pageMinEen);
          $max = (60 + $min);
        }

        for ($i = $min; $i < $max; $i++) {
          $today                 = Carbon::now()->subDays($i)->format('Y-m-d');
          $response[$i]["datum"] = Carbon::parse($today)->format('d-m-Y');
          $urenregistratie       = DB::table('urenregistratie')->select('datum')->where('medewerker_id', $id)->where('datum', $today)->where('tijdelijk', 0)->where('goedkeuren', 0)->first();
          $verlof                = DB::table('verlof')->where('medewerker_id', $id)->where('datum', $today)->where('akkoord', 1)->pluck('datum')->first();
          $feestdag              = DB::table('feestdagen')->select("datum", "feestdag")->where('datum', $today)->where('disabled', 0)->pluck("feestdag")->first();

          $response[$i]["dag"]        = $dagen[Carbon::parse($today)->dayOfWeek];
          $response[$i]["feestdagen"] = $feestdag        ? true : false;
          $response[$i]["verlof"]     = $verlof          ? true : false;
          $response[$i]["ingevoerd"]  = $urenregistratie ? true : false;
        }

        return view('uren.invoeren.index', ['user' => $user, 'response' => $response, 'page' => $page]);
      }
    public function inzien(){
      $id = Auth::user()->id;
      $aantal = isset($_GET['aantal']) ? $_GET['aantal'] : null;
      $user = User::where('id', $id)->firstOrFail();
      $date = Carbon::createFromFormat('d-m-Y', Route::current()->parameter('date'))->format('Y-m-d');
      $kentekens = DB::table('kentekens')->get();
      $naca = DB::table('naca_codes')->get();
      $redenen = DB::table('verlofredenen')->get();
      $facturabelopties = DB::table('facturabelopties')->get();
      $type = "update";
      $projecten = UrenRegistratie::where('medewerker_id', $id)->where('datum', $date)->where('tijdelijk', 0)->where('goedkeuren', 0)->with("machines", "uursoort", "machineuren", "projecttaken", "project")->get();
      if ($aantal == null) {
        if(count($projecten) == 0) {
          $aantal = 1;
        } else {
          $aantal = count($projecten);
        }
      }

      if (count($projecten) <= 0) {
        $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
        $type = "insert";
        $projecten = DB::table('standaarduren')->select("medewerkers_id", "dag", "standaarduren", "begintijd", "eindtijd", "pauze", "ladenlossen_begintijd", "ladenlossen_eindtijd", "ladenlossen_middag_begintijd", "ladenlossen_middag_eindtijd", "heenreis_woon_werk", "terugreis_woon_werk", "heenreis_huis_project", "terugreis_huis_project", "ziekteuren", "feesturen", "tijdvoortijd", "bestuurder", "bijrijder", "kenteken_id", "verlofreden_id", "naca_id", "reisuren", "verlof", "opmerkingen", "role_id")->where('dag', $dagen[Carbon::parse($date)->dayOfWeek])->where('medewerkers_id', $user->id)->get();
      }

      $urengetal = false;
      foreach ($user->permissions() as $permission) {
        if ($permission->permission == "uren o.b.v. getal") {
          $urengetal = true;
        }
      }

      if (count($projecten) <= 0) {
        $type = "insert";
        $projecten = [new \StdClass() ];
      }

      if (isset($aantal)) {
        if (count($projecten) > $aantal) {
          UrenRegistratie::where('id', $projecten[$aantal]->id)->delete();
          $projecten = $projecten->slice(0, $aantal);
        } elseif(count($projecten) < $aantal) {

          $p = [];
          for ($i = 0; $i < $aantal; $i++) {
            if (!empty($projecten[$i])) {
              $p[] = $projecten[$i];
            } else {
              $p[] = null;
            }
          }
          $projecten = $p;
        }
      }
      activity()->log(Auth::user()->name . ' ('.Auth::user()->id.') heeft de uren ingevoerd.');
      return view('uren.invoeren.inzien', ['projecten' => $projecten, 'kentekens' => $kentekens, 'urengetal' => $urengetal, 'aantal' => $aantal, 'type' => $type, 'redenen' => $redenen, 'facturabelopties' => $facturabelopties, 'naca' => $naca, 'date' => $date, 'user' => $user]);
    }
    public function weekoverzicht($domein, $year = null) {
      $id = Auth::user()->id;
      if($year == null){
        $year = Carbon::now()->format('Y');
      }
      $user = User::where('id', $id)->firstOrFail();

      $urengetal = false;
      foreach ($user->permissions() as $permission) {
        if ($permission->permission == "uren o.b.v. getal") {
          $urengetal = true;
        }
      }

      $redenen = DB::table('verlofredenen')->get();
      $facturabelopties = DB::table('facturabelopties')->get();
      $naca = DB::table('naca_codes')->get();

      $uren = UrenRegistratie::where('medewerker_id', $user->id)->where(DB::raw("YEAR(datum)"), $year)->where('tijdelijk', 0)->where('goedkeuren', 0)->with('kenteken')->with('verlofRtl')->with('naca')->orderBy('datum', 'DESC')->get();

      return view('uren.invoeren.weekoverzicht', [
        'user' => $user,
        'uren' => $uren,
        'year' => $year,
        'urengetal' => $urengetal,
        'redenen' => $redenen,
        'facturabelopties' => $facturabelopties,
        'naca' => $naca
      ]);
    }
    private function previousDayFilled($date){
      $yesterday = Carbon($date)->subDay();
      return UrenRegistratie::select('datum')->where('medewerker_id', User::id())->where('datum', $yesterday)->where('tijdelijk', 0)->where('goedkeuren', 0)->exists();
    }

    public function invoeren(){

      $id = Auth::user()->id;
      $user = User::where('id', $id)->firstOrFail();
      $date = Carbon::createFromFormat('d-m-Y', Route::current()->parameter('date'))->format('Y-m-d');

        $projectnummersQuery = Project::where('active', '1')->with('taken');

        $statussen = getSettingJson('urenregistratie_invoeren_projectstatussen_portal');
        if ($statussen) {
            $projectnummersQuery->whereIn('status', $statussen);
        }

        $projectnummers = $projectnummersQuery->get();

      if (getSettingValue('urenregistratie_verplicht_vorige_dag_ingevuld') && !$this->previousDayFilled($date)) {
        return redirect()->back()->with('warning', 'Zorg eerst dat uw voorgaande urenregistratie compleet is.');
      }

      if(getSettingValue('urenregistratie_invoeren_projectnummers') == 'perbv'){
        $projectnummers = $projectnummers->where('bv', $user->bv_id);
      }
      if(getSettingValue('urenregistratie_invoeren_offertenummers') == 'aan'){
        $offertenummers = Offertes::where(['active' => 1, 'project_id' => null, 'type' => 1])->where(function ($query){$query->where("status", "In aanmaak")->orWhere("status", "LIKE", "Accorderen%")->orWhere("status", "Uitgebracht")->orWhere("status", "like", "verzonden%");})->with('klant')->get();
      }

      $kentekens = DB::table('kentekens')->get();
      $naca = DB::table('naca_codes')->get();
      $redenen = DB::table('verlofredenen')->get();
      $facturabelopties = DB::table('facturabelopties')->get();
      $type = "update";
      $projecten = UrenRegistratie::where('medewerker_id', $id)->where('datum', $date)->get();
      $urengetal = false;
      $machines = Machines::where("active", 1)->with("group")->get();
      $uursoorten = Uursoorten::where("active", 1)->get();
      if(getSettingValue('urenregistratie_invoeren_uursoorten') == 'bv'){
        $uursoorten = $uursoorten->where('bv', Auth::user()->bv->id);
      }
      $uursoorten = $uursoorten->sortBy('code');
      $planning = Planning::where("user_id", $id)->where("datum",$date)->with("project", "offerte", "machines", "taken")->get();
      $werkbonnen = Werkbonnen::where("user_id", $id)->where("datum", Carbon::parse($date)->format('d-m-Y'))->where("active", 1)->with("project", "values")->get();
      foreach ($user->permissions() as $permission) {
        if ($permission && $permission->permission == "uren o.b.v. getal") {
          $urengetal = true;
        }
      }

      if (count($projecten) <= 0) {
        $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
        $type = "insert";
        $projecten = DB::table('standaarduren')->where('dag', $dagen[Carbon::parse($date)->dayOfWeek])->where('medewerkers_id', $user->id)->get();
      }

      activity()->log(Auth::user()->name . ' ('.Auth::user()->id.') heeft de uren ingevoerd.');
      return view('uren.invoeren.invoeren', [
        'projecten' => $projecten,
        'kentekens' => $kentekens,
        'urengetal' => $urengetal,
        'type' => $type,
        'redenen' => $redenen,
        'facturabelopties' => $facturabelopties,
        'naca' => $naca,
        'date' => $date,
        'user' => $user,
        'projectnummers' => $projectnummers,
        'offertenummers' => $offertenummers ?? [],
        "machines" => $machines,
        "uursoorten" => $uursoorten,
        "planning" => $planning,
        "werkbonnen" => $werkbonnen,
      ]);

      }
    public function store(Request $request) {
      if($request->action == 'tijdelijk'){
        $tijdelijk = 1;
      }
      else{
        $tijdelijk = 0;
      }

      $totaalDagUren = 0;
      $totaalDagUren_incl_verlof = 0;
      $feestdaguren = false;
      $atvUren = false;

      $date = Carbon::parse(Route::current()->parameter('date'))->format('Y-m-d');
      $user_id = Auth::user()->id;
      $tijdvoortijdgezet = 0;

      $user = User::where('id', $user_id)->firstOrFail();

      UrenRegistratie::where('medewerker_id', $user->id)->where('datum', $date)->where('tijdelijk', 1)->delete();
      foreach($request->projecten ?? [] as $i => $project){
        if ($project["type"] == 'insert') {
          $ziekteuren = 0;
          $tijdvoortijd = 0;
          $feesturen = 0;
          $verlofuren = 0;
          $bijzonderverlofuren = 0;
          $roostervrij = 0;
          $atv = 0;
          $overuren125 = 0;
          $overuren128 = 0;
          $overuren150 = 0;
          $overuren165 = 0;
          $overuren200 = 0;
          $pauze       = 0;
          $totaaluren  = 0;
          $totaaluren_incl_verlof = 0;

          if (isset($project["tijdvoortijd"])) {
            $tijdvoortijdgezet = 1;
          }

          if (!empty($project["pauze"])) {
            $pauze = $project["pauze"];
          }

          if (isset($project["gewerkteuren"])) {
            $totaaluren = $project["gewerkteuren"] - $pauze;
            $totaaluren_incl_verlof = $totaaluren;
          }
          else {
            $totaaluren = strtotime($project["eindtijd"]) - strtotime($project["begintijd"]);
            $totaaluren = ($totaaluren / 3600) - $pauze;
            $totaaluren_incl_verlof = $totaaluren;
          }

          $hoofdReden = HoofdVerlofreden::getVerlofType($project["verlofreden"]);

          if (isset($project["verlof"]) && isset($project["verlofreden"])) {
            if ($hoofdReden == 'Ziekte') {
              $ziekteuren = $project["verlof"];
              $totaaluren = $totaaluren - $ziekteuren;

              if ($totaaluren < 0) {
                $ziekteuren = $ziekteuren + $totaaluren;
                $totaaluren = 0;
              }
            } elseif ($hoofdReden == 'Verlof') {
              $verlofuren = $project["verlof"];
              $totaaluren = $totaaluren - $verlofuren;
              if ($totaaluren < 0) {
                $verlofuren = $verlofuren + $totaaluren;
                $totaaluren = 0;
              }
            } elseif ($hoofdReden == 'Bijzonder verlof') {
              $bijzonderverlofuren = $project["verlof"];
              $totaaluren = $totaaluren - $bijzonderverlofuren;
              if ($totaaluren < 0) {
                $bijzonderverlofuren = $bijzonderverlofuren + $totaaluren;
                $totaaluren = 0;
              }
            } elseif ($hoofdReden == 'Roostervrije uren') {
              $roostervrij = $project["verlof"];
              $totaaluren = $totaaluren - $roostervrij;
              if ($totaaluren < 0) {
                $roostervrij = $roostervrij + $totaaluren;
                $totaaluren = 0;
              }
            }elseif ($hoofdReden == 'Feestdag') {
              $feestdaguren = true;
              $feesturen = $project["verlof"];
              $totaaluren = $totaaluren - $feesturen;
              if ($totaaluren < 0) {
                $feesturen = $feesturen + $totaaluren;
                $totaaluren = 0;
              }
            }elseif ($hoofdReden == 'ATV') {
              $atvUren = true;
              $atv = $project["verlof"];
              $totaaluren = $totaaluren - $atv;
              if ($totaaluren < 0) {
                $atv = $atv + $totaaluren;
                $totaaluren = 0;
              }
            } else {
              $verlofuren = $project["verlof"];
              $totaaluren = $totaaluren - $verlofuren;
              if ($totaaluren < 0) {
                $verlofuren = $verlofuren + $totaaluren;
                $totaaluren = 0;
              }
            }
          }
          else {
            $verlofuren = 0;
          }

          $naca = null;
          if (!empty($project["naca"])) {
            if ($project["naca"] == "default") {
              $naca = null;
            } else {
              $naca = $project["naca"];
            }
          }



          $bestuurder = null;
          if(!empty($project)) {
            if(isset($project["bestuurder"]) && $project["bestuurder"] == "1") {
              $bestuurder = "1";
            } else {
              $bestuurder = "0";
            }
          }

          // Reisuren uitschakelen
          if($user->all_in === '1' || $user->disable_reisuren === '1'){
            $project["reisuren"] = 0;
          }

          // Custom berekeningen per klant
          $subdomein = getSubdomein();
          if (method_exists(UrenregistratieClientFunctions::class, $subdomein)) {
            $project = UrenregistratieClientFunctions::$subdomein($project, $request);
          }

          $insertId = DB::table('urenregistratie')->insertGetId([
            'medewerker_id' => $user_id,
            'datum'         => $date,
            'projectnummer' => !empty($project["projectnummer"]) ? $project["projectnummer"] : null,
            'pauze'         => !empty($project["pauze"])         ? $project["pauze"]         : 0,
            'begintijd'     => !empty($project["begintijd"])     ? Carbon::parse($project["begintijd"])->format('H:i') : null,
            'eindtijd'      => !empty($project["eindtijd"])      ? Carbon::parse($project["eindtijd"])->format('H:i')  : null,
            'gewerkte_uren' => !empty($project["gewerkteuren"])  ? $project["gewerkteuren"]  : 0,
            'reisuren'      => !empty($project["reisuren"])      ? $project["reisuren"] : 0,
            'kilometers'      => !empty($project["kilometers"])  ? $project["kilometers"]  : 0,
            'overnachting'  => !empty($project["overnachting"])  ? $project["overnachting"]  : null,

            'heenreis_huisproject'   => !empty($project["heenreis_huisproject"])  ? $project["heenreis_huisproject"]  : null,
            'terugreis_huisproject'  => !empty($project["terugreis_huisproject"]) ? $project["terugreis_huisproject"] : null,
            'heenreis_woonwerk'      => !empty($project["heenreis_woonwerk"])     ? $project["heenreis_woonwerk"]     : null,
            'terugreis_woonwerk'     => !empty($project["terugreis_woonwerk"])    ? $project["terugreis_woonwerk"]    : null,

            'ladenlossen_begintijd'        => !empty($project["ladenlossen_begintijd"])        ? Carbon::parse($project["ladenlossen_begintijd"])->format('H:i:s')        : null,
            'ladenlossen_eindtijd'         => !empty($project["ladenlossen_eindtijd"])         ? Carbon::parse($project["ladenlossen_eindtijd"])->format('H:i:s')         : null,
            'ladenlossen_middag_begintijd' => !empty($project["ladenlossen_middag_begintijd"]) ? Carbon::parse($project["ladenlossen_middag_begintijd"])->format('H:i:s') : null,
            'ladenlossen_middag_eindtijd'  => !empty($project["ladenlossen_middag_eindtijd"])  ? Carbon::parse($project["ladenlossen_middag_eindtijd"])->format('H:i:s')  : null,
            'bestuurder'      => $bestuurder,
            'bijrijder'       => !empty($project["bijrijder"])   ? $project["bijrijder"]   : null,
            'kenteken_id'     => !empty($project["kenteken"])    ? $project["kenteken"]    : null,
            'verlof'          => $verlofuren,
            'bijzonderverlof' => $bijzonderverlofuren,
            'roostervrij' => $roostervrij,
            'ziekteuren'      => $ziekteuren,
            'feesturen'       => $feesturen,
            'atv_uren'       => $atv,
            'facturabel_id'  => !empty($project["facturabel"]) ? $project["facturabel"] : null,
            'verlofreden_id'  => !empty($project["verlofreden"]) ? $project["verlofreden"] : null,
            'naca_id'         => $naca,
            'opmerkingen'     => !empty($project["opmerkingen"]) ? $project["opmerkingen"] : null,
            'totaaluren100'      => $totaaluren,
            'totaal_overuren150' => 0,
            'totaal_overuren165' => 0,
            'totaal_overuren200' => 0,
            'tijdvoortijd' => 0,
            'totaaldaguren100' => 0,
            'tijdelijk' => $tijdelijk,
            'uursoort_id' => $project['uursoort'] ?? null,

          ]);
          $totaalDagUren += $totaaluren;
          $totaalDagUren_incl_verlof += $totaaluren_incl_verlof;

          foreach($project["machines"] ?? [] as $machine){
            $begintijd = null;
            $eindtijd = null;

            foreach($project["machinetijden"] ?? [] as $machineid => $tijden){
              if($machineid == $machine){
                $begintijd = $tijden['begintijd'];
                $eindtijd = $tijden['eindtijd'];
              }
            }

            UrenRegistratieMachines::insert([
              'urenregistratie_id' => $insertId,
              'machine_id' => $machine,
              'begintijd' => $begintijd,
              'eindtijd' => $eindtijd,
            ]);
          }
          foreach($project["project_taken"] ?? [] as $taak_id){
            UrenRegistratieProjectTaken::insert([
              'urenregistratie_id' => $insertId,
              'taak_id' => $taak_id,
            ]);
          }
        }
      }

      Cache::forget($request->fullUrl());

      $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
      $dag   = $dagen[Carbon::parse($request->date)->dayOfWeek];
      $standaarduren = DB::table('standaarduren')->where('dag', $dag)->where('medewerkers_id', $user_id)->first();
      $standaarduren = $standaarduren->standaarduren ?? 0;

      $overuren = [
        '100' => $totaalDagUren,
        '125' => 0,
        '128' => 0,
        '150' => 0,
        '165' => 0,
        '200' => 0,
        'tijdvoortijd' => 0,
      ];
      $sett = json_decode(getSettingValue('uren_overuren') ?? '[]', true);
      $minuren = 0;
      if($user->all_in == 1){
        $overuren['125'] = 0;
        $overuren['128'] = 0;
        $overuren['150'] = 0;
        $overuren['165'] = 0;
        $overuren['200'] = 0;
        DB::table('urenregistratie')->where('id', $insertId)->update(['reisuren' => 0]);
      }
      else {
        if (getSettingCheckbox('urenregistratie_advanced_overuren')){
          $overuren = UrenRegistratie::calcAdvancedOveruren($request);
        }
        else{
          if($user->nul_uren == 0 && !(Carbon::parse($request->date)->isWeekend())) {
            if ($overuren['100'] > $standaarduren) {
              $overuren[($sett['weekdagen'] ?? '150')] = $overuren['100'] - $standaarduren;
              $overuren['100'] = $standaarduren;
            }
            if($tijdvoortijdgezet == 1){
              if ($overuren['150'] > 0 || $overuren['125'] > 0 || $overuren[($sett['weekdagen'] ?? '150')] > 0) {
                $overuren['tijdvoortijd'] = $overuren[($sett['weekdagen'] ?? '150')];
                $overuren['125'] = 0;
                $overuren['128'] = 0;
                $overuren['150'] = 0;
                $overuren['165'] = 0;
                $overuren['200'] = 0;
              }
              elseif ($totaalDagUren_incl_verlof < $standaarduren) {
                $overuren['100'] = $standaarduren;
                $overuren['tijdvoortijd'] = $totaalDagUren_incl_verlof - $standaarduren;
              }
            }
          }
          elseif ($user->nul_uren == 0) {
            if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SATURDAY)) {
              $overuren[($sett['zaterdag'] ?? '165')] = $overuren['100'];
              $overuren['100'] = 0;
            }
            if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SUNDAY)) {
              $overuren[($sett['zondag'] ?? '200')] = $overuren['100'];
              $overuren['100'] = 0;
            }
            if($tijdvoortijdgezet == 1) {
              $overuren['tijdvoortijd'] = $overuren[($sett['zaterdag'] ?? '165')] + $overuren[($sett['zondag'] ?? '200')];
              $overuren['125'] = 0;
              $overuren['128'] = 0;
              $overuren['150'] = 0;
              $overuren['165'] = 0;
              $overuren['200'] = 0;
            }
          }
          if ($feestdaguren || $atvUren){
            $overuren['100'] = 0;
            $overuren['125'] = 0;
            $overuren['128'] = 0;
            $overuren['150'] = 0;
            $overuren['165'] = 0;
            $overuren['200'] = 0;
          }
        }
        if ($totaalDagUren_incl_verlof < $standaarduren && $tijdvoortijdgezet == 0) {
          $minuren = $standaarduren - $totaalDagUren_incl_verlof;
        }
      }

      DB::table('urenregistratie')->where('id', $insertId)->update([
        'totaaldaguren100' => $overuren['100'],
        'totaal_overuren125' => $overuren['125'],
        'totaal_overuren128' => $overuren['128'],
        'totaal_overuren150' => $overuren['150'],
        'totaal_overuren165' => $overuren['165'],
        'totaal_overuren200' => $overuren['200'],
        'tijdvoortijd' => $overuren['tijdvoortijd'],
        'minuren' => $minuren ?? 0,
      ]);

      $log = Auth::user()->name." ".Auth::user()->lastname." heeft uren ingevoerd.";
      actLog($log, User::id(), 2);

      return redirect('/uren/invoeren')->with('status', 'De uren zijn ingevoerd.');
    }

    public function correctie(){
      $date = Carbon::createFromFormat('d-m-Y', Route::current()->parameter('date'))->format('Y-m-d');
      return view('uren.invoeren.correctie', [ 'date' => $date ]);
    }
    public function storecorrectie(Request $request) {
      $id = Auth::user()->id;
      $date = Carbon::createFromFormat('d-m-Y', $request->date)->format('Y-m-d');
      $user = User::where('id', $id)->with('role')->firstOrFail();

      Message::insert([
        'message' => $request->correctiebericht,
        'date' => $date,
        'user_id' => $id,
      ]);

      if(!empty($user->role->correctie_mail )){Mail::to($user->role->correctie_mail )->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->correctiebericht));}
      if(!empty($user->role->correctie_mail2)){Mail::to($user->role->correctie_mail2)->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->correctiebericht));}
      if(!empty($user->role->correctie_mail3)){Mail::to($user->role->correctie_mail3)->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->correctiebericht));}
      return redirect('/uren/invoeren')->with('status', 'De correctie is aangevraagd.');
    }

}
