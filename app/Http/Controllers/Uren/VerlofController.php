<?php

namespace App\Http\Controllers\Uren;

use App\ActivityLog;
use App\Planning;
use App\Verlofreden;
use App\HoofdVerlofreden;
use Auth;
use Route;
use DB;
use App\Standaarduren;
use App\User;
use App\Device;
use App\Verlof;
use App\UrenRegistratie;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Mail\VerlofAanvraag;
use App\Exports\VerlofExport;
use Maatwebsite\Excel\Facades\Excel;

use DatePeriod;
use DateInterval;

class VerlofController extends Controller
{

  public function index()
  {
    $verlof = Verlof::query()->with(['user', 'door'])->where("beoordeeld", 0)
      ->whereHas('user', function ($query) {
        $query->where('active', 1);
      })->orderBy('datum', 'ASC')->paginate(100);

    return view('uren.verlof.index', ['verlof' => $verlof]);
  }

  public function afhandelen(Request $request, $subdomein)
  {
    $status = $request->query('status', 'in_behandeling');
    $startDate = $request->query('start_date');
    $endDate = $request->query('end_date');

    $applyDateFilter = function ($query) use ($startDate, $endDate) {
      if ($startDate) {
        $query->where('datum', '>=', $startDate);
      }
      if ($endDate) {
        $query->where('datum', '<=', $endDate);
      }
      return $query;
    };

    $verlofQuery = Verlof::query()->with('user')->where('beoordeeld', 0);
    $verlofQuery = $applyDateFilter($verlofQuery);
    $verlof = $verlofQuery->orderBy('medewerker_id')->orderBy('datum')->get();

    $grouped = [];
    foreach ($verlof as $v) {
      $userId = $v->medewerker_id;
      $redenId = $v->reden_id;
      if (!isset($grouped[$userId])) {
        $grouped[$userId] = [];
      }
      if (!isset($grouped[$userId][$redenId])) {
        $grouped[$userId][$redenId] = [];
      }
      $grouped[$userId][$redenId][] = $v;
    }

    $finalGroups = [];
    foreach ($grouped as $userId => $redenGroups) {
      foreach ($redenGroups as $redenId => $rows) {
        usort($rows, function($a, $b) {
          return strcmp($a->datum, $b->datum);
        });
        $currentGroup = [];
        $lastDate = null;
        foreach ($rows as $row) {
          $date = Carbon::parse($row->datum);
          if ($lastDate && $date->diffInDays($lastDate) === 1) {
            $currentGroup[] = $row;
          } else {
            if (!empty($currentGroup)) {
              $finalGroups[] = $currentGroup;
            }
            $currentGroup = [$row];
          }
          $lastDate = $date;
        }
        if (!empty($currentGroup)) {
          $finalGroups[] = $currentGroup;
        }
      }
    }

    return view('uren.verlof.afhandelen', [
      'finalGroups' => $finalGroups,
      'verlof' => $verlof,
      'status' => $status,
      'startDate' => $startDate,
      'endDate' => $endDate
    ]);
  }

  public function verlofGoedgekeurd()
  {
    $maanden = getMaanden();
    $ver = Verlof::query()->with(['user', 'door'])->where("beoordeeld", 1)->where("akkoord", 1)->orderBy('datum', 'DESC')->get();
    $verlof = [];
    foreach ($ver as $row) {
      $verlof[Carbon::parse($row->datum)->year][Carbon::parse($row->datum)->month][] = $row;
    }
    return view("uren.verlof.goedgekeurd", [
      "verlof" => $verlof,
      "maanden" => $maanden,
    ]);
  }

  public function verlofAfgekeurd()
  {
    $maanden = getMaanden();
    $ver = Verlof::query()->with(['user', 'door'])->where("beoordeeld", 1)->where("akkoord", 0)->orderBy('datum', 'DESC')->get();
    $verlof = [];
    foreach ($ver as $row) {
      $verlof[Carbon::parse($row->datum)->year][Carbon::parse($row->datum)->month][] = $row;
    }
    return view("uren.verlof.afgekeurd", [
      "verlof" => $verlof,
      "maanden" => $maanden,
    ]);
  }

  public function edit()
  {
    $verlof = Verlof::query()->with(['user', 'door'])->where('id', Route::current()->parameter('id'))->firstOrFail();
    return view('uren.verlof.edit', ['verlof' => $verlof]);
  }

  public function aanvragen($subdomein, $id)
  {
    $medewerker = User::find($id);
    $verlofsaldo = $medewerker ? $medewerker->verlofsaldoExclusief(date('Y')) : null;

    $verlof = Verlof::where('medewerker_id', $id)
      ->where('beoordeeld', 0)
      ->whereDate('datum', '>=', now()->toDateString())
      ->get();

    $ingepland = [];
    foreach ($verlof as $aanvraag) {
      $hasPlanning = Planning::where('user_id', $id)
        ->where('datum', $aanvraag->datum)
        ->exists();
      $ingepland[$aanvraag->id] = $hasPlanning;
    }

    return view('uren.verlof.aanvraag', [
      'verlofsaldo' => $verlofsaldo,
      'verlof' => $verlof,
      'ingepland' => $ingepland,
    ]);
  }

  public function verlofRedenen(Request $request)
  {
    $redenen = Verlofreden::where('active', 1)->get();
    return view('uren.verlof.verlofredenen', ['verlofredenen' => $redenen]);
  }

  public function updateRedenen(Request $request)
  {
    foreach ($request->verlofredenen as $redenData) {
      if (isset($redenData['active']) && $redenData['active'] == '0') {
        if (isset($redenData['id'])) {
          Verlofreden::where('id', $redenData['id'])->update(['active' => 0]);
        }
        continue;
      }

      if (!empty($redenData['id'])) {
        Verlofreden::where('id', $redenData['id'])->update([
          'reden' => $redenData['reden'] ?? '',
          'omschrijving' => $redenData['omschrijving'] ?? '',
          'standaard_reden' => $redenData['standaard_reden'] ?? '',
          'role_id' => $redenData['role_id'] ?? 0,
          'kleur' => $redenData['kleur'] ?? '#000000',
          'active' => 1,
        ]);
      } else {
        Verlofreden::create([
          'reden' => $redenData['reden'] ?? '',
          'omschrijving' => $redenData['omschrijving'] ?? '',
          'standaard_reden' => $redenData['standaard_reden'] ?? '',
          'role_id' => $redenData['role_id'] ?? 0,
          'kleur' => $redenData['kleur'] ?? '#000000',
          'active' => 1,
        ]);
      }
    }

    return response()->json(['message' => 'Redenen opgeslagen']);
  }

  // public function store(Request $request)
  // {
  //   Verlof::where('id', Route::current()->parameter('id'))->update([
  //     'akkoord'      => $request->status,
  //     'akkoord_door' => Auth::id(),
  //     'beoordeeld'   => 1
  //   ]);

  //   $verlof = Verlof::where('id', Route::current()->parameter('id'))->firstOrFail();
  //   $user = User::where('id', $verlof->medewerker_id)->first();

  //   $devices = Device::where('user_id', $verlof->medewerker_id)->pluck('device_id')->unique()->toArray();

  //   if ($request->status == 1) {
  //     pushToAll("Verlofaanvraag", "De verlofaanvraag van " . $request->date . " is goedgekeurd.", $devices);

  //     $dagen = ["zondag", "maandag", "dinsdag", "woensdag", "donderdag", "vrijdag", "zaterdag"];
  //     $dag   = $dagen[Carbon::parse($verlof->datum)->dayOfWeek];
  //     $standaarduren = DB::table('standaarduren')->where('medewerkers_id', $verlof->medewerker_id)->where('dag', $dag)->first();


  //     if ($verlof->van == null && $verlof->tot == null) {
  //       UrenRegistratie::insert([
  //         'medewerker_id' => $verlof->medewerker_id,
  //         'datum' => $verlof->datum,
  //         'projectnummer' => '-',
  //         'gewerkte_uren' => $standaarduren->standaarduren,
  //         'pauze'         => $standaarduren->pauze,
  //         'begintijd'     => $standaarduren->begintijd,
  //         'eindtijd'      => $standaarduren->eindtijd,
  //         'reisuren'      => 0,
  //         'overnachting'  => null,
  //         'heenreis_huisproject'   => null,
  //         'terugreis_huisproject'  => null,
  //         'heenreis_woonwerk'      => null,
  //         'terugreis_woonwerk'     => null,
  //         'ladenlossen_begintijd'        => null,
  //         'ladenlossen_eindtijd'         => null,
  //         'ladenlossen_middag_begintijd' => null,
  //         'ladenlossen_middag_eindtijd'  => null,
  //         'bestuurder'      => 0,
  //         'bijrijder'       => null,
  //         'kenteken_id'     => null,
  //         'verlof'          => $standaarduren->standaarduren,
  //         'ziekteuren'      => 0.00,
  //         'feesturen'       => 0.00,
  //         'verlofreden_id'  => 2,
  //         'naca_id'         => null,
  //         'opmerkingen'     => null,
  //         'totaaluren100'      => 0.00,
  //         'totaal_overuren150' => 0,
  //         'totaal_overuren200' => 0,
  //         'tijdvoortijd' => 0,
  //         'totaaldaguren100' => 0,
  //         'tijdelijk' => 0
  //       ]);
  //     }

  //     if (getSettingValue('planning_verlof_blok_weergeven') == 'aan') {
  //       $verlof->toPlanning($standaarduren);
  //     }
  //   } elseif ($request->status == 0) {
  //     UrenRegistratie::where("datum", ">", Carbon::now()->format("Y-m-d"))->where("datum", $verlof->datum)->whereNotNull("verlofreden_id")->delete();
  //     pushToAll("Verlofaanvraag", "De verlofaanvraag van " . $request->date . " is afgewezen.", $devices);
  //   }

  //   activity()->log(Auth::user()->name . ' (' . Auth::user()->id . ') heeft de verlofaanvraag van ' . $user->name . ' (' . $user->id . ') beoordeeld.');

  //   return redirect('/uren/verlof')->with('status', 'Aanvraag is afgehandeld.');
  // }

  public function aanvraag(Request $request)
  {
    $selectedVerlofIds = $request->input('verlof');
    $status = (int) $request->input('status');
    $currentUserId = auth()->id();

    foreach ($selectedVerlofIds as $verlofId) {
      $verlof = Verlof::find($verlofId);
      if (!$verlof || $verlof->beoordeeld) {
        continue;
      }

      $verlof->beoordeeld = 1;
      $verlof->akkoord = $status;
      $verlof->akkoord_door = $currentUserId;
      $verlof->beoordeel_opmerking = $request->input('opmerking') ?? null;
      $verlof->updated_at = now();
      $verlof->save();

      $devices = Device::where('user_id', $verlof->medewerker_id)->pluck('device_id')->unique()->toArray();
      $dateStr = $verlof->datum;

      if ($status === 1) {
        pushToAll("Verlofaanvraag", "De verlofaanvraag van $dateStr is goedgekeurd.", $devices);

        $dagen = ["zondag", "maandag", "dinsdag", "woensdag", "donderdag", "vrijdag", "zaterdag"];
        $dag = $dagen[Carbon::parse($verlof->datum)->dayOfWeek];
        $standaarduren = Standaarduren::where('medewerkers_id', $verlof->medewerker_id)
          ->where('dag', $dag)
          ->first();

        if ($standaarduren) {
          if (is_null($verlof->van) && is_null($verlof->tot)) {
            $hoofdReden = HoofdVerlofreden::getVerlofType($verlof->reden_id);

            UrenRegistratie::create([
              'medewerker_id' => $verlof->medewerker_id,
              'datum' => $verlof->datum,
              'projectnummer' => '-',
              'gewerkte_uren' => $standaarduren->standaarduren,
              'pauze' => $standaarduren->pauze,
              'begintijd' => $standaarduren->begintijd,
              'eindtijd' => $standaarduren->eindtijd,
              'reisuren' => 0,
              'bestuurder' => 0,
              'verlof' => $hoofdReden == 'Verlof' ? $standaarduren->standaarduren : 0,
              'bijzonderverlof' => $hoofdReden == 'Bijzonder verlof' ? $standaarduren->standaarduren : 0,
              'ziekteuren' => $hoofdReden == 'Ziekte' ? $standaarduren->standaarduren : 0,
              'feesturen' => $hoofdReden == 'Feestdag' ? $standaarduren->standaarduren : 0,
              'verlofreden_id' => $verlof->reden_id,
              'naca_id' => null,
              'totaaluren100' => 0.00,
              'totaal_overuren150' => 0,
              'totaal_overuren200' => 0,
              'tijdvoortijd' => 0,
              'totaaldaguren100' => 0,
              'tijdelijk' => 0
            ]);
          }
          if (getSettingValue('planning_verlof_blok_weergeven') === 'aan') {
            $verlof->toPlanning($standaarduren, $verlof->id);
          }
        }
      } else {
        pushToAll("Verlofaanvraag", "De verlofaanvraag van $dateStr is afgewezen.", $devices);
        UrenRegistratie::where('datum', $verlof->datum)
          ->where('medewerker_id', $verlof->medewerker_id)
          ->whereNotNull('verlofreden_id')
          ->delete();
      }
    }

    $message = $status === 1
      ? 'Geselecteerde verlofaanvragen zijn goedgekeurd.'
      : 'Geselecteerde verlofaanvragen zijn afgewezen.';

    return redirect('/uren/verlof/afhandelen')->with('status', $message);
  }

  public function aanvragenInzien(Request $request, $subdomein, $id, $year)
  {
    $currentYear = (int)date('Y');
    if ($year > $currentYear) {
      $year = $currentYear;
    }
    if (!Auth::user()->HasPermissionTo('Verlof afhandelen') && $id != Auth::user()->id) {
      return redirect('/uren/verlof/aanvragen/inzien/');
    }
    $status = $request->query('status', 'all');
    $startDate = $request->query('start_date');
    $endDate = $request->query('end_date');

    $applyDateFilter = function ($query) use ($startDate, $endDate, $year) {
      $query->whereYear('datum', $year);
      if ($startDate) {
        $query->where('datum', '>=', $startDate);
      }
      if ($endDate) {
        $query->where('datum', '<=', $endDate);
      }
      return $query;
    };

    $verlofQuery = Verlof::query()->where('medewerker_id', $id);
    if ($status === 'in_behandeling') {
      $verlofQuery->where('beoordeeld', 0);
    } elseif ($status === 'goedgekeurd') {
      $verlofQuery->where('beoordeeld', 1)->where('akkoord', 1);
    } elseif ($status === 'afgewezen') {
      $verlofQuery->where('beoordeeld', 1)->where('akkoord', 0);
    } elseif ($status === 'all') {
      $verlofQuery->where('beoordeeld', 0)->where('akkoord', 0);
    }
    $verlofQuery = $applyDateFilter($verlofQuery);
    $verlof = $verlofQuery->orderBy('datum')->get();

    $grouped = [];
    foreach ($verlof as $v) {
      $key = $v->reden_id;
      if (!isset($grouped[$key])) {
        $grouped[$key] = [];
      }
      $grouped[$key][] = $v;
    }
    $finalGroups = [];
    foreach ($grouped as $key => $rows) {
      $currentGroup = [];
      $lastDate = null;
      foreach ($rows as $row) {
        $date = Carbon::parse($row->datum);
        if ($lastDate && $date->diffInDays($lastDate) === 1) {
          $currentGroup[] = $row;
        } else {
          if (!empty($currentGroup)) {
            $finalGroups[] = $currentGroup;
          }
          $currentGroup = [$row];
        }
        $lastDate = $date;
      }
      if (!empty($currentGroup)) {
        $finalGroups[] = $currentGroup;
      }
    }

    $medewerker = User::find($id);

    $start = date('Y-m-d', strtotime('01-01-'.$year));
    $end = date('Y-m-d', strtotime('31-12-'.$year));
    $gebruiktSaldo = $medewerker->gebruiktVerlofSaldo($start, $end);
    $resterendSaldo = $medewerker->verlofsaldoExclusief($year);
    $contractueelSaldo = $medewerker->standaard_verlofsaldo;
    $aanvullendSaldo = $medewerker->verlofsaldo($year);

    $verlofGoedgekeurd = Verlof::where('medewerker_id', $id)
      ->where('beoordeeld', 1)
      ->where('akkoord', 1);
    $verlofGoedgekeurd = $applyDateFilter($verlofGoedgekeurd)->orderBy('datum')->get();
    $groupedGoedgekeurd = [];
    foreach ($verlofGoedgekeurd as $v) {
      $key = $v->reden_id;
      if (!isset($groupedGoedgekeurd[$key])) {
        $groupedGoedgekeurd[$key] = [];
      }
      $groupedGoedgekeurd[$key][] = $v;
    }
    $finalGroupsGoedgekeurd = [];
    foreach ($groupedGoedgekeurd as $key => $rows) {
      $currentGroup = [];
      $lastDate = null;
      foreach ($rows as $row) {
        $date = Carbon::parse($row->datum);
        if ($lastDate && $date->diffInDays($lastDate) === 1) {
          $currentGroup[] = $row;
        } else {
          if (!empty($currentGroup)) {
            $finalGroupsGoedgekeurd[] = $currentGroup;
          }
          $currentGroup = [$row];
        }
        $lastDate = $date;
      }
      if (!empty($currentGroup)) {
        $finalGroupsGoedgekeurd[] = $currentGroup;
      }
    }

    $verlofAfgewezen = Verlof::where('medewerker_id', $id)
      ->where('beoordeeld', 1)
      ->where('akkoord', 0);
    $verlofAfgewezen = $applyDateFilter($verlofAfgewezen)->orderBy('datum')->get();
    $groupedAfgewezen = [];
    foreach ($verlofAfgewezen as $v) {
      $key = $v->reden_id;
      if (!isset($groupedAfgewezen[$key])) {
        $groupedAfgewezen[$key] = [];
      }
      $groupedAfgewezen[$key][] = $v;
    }
    $finalGroupsAfgewezen = [];
    foreach ($groupedAfgewezen as $key => $rows) {
      $currentGroup = [];
      $lastDate = null;
      foreach ($rows as $row) {
        $date = Carbon::parse($row->datum);
        if ($lastDate && $date->diffInDays($lastDate) === 1) {
          $currentGroup[] = $row;
        } else {
          if (!empty($currentGroup)) {
            $finalGroupsAfgewezen[] = $currentGroup;
          }
          $currentGroup = [$row];
        }
        $lastDate = $date;
      }
      if (!empty($currentGroup)) {
        $finalGroupsAfgewezen[] = $currentGroup;
      }
    }

    return view('uren.verlof.inzien', [
      'medewerker' => $medewerker,
      'finalGroups' => $finalGroups,
      'finalGroupsGoedgekeurd' => $finalGroupsGoedgekeurd,
      'finalGroupsAfgewezen' => $finalGroupsAfgewezen,
      'verlof' => $verlof,
      'status' => $status,
      'startDate' => $startDate,
      'endDate' => $endDate,
      'year' => $year,
      'gebruiktSaldo' => $gebruiktSaldo,
      'resterendSaldo' => $resterendSaldo,
      'contractueelSaldo' => $contractueelSaldo,
      'aanvullendSaldo' => $aanvullendSaldo,
    ]);
  }

  public function inzien(Request $request, $subdomein,  $year)
  {
    $status = $request->query('status', 'all');
    $startDate = $request->query('start_date');
    $endDate = $request->query('end_date');

    $applyDateFilter = function ($query) use ($startDate, $endDate) {
      if ($startDate) {
        $query->where('datum', '>=', $startDate);
      }
      if ($endDate) {
        $query->where('datum', '<=', $endDate);
      }
      return $query;
    };

    $verlofQuery = Verlof::query()->with(['user'])->whereYear('datum', $year);
    if ($status === 'in_behandeling') {
      $verlofQuery->where('beoordeeld', 0);
    } elseif ($status === 'goedgekeurd') {
      $verlofQuery->where('beoordeeld', 1)->where('akkoord', 1);
    } elseif ($status === 'afgewezen') {
      $verlofQuery->where('beoordeeld', 1)->where('akkoord', 0);
    } elseif ($status === 'all') {
      $verlofQuery->where('beoordeeld', 0)->where('akkoord', 0);
    }
    $verlofQuery = $applyDateFilter($verlofQuery);
    $verlof = $verlofQuery->orderBy('medewerker_id')->orderBy('datum')->get();

    $grouped = [];
    foreach ($verlof as $v) {
      $key = $v->medewerker_id . '-' . $v->reden_id;
      if (!isset($grouped[$key])) {
        $grouped[$key] = [];
      }
      $grouped[$key][] = $v;
    }
    $finalGroups = [];
    foreach ($grouped as $key => $rows) {
      $currentGroup = [];
      $lastDate = null;
      foreach ($rows as $row) {
        $date = Carbon::parse($row->datum);
        if ($lastDate && $date->diffInDays($lastDate) === 1) {
          $currentGroup[] = $row;
        } else {
          if (!empty($currentGroup)) {
            $finalGroups[] = $currentGroup;
          }
          $currentGroup = [$row];
        }
        $lastDate = $date;
      }
      if (!empty($currentGroup)) {
        $finalGroups[] = $currentGroup;
      }
    }

    $finalGroupsGoedgekeurd = $finalGroupsAfgewezen = [];
    if ($status === 'all') {
      $verlofGoedgekeurd = Verlof::where('beoordeeld', 1)
        ->where('akkoord', 1)
        ->whereYear('datum', $year)
        ->with(['user', 'door']);
      $verlofGoedgekeurd = $applyDateFilter($verlofGoedgekeurd)->orderBy('medewerker_id')->orderBy('datum')->get();
      $groupedGoedgekeurd = [];
      foreach ($verlofGoedgekeurd as $v) {
        $key = $v->medewerker_id . '-' . $v->reden_id;
        if (!isset($groupedGoedgekeurd[$key])) {
          $groupedGoedgekeurd[$key] = [];
        }
        $groupedGoedgekeurd[$key][] = $v;
      }
      foreach ($groupedGoedgekeurd as $key => $rows) {
        $currentGroup = [];
        $lastDate = null;
        foreach ($rows as $row) {
          $date = Carbon::parse($row->datum);
          if ($lastDate && $date->diffInDays($lastDate) === 1) {
            $currentGroup[] = $row;
          } else {
            if (!empty($currentGroup)) {
              $finalGroupsGoedgekeurd[] = $currentGroup;
            }
            $currentGroup = [$row];
          }
          $lastDate = $date;
        }
        if (!empty($currentGroup)) {
          $finalGroupsGoedgekeurd[] = $currentGroup;
        }
      }
      $verlofAfgewezen = Verlof::where('beoordeeld', 1)
        ->where('akkoord', 0)
        ->whereYear('datum', $year)
        ->with(['user', 'door']);
      $verlofAfgewezen = $applyDateFilter($verlofAfgewezen)->orderBy('medewerker_id')->orderBy('datum')->get();
      $groupedAfgewezen = [];
      foreach ($verlofAfgewezen as $v) {
        $key = $v->medewerker_id . '-' . $v->reden_id;
        if (!isset($groupedAfgewezen[$key])) {
          $groupedAfgewezen[$key] = [];
        }
        $groupedAfgewezen[$key][] = $v;
      }
      foreach ($groupedAfgewezen as $key => $rows) {
        $currentGroup = [];
        $lastDate = null;
        foreach ($rows as $row) {
          $date = Carbon::parse($row->datum);
          if ($lastDate && $date->diffInDays($lastDate) === 1) {
            $currentGroup[] = $row;
          } else {
            if (!empty($currentGroup)) {
              $finalGroupsAfgewezen[] = $currentGroup;
            }
            $currentGroup = [$row];
          }
          $lastDate = $date;
        }
        if (!empty($currentGroup)) {
          $finalGroupsAfgewezen[] = $currentGroup;
        }
      }
    }

    return view('uren.verlof.alleverlof', [
      'year' => $year,
      'finalGroups' => $finalGroups,
      'finalGroupsGoedgekeurd' => $finalGroupsGoedgekeurd,
      'finalGroupsAfgewezen' => $finalGroupsAfgewezen,
      'verlof' => $verlof,
      'status' => $status,
      'startDate' => $startDate,
      'endDate' => $endDate
    ]);
  }

  public function invoeren($subdomein)
  {
    $id = Auth::user()->id;
    $verlof = Verlof::where('medewerker_id', $id)
      ->orderBy('datum', 'desc')
      ->get();

    $grouped = [];
    foreach ($verlof->sortBy('datum') as $v) {
      $key = $v->reden_id;
      if (!isset($grouped[$key])) {
        $grouped[$key] = [];
      }
      $grouped[$key][] = $v;
    }

    $finalGroupsMijnVerlof = [];
    foreach ($grouped as $key => $rows) {
      $currentGroup = [];
      $lastDate = null;
      foreach ($rows as $row) {
        $date = Carbon::parse($row->datum);
        if ($lastDate && $date->diffInDays($lastDate) === 1) {
          $currentGroup[] = $row;
        } else {
          if (!empty($currentGroup)) {
            $finalGroupsMijnVerlof[] = $currentGroup;
          }
          $currentGroup = [$row];
        }
        $lastDate = $date;
      }
      if (!empty($currentGroup)) {
        $finalGroupsMijnVerlof[] = $currentGroup;
      }
    }

    $allUsersVerlofGrouped = [];
    if (Auth::user()->hasPermissionTo('Verlof afhandelen')) {
      $users = getUsers()->where('id', '!=', $id);
      foreach ($users as $user) {
        $userVerlof = Verlof::where('medewerker_id', $user->id)
          ->orderBy('datum', 'desc')
          ->get();
        $groupedUser = [];
        foreach ($userVerlof->sortBy('datum') as $v) {
          $key = $v->reden_id;
          if (!isset($groupedUser[$key])) {
            $groupedUser[$key] = [];
          }
          $groupedUser[$key][] = $v;
        }
        $finalGroups = [];
        foreach ($groupedUser as $key => $rows) {
          $currentGroup = [];
          $lastDate = null;
          foreach ($rows as $row) {
            $date = Carbon::parse($row->datum);
            if ($lastDate && $date->diffInDays($lastDate) === 1) {
              $currentGroup[] = $row;
            } else {
              if (!empty($currentGroup)) {
                $finalGroups[] = $currentGroup;
              }
              $currentGroup = [$row];
            }
            $lastDate = $date;
          }
          if (!empty($currentGroup)) {
            $finalGroups[] = $currentGroup;
          }
        }
        $allUsersVerlofGrouped[$user->id] = [
          'user' => $user,
          'groups' => $finalGroups
        ];
      }
    }
    return view('uren.verlof.invoeren', [
      'verlof' => $verlof,
      'finalGroupsMijnVerlof' => $finalGroupsMijnVerlof,
      'allUsersVerlofGrouped' => $allUsersVerlofGrouped
    ]);
  }

  public function export($subdomein, $year, $month, $status)
  {
    $stat = ["A", "G"];
    $log = Auth::user()->name . " " . Auth::user()->lastname . " heeft de verlofaanvragen gedownload.";
    ActivityLog::insert([
      "client_id" => getClientId(),
      "user_id" => Auth::user()->id,
      "module_id" => 3,
      "log" => $log,
      "created_at" => Carbon::now(),
    ]);
    return Excel::download(new VerlofExport($year, $month, $status), 'Verlof' . $year . str_pad($month, 2, 0, STR_PAD_LEFT) . $stat[$status] . '.xlsx');
  }

  public function verlofAanvragen(Request $request)
  {
    $daterange = $request->input('daterange');
    [$start, $end] = array_map('trim', explode(' - ', $daterange));
    $startDate = Carbon::createFromFormat('d-m-Y', $start);
    $endDate = Carbon::createFromFormat('d-m-Y', $end);
    $medewerker_ids = $request->input('medewerker_id');
    $reden_id = $request->input('reden_id');
    $opmerking = $request->input('opmerking');
    $akkoord_door = 0;
    $hasPermission = Auth::user()->hasPermissionTo('verlof afhandelen');

    if ($hasPermission) {
      $akkoord_door = Auth::user()->id;
    }

    $days = $startDate->diffInDays($endDate) + 1;
    $dagen = ["zondag", "maandag", "dinsdag", "woensdag", "donderdag", "vrijdag", "zaterdag", "zondag"];
    $verlofCreated = false;
    $processedEmployees = [];

    if (!is_array($medewerker_ids)) {
      $medewerker_ids = [$medewerker_ids];
    }

    foreach ($medewerker_ids as $medewerker_id) {
      if (empty($medewerker_id)) {
        continue;
      }

      $user = User::with('role')->findOrFail($medewerker_id);
      $employeeVerlofCreated = false;

      for ($i = 0; $i < $days; $i++) {
        $date = $startDate->copy()->addDays($i);
        $standaarduren = Standaarduren::where('medewerkers_id', $medewerker_id)
          ->where('dag', $dagen[$date->dayOfWeek])
          ->first();

        if (empty($standaarduren) || empty($standaarduren->standaarduren) || $standaarduren->standaarduren <= 0) {
          continue;
        }

        $verlof = new Verlof();
        $verlof->datum = $date->format('Y-m-d');
        $verlof->medewerker_id = $medewerker_id;
        $verlof->reden_id = $reden_id;
        $verlof->opmerkingen = $opmerking;
        $verlof->beoordeeld = $hasPermission ? 1 : 0;
        $verlof->akkoord = $hasPermission ? 1 : 0;
        $verlof->akkoord_door = $akkoord_door;

        if ($days === 1) {
          $verlof->van = $request->input('start_time') ?: null;
          $verlof->tot = $request->input('end_time') ?: null;
        }

        $verlof->save();
        $employeeVerlofCreated = true;
        $verlofCreated = true;

        if ($hasPermission) {
          if (getSettingValue('planning_verlof_blok_weergeven') == 'aan') {
            $verlof->toPlanning($standaarduren, $verlof->id);
          }

          if (is_null($verlof->van) && is_null($verlof->tot)) {
            $hoofdReden = HoofdVerlofreden::getVerlofType($verlof->reden_id);
            UrenRegistratie::create([
              'medewerker_id' => $verlof->medewerker_id,
              'datum' => $verlof->datum,
              'projectnummer' => '-',
              'gewerkte_uren' => $standaarduren->standaarduren,
              'pauze' => $standaarduren->pauze,
              'begintijd' => $standaarduren->begintijd,
              'eindtijd' => $standaarduren->eindtijd,
              'reisuren' => 0,
              'bestuurder' => 0,
              'verlof' => $hoofdReden == 'Verlof' ? $standaarduren->standaarduren : 0,
              'bijzonderverlof' => $hoofdReden == 'Bijzonder verlof' ? $standaarduren->standaarduren : 0,
              'ziekteuren' => $hoofdReden == 'Ziekte' ? $standaarduren->standaarduren : 0,
              'feesturen' => $hoofdReden == 'Feestdag' ? $standaarduren->standaarduren : 0,
              'verlofreden_id' => $reden_id,
              'totaaluren100' => 0.00,
              'totaal_overuren150' => 0,
              'totaal_overuren200' => 0,
              'tijdvoortijd' => 0,
              'totaaldaguren100' => 0,
              'tijdelijk' => 0
            ]);
          }
        }
      }

      if ($employeeVerlofCreated) {
        $processedEmployees[] = $user;
      }
    }

    if (!$hasPermission && $verlofCreated) {
      $van = $startDate->format('d-m-Y');
      $tot = $days > 1 ? $endDate->format('d-m-Y') : null;
      $start_time = $request->input('start_time');
      $end_time = $request->input('end_time');

      foreach ($processedEmployees as $user) {
        $mail = $user->role->verlof_mail ?? null;
        $mail2 = $user->role->verlof_mail2 ?? null;
        $mail3 = $user->role->verlof_mail3 ?? null;

        $mailData = new VerlofAanvraag(
          $user->name . ' ' . $user->lastname,
          $van,
          $tot,
          $start_time,
          $end_time,
          $opmerking
        );

        if (!empty($mail)) {
          Mail::to($mail)->send($mailData);
        }
        if (!empty($mail2)) {
          Mail::to($mail2)->send($mailData);
        }
        if (!empty($mail3)) {
          Mail::to($mail3)->send($mailData);
        }
      }
    }

    $employeeCount = count($processedEmployees);
    if ($employeeCount > 1) {
      $message = $hasPermission
        ? "Verlof is ingevoerd voor {$employeeCount} medewerkers"
        : "Verlof is aangevraagd voor {$employeeCount} medewerkers";
    } else {
      $message = $hasPermission ? 'Verlof is ingevoerd' : 'Verlof is aangevraagd';
    }

    return redirect('/uren/verlof/invoeren')->with('status', $message);
  }
}
