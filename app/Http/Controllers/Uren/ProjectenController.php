<?php

namespace App\Http\Controllers\Uren;

use App\Aanvragen;
use App\ActivityLog;
use App\ExplorerFiles;
use App\Offerteteksten;
use App\ProjectCustomRows;
use App\ProjectenCustomTablesRows;
use App\ProjectenCustomTablesRowsValues;
use App\ProjectenExportTemplates;
use App\ProjectenLocaties;
use App\ProjectenTakenTemplates;
use App\Role;
use App\Vestigingen;
use App\WerkbonnenTemplates;
use DB;
use Config;
use Auth;
use PDF;
use Illuminate\Support\Facades\Storage;
use Route;
use App\Project;
use App\User;
use App\Offertes;
use App\Offerteregels;
use App\Opdrachtbonnen;
use App\OpdrachtbonTermijnen;
use Illuminate\Support\Facades\Mail;
use App\Mail\sendOpdrachtbon;
use App\Klanten;
use App\UrenRegistratie;
use App\Facturabelopties;
use App\BV;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use SoapBox\Formatter\Formatter;

use App\Imports\ProjectImport;
use App\Exports\ProjectExport;

use App\Http\Controllers\Controller;
use App\ProjectTaken;

class ProjectenController extends Controller {

  public function index($subdomein) {
    injectSettings('uren.projecten.index');

    $werkbonnenTemplates = WerkbonnenTemplates::with('keywords')->get();

    return view('uren.projecten.index', [
      'werkbonnenTemplates' => $werkbonnenTemplates
    ]);
  }

  public function new(){
    $klanten = Klanten::where("status", 1)->orderBy('naam')->orderBy('contactpersoon_voornaam')->with("contactpersonen", "locaties.contactpersonen")->get();
    $bvs = BV::where("client_id", getClientId())->get();
    $velden = json_decode(getSettingValue("projecten_velden"), true);
    $files = ExplorerFiles::where('active', 1)->get()->keyBy('id');
    $vestigingen = Vestigingen::where('active', 1)->with('managers', 'rayon_managers')->orderBy('naam', 'ASC')->get();

    return view("uren.projecten.create",[
      "users" => getUsers(),
      "klanten" => $klanten,
      "bvs" => $bvs,
      "velden" => $velden,
      "explorerFiles" => $files,
      "vestigingen" => $vestigingen
    ]);
  }
  public function store(Request $request){
    try{
      if(isset($request->aanvraag)){
        Aanvragen::where('aanvraagnummer', $request->aanvraag)->update(["status" => "Afgerond"]);
      }

      $klant = Klanten::where("id", $request->opdrachtgever)->first();
      if(isset($klant)){
        $opdrachtgever = $klant->naam ?? $klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam;
      }
      else{
        $opdrachtgever = $request->opdrachtgever;
      }


      if(isset($request->generateProjNum)){
        $projectnummer = Project::nummer($request->bv ?? getBv());
      }else{
        $projectnummer = $request->projectnummer;
      }

      if(isset($request->projectleider)){
        $projectleider = (getUser($request->projectleider)->name ?? '').' '.(getUser($request->projectleider)->lastname ?? '');
      }

      $id = Project::insertGetId([
        'status' => $request->status ?? 'Opdracht',
        'bv' => $request->bv ?? '',
        'code' => $request->code,
        'opdrachtgever' => $opdrachtgever,
        'klant_id' => $klant->id ?? null,
        'vestiging_id' => $request->vestiging,
        'manager_id' => $request->manager,
        'rayonmanager_id' => $request->rayonmanager,
        'projectleider' => $projectleider ?? '',
        'projectleider_id' => $request->projectleider,
        'projectnr' => $projectnummer,
        'projectnaam' => $request->projectnaam,
        'opdrachtnummer' => $request->opdrachtnummer,
        'taaknummer' => $request->taaknummer,
        'adres' => $request->adres,
        'huisnummer' => $request->huisnummer,
        'toevoeging' => $request->toevoeging,
        'postcode' => $request->postcode,
        'woonplaats' => $request->woonplaats,
        'reis' => ($request->reistijd['h'] ?? 0) + (($request->reistijd['m'] ?? 0) / 60),
        'omschrijving' => $request->omschrijving,
        'contactpersoon_id' => $request->contactpersoon,
        'created_at' => Carbon::now(),
      ]);

      $this->storeProjectFiles($id, $request);
      $this->storeProjectRows($id, $request);
      $this->storeUploadedOfferteXml($id, $request, $projectnummer);
      $this->storeProjectLocatie($id, $request);
      $this->storeCustomTables($id, $request);

      Project::createDefaultFolders($id, $projectnummer);

      $project = Project::where('id', $id)->first();

      $log = Auth::user()->name." ".Auth::user()->lastname." heeft het project '".$project->projectnr."' aangemaakt.";
      actLog($log, User::id(), 7);


      return redirect("uren/projectnummers/index?status=".($request->status ?? 'Opdracht'))->with("status","Project <b>".$request->projectnr."</b> aangemaakt.");
    }
    catch (\Exception $e){
      actError($e);
      return redirect("uren/projectnummers/index?status=".($request->status ?? 'Opdracht'))->with("warning","Er is iets foutgegaan!");
    }
  }
  public function edit($subdomein, $id) {
    $project = Project::where('id', $id)->with("offerte", "offertes", "meerwerk_offertes", "contactpersoon", "custom", "vestiging", "locatie", "locatieContactpersoon")->firstOrFail();
    $project->setRelation('custom', $project->custom->keyBy('keyword'));
    $bvs = BV::where("client_id", getClientId())->get();
    $klanten = Klanten::where("status", 1)->orderBy('naam')->orderBy('contactpersoon_voornaam')->with("contactpersonen", "locaties.contactpersonen")->get();
    $velden = json_decode(getSettingValue("projecten_velden"), true);
    $vestigingen = Vestigingen::where('active', 1)->with('managers', 'rayon_managers')->orderBy('naam', 'ASC')->get();

    return view('uren.projecten.edit', [
      "users" => getUsers(),
      'project' => $project,
      'bvs' => $bvs,
      'klanten' => $klanten,
      'velden' => $velden,
      'vestigingen' => $vestigingen,
      'customExclude' => ['woocommerce_items' => true],
      'facturen' => $project->facturen(),
    ]);
  }
  public function update(Request $request) {
    $project = Project::where("id",  Route::current()->parameter('id'))->first();

    $klant = Klanten::where("id", $request->opdrachtgever)->first();
    if(isset($klant)){
      $opdrachtgever = $klant->naam ?? $klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam;
    }
    else{
      $opdrachtgever = $request->opdrachtgever;
    }

    if(isset($request->projectleider)){
      $projectleider = (getUser($request->projectleider)->name ?? '').' '.(getUser($request->projectleider)->lastname ?? '');
    }

    Project::where('id', Route::current()->parameter('id'))->update([
      'bv' => $request->bv,
      'code' => $request->code,
      'opdrachtgever' => $opdrachtgever,
      'klant_id' => $klant->id ?? null,
      'vestiging_id' => $request->vestiging,
      'manager_id' => $request->manager,
      'rayonmanager_id' => $request->rayonmanager,
      'projectleider' => $projectleider ?? '',
      'projectleider_id' => $request->projectleider,
      'projectnr' => $request->projectnummer,
      'projectnaam' => $request->projectnaam,
      'opdrachtnummer' => $request->opdrachtnummer,
      'taaknummer' => $request->taaknummer,
      'adres' => $request->adres,
      'huisnummer' => $request->huisnummer,
      'toevoeging' => $request->toevoeging,
      'postcode' => $request->postcode,
      'woonplaats' => $request->woonplaats,
      'reis' => ($request->reistijd['h'] ?? 0) + (($request->reistijd['m'] ?? 0) / 60),
      'omschrijving' => $request->omschrijving,
      'contactpersoon_id' => $request->contactpersoon,
      'exact_globe_update_required' => 1
    ]);

    $project = Project::where("id", Route::current()->parameter('id'))->first();

    $this->storeProjectRows($project->id, $request);
    $this->storeUploadedOfferteXml($project->id, $request, $request->projectnummer);
    $this->storeProjectLocatie($project->id, $request);
    $this->storeCustomTables($project->id, $request);

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft het project '".$project->projectnr."'  gewijzigd.";
    actLog($log, User::id(), 7);

    return redirect('/uren/projectnummers/index?status='.$project->status)->with('status', 'Project <b>'. $request->projectnummer .'</b> is gewijzigd.');
  }
  public function preview($subdomein, $id){
    $data = $this->edit($subdomein, $id)->getData();
    $data['preview'] = true;

    return view('uren.projecten.edit', $data);
  }

  private function storeProjectFiles($id, $request){
    try {
			foreach($request->explorer_files ?? [] as $file_id){
				$file = ExplorerFiles::find($file_id);

				if($file->path == '/Projecten/Tijdelijk'){
					$file->move("/Projecten/({$id})");
				}
				else{
					$file->copy("/Projecten/({$id})");
				}

			}
    }
    catch (\Exception $e){actError($e);}
  }
  private function storeProjectRows($id, $request){
    try{
      ProjectCustomRows::where('project_id', $id)->delete();

      foreach ($request->custom ?? [] as $keyword => $value){
        if(!isset($value)){continue;}
        $setting = json_decode((getSettingValue('projecten_cusotm_rows') ?? '[]'), true);
        $key = find('keyword', $keyword, $setting);

        //files
        if($key['type'] == 'file' && $request->file("custom.$keyword")){
          $file = ExplorerFiles::insertFromRequest([
            'file' => $request->file("custom.$keyword"),
            'path' => "/Projecten/({$id})",
            'name' => $keyword,
            'description' => $key['name'] ?? '',
          ]);
        }

        ProjectCustomRows::insert([
          'project_id' => $id,
          'keyword' => $keyword,
          'name' => $key['name'] ?? '',
          'type' => $key['type'] ?? '',
          'data' => json_encode($key['data']) ?? '',
          'value' => isset($file) ? $file->src : $value,
        ]);
      }
    }
    catch (\Exception $e){actError($e);}
  }
  private function storeUploadedOfferteXml($id, $request, $projectnummer){
    if(!$request->offerteupload){return;}
    try{
      $object = simplexml_load_file($request->offerteupload);
      $object = json_decode(json_encode($object), TRUE);
      $lagen = json_decode(getSettingValue('offerte_xml_totaal') ?? '[]', true);
      if(getSettingValue('offerte_xml_totaal_regeltype') == 'regel'){
        if(count($lagen) == 1){
          $totaalprijs = $object[$lagen['laag_1']];
        }elseif(count($lagen) == 2){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']];
        }elseif(count($lagen) == 3){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']];
        }elseif(count($lagen) == 4){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']];
        }elseif(count($lagen) == 5){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']][$lagen['laag_5']];
        }
      }elseif(getSettingValue('offerte_xml_totaal_regeltype') == 'attribuut'){
        if(count($lagen) == 1){
          $totaalprijs = $object[$lagen['laag_1']]['@attributes'];
        }elseif(count($lagen) == 2){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']]['@attributes'];
        }elseif(count($lagen) == 3){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']]['@attributes'];
        }elseif(count($lagen) == 4){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']]['@attributes'];
        }elseif(count($lagen) == 5){
          $totaalprijs = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']][$lagen['laag_5']]['@attributes'];
        }
      }
      $lagen = json_decode(getSettingValue('offerte_xml_btw') ?? '[]', true);
      if(getSettingValue('offerte_xml_btw_regeltype') == 'regels' || getSettingValue('offerte_xml_btw_regeltype') == 'regelattributen'){
        if(count($lagen) == 1){
          $btw = $object[$lagen['laag_1']];
        }elseif(count($lagen) == 2){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']];
        }elseif(count($lagen) == 3){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']];
        }elseif(count($lagen) == 4){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']];
        }elseif(count($lagen) == 5){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']][$lagen['laag_5']];
        }
      }elseif(getSettingValue('offerte_xml_btw_regeltype') == 'attributen'){
        if(count($lagen) == 1){
          $btw = $object[$lagen['laag_1']]['@attributes'];
        }elseif(count($lagen) == 2){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']]['@attributes'];
        }elseif(count($lagen) == 3){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']]['@attributes'];
        }elseif(count($lagen) == 4){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']]['@attributes'];
        }elseif(count($lagen) == 5){
          $btw = $object[$lagen['laag_1']][$lagen['laag_2']][$lagen['laag_3']][$lagen['laag_4']][$lagen['laag_5']]['@attributes'];
        }
      }
      $offerteId = Offertes::insertGetId([
        "klant_id" => $request->opdrachtgever,
        "offertenummer" => $projectnummer,
        "naam" => $request->projectnaam ?? $projectnummer,
        "template_id" => 1,
        "user_id" => Auth::user()->id,
        "offerte_datum" => Carbon::now(),
        "geldig_tot" => Carbon::now()->addDays(30),
        "Status" => "Akkoord",
        "token" => randomString(20),
        "inclBtw" => 0,
        "aanvragen" => json_encode([]),
        "deelbaar" => 0,
        "project_id" => $id,
        "versie" => 1,
        "created_at" => Carbon::now(),
      ]);
      $this->xmlToRegels($offerteId, $totaalprijs, $btw, $request);
    }catch (\Exception $e){
    actError($e);}
  }
  private function storeCustomTables($id, $request){
    ProjectenCustomTablesRows::where('project_id', $id)->update(['project_id' => "-{$id}"]);
    ProjectenCustomTablesRowsValues::where('project_id', $id)->each(function($value) use ($id){
      $value->project_id = "-{$id}";
      $value->table_row_id = "-{$value->table_row_id}";
      $value->save();
    });

    foreach($request->custom_tables ?? [] as $table_key => $table){

      foreach($table['rows'] ?? [] as $row){

        $table_row_id = ProjectenCustomTablesRows::insertGetId([
          'project_id' => $id,
          'table_keyword' => $table_key,
        ]);

        foreach($row as $column_key => $value){

          ProjectenCustomTablesRowsValues::insert([
            'project_id' => $id,
            'table_row_id' => $table_row_id,
            'column_keyword' => $column_key,
            'value' => $value,
          ]);

        }

      }

    }
  }

  private function storeProjectLocatie($id, $request){
    ProjectenLocaties::where(['project_id' => $id])->update(["project_id" => '-'.$id]);
    if (isset($request->locatie)){
      $existingRecord = ProjectenLocaties::where(['project_id' => -$id , 'locatie_id' => $request->locatie])->first();

      if ($existingRecord) {
        $existingRecord->update([
          'project_id' => $id,
          'locatie_contactpersoon_id' => $request->locatieContactpersoon ?? null
        ]);
      } else {
        ProjectenLocaties::insert([
          'project_id' => $id,
          'locatie_id' => $request->locatie,
          'locatie_contactpersoon_id' => $request->locatieContactpersoon ?? null
        ]);
      }
    }
  }


  private function xmlToRegels($offerteId, $totaalprijs, $btw, $request){
      if(getSettingValue('offerte_xml_btw_regeltype') == 'regelattributen'){
        foreach($btw as $row){
          foreach($row['@attributes'] as $name => $value){
            if($name != '' && !is_numeric($value)){
             $tekst = $value;
            }elseif($name != '' && $value != '0' && $value != '0.00' && $value != '0,00'){
              Offerteregels::insert([
                "offerte_id" => $offerteId,
                "tekst" => ucfirst(strtolower($tekst)),
                "stukprijs" => $value,
                "kostprijs" => 0,
                "aantal" => 1,
                "btw" => 21,
                "totaalprijs" => $value,
                "created_at" => Carbon::now(),
                "deelbaar" => 0,
              ]);
            }
          }
        }
      }else{
        $totaalbtw = 0;
        foreach($btw as $name => $value){
          if($name != 'UREN' && $value != '0' && $value != '0,00' && $value != '0.00'){
            $totaalbtw += intval($value);
            Offerteregels::insert([
              "offerte_id" => $offerteId,
              "tekst" => ucfirst(strtolower($name)),
              "stukprijs" => $value,
              "kostprijs" => 0,
              "aantal" => 1,
              "btw" => 21,
              "totaalprijs" => $value,
              "created_at" => Carbon::now(),
              "deelbaar" => 0,
            ]);
          }
        }
      }
      $offertetotaal = 0;
      foreach($totaalprijs as $name => $value){
        if($name != 'UREN' && $value != '0' && $value != '0,00' && $value != '0.00'){
          $offertetotaal += intval($value);
          Offerteregels::insert([
            "offerte_id" => $offerteId,
            "tekst" => ucfirst(strtolower($name)),
            "stukprijs" => $value,
            "kostprijs" => 0,
            "aantal" => 1,
            "btw" => 21,
            "totaalprijs" => $value,
            "created_at" => Carbon::now(),
            "deelbaar" => 0,
          ]);
        }
      }
      Offerteteksten::insert([
        "offerte_id" => $offerteId,
        "tekst" => $totaalbtw,
        "keyword" => 'btw',
      ]);
      $totaalin = $offertetotaal;
      Offerteteksten::insert([
        "offerte_id" => $offerteId,
        "tekst" => $totaalin,
        "keyword" => 'totaalIn',
      ]);
      $totaalex = $totaalin - $totaalbtw;
      Offerteteksten::insert([
        "offerte_id" => $offerteId,
        "tekst" => $totaalex,
        "keyword" => 'totaalEx',
      ]);

  }

  public function calculate(Request $request){
    $projecten = Project::with("offerte")->whereIn('id', $request->ids ?? [])->get();
    foreach($projecten as $project){
      $output[$project->projectnr] = sprintf("%.2f", UrenRegistratie::where("projectnummer",$project->projectnr)->sum("totaaluren100"));
    }
    $output['-'] = UrenRegistratie::where("projectnummer",'-')->sum("totaaluren100");
    return response()->json([
      'projecten' => $output ?? [],
    ], 201);
  }
  public function calculateMachine(Request $request){
    $projecten = Project::with("uren")->whereIn('id', $request->ids ?? [])->get();
    foreach($projecten as $project){
      $totaal = 0;
      foreach($project->uren as $uren){
        foreach($uren->machineuren as $row){
          if($row->begintijd != null && $row->eindtijd != null){
            $diffinminutes = Carbon::parse($row->eindtijd)->diffInMinutes(Carbon::parse($row->begintijd))/60;
            $x = $diffinminutes * 4;
            $x = round($x);
            $x = $x / 4;
            $totaal = $totaal + $x;
          }
        }
      }
      $output[$project->projectnr] = sprintf("%.2f", $totaal);
    }
    $geenproject = UrenRegistratie::where("projectnummer",'-')->with('machineuren')->get();
    $totaalgeenproject = 0;
    foreach($geenproject as $proj){
      foreach($proj->machineuren as $row){
        if($row->begintijd != null && $row->eindtijd != null){
          $diffinminutes = Carbon::parse($row->eindtijd)->diffInMinutes(Carbon::parse($row->begintijd))/60;
          $x = $diffinminutes * 4;
          $x = round($x);
          $x = $x / 4;
          $totaalgeenproject = $totaalgeenproject + $x;
        }
      }
    }
    $output['-'] = sprintf("%.2f", $totaalgeenproject);
    return response()->json([
      'projecten' => $output ?? [],
    ], 201);
  }

  public function excel() {
    return view('uren.projecten.upload');
  }
  public function upload(Request $request) {
    DB::table('projecten')->where('active', 1)->update(['active' => 0]);
    try {
      Excel::import(new ProjectImport, $request->document);
      actLog(User::name().' heeft projecten geupload via Excel.', User::id(), 7);
      return redirect('/uren/projectnummers/index?status=Acquisitie')->with('status', $request->document->getClientOriginalName() . ' is geüpload.');
    } catch(Exception $e) {
      return redirect('/uren/projectnummers/index?status=Acquisitie')->with('status', $request->document->getClientOriginalName() . ' is geen geldig document.');
    }
  }
  public function download($subdomein, $id) {
    $log = Auth::user()->name." ".Auth::user()->lastname."  heeft het projectenoverzicht gedownload.";
    actLog($log, Auth::user()->id, 7);


    return Excel::download(new ProjectExport($id), 'Projecten.xlsx');
  }

  public function projectUren($subodmein, $projectId = null, $startDate = null, $endDate = null){
    $urengetal = Auth::user()->hasPermissionTo("uren o.b.v. getal");
    if(isset($projectId)){
      $project = Project::where("id", $projectId)->with("uren")->first();
    }
    else{
      $uren = UrenRegistratie::with('user', 'facturabelopties')->where('projectnummer', '-')->get();
    }

    $preview = false;
    if (isset($startDate) || isset($endDate)) {
      $preview = true;
      $betweenDates = ['startDate' => $startDate, 'endDate' => $endDate];
    }

    return view("uren.projecten.projectUren",[
      'preview' => $preview,
      'urengetal' => $urengetal,
      'project' => $project ?? null,
      'uren' => $uren ?? [],
      'betweenDates' => $betweenDates ?? [],
    ]);
  }
  public function projectMachineUren($subodmein, $projectId = null){
    $project = Project::where("id", $projectId)->with("uren")->first();
    foreach($project->uren as $uren){
      $totaal = 0;
      foreach($uren->machineuren as $row){
        if($row->begintijd != null && $row->eindtijd != null){
          $diffinminutes = Carbon::parse($row->eindtijd)->diffInMinutes(Carbon::parse($row->begintijd))/60;
          $x = $diffinminutes * 4;
          $x = round($x);
          $x = $x / 4;
          $row->totaal = $totaal + $x;
        }
      }
    }
    return view("uren.projecten.projectMachineUren",[
      'project' => $project ?? null,
      'uren' => $uren ?? [],
    ]);
  }
  public function gefactureerd($subdomein, $id){
    $uren = DB::table('urenregistratie')->where("id", $id)->update([
      "gefactureerd" => 1,
    ]);
    $project = UrenRegistratie::where("id",$id)->with("project")->first()->project;
    $url = isset($project) ? $project->id : '';
    return redirect('uren/projecturen/'.$url)->with("status","Actie voltooid");
  }

  public function takenEdit($subdomein, $id){
    if($id == 'todo'){
      $project = new Project;
      $project->id = 0;
      $project->taken = ProjectTaken::where('project_id', 0)->with("planning", "custom", "subtaken")->where('active', 1)->orderBy('created_at', "DESC")->get();
    }else{
      $project = Project::where("id", $id)->with("_bv", "offerte", "taken", "completed_taken", "planning")->firstOrFail();
    }
    $templates = ProjectenTakenTemplates::where('active', 1)->with('taken')->orderBy('name', 'ASC')->get();

    return view('uren.projecten.taken.window', [
      'project' => $project,
      'templates' => $templates,
    ]);
  }
  public function takenTemplates(){
    $templates = ProjectenTakenTemplates::where('active', 1)->orderBy('name', 'ASC')->get();

    return view('uren.projecten.taken.templates', [
      'templates' => $templates,
    ]);
  }
  public function takenTemplatesEdit($sub, $id){
    $template = ProjectenTakenTemplates::where('id', $id)->with('taken')->firstOrFail();

    return view('uren.projecten.taken.template', [
      'template' => $template,
    ]);
  }

  public function get(){
    $projecten = Project::orderBy('code')->get();
    return response()->json(['projecten' => $projecten]);
  }

  public function copyfiles($files){
    foreach($files as $id){
      $file = ExplorerFiles::where('id', $id)->first();
      $string = randomString(15).".".$file->extension;
      $arr[] = $string;
      $data = Storage::disk('client')->get('explorer/files/'.$file->src);
      Storage::disk("client")->put("projecten/files/".$string, $data);
    };
    return $arr ?? [];
  }

  public function listSummary($subdomain, $p, $kId){
    $project = Project::where('id', $p)->with(
      'taken.werkbonnen.template',
      'taken.werkbonnen.keywords',
      'taken.subtaken.werkbonnen.template',
      'taken.subtaken.werkbonnen.keywords')->first();

    return view('uren.projecten.iframe.listInputSummary', [
      'project' => $project,
      'kId' => $kId
    ]);
  }

  public function opdrachtbon($subdomain, $id, Request $request){
    $project = Project::where("id", $id)->with('klant')->firstOrFail();
    $offerte = Offertes::where('project_id', $id)->with('regels')->firstOrFail();

    $opdrachtbonid = Opdrachtbonnen::insertGetId([
      'project_id' => $project->id,
      'offerte_id' => $offerte->id,
      'token' => randomString(25),
      'aanmaker' => Auth::user()->name.' '.Auth::user()->lastname,
      'active' => 1,
    ]);

    foreach($request->opdrbevtermijnen as $i => $termijn){
      OpdrachtbonTermijnen::insert([
        "opdrachtbon_id" => $opdrachtbonid,
        "termijn" => $termijn,
        "omschrijving" => $request->opdrbevomschrijvingen[$i],
      ]);
    }

    $data = [
      "request" => $request,
      "project" => $project,
      "offerte" => $offerte,
      "aanmaker" => Auth::user()->name.' '.Auth::user()->lastname,
      "aanmaakdatum" => Carbon::now()->format('d-m-Y'),
    ];

    $pdf = PDF::loadView("opdrachtbon.pdf.template".getClientId(), $data);
    return $pdf->stream( $offerte->klanten->naam.' '.$offerte->offertenummer."_opdrachtbon.pdf");
  }
  public function opdrachtbonByToken($subdomain, $token){
    $opdrachtbon = Opdrachtbonnen::where('token', $token)->with('project','termijnen')->firstOrFail();
    $offerte = Offertes::where('id', $opdrachtbon->offerte_id)->with('regels')->firstOrFail();

    $handtekening = User::where('id', $offerte->user_id)->first()->handtekening;

    $data = [
      "opdrachtbon" => $opdrachtbon,
      "project" => $opdrachtbon->project,
      "offerte" => $offerte,
      "handtekening" => $handtekening,
      "aanmaker" => $opdrachtbon->aanmaker,
      "aanmaakdatum" => Carbon::now()->format('d-m-Y'),
    ];

    $pdf = PDF::loadView("opdrachtbon.pdf.template".getClientId(), $data);
    return $pdf->stream( $offerte->klanten->naam.' '.$offerte->offertenummer."_opdrachtbon.pdf");
  }
  public function sendOpdrachtbon(Request $request){
    try{
      $emails = explode(",", str_replace(" ", "", $request->email));
      foreach($emails as $index => $mail){
        if(!isset($mail) || $mail == ""){
          unset($emails[$index]);
          continue;
        }
        if(!verifyEmail($mail)){
          return response(null, 500);
        }
      }

      $subdomein = $request->route()->parameter('subdomain');
      $opdrachtbon = Opdrachtbonnen::where("id", $request->opdrachtbonId)->with("project", "termijnen")->firstOrFail();

      if(!isset($opdrachtbon->token)){
        $opdrachtbon->token = randomString(25);
        $opdrachtbon->save();
      }

      foreach ($request->explorer_files ?? [] as $id){
        $file = ExplorerFiles::where('id', $id)->first();
        if(!isset($file)){ continue; }

        $files[] = url("api/file/explorer/files/".$file->src);
      }
      foreach ($request->file('files') ?? [] as $file){
        $file = ExplorerFiles::insertFromRequest([
          'file' => $file,
          'name' => randomString(5).'_'.$file->getClientOriginalName(),
          'path' => '/opdrachtbonnen/verzonden',
          'description' => null,
        ]);
        $files[] = url('/api/file/explorer/files/'.$file->src);
      }

      foreach ($emails as $row){
        Mail::to($row)->send(new sendOpdrachtbon($request->subject, $request->mess, $subdomein, $opdrachtbon, $request->sender, $files));
      }

      Opdrachtbonnen::where('id', $opdrachtbon->id)->update([
        'status' => 'Verzonden',
        'sent_to' => $request->email,
        'sent_at' => Carbon::now(),
      ]);

      $log = User::name()." heeft de opdrachtbon van project ".$opdrachtbon->project->projectnummer." verstuurd aan ".implode(", ", $emails).".";
      actLog($log, Auth::user()->id, 27);

      return response()->json([
        'emails' => $emails,
      ]);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function ondertekenenOpdrachtbon($subdomein, $token){
    $opdrachtbon = Opdrachtbonnen::where("token", $token)->with("termijnen", "project")->firstOrFail();
    Opdrachtbonnen::where("token", $token)->update([
      "mail_geopend" => Carbon::now(),
    ]);

    $bvs = BV::get()->keyBy("id");

    return view("opdrachtbon.ondertekenen",[
        "opdrachtbon" => $opdrachtbon,
        "subdomein" => $subdomein,
        "bvs" => $bvs,
        ]);
  }
  public function ondertekendOpdrachtbon(Request $request){
    try{
    $opdrachtbon = Opdrachtbonnen::where('token', $request->opdrachtbonToken)->with('project', 'termijnen')->firstOrFail();
    $handtekening = $this->storeHandtekening($request->hiddenCanvas);

    Opdrachtbonnen::where("id", $opdrachtbon->id)->update([
      "handtekening" => $handtekening,
      "handtekening_plaats" => $request->signature_plaats ?? null,
      "status" => "Akkoord",
      "beslisdatum" => Carbon::now()
    ]);

    return redirect("opdrachtbonnen/ondertekenen/".$opdrachtbon->token);
    }
    catch (\Exception $e){
      actError($e);
      return redirect("opdrachtbonnen/ondertekenen/".$opdrachtbon->token)->with('warning', 'Er is iets foutgegaan, Opdrachtbon is mogelijk niet ondertekend!');
    }
  }

  private function storeHandtekening($base64){
    $string =str_replace("data:image/png;base64,","", $base64);
    $string = str_replace(' ', '+', $string);
    $data = base64_decode($string);
    $fileName = randomString(7).".png";
    file_put_contents('../../client/public/img/handtekeningen/'.$fileName, $data);

    return $fileName;
  }

  public function kastvakken(){
    return view('uren.projecten.kastvakken');
  }
}
