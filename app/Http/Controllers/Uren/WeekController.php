<?php

namespace App\Http\Controllers\Uren;

use App\ActivityLog;
use DB;
use Route;
use Auth;
use Storage;
use Response;
use App\Exports\ControlelijstExport;
use SoapBox\Formatter\Formatter;
use App\User;
use App\UrenRegistratie;
use App\Machines;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class WeekController extends Controller {

  public function index() {
    $users = User::where(["active" => 1, "extern" => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->orderBy('lastname')->paginate(100);
    foreach ($users as $u) {
    $year = date("Y");
    $years = [];
      for($i = 0; $i <= 5; $i++) {
        if(DB::table('urenregistratie')->whereYear('datum', $year)->where('medewerker_id', $u->id)->where('tijdelijk', 0)->where('goedkeuren', 0)->first()) {
          $years[] = (int)$year;
        }
        $year--;
      }
      $u->years = $years;
      $user[] = $u;
    }
    return view('uren.week.index', ['users' => $users]);
  }

  public function overzicht() {
    $user = User::where('id', Route::current()->parameter('id'))->with('bv')->firstOrFail();
    $year = Route::current()->parameter('year');
    $months = ["", "Januari", "Februari", "Maart", "April", "Mei", "Juni", "Juli", "Augustus", "September", "Oktober", "November", "December"];
    $mutatieregels = DB::table('mutatieregel')->where('medewerkers_id', $user->id)->where('jaar', $year)->get();

    return view('uren.week.overzicht', [
      'user' => $user,
      'year' => $year,
      "months" => $months,
      'mutatieregels' => $mutatieregels
    ]);
  }

  public function overzichtVierWeken($subdomein, $userid, $jaar){
    $user = User::where('id', $userid)->first();
    $uren = UrenRegistratie::where('medewerker_id', $userid)->whereYear('datum', $jaar)->where('tijdelijk', 0)->where('goedkeuren', 0)->get();
    $periode = CarbonPeriod::create(Carbon::parse('01-01-'.$jaar)->firstOfYear(1), '4 weeks', Carbon::parse('01-01-'.$jaar)->lastOfYear(0));
    $periodenummer = 0;
    $weekuren = [];
    foreach($periode as $period){
      $periodenummer ++;
      $endofperiod = Carbon::parse($period)->addWeeks(4)->subDays(1);
      $weekuren[$periodenummer]['mutatieregel'] = DB::table('mutatieregel')->where('medewerkers_id', $user->id)->where('periode', $periodenummer)->first();

      $aantaluren = count($uren->where('datum', '>=', Carbon::parse($period)->format('Y-m-d'))->where('datum', '<', Carbon::parse($endofperiod)->format('Y-m-d'))->groupBy('datum'));
      $aantaldagen = Carbon::parse($period)->diffInDays(Carbon::parse($endofperiod));
      $weekuren[$periodenummer]['allesingevoerd'] = false;
      $weekuren[$periodenummer]['visible'] = true;
      if($aantaluren == $aantaldagen || Carbon::parse($user->start_date != null && $user->start_date != '0000-00-00 00:00:00' ? $user->start_date : $user->created_at)->format('Y-m') == Carbon::parse($period)->format('Y-m')){
        $weekuren[$periodenummer]['allesingevoerd'] = true;
      }
      if(($aantaluren == 0 && Carbon::parse($user->start_date != null && $user->start_date != '0000-00-00 00:00:00' ? $user->start_date : $user->created_at)->format('Y-m') >= Carbon::parse($period)->format('Y-m')) || ($aantaluren == 0 && Carbon::now()->format('Y-m') <= Carbon::parse($period)->format('Y-m'))){
        $weekuren[$periodenummer]['visible'] = false;
      }

      $weekperiode = CarbonPeriod::create($period, '1 week', $endofperiod);
      foreach($weekperiode as $week){
        $urenweek = $uren->where('datum', '>=', Carbon::parse($week)->format('Y-m-d'))->where('datum', '<=', Carbon::parse($week)->addDays(6)->format('Y-m-d'));
        $weekuren[$periodenummer][$week->weekOfYear]['totaal100']           = $urenweek->sum('totaaluren100');
        $weekuren[$periodenummer][$week->weekOfYear]['totaaldag100']        = $urenweek->sum('totaaldaguren100');
        $weekuren[$periodenummer][$week->weekOfYear]['totaal125']           = $urenweek->sum('totaal_overuren125');
        $weekuren[$periodenummer][$week->weekOfYear]['totaal128']           = $urenweek->sum('totaal_overuren128');
        $weekuren[$periodenummer][$week->weekOfYear]['totaal150']           = $urenweek->sum('totaal_overuren150');
        $weekuren[$periodenummer][$week->weekOfYear]['totaal165']           = $urenweek->sum('totaal_overuren165');
        $weekuren[$periodenummer][$week->weekOfYear]['totaal200']           = $urenweek->sum('totaal_overuren200');
        $weekuren[$periodenummer][$week->weekOfYear]['reisuren']            = $urenweek->sum('reisuren');
        $weekuren[$periodenummer][$week->weekOfYear]['kilometers']            = $urenweek->sum('kilometers');
        $weekuren[$periodenummer][$week->weekOfYear]['ziekteuren']          = $urenweek->sum('ziekteuren');
        $weekuren[$periodenummer][$week->weekOfYear]['roostervrij']          = $urenweek->sum('roostervrij');
        $weekuren[$periodenummer][$week->weekOfYear]['verlofuren']          = $urenweek->sum('verlof');
        $weekuren[$periodenummer][$week->weekOfYear]['bijzonderverlofuren'] = $urenweek->sum('bijzonderverlof');
        $weekuren[$periodenummer][$week->weekOfYear]['feesturen']           = $urenweek->sum('feesturen');
        $weekuren[$periodenummer][$week->weekOfYear]['tijdvoortijd']        = $urenweek->sum('tijdvoortijd');
      }
    }
    return view('uren.week.overzichtvierweken', [
      'user' => $user,
      'year' => $jaar,
      'weekuren' => $weekuren
    ]);
  }

  public function berekenOverzicht($maand, $year, $user_id) {
    $user = User::where('id', $user_id)->with('bv', 'standaarduren')->firstOrFail();
    $months = ["", "Januari", "Februari", "Maart", "April", "Mei", "Juni", "Juli", "Augustus", "September", "Oktober", "November", "December"];
    $week = 0;
    $totaaluren100 = 0;
    $totaaldaguren100 = 0;
    $totaalOveruren125 = 0;
    $totaalOveruren128 = 0;
    $totaalOveruren150 = 0;
    $totaalOveruren165 = 0;
    $totaalOveruren200 = 0;
    $reisuren = 0;
    $kilometers = 0;
    $heenreis_woonwerk = 0;
    $terugreis_woonwerk = 0;
    $heenreis_huisproject = 0;
    $terugreis_huisproject = 0;
    $ziekteuren = 0;
    $roostervrij = 0;
    $verlofuren = 0;
    $bijzonderverlofuren = 0;
    $feesturen = 0;
    $atv = 0;
    $tijdvoortijd = 0;
    $overnachtingen = 0;
    $totaalminuren = 0;
    $empty = false;
    $weekoverzicht = [];
    $allesingevoerd = false;

    $uren = UrenRegistratie::where('medewerker_id', $user->id)->whereMonth('datum', $maand)->whereYear('datum', $year)->where('tijdelijk', 0)->where('goedkeuren', 0)->select(DB::RAW("
      MONTH(datum) as month,
      WEEKOFYEAR(datum) as week,
      SUM(totaal_ladenlossen) as totaal_ladenlossen,
      datum,
      SUM(totaaluren100) AS totaaluren100,
      SUM(totaaldaguren100) AS totaaldaguren100,
      SUM(totaal_overuren125) as totaal_overuren125,
      SUM(totaal_overuren128) as totaal_overuren128,
      SUM(totaal_overuren150) as totaal_overuren150,
      SUM(totaal_overuren165) as totaal_overuren165,
      SUM(totaal_overuren200) as totaal_overuren200,
      SUM(reisuren) as reisuren,
      SUM(kilometers) as kilometers,
      SUM(heenreis_woonwerk) as heenreis_woonwerk,
      SUM(terugreis_woonwerk) as terugreis_woonwerk,
      SUM(heenreis_huisproject) as heenreis_huisproject,
      SUM(terugreis_huisproject) as terugreis_huisproject,
      SUM(ziekteuren) as ziekteuren,
      SUM(tijdvoortijd) as tijdvoortijd,
      SUM(roostervrij) as roostervrij,
      SUM(verlof) as verlofuren,
      SUM(bijzonderverlof) as bijzonderverlofuren,
      SUM(feesturen) as feesturen,
      SUM(atv_uren) as atv_uren,
      SUM(minuren) as minuren,
      overnachting
    ", [':user_id' => $user->id]))->groupBy('datum')->orderBy('datum', 'ASC')->get();

    $aantal = UrenRegistratie::where('medewerker_id', $user->id)->whereMonth('datum', $maand)->whereYear('datum', $year)->where('tijdelijk', 0)->where('goedkeuren', 0)->select(DB::RAW("DISTINCT(datum) as datum"))->get();
    $dateObj   = \DateTime::createFromFormat('!m', $maand);
    $monthName = $dateObj->format('F'); // March
    $aantalDagen = new Carbon("last day of " . $monthName . " " . $year);
    if ($aantalDagen->format('d') <= count($aantal)) {
      $allesingevoerd = true;
    }

    foreach($uren as $index => $weekrow) {
      if(!empty($weekrow)) {
          if ($week == $weekrow->week) {
            $week = $weekrow->week;
            $maand = $months[$weekrow->month];
            $totaaluren100 += $weekrow->totaaluren100;
            $totaaldaguren100 = $totaaldaguren100 + $weekrow->totaaldaguren100;
            $totaalOveruren125 = $totaalOveruren125 + $weekrow->totaal_overuren125;
            $totaalOveruren128 = $totaalOveruren128 + $weekrow->totaal_overuren128;
            $totaalOveruren150 = $totaalOveruren150 + $weekrow->totaal_overuren150;
            $totaalOveruren165 = $totaalOveruren165 + $weekrow->totaal_overuren165;
            $totaalOveruren200 = $totaalOveruren200 + $weekrow->totaal_overuren200;
            $reisuren = $reisuren + $weekrow->reisuren;
            $kilometers = $kilometers + $weekrow->kilometers;
            $heenreis_woonwerk = $heenreis_woonwerk + $weekrow->heenreis_woonwerk;
            $terugreis_woonwerk = $terugreis_woonwerk + $weekrow->terugreis_woonwerk;
            $heenreis_huisproject = $heenreis_huisproject + $weekrow->heenreis_huisproject;
            $terugreis_huisproject = $terugreis_huisproject + $weekrow->terugreis_huisproject;
            $ziekteuren = $ziekteuren + $weekrow->ziekteuren;
            $tijdvoortijd = $tijdvoortijd + $weekrow->tijdvoortijd;
            $roostervrij = $roostervrij + $weekrow->roostervrij;
            $verlofuren = $verlofuren + $weekrow->verlofuren;
            $bijzonderverlofuren = $bijzonderverlofuren + $weekrow->bijzonderverlofuren;
            $feesturen = $feesturen + $weekrow->feesturen;
            $atv = $atv + $weekrow->atv_uren;
            if ($weekrow->overnachting != null) {$overnachting = 1;} else {$overnachting = 0;}
            $overnachtingen = $overnachtingen + $overnachting;
            $totaalminuren = $totaalminuren + $weekrow->minuren;
          } else {
            // WEEK WEGSCHRIJVEN, LEGEN EN NIEUW BEGINNEN
            if ($week != 0) {
              $weekoverzicht[] = [
                'jaar'  => $year,
                'maand' => $maand,
                'weeknummer' => $week,
                'totaaluren100' => $totaaluren100,
                'totaaldaguren100' => $totaaldaguren100,
                'totaaloveruren125' => $totaalOveruren125,
                'totaaloveruren128' => $totaalOveruren128,
                'totaaloveruren150' => $totaalOveruren150,
                'totaaloveruren165' => $totaalOveruren165,
                'totaaloveruren200' => $totaalOveruren200,
                'reisuren' => $reisuren,
                'kilometers' => $kilometers,
                'heenreis_woonwerk' => $heenreis_woonwerk,
                'terugreis_woonwerk' => $terugreis_woonwerk,
                'heenreis_huisproject' => $heenreis_huisproject,
                'terugreis_huisproject' => $terugreis_huisproject,
                'roostervrij'    => $roostervrij,
                'verlofuren'    => $verlofuren,
                'bijzonderverlofuren'    => $bijzonderverlofuren,
                'feesturen'     => $feesturen,
                'atv_uren'     => $atv,
                'tijdvoortijd'    => $tijdvoortijd,
                'ziekteuren'    => $ziekteuren,
                'overnachtingen' => $overnachtingen,
                'totaalminuren' => $totaalminuren,
                'allesingevoerd' => $allesingevoerd
              ];

              $totaaluren100 = 0;
              $totaaldaguren100 = 0;
              $totaalOveruren125 = 0;
              $totaalOveruren128 = 0;
              $totaalOveruren150 = 0;
              $totaalOveruren165 = 0;
              $totaalOveruren200 = 0;
              $reisuren = 0;
              $kilometers = 0;
              $heenreis_woonwerk = 0;
              $terugreis_woonwerk = 0;
              $heenreis_huisproject = 0;
              $terugreis_huisproject = 0;
              $ziekteuren = 0;
              $tijdvoortijd = 0;
              $roostervrij = 0;
              $verlofuren = 0;
              $bijzonderverlofuren = 0;
              $feesturen = 0;
              $atv = 0;
              $overnachtingen = 0;
              $totaalminuren = 0;
            }
            $week = $weekrow->week;
            $maand = $months[$weekrow->month];
            $totaaluren100 = $totaaluren100 + $weekrow->totaaluren100;
            $totaaldaguren100 = $totaaldaguren100 + $weekrow->totaaldaguren100;
            $totaalOveruren125 = $totaalOveruren125 + $weekrow->totaal_overuren125;
            $totaalOveruren128 = $totaalOveruren128 + $weekrow->totaal_overuren128;
            $totaalOveruren150 = $totaalOveruren150 + $weekrow->totaal_overuren150;
            $totaalOveruren165 = $totaalOveruren165 + $weekrow->totaal_overuren165;
            $totaalOveruren200 = $totaalOveruren200 + $weekrow->totaal_overuren200;
            $reisuren = $reisuren + $weekrow->reisuren;
            $kilometers = $kilometers + $weekrow->kilometers;
            $heenreis_woonwerk = $heenreis_woonwerk + $weekrow->heenreis_woonwerk;
            $terugreis_woonwerk = $terugreis_woonwerk + $weekrow->terugreis_woonwerk;
            $heenreis_huisproject = $heenreis_huisproject + $weekrow->heenreis_huisproject;
            $terugreis_huisproject = $terugreis_huisproject + $weekrow->terugreis_huisproject;
            $ziekteuren = $ziekteuren + $weekrow->ziekteuren;
            $tijdvoortijd = $tijdvoortijd + $weekrow->tijdvoortijd;
            $roostervrij = $roostervrij + $weekrow->roostervrij;
            $verlofuren = $verlofuren + $weekrow->verlofuren;
            $bijzonderverlofuren = $bijzonderverlofuren + $weekrow->bijzonderverlofuren;
            $feesturen = $feesturen + $weekrow->feesturen;
            $atv = $atv + $weekrow->atv_uren;
            if ($weekrow->overnachting != null) {$overnachting = 1;} else {$overnachting = 0;}
            $overnachtingen = $overnachtingen + $overnachting;
            $totaalminuren = $totaalminuren + $weekrow->minuren;
          }
      }
      $maand = $months[$weekrow->month];
    }

    // Laatste week wegschrijven
    $weekoverzicht[] = [
      'jaar'  => $year,
      'maand' => $maand,
      'weeknummer' => $week,
      'totaaluren100'    => $totaaluren100,
      'totaaldaguren100' => $totaaldaguren100,
      'totaaloveruren125' => $totaalOveruren125,
      'totaaloveruren128' => $totaalOveruren128,
      'totaaloveruren150' => $totaalOveruren150,
      'totaaloveruren165' => $totaalOveruren165,
      'totaaloveruren200' => $totaalOveruren200,
      'reisuren' => $reisuren,
      'kilometers' => $kilometers,
      'heenreis_woonwerk' => $heenreis_woonwerk,
      'terugreis_woonwerk' => $terugreis_woonwerk,
      'heenreis_huisproject' => $heenreis_huisproject,
      'terugreis_huisproject' => $terugreis_huisproject,
      'roostervrij'    => $roostervrij,
      'verlofuren'    => $verlofuren,
      'bijzonderverlofuren'    => $bijzonderverlofuren,
      'feesturen'     => $feesturen,
      'atv_uren'     => $atv,
      'ziekteuren'    => $ziekteuren,
      'tijdvoortijd'   => $tijdvoortijd,
      'overnachtingen' => $overnachtingen,
      'allesingevoerd' => $allesingevoerd,
      'totaalminuren' => $totaalminuren
    ];
    return $weekoverzicht;
  }

  public function weekoverzicht() {
    $week = Route::current()->parameter('week');
    $year = Route::current()->parameter('year');
    $user = User::where('id', Route::current()->parameter('id'))->firstOrFail();

    $urengetal = false;
    foreach ($user->permissions() as $permission) {
      if ($permission->permission == "uren o.b.v. getal") {
        $urengetal = true;
      }
    }

    $redenen = DB::table('verlofredenen')->get();
    $naca = DB::table('naca_codes')->get();

    $weken = UrenRegistratie::where(DB::raw("WEEKOFYEAR(datum)"), $week)->where('medewerker_id', $user->id)->where(DB::raw("YEAR(datum)"), $year)->where('tijdelijk', 0)->where('goedkeuren', 0)->with('kenteken', 'verlofRtl', 'naca', 'project')->orderBy('datum', 'ASC')->get();
    return view('uren.week.weekoverzicht', ['user' => $user, 'weken' => $weken, 'year' => $year, 'urengetal' => $urengetal, 'redenen' => $redenen, 'naca' => $naca]);
  }

  public function wekenoverzicht() {

	  $weekoverzichten = DB::table('urenregistratie')
		  ->where('tijdelijk', 0)->where('goedkeuren', 0)
		  ->select('datum', DB::raw('WEEK(datum, 1) as weeknummer'), DB::raw('YEAR(datum) as jaar'))
		  ->groupBy(DB::raw('WEEK(datum, 1)'), DB::raw('YEAR(datum)'))
		  ->orderBy('datum', 'desc')
		  ->paginate(53);

    return view('uren.week.controlelijst', ['weekoverzichten' => $weekoverzichten]);
  }

  public function controlelijst() {

    $jaar = Route::current()->parameter('jaar');
    $weeknummer = Route::current()->parameter('weeknummer');
    $url = $_SERVER['HTTP_HOST'];
    $uren = UrenRegistratie::where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->where(["users.active" => 1, "users.extern" => 0])->where(function ($query) {$query->where('users.end_date', '>=', Carbon::now())->orWhere('users.end_date', null);})->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->with('project', 'user')->get();
    $controlemachines = UrenRegistratie::where('tijdelijk', 0)->where('goedkeuren', 0)->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->with('machineuren', 'user')->orderBy(DB::raw('datum'), 'asc')->orderBy(DB::raw('begintijd'), 'asc')->get();
    $controlelijsten = [];
    foreach($uren as $uur){
      $controlelijsten[$uur->medewerker_id][] = $uur;
    }
    $machinearr = [];
    foreach($controlemachines as $row){
      foreach($row->machineuren as $uur){
        $machinearr[] = $uur->machine_id;
      }
    }
    $machines = Machines::where('active', 1)->whereIn('id', $machinearr)->get();
    // $urengetal = false;
    // foreach (Auth::user()->permissions() as $permission) {
    //   if ($permission->permission == "uren o.b.v. getal") {
    //     $urengetal = true;
    //   }
    // }
    return view('uren.week.controlelijstweek', [
      'controlelijsten' => $controlelijsten,
      'jaar' => $jaar,
      'weeknummer' => $weeknummer,
      'url' => $url,
      'machines' => $machines,
      'controlemachines' => $controlemachines,
    ]);
  }

  public function exportcontrolelijst() {
    //activity()->log(Auth::user()->name . ' ('.Auth::user()->id.') heeft de controlelijst geëxporteerd.');

    $jaar = Route::current()->parameter('jaar');
    $weeknummer = Route::current()->parameter('weeknummer');
    $jobtype = Route::current()->parameter('jobtype');

    if ($jobtype == 'kantoor') {
      $controlelijsten = UrenRegistratie::with('user')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->where('users.jobtype', 'kantoor')->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    } else if ($jobtype == 'buitendienst') {
      $controlelijsten = UrenRegistratie::with('user')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->where('users.jobtype', 'buitendienst')->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    } else {
      $controlelijsten = UrenRegistratie::with('user')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    }
    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de controlelijst als xlsx gedownload.";
    ActivityLog::insert([
      "client_id" => getClientId(),
      "user_id" => Auth::user()->id,
      "module_id" => 2,
      "log" => $log,
      "created_at" => Carbon::now(),
    ]);
    return \Excel::download(new ControlelijstExport($controlelijsten), 'Controlelijst'.$jaar.'-'.$weeknummer.'.xlsx');
  }

  public function xmlexportcontrolelijst() {
    $jaar = Route::current()->parameter('jaar');
    $weeknummer = Route::current()->parameter('weeknummer');
    $jobtype = Route::current()->parameter('jobtype');

    if ($jobtype == 'kantoor') {
      $controlelijsten = DB::table('urenregistratie')->where(
        function ($query) {$query->where("gewerkte_uren", "!=", "0")->orWhere("totaaluren100", "!=", "0");}
      )->whereNull('verlofreden_id')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->where('users.jobtype', 'kantoor')->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    }
    else if ($jobtype == 'buitendienst') {
      $controlelijsten = DB::table('urenregistratie')->where(
        function ($query) {$query->where("gewerkte_uren", "!=", "0")->orWhere("totaaluren100", "!=", "0");}
      )->whereNull('verlofreden_id')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->where('users.jobtype', 'buitendienst')->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    }
    else {
      $controlelijsten = DB::table('urenregistratie')->where(
        function ($query) {$query->where("gewerkte_uren", "!=", "0")->orWhere("totaaluren100", "!=", "0");}
      )->whereNull('verlofreden_id')->where('urenregistratie.tijdelijk', 0)->leftJoin('users', 'urenregistratie.medewerker_id', '=', 'users.id')->leftJoin('verlofredenen', 'urenregistratie.verlofreden_id', '=', 'verlofredenen.id')->leftJoin('naca_codes', 'urenregistratie.naca_id', '=', 'naca_codes.id')->where(DB::raw('YEAR(datum)'), $jaar)->where(DB::raw('WEEKOFYEAR(datum)'), $weeknummer)->orderBy('users.lastname', 'asc')->orderBy('users.name', 'asc')->orderBy(DB::raw('datum'), 'asc')->get();
    }

    $wrapper = getSettingValue("controlelijst-export-naam");
    $regels = getSettingValue("controlelijst-export-regels");
    $layout = json_decode(getSettingValue("controlelijst-export-layout"), true);

    $array = [];
    foreach($controlelijsten as $row){
      $arr = [];
      foreach($layout as $key => $value){
        if(str_contains($key, "custom")){
          $arr[$value[0]] = $value[1];
        }
        elseif (str_contains($key, ',')) {
          $val = "";
          $keys = explode(',', $key);
          foreach($keys as $i => $k) {
            if($i == count($keys)-1){
              $val = $val . ((array)$row)[$k];
            }
            else{
              $val = $val . ((array)$row)[$k] . " ";
            }
          }
          $arr[$value] = $val;
        }
        else {
          $arr[$value] = ((array)$row)[$key] ?? null;
        }
      }

      $arr['@name'] = $regels;
      $array[] = $arr;
    }

    $log = User::name()." heeft de controlelijst als xml gedownload.";
    actLog($log, User::id(), 2);


    $xml = Formatter::make($array, Formatter::ARR)->toXml($wrapper);
    $file = "temp.xml";
    file_put_contents($file, $xml);

//    return response($xml, 200)->header('Content-Type', 'application/xml');
    return Response::download($file, $wrapper . ".xml", ["Content-type:" => "application/xhtml+xml"]);
  }

}
