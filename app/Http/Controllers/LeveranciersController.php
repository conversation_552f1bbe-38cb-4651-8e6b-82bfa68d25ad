<?php

namespace App\Http\Controllers;

use App\ExplorerFiles;
use App\Klanten;
use App\Leveranciers;
use App\LeveranciersBanknummers;
use App\LeveranciersContactpersonen;
use App\LeveranciersDocumenten;
use App\LeveranciersLocaties;
use App\LeveranciersWka;
use App\User;
use App\Vestigingen;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen;

class LeveranciersController extends Controller
{
    private $leveranciersRelations = ['contactpersonen', 'locaties', 'banknrs', 'inkoopfacturen', 'inkoopbonnen', 'wka', 'documenten'];

    public function index(){
      $leveranciers = Leveranciers::where('active', 1)->with($this->leveranciersRelations)->get();
      return view('leveranciers.index', [
        'leveranciers' => $leveranciers
      ]);
    }
    public function tarieven(){
      $leveranciers = Leveranciers::where('active', 1)->orderBy('naam', 'ASC')->get();
      return view('leveranciers.tarieven', [
        'leveranciers' => $leveranciers,
      ]);
    }

    public function create(){
      $vestigingen = Vestigingen::where("active", 1)->orderBy("naam", "ASC")->get();

      return view('leveranciers.create', [
        'vestigingen' => $vestigingen,
      ]);
    }
    public function store(Request $request){
      try{
        $crediteurnummer = $request->crediteurnummer ?? Leveranciers::crediteurnummer();

        $id = Leveranciers::insertGetId([
          'bv' => $request->bv ?? firstBv(),
          "vestiging_id" => $request->vestiging,
          "naam" => $request->bedrijfsnaam,
          "email" => $request->email,
          "website" => $request->website,
          "telefoonnummer" => $request->telefoonnummer,
          "faxnummer" => $request->faxnummer,
          "kvk" => $request->kvk,
          "btw_nr" => $request->btwnummer,
          "btw_verlegd" => $request->btw_verlegd,
          "crediteurnummer" => $crediteurnummer,
          "located_in_nl" => $request->located_in_nl,

          "contactpersoon_titel" => $request->contactpersoon_titel,
          "contactpersoon_voornaam" => $request->contactpersoon_voornaam,
          "contactpersoon_achternaam" => $request->contactpersoon_achternaam,
          "contactpersoon_functie" => $request->contactpersoon_functie,
          "contactpersoon_email" => $request->contactpersoon_email,
          "contactpersoon_telefoon" => $request->contactpersoon_telefoon,
          "contactpersoon_mobiel" => $request->contactpersoon_mobiel,

          'straat' => $request->straat,
          'huisnummer' => $request->huisnummer,
          'toevoeging' => $request->toevoeging,
          'postcode' => $request->postcode,
          'plaats' => $request->plaats,
          'land' => $request->land,

          'postadres_straat' => $request->postadres_straat,
          'postadres_huisnummer' => $request->postadres_huisnummer,
          'postadres_toevoeging' => $request->postadres_toevoeging,
          'postadres_postcode' => $request->postadres_postcode,
          'postadres_plaats' => $request->postadres_plaats,
          'postadres_land' => $request->postadres_land,

          'exact_globe_gl' => $_POST['exact_gl'] ?? null,

	        'business_central_tax_area_code' => $request->bc_tax_area ?? null,

	        "created_at" => Carbon::now(),
        ]);
        $leverancier = Leveranciers::where('id', $id)->first();

        $this->storeLocaties($request, $id);
        $this->storeContactpersonen($request, $id);
        $this->storeBanknummers($request, $id);

          $this->storeDocumenten($request, $leverancier->id, $request->documenten);

        $log = User::name().' heeft de leverancier '.$leverancier->titel().' toegevoegd.';
        actLog($log, User::id(), 43);

        return redirect('leveranciers?active=1')->with('status', 'Leverancier opgeslagen!');
      }
      catch (\Exception $e){
        actError($e);
        return redirect('leveranciers?active=1')->with('warning', 'Er is iets foutgegaan!');
      }
    }
    public function edit($sub, $id){
      $data = $this->create()->getData();

      $data['leverancier'] = Leveranciers::where('id', $id)->with($this->leveranciersRelations)->firstOrFail();

      return view('leveranciers.create', $data);
    }
    public function update(Request $request){
      try{
        $leverancier = Leveranciers::where('id', $request->leverancier)->firstOrFail();
        Leveranciers::where('id', $leverancier->id)->update([
          'bv' => $request->bv ?? firstBv(),
          "vestiging_id" => $request->vestiging,
          "naam" => $request->bedrijfsnaam,
          "email" => $request->email,
          "website" => $request->website,
          "telefoonnummer" => $request->telefoonnummer,
          "faxnummer" => $request->faxnummer,
          "kvk" => $request->kvk,
          "btw_nr" => $request->btwnummer,
          "btw_verlegd" => $request->btw_verlegd,
          "crediteurnummer" => $request->crediteurnummer,
          "located_in_nl" => $request->located_in_nl,

          "contactpersoon_titel" => $request->contactpersoon_titel,
          "contactpersoon_voornaam" => $request->contactpersoon_voornaam,
          "contactpersoon_achternaam" => $request->contactpersoon_achternaam,
          "contactpersoon_functie" => $request->contactpersoon_functie,
          "contactpersoon_email" => $request->contactpersoon_email,
          "contactpersoon_telefoon" => $request->contactpersoon_telefoon,
          "contactpersoon_mobiel" => $request->contactpersoon_mobiel,

          'straat' => $request->straat,
          'huisnummer' => $request->huisnummer,
          'toevoeging' => $request->toevoeging,
          'postcode' => $request->postcode,
          'plaats' => $request->plaats,
          'land' => $request->land,

          'postadres_straat' => $request->postadres_straat,
          'postadres_huisnummer' => $request->postadres_huisnummer,
          'postadres_toevoeging' => $request->postadres_toevoeging,
          'postadres_postcode' => $request->postadres_postcode,
          'postadres_plaats' => $request->postadres_plaats,
          'postadres_land' => $request->postadres_land,

          'exact_globe_gl' => $_POST['exact_gl'] ?? null,
          'exact_globe_update_required' => 1,

					'business_central_tax_area_code' => $request->bc_tax_area ?? null,

          "created_at" => Carbon::now(),
        ]);
        $leverancier->refresh();

        $this->storeLocaties($request, $leverancier->id);
        $this->storeContactpersonen($request, $leverancier->id);
        $this->storeBanknummers($request, $leverancier->id);

          $this->storeDocumenten($request, $leverancier->id, $request->documenten);

        $log = User::name().' heeft de leverancier '.$leverancier->titel().' gewijzigd.';
        actLog($log, User::id(), 43);

        return redirect('leveranciers?active=1')->with('status', 'Leverancier opgeslagen!');
      }
      catch (\Exception $e){
        actError($e);
        return redirect('warning')->with('status', 'Er is iets foutgegaan!');
      }
    }

    private function storeContactpersonen($request, $id){
      try{
        LeveranciersContactpersonen::where("leverancier_id", $id)->update(["active" => 0]);

        foreach ($request->contactpersonen ?? [] as $index => $contactId){
          $data = [
            "leverancier_id" => $id,
            "titel" => $request->contactpersoon_titel_arr[$index] ?? null,
            "voornaam" => $request->contactpersoon_voornaam_arr[$index] ?? null,
            "achternaam" => $request->contactpersoon_achternaam_arr[$index] ?? null,
            "functie" => $request->contactpersoon_functie_arr[$index] ?? null,
            "email" => $request->contactpersoon_email_arr[$index] ?? null,
            "telefoon" => $request->contactpersoon_telefoon_arr[$index] ?? null,
            "mobiel" => $request->contactpersoon_mobiel_arr[$index] ?? null,
            "active" => 1,
          ];

          if(!isset($contactId)){
            LeveranciersContactpersonen::insert($data);
            continue;
          }

          $data["updated_at"] = Carbon::now();
          LeveranciersContactpersonen::where("id", $contactId)->update($data);
        }
      }
      catch (\Exception $e){actError($e);}

    }
    private function storeLocaties($request, $id){
      try{
        LeveranciersLocaties::where("leverancier_id", $id)->update(["active" => 0]);

        foreach ($request->locaties ?? [] as $index => $locatieId){
          $data = [
            "leverancier_id" => $id,
            "postcode" => $request->locaties_postcode[$index] ?? null,
            "huisnummer" => $request->locaties_huisnummer[$index] ?? null,
            "toevoeging" => $request->locaties_toevoeging[$index] ?? null,
            "straat" => $request->locaties_straat[$index] ?? null,
            "plaats" => $request->locaties_plaats[$index] ?? null,
            "land" => $request->locaties_land[$index] ?? null,
            "active" => 1,
          ];

          if(!isset($locatieId)){
            LeveranciersLocaties::insert($data);
            continue;
          }

          $data["updated_at"] = Carbon::now();
          LeveranciersLocaties::where("id", $locatieId)->update($data);
        }
      }
      catch (\Exception $e){actError($e);}
    }
    private function storeBanknummers($request, $id){
      try{
        LeveranciersBanknummers::where("leverancier_id", $id)->update(["active" => 0]);

        foreach ($request->ibans ?? [] as $index => $ibanId){
          $data = [
            "leverancier_id" => $id,
            "iban" => $request->banknr_iban[$index] ?? null,
            "bic" => $request->banknr_bic[$index] ?? null,
            "g_rekening" => isset($request->banknr_g_rekening[$index]) ? 1 : 0,
            "g_rekening_percentage" => $request->g_rekening_percentage[$index] ?? null,
            "active" => 1,
          ];

          if(!isset($ibanId)){
            LeveranciersBanknummers::insert($data);
            continue;
          }

          $data["updated_at"] = Carbon::now();
          LeveranciersBanknummers::where("id", $ibanId)->update($data);
        }
      }
      catch (\Exception $e){actError($e);}
    }

    private function storeDocumenten($request, $id, $data)
    {
        try {
            foreach ($data ?? [] as $type => $keywords) {
                ExplorerFiles::where('path', "/Leveranciers/{$id}/{$type}")->update(['active' => 0]);

                foreach ($keywords ?? [] as $index => $keyword) {
                    $inputKey = "documenten.$type.$index";

                    if ($request->hasFile($inputKey)) {
                        $file = $request->file($inputKey);
                        $filename = randomString() . '.' . $file->getClientOriginalExtension();

                        ExplorerFiles::insertFromRequest([
                            'file' => $file,
                            'name' => ucfirst(str_replace('_', ' ', $index)),
                            'path' => "/Leveranciers/{$id}/{$type}",
                            'description' => null,
                        ]);

                        LeveranciersDocumenten::updateOrInsert([
                            'leverancier_id' => $id,
                            'type' => $type,
                            'keyword' => $index,
                        ], [
                            'bestand' => $filename,
                            'verloopdatum' => $request->get("{$index}_verloop") ?? now()->format('Y-m-d'),
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            actError($e);
        }
    }


    public function windowShow($sub, $id){
      $data = $this->edit($sub, $id)->getData();

      $data['preview'] = true;

      return view('leveranciers.create', $data);
    }

}
