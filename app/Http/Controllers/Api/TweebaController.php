<?php

namespace App\Http\Controllers\Api;

use App\Classes\TweebaApi;
use App\Imports\TweeBaKortingsgroepen;
use App\Leveranciers;
use App\Settings;
use App\TweeBa\Kortingsgroepen;
use App\TweeBa\Metadata;
use Carbon\Carbon;
use Dompdf\Exception;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Http\Controllers\Controller;

class TweebaController extends Controller{

    public function connect(Request $request){
        try{
            $credentials = [
                'username' => tes_encrypt($request->username),
                'password' => tes_encrypt($request->password),
                'client_id' => tes_encrypt($request->client_id),
                'client_password' => tes_encrypt($request->client_password),
            ];
            Settings::updateOrInsert(["naam" => "2ba_credentials"], ["value" => json_encode($credentials)]);

            $connected = tweeba()->connect($credentials);

            if(!$connected){
                Settings::where('naam', '2ba_credentials')->delete();
                return response(['message' => 'Ongeldige credentials'], 501);
            }

            return response(null, 201);

        }catch(\Exception $e){
            return catchResponse($e);
        }

    }

    //Products
    public function productsSearch(Request $request)
    {
      try {
        $api = tweeba();
        $response = $api->productsSearch($request->search);
        $this->handleResponse($response);

        return response([
          'products' => $response->data->Products ?? []
        ], 200);
      } catch (\Exception $e) {
        return catchResponse($e);
      }
    }

    //Supliers
    public function suppliersSearch(Request $request){
        try{
            $api = tweeba();
            $response = $api->suppliersSearch($request->search);
            $this->handleResponse($response);

            if(isset($request->exlude_existing)){
                $filtered = array_filter($response->data, function($supplier){
                    return !(Metadata::where([
                        'instance' => 'leveranciers',
                        'meta_key' => 'GLN',
                        'meta_value' => $supplier->GLN
                    ])->exists());
                });

                $response->data = resetIndex($filtered);
            }

            return response([
                'suppliers' => $response->data
            ], 200);
        }
        catch(\Exception $e){ return catchResponse($e); }
    }
    public function suppliersImport(Request $request){
        try{
            $api = tweeba();

            $response = $api->suppliersGet($request->GLN);
            $this->handleResponse($response);

            $supplier = $response->data;
            if(!$supplier){ abort(417); }


            $leverancier_id = Leveranciers::insertGetId([
                'bv' => getBv()->id,
                'naam' => $supplier->Name,
                'crediteurnummer' => Leveranciers::crediteurnummer(),

                'straat' => $supplier->CompanyVisitAddress,
                'plaats' => $supplier->CompanyVisitCity,
                'postcode' => $supplier->CompanyVisitPostalCode,
                'land' => $supplier->CompanyVisitCountryCode,

                'postadres_straat' => $supplier->CompanyVisitAddress,
                'postadres_plaats' => $supplier->CompanyVisitCity,
                'postadres_postcode' => $supplier->CompanyVisitPostalCode,
                'postadres_land' => $supplier->CompanyVisitCountryCode,
            ]);
            Metadata::insert([
                'instance' => 'leveranciers',
                'instance_id' => $leverancier_id,
                'meta_key' => 'GLN',
                'meta_value' => $supplier->GLN
            ]);

            return response(null, 201);
        }
        catch(Exception $e){ return catchResponse($e); }
    }

    //Trade Items
    public function tradeItemsForProduct(Request $request){
        try{
            $api = tweeba();
            $response = $api->tradeItemsForProduct($request->trade_item);
            $this->handleResponse($response);

            $trade_items = $response->data;
            foreach($trade_items as $trade_item){
                if(isset($request->include_net_price)){
                    $price_response = $api->tradeItemsNetPrice([
                        'id' => $trade_item->Id
                    ]);
                    $this->handleResponse($price_response);

                    $trade_item->_net_price = $price_response->data;
                }
                if(isset($request->include_stock_availability)){
                    $stock_response = $api->stockAvailability([
                        'GLN' => $trade_item->SupplierGLN,
                        'tradeItemId' => $trade_item->TradeItemId,
                    ]);
                    $this->handleResponse($stock_response);

                    $trade_item->_stock = $stock_response->data;
                }
                if(isset($request->include_details)){
                    $details_response = $api->tradeitemDetails([
                      'GLN' => $trade_item->SupplierGLN,
                      'tradeItemId' => $trade_item->TradeItemId,
                    ]);
                    $this->handleResponse($details_response);
                    $trade_item->details = $details_response->data;
                }
            }

            return response([
                'trade_items' => $trade_items,
            ], 200);
        }
        catch(\Exception $e){ return catchResponse($e); }

    }

    //Utility
    private function handleResponse($response){
        if($response->status){
            return;
        }
        abort($response->code, $response->error);
    }


}
