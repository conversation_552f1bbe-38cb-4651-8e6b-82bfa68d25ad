<?php

namespace App\Http\Controllers\Api;
use App\Abonnementen;
use App\AbonnementenFormulieren;
use App\AbonnementenFormulierKortingSelect;
use App\AbonnementenItems;
use App\AbonnementenItemsUitgaves;
use App\AbonnementenVerenigingen;
use App\AbonnementFacturen;
use App\AbonnementItems;
use App\BV;
use App\Facturen;
use App\FacturenAdres;
use App\Klanten;
use App\KlantenContactpersonen;
use App\Mail\abonnementCredentials;
use App\Mail\BlankMail;
use Auth;
use Carbon\Carbon;
use Config;
use http\Env\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Route;
use App\Http\Controllers\Controller;
use MikeMcLin\WpPassword\Facades\WpPassword;

class AbonnementenController extends Controller {

  public function getImportedToSend(){
	  $abonnementen = Abonnementen::where(['imported' => 1])->whereNull('wachtwoord')->whereHas('items', function($query) {
		  $query->where('form_type', 'digitaal');
	  })->get();
    return response(['abonnementen' => $abonnementen], 200);
  }
  public function sendCredentials(Request $request){
    try{
      $abonnement = Abonnementen::where('id', $request->abonnement)->with('items', '_bv', 'facturen')->firstOrFail();

      $password = randomString();
      $hash = WpPassword::make($password);

      Abonnementen::where(['id' => $abonnement->id])->update(['wachtwoord' => $hash]);
      $abonnement->refresh();

      Mail::to($abonnement->email)->send(new abonnementCredentials(getSubdomein(), $abonnement, $password));

      $abonnement->toegang(['hash' => $abonnement->wachtwoord]);
      return response(null, 200);
    }
    catch (\Exception $e){
      actError($e);
      return response(['message' => $e->getMessage()], 500);
    }
  }

  public function notifySubscribers(Request $request){
    $abonnementen = AbonnementenItems::where("id", $request->itemId)->with("abonnementen.facturen", "abonnementen.klant")->first()->abonnementen;
    if (!$abonnementen) { return response(['message' => "abonnement niet gevonden"], 404); }
    $subscriberMails = [];

    foreach ($abonnementen as $abonnement){
      if ($abonnement->hasActiveSubscription($request->itemId)){
        $subscriberMails[] = $abonnement->klant->email;
      }
    }
    $subscriberMails = array_unique($subscriberMails);

    $bv = BV::where('id', $abonnementen[0]->bv)->first();
    $sender = getSettingValue("abonnement_email_afzender") ?? '<EMAIL>';

    foreach ($subscriberMails as $email){
      Mail::to($email)->send(new BlankMail(
        $bv,
        $sender,
        $bv->name,
        $request->fields['subject'],
        $request->fields['mess'],
      ));
    }
    return response($request, 200);
  }

  public function update(Request $request){
    AbonnementenFormulieren::where('id', $request->id)->update([
      'pay_factuur' => $request->factuur,
      'pay_ideal' => $request->ideal,
      'discount' => $request->discount,
      'factuur_prefix' => $request->prefix,
      'css' => $request->css,
      'discount_layout_priority' => $request->discountLayoutPriority,
      'subscription_type_layout' => $request->subscriptionType,
      'header_visible' => $request->headerVisibility,
      'exact_amount_buttons' => $request->exactAmountButtons,
      'submit_button_text' => $request->submitButtonText
    ]);

    AbonnementenFormulierKortingSelect::where('formulier_id', $request->id)->delete();
    foreach ($request->korting_select ?? [] as $korting_select_id){
      AbonnementenFormulierKortingSelect::insert([
        'formulier_id' => $request->id,
        'select_id' => $korting_select_id,
      ]);
    }

    return response(null, 200);
  }

  public function checkItemUitgave($subdomein, $addUitgavesToCheck = 0){
    return response(["items" => \App\Http\Controllers\AbonnementenController::checkItemUitgave($addUitgavesToCheck)], 200);
  }

  public function uitgaveReminderCronjob(){
    $sender = getSettingValue("abonnement_email_afzender") ?? '<EMAIL>';
    $clientMail = getSettingValue("abonnement_uitgave_reminder_email");
    $bv = getBv();
    if ($clientMail == null || trim($clientMail) == "") { return; }
    $items = \App\Http\Controllers\AbonnementenController::checkItemUitgave(1);
    if ($items == []) { return; }
    $message = "Beste " . getClient()->name . ",<br>" . "Sommige abonnementen hebben te weinig uitgaves. Het wordt aangeraden om deze aan te vullen.<br><br>Met vriendelijke groet,<br>Tessa.";

    Mail::to($clientMail)->send(new BlankMail(
      $bv,
      $sender,
      $bv->name,
      'Herinnering abonnement uitgaves aanvullen.',
      $message
    ));
  }

  public function autoVerlengen(){
    try{
      $abonnementen = Abonnementen::where('id', 1738)->with('items_con')->get();
      foreach($abonnementen as $abonnement){
        if($abonnement->soort_klant == "ip"){
          $lastFactuurCon = $abonnement->lastFactuurCon();
          $dates = json_decode($lastFactuurCon->items, true)['ip'];
          $nextReleaseDateNotPayedFor = Carbon::parse($dates['end']);
          $diff = $nextReleaseDateNotPayedFor->diffInDays(date("Y-m-d"));

          if ($diff <= 14) {
            $factuur = $lastFactuurCon->factuur;
            $newfact = $factuur->cloneFactuur();
            $startdate = Carbon::parse($dates['end'])->addDay();
            $dates = [
              'start' => $startdate->format('Y-m-d'),
              'end' => $startdate->addYear()->format('Y-m-d'),
            ];
            AbonnementFacturen::insert([
              'factuur_id' => $newfact->id,
              'abonnement_id' => $abonnement->id,
              'items' => json_encode(['ip' => $dates]),
              'created_at' => Carbon::now(),
            ]);
            Mail::to('<EMAIL>')->send(new BlankMail(
              getBv(),
              getSettingValue("abonnement_email_afzender"),
              getBv()->name,
              'IP Abonnement verlengd',
              'Het IP abonnement van ' . $abonnement->voornaam . ' ' . $abonnement->achternaam . '(' . $abonnement->organisatie . ')' .  ' moet verlengd worden. Controlleer en verstuur de factuur!'
            ));
          }
          continue;
        }
        foreach ($abonnement->items_con as $item_con){
          if (!($item_con->verlengen ?? false) || !($item_con->active ?? false)) { continue; }
          $lastFactuurCon = $item_con->lastFactuurCon();
          $dates = json_decode($lastFactuurCon->items, true)[$item_con->item_id];
          $uitgaves = AbonnementenItemsUitgaves::where("item_id", $item_con->item_id)->get();
          $uitgaveDatums = [];

          foreach ($uitgaves as $uitgave ){
            if (Carbon::parse($uitgave->date)->lt(Carbon::parse($dates['end'])) || Carbon::parse($uitgave->date)->eq(Carbon::parse($dates['end']))){ continue; }
            $uitgaveDatums[] = Carbon::parse($uitgave->date);
          }

          if ($item_con->item->geldigheid == "Uitgaves"){
            if (count($uitgaveDatums) <= 0 || count($uitgaveDatums) < $item_con->item->aantal_uitgaves){ continue; }
            $nextReleaseDateNotPayedFor = min($uitgaveDatums);
          }
          else{
            $nextReleaseDateNotPayedFor = Carbon::parse($dates['end']);
          }
          $diff = $nextReleaseDateNotPayedFor->diffInDays(date("Y-m-d"));

          if ($diff <= 14) {
            $request = new Request();

            $data = [
              'abonnement' => $abonnement->id,
              'item' => $item_con->item_id,
            ];

            $request->merge($data);
            $factuur = (new \App\Http\Controllers\AbonnementenController())->newFactuur($request)->getData()->factuur;
            $factuur = Facturen::where('id', $factuur->id)->first();
            Abonnementen::sendFactuur($abonnement, $factuur);
          }
        }
      }
    }catch(\Exception $e){
      actError($e);
      return;
    }
  }

  public function verenigingenGet(){
    $verenigingen = AbonnementenVerenigingen::where(['active' => 1])->get();
    return response(['verenigingen' => $verenigingen]);
  }

  public function toggleSubscription($subdomein, Request $request){
    $abonnementItem = AbonnementItems::where("id", $request->abonnementenItemsId)->first();
    $abonnementItem->verlengen = $abonnementItem->verlengen ? 0 : 1;
    $abonnementItem->save();
    return response(['verlengen' => $abonnementItem->verlengen]);
  }

  public function refreshFactuurPdf(Request $request){
    $factuur = Facturen::where('id', $request->factuur)->with('abonnement', 'adres')->first();
    $abonnement = $factuur->abonnement;
    $contactnaam = $abonnement->klant->contactpersoon_voornaam." ".$abonnement->klant->contactpersoon_achternaam;
    if($abonnement->factuur_contactpersoon && $abonnement->factuur_contactpersoon != 'klant_contactpersoon'){
      $cp = KlantenContactpersonen::where('id', $abonnement->factuur_contactpersoon)->first();
      $contactnaam = $cp->voornaam." ".$cp->achternaam;
    }
    $factuur->referentie = $abonnement->factuur_referentie;
    if(!$factuur->adres){
      FacturenAdres::insert([
        "factuur_id" => $factuur->id,
        "klant" => $abonnement->klant->title(),
        "straat" => $abonnement->klant->postadres_straat,
        "huisnummer" => $abonnement->klant->postadres_huisnummer,
        "postcode" => $abonnement->klant->postadres_postcode,
        "plaats" => $abonnement->klant->postadres_plaats,
        "tav" => $contactnaam,
      ]);
    }else{
      FacturenAdres::where('factuur_id', $factuur->id)->update([
        "klant" => $abonnement->klant->title(),
        "straat" => $abonnement->klant->postadres_straat,
        "huisnummer" => $abonnement->klant->postadres_huisnummer,
        "postcode" => $abonnement->klant->postadres_postcode,
        "plaats" => $abonnement->klant->postadres_plaats,
        "tav" => $contactnaam,
      ]);
    }
    $factuur->refreshPdf();
    return response(null, 200);
  }
}
