<?php

namespace App\Http\Controllers\Api;

use App\Verlof;
use DB;
use App;
use App\Classes\UrenregistratieClientFunctions;
use Config;
use Route;
use Auth;
use App\User;
use App\Device;
use App\Message;
use App\UrenRegistratie;
use App\Uursoorten;
use App\Facturabel;
use App\UrenRegistratieProjectTaken;
use App\UrenRegistratieMachines;
use App\Mail\NewCorrection;
use App\HoofdVerlofreden;
use Validator;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\UrenRequest;
use App\Mail\BlankMail;
use App\Project;

class UrenController extends Controller {

  public function get(Request $request){
    try{
      $verlof = [];
      $users = User::all();
      if (isset($request->start) && $request->end) {
        $startYear = Carbon::parse($request->start)->year;
        $endYear = Carbon::parse($request->end)->year;
        $years = range($startYear, $endYear);

        foreach ($users as $user) {
          $verlofsaldo = 0;
          foreach ($years as $year) {
            $verlofsaldo += $user->totalVerlofSaldoJaar($year);
          }
          $verlof[] = [
            'user_id' => $user->id,
            'name' => $user->name . ' ' . $user->lastname,
            'verlofsaldo' => $verlofsaldo,
            'gebruiktverlofsaldo' => $user->gebruiktVerlofSaldo($request->start ?? null, $request->end ?? null),
          ];
        }
      }
      $uren = UrenRegistratie::with($request->relations ?? [])->get();

      if(isset($request->start)){
        $uren = $uren->where('datum', '>=', $request->start);
      }
      if(isset($request->end)){
        $uren = $uren->where('datum', '<=', $request->end);
      }
      if(isset($request->user)){
        $uren = $uren->where('medewerker_id', $request->user);
      }
      if(isset($request->users)){
        $uren = $uren->whereIn('medewerker_id', $request->users);
      }
      if(isset($request->project)){
        $uren = $uren->where('projectnummer', $request->project);
      }
      if(isset($request->projectids)){
        $projectnummers = Project::whereIn('id', $request->projectids)->pluck('projectnr');
        $uren = $uren->whereIn('projectnummer', $projectnummers);
      }
      if(isset($request->bv)){
        foreach($uren as $i => $uur){
          if($uur->user->bv_id != $request->bv){$uren->forget($i);}
        }
      }

      if(isset($request->groupByGroupBy)){
        $uren = groupByGroupBy($uren, $request->groupByGroupBy[0], $request->groupByGroupBy[1]);
        return response()->json(['uren' => $uren,'verlof' => $verlof], 201);
      }
      if(isset($request->groupBy)){
        $uren = $uren->groupBy($request->groupBy);
        return response()->json(['uren' => $uren,'verlof' => $verlof], 201);
      }

      $uren = resetIndex($uren);
      return response()->json(['uren' => $uren,'verlof' => $verlof], 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }

  public function correction(Request $request) {
    $device = Device::where('api_token', $request->token)->firstOrFail();

    $date = Carbon::createFromFormat('d-m-Y', $request->date)->format('Y-m-d');
    $user = User::where('id', $device->user_id)->with('role')->firstOrFail();

    Message::insert([
      'message' => $request->message,
      'date' => $date,
      'user_id' => $device->user_id,
    ]);

    if(!empty($user->role->correctie_mail )){Mail::to($user->role->correctie_mail )->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->message));}
    if(!empty($user->role->correctie_mail2)){Mail::to($user->role->correctie_mail2)->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->message));}
    if(!empty($user->role->correctie_mail3)){Mail::to($user->role->correctie_mail3)->send(new NewCorrection($user->name, $user->lastname, $request->date, $request->message));}

    return response()->json(['success' => 'success'], 200);
  }

  public function storeWeek(Request $request) {
      $projecten = json_decode($request->projects, true);
      $data = [];
      foreach($projecten as $project){
        $newrequest = new Request();
        $data['projects'] = json_encode([$project]);
        $data['token'] = $request->token;
        $data['date'] = $project['date'];
        $newrequest->merge($data);
        $this->store($newrequest);
      }
  }

  public function store(Request $request) {
    try{
      $device = Device::where('api_token', $request->token)->firstOrFail();
      $user = User::where('id', $device->user_id)->with('bv')->firstOrFail();
      $projecten = json_decode($request->projects, true);
      $totaalDagUren = 0;
      $tijdvoortijdgezet = 0;
      $feestdaguren = false;
      $atvUren = false;
      $totaalDagUren_incl_verlof = 0;

      $this->clearDay($request);

      foreach($projecten as $project){
        $ziekteuren   = 0;
        $werktijd     = 0;
        $tijdvoortijd = 0;
        $feesturen    = 0;
        $atv    = 0;
        $roostervrij    = 0;
        $verlofuren   = 0;
        $bijzonderverlofuren   = 0;
        $totaaluren   = 0;
        $overuren125  = 0;
        $overuren150  = 0;
        $overuren165  = 0;
        $overuren200  = 0;
        $pauze        = 0;
        $totaaluren_incl_verlof = 0;

        if (($project["tijdvoortijd"] ?? false)) {
          $tijdvoortijdgezet = 1;
        }

        if (!empty($project["pauze"])) {
          $pauze = $project["pauze"];
        }

        $obv = isset((userPermissions($user->id))['uren o.b.v. getal']);
        if($obv){
          $totaaluren = $project["gewerkteuren"] - $pauze;
          $totaaluren_incl_verlof = $totaaluren;
        }
        else{
          $totaaluren = strtotime($project["eindtijd"]) - strtotime($project["begintijd"]);
          $totaaluren = ($totaaluren / 3600) - $pauze;
          $totaaluren_incl_verlof = $totaaluren;
        }

        $hoofdReden = HoofdVerlofreden::getVerlofType($project["verlofreden"]);

        if (isset($project["verlof"]) && $project["verlof"] && isset($project["verlofreden"]) && $project["verlofreden"]) {
          if ($hoofdReden == 'Ziekte') {
            $ziekteuren = $project["verlof"];
            $totaaluren = $totaaluren - $ziekteuren;
            if ($totaaluren < 0) {
              $ziekteuren = $ziekteuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Verlof') {
            $verlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $verlofuren;
            if ($totaaluren < 0) {
              $verlofuren = $verlofuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Bijzonder verlof') {
            $bijzonderverlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $bijzonderverlofuren;
            if ($totaaluren < 0) {
              $bijzonderverlofuren = $bijzonderverlofuren + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Feestdag') {
            $feestdaguren = true;
            $feesturen = $project["verlof"];
            $totaaluren = $totaaluren - $feesturen;
            if ($totaaluren < 0) {
              $feesturen = $feesturen + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'ATV') {
            $atvUren = true;
            $atv = $project["verlof"];
            $totaaluren = $totaaluren - $atv;
            if ($totaaluren < 0) {
              $atv = $atv + $totaaluren;
              $totaaluren = 0;
            }
          } elseif ($hoofdReden == 'Roostervrije uren') {
            $roostervrij = $project["verlof"];
            $totaaluren = $totaaluren - $roostervrij;
            if ($totaaluren < 0) {
              $roostervrij = $roostervrij + $totaaluren;
              $totaaluren = 0;
            }
          } else {
            $verlofuren = $project["verlof"];
            $totaaluren = $totaaluren - $verlofuren;
            if ($totaaluren < 0) {
              $verlofuren = $verlofuren + $totaaluren;
              $totaaluren = 0;
            }
          }
        }

        $naca = null;
        if(!empty($project["naca"])) {
          $n = DB::table('naca_codes')->where('code', $project["naca"])->first();
          $naca = $n->id;
        }

        $project["reisuren"] = str_replace(',', '.', ( $project["reisuren"] ?? 0));

//      Reisuren uitschakelen
        if($user->all_in === '1' || $user->disable_reisuren === '1'){
          $reisuren = 0;
        }

        // Custom berekeningen per klant
        $subdomein = getSubdomein();
        if (method_exists(UrenregistratieClientFunctions::class, $subdomein)) {
          $project = UrenregistratieClientFunctions::$subdomein($project, $request);
        }

        $insertId = DB::table('urenregistratie')->insertGetId([
          'medewerker_id' => $user->id,
          'datum'         => Carbon::parse($request->date)->format("Y-m-d"),
          'projectnummer' => !empty($project["projectnummer"]) ? $project["projectnummer"] : null,
          'pauze'         => !empty($project["pauze"])         ? $project["pauze"]         : 0,
          'gewerkte_uren' => !empty($project["gewerkteuren"])  ? $project["gewerkteuren"]  : 0,
	        'kilometers'    => !empty($project["kilometers"])    ? $project["kilometers"]  : 0,
	        'begintijd'     => !empty($project["begintijd"])     ? Carbon::parse($project["begintijd"])->format('H:i') : null,
          'eindtijd'      => !empty($project["eindtijd"])      ? Carbon::parse($project["eindtijd"])->format('H:i')  : null,
          'begintijd_heenreis' => !empty($project["begintijd_heenreis"]) ? Carbon::parse($project["begintijd_heenreis"])->format('H:i') : null,
          'eindtijd_heenreis' => !empty($project["eindtijd_heenreis"]) ? Carbon::parse($project["eindtijd_heenreis"])->format('H:i')  : null,
          'begintijd_terugreis' => !empty($project["begintijd_terugreis"]) ? Carbon::parse($project["begintijd_terugreis"])->format('H:i') : null,
          'eindtijd_terugreis' => !empty($project["eindtijd_terugreis"]) ? Carbon::parse($project["eindtijd_terugreis"])->format('H:i')  : null,
          'reisuren'      => !empty($project["reisuren"])      ? $project["reisuren"] : 0,
          'overnachting'  => !empty($project["overnachting"])  ? $project["overnachting"]  : null,

          'heenreis_huisproject'   => !empty($project["heenreis_huisproject"])  ? $project["heenreis_huisproject"]  : null,
          'terugreis_huisproject'  => !empty($project["terugreis_huisproject"]) ? $project["terugreis_huisproject"] : null,
          'heenreis_woonwerk'      => !empty($project["heenreis_woonwerk"])     ? $project["heenreis_woonwerk"]     : null,
          'terugreis_woonwerk'     => !empty($project["terugreis_woonwerk"])    ? $project["terugreis_woonwerk"]    : null,

          'ladenlossen_begintijd'        => !empty($project["ladenlossen_begintijd"])        ? Carbon::parse($project["ladenlossen_begintijd"])->format('H:i:s')        : null,
          'ladenlossen_eindtijd'         => !empty($project["ladenlossen_eindtijd"])         ? Carbon::parse($project["ladenlossen_eindtijd"])->format('H:i:s')         : null,
          'ladenlossen_middag_begintijd' => !empty($project["ladenlossen_middag_begintijd"]) ? Carbon::parse($project["ladenlossen_middag_begintijd"])->format('H:i:s') : null,
          'ladenlossen_middag_eindtijd'  => !empty($project["ladenlossen_middag_eindtijd"])  ? Carbon::parse($project["ladenlossen_middag_eindtijd"])->format('H:i:s')  : null,

          'bestuurder' => !empty($project["bestuurder"]) ? ($project["bestuurder"] === "true" ? 1 : 0) : null,
          'bijrijder'       => !empty($project["bijrijder"])   ? $project["bijrijder"]   : null,
          'kenteken_id'     => !empty($project["kenteken_id"]) ? $project["kenteken_id"] : null,
          'roostervrij'     => $roostervrij,
          'verlof'          => $verlofuren,
          'bijzonderverlof' => $bijzonderverlofuren,
          'ziekteuren'      => $ziekteuren,
          'feesturen'       => $feesturen,
          'atv_uren'       => $atv,
          'facturabel_id'  => !empty($project["facturabel"]) ? $project["facturabel"] : null,
          'verlofreden_id'  => !empty($project["verlofreden"]) ? $project["verlofreden"] : null,
          'naca_id'         => $naca,
          'opmerkingen'     => !empty($project["opmerkingen"]) ? $project["opmerkingen"] : null,

          'totaaluren100'      => $totaaluren,
          'totaal_overuren150' => 0,
          'totaal_overuren165' => 0,
          'begintijd_eindtijd' => empty($project["gewerkteuren"]) ? true : false,
          'totaal_overuren200' => 0,
          'tijdvoortijd' => 0,
          'totaaldaguren100' => 0,
          'tijdelijk' => 0,
          'uursoort_id' => $project['uursoort_id'] ?? null,

        ]);

        $totaalDagUren += $totaaluren;
        $totaalDagUren_incl_verlof += $totaaluren_incl_verlof;

        foreach($project["machines"] ?? [] as $machine){
          if(!isset($machine['id'])){ return; }
          UrenRegistratieMachines::insert([
            "urenregistratie_id" => $insertId,
            "machine_id" => $machine["id"],
            "begintijd" => Carbon::parse($machine["begintijd"])->format('H:i'),
            "eindtijd" => Carbon::parse($machine["eindtijd"])->format('H:i'),
          ]);
        }
        foreach($project["taken"] ?? [] as $taak){
          UrenRegistratieProjectTaken::insert([
            'urenregistratie_id' => $insertId,
            'taak_id' => $taak
          ]);
        }
      }

      $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
      $dag   = $dagen[Carbon::parse($request->date)->dayOfWeek];
      $standaarduren = DB::table('standaarduren')->where('dag', $dag)->where('medewerkers_id', $user->id)->first();
      $standaarduren = $standaarduren->standaarduren ?? 0;


      // if ($user->tijdvoortijd == true) {
      //   if($user->nul_uren == 0) {
      //     if ($totaaluren > $standaarduren) {
      //       $overuren125 = $totaaluren - $standaarduren;
      //       $totaaluren = $standaarduren;
      //     } else {
      //       $overuren125 = 0;
      //     }
      //   }
      // } else {
      //   $tijdvoortijd  = $totaaluren - $standaarduren;
      //   $totaaluren = $standaarduren;
      // }


      $overuren = [
        '100' => $totaalDagUren,
        '125' => 0,
        '150' => 0,
        '165' => 0,
        '200' => 0,
        'tijdvoortijd' => 0,
      ];
      $sett = json_decode(getSettingValue('uren_overuren') ?? '[]', true);
      $minuren = 0;
      if($user->all_in == 1){
        $overuren['125'] = 0;
        $overuren['150'] = 0;
        $overuren['165'] = 0;
        $overuren['200'] = 0;
        DB::table('urenregistratie')->where('id', $insertId)->update(['reisuren' => 0]);
      }
      else {
        if (getSettingCheckbox('urenregistratie_advanced_overuren')){
          $overuren = UrenRegistratie::calcAdvancedOveruren($request);
        }
        else{
          if($user->nul_uren == 0 && !(Carbon::parse($request->date)->isWeekend())) {
            if ($overuren['100'] > $standaarduren) {
              $overuren[($sett['weekdagen'] ?? '150')] = $overuren['100'] - $standaarduren;
              $overuren['100'] = $standaarduren;
            }
            if($tijdvoortijdgezet == 1){
              if ($overuren['150'] > 0 || $overuren['125'] > 0 || $overuren[($sett['weekdagen'] ?? '150')] > 0) {
                $overuren['tijdvoortijd'] = $overuren[($sett['weekdagen'] ?? '150')];
                $overuren['125'] = 0;
                $overuren['150'] = 0;
                $overuren['165'] = 0;
                $overuren['200'] = 0;
              }
              elseif ($totaalDagUren_incl_verlof < $standaarduren) {
                $overuren['100'] = $standaarduren;
                $overuren['tijdvoortijd'] = $totaalDagUren_incl_verlof - $standaarduren;
              }
            }
          }
          elseif ($user->nul_uren == 0) {
            if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SATURDAY)) {
              $overuren[($sett['zaterdag'] ?? '165')] = $overuren['100'];
              $overuren['100'] = 0;
            }
            if(Carbon::parse($request->date)->isDayOfWeek(Carbon::SUNDAY)) {
              $overuren[($sett['zondag'] ?? '200')] = $overuren['100'];
              $overuren['100'] = 0;
            }
            if($tijdvoortijdgezet == 1) {
              $overuren['tijdvoortijd'] = $overuren[($sett['zaterdag'] ?? '165')] + $overuren[($sett['zondag'] ?? '200')];
              $overuren['125'] = 0;
              $overuren['150'] = 0;
              $overuren['165'] = 0;
              $overuren['200'] = 0;
            }
          }
          if ($feestdaguren || $atvUren){
            $overuren['100'] = 0;
            $overuren['125'] = 0;
            $overuren['150'] = 0;
            $overuren['165'] = 0;
            $overuren['200'] = 0;
          }
        }
        if ($totaalDagUren_incl_verlof < $standaarduren && $tijdvoortijdgezet == 0) {
          $minuren = $standaarduren - $totaalDagUren_incl_verlof;
        }
      }

      $overurenGoedkeuren = 0;
      $setting = getSettingJson('urenregistratie_overuren_goedkeuren');
      if($setting[$user->bv->id]['active'] ?? false == 'on'){
        $overurensum = 0;
        foreach($overuren as $value){
          $overurensum += $value;
        }
        if($overurensum > 0){
          $overurenGoedkeuren = 1;
          $datum = Carbon::parse($request->date)->format('d-m-Y');
          $button = emailBtnContent(getSubdomein(), "/uren/correction/$datum/user/$user->id", 'Bekijken');
          Mail::to($setting[$user->bv->id]['email'])->send(
            new BlankMail(
              $user->bv,
              '<EMAIL>',
              $user->bv->name,
              "$user->name $user->lastname heeft overuren ingevoerd",
              "Er zijn overuren ingevoerd door $user->name $user->lastname op $datum. Deze moeten nog goedgekeurd worden.<br><br>$button<br><br>Xx Tessa",
            )
          );
        }
      }

      $uren = UrenRegistratie::where('id', $insertId)->first();
      $uren->totaaldaguren100 = $overuren['100'];
      $uren->totaal_overuren125 = $overuren['125'];
      $uren->totaal_overuren150 = $overuren['150'];
      $uren->totaal_overuren165 = $overuren['165'];
      $uren->totaal_overuren200 = $overuren['200'];
      $uren->minuren = $minuren;
      $uren->tijdvoortijd = $overuren['tijdvoortijd'];
      $uren->goedkeuren = $overurenGoedkeuren;
      $uren->save();

      $log = $user->name." ".$user->lastname."  heeft uren ingevoerd (App).";
      actLog($log, $user->id, 2);

      return response()->json(["status" => "success", "message" => "Urenregistratie is ingediend!"], 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function clearDay($request){
    try{
      $device = Device::where('api_token', $request->token)->firstOrFail();
      $user = User::where('id', $device->user_id)->firstOrFail();

      UrenRegistratie::where(['medewerker_id' => $user->id, 'datum' => Carbon::parse($request->date)->format("Y-m-d")])->delete();
    }
    catch (\Exception $e){ actError($e); }
  }
  public function feestdagen() {
    $feestdagen = DB::table('feestdagen')->get();
    return response()->json($feestdagen);
  }
  public function datums(Request $request) {
    $dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
    $device = Device::where('api_token', $request->token)->firstOrFail();
    $response = [];

    $user = User::where('id', $device->user_id)->firstOrFail();

    try{
      for ($i = 0; $i < 100; $i++) {
        $today                 = Carbon::now()->subDays($i)->format('Y-m-d');
        if(getSettingCheckbox('ignore_day_'.(Carbon::parse($today)->dayOfWeek + 1))){ continue; }

        $response[$i]["datum"] = Carbon::parse($today)->format('d-m-Y');
        $urenregistratie       = DB::table('urenregistratie')->where('medewerker_id', $device->user_id)->where('datum', $today)->where('tijdelijk', 0)->first();
        $verlof                = DB::table('verlof')->where('medewerker_id', $device->user_id)->where('datum', $today)->where('akkoord', 1)->pluck('datum')->first();
        $feestdag              = DB::table('feestdagen')->select("datum", "feestdag")->where('datum', $today)->where('disabled', 0)->pluck("feestdag")->first();
        $correctie             = Message::where('user_id', $device->user_id)->where('date', $today)->first();

        if (Carbon($today)->gte(Carbon($user->start_date ?? '2000-01-01'))) {
          $response[$i]["dag"]        = $dagen[Carbon::parse($today)->dayOfWeek];
          $response[$i]["feestdagen"] = $feestdag ? true : false;
          $response[$i]["verlof"]     = $verlof ? true : false;
          $response[$i]["ingevoerd"]  = $urenregistratie ? true : false;
          $response[$i]["goedkeuren"] = isset($urenregistratie) ? ($urenregistratie->goedkeuren ? true : false) : false;
          $response[$i]["correctie"]  = isset($correctie) && $correctie->active ? true : false;
        }
      }
    }
    catch (\Exception $e){ actError($e); }

    $response = resetIndex($response);

    return response()->json($response, 201);
  }
  public function date(Request $request) {
    try{
      $device = Device::where('api_token', $request->token)->firstOrFail();
      $user = User::where('id', $device->user_id)->firstOrFail();
      $date = Carbon::parse($request->date)->format('Y-m-d');
      $uren = UrenRegistratie::where('medewerker_id', $user->id)->where('datum', $date)->with(['kenteken', 'verlofRtl', 'facturabelopties', 'naca', 'uursoort', 'project', 'projecttaken'])->where('tijdelijk', 0)->where('goedkeuren', 0)->get();
      $correctie = Message::where('user_id', $user->id)->where('date', $date)->first();
      foreach ($uren as $row){
        foreach ($row->machineuren as $rel){
          $rel->id = $rel->machine->id;
          $rel->name = $rel->machine->name;
          $rel->begintijd = Carbon::parse($rel->begintijd)->format('H:i');
          $rel->eindtijd = Carbon::parse($rel->eindtijd)->format('H:i');
        }
        $row->machines = $row->machineuren;
      }

      return response()->json(['uren' => $uren, 'correctie' => $correctie], 200);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function week(Request $request)
  {
      try {
          $device = Device::where('api_token', $request->token)->firstOrFail();
          $user = User::findOrFail($device->user_id);

          $startOfWeek = Carbon::now()->subWeek()->startOfWeek(Carbon::MONDAY);
          $endOfWeek = $startOfWeek->copy()->endOfWeek(Carbon::SUNDAY);

          // Check if there are records in the previous week
          $hasRecords = UrenRegistratie::where('medewerker_id', $user->id)
              ->whereBetween('datum', [$startOfWeek, $endOfWeek])
              ->exists();
          if ($hasRecords) {
              return response()->json(false, 200);
          }

          $weekDates = [
              'week_start' => $startOfWeek->toDateString(),
              'week_end' => $endOfWeek->toDateString(),
              'dates' => [
                  'maandag' => $startOfWeek->toDateString(),
                  'dinsdag' => $startOfWeek->copy()->addDay()->toDateString(),
                  'woensdag' => $startOfWeek->copy()->addDays(2)->toDateString(),
                  'donderdag' => $startOfWeek->copy()->addDays(3)->toDateString(),
                  'vrijdag' => $startOfWeek->copy()->addDays(4)->toDateString(),
                  'zaterdag' => $startOfWeek->copy()->addDays(5)->toDateString(),
                  'zondag' => $startOfWeek->copy()->addDays(6)->toDateString(),
              ],
          ];

          return response()->json($weekDates, 200);
      } catch (\Exception $e) {
          actError($e);
          return response(null, 500);
      }
  }
  public function overzicht(Request $request) {
    $device = Device::where('api_token', $request->token)->firstOrFail();
    $userid = $device->user_id;
    $year = $request->year ?? Carbon::now()->year;
    if($year == Carbon::now()->year){
      $months = Carbon::now()->month;
    }
    else{
      $months = 12;
    }

    $uren = [];
    for($m = $months; $m >= 1; $m--) {
      foreach(array_reverse(App::make('App\Http\Controllers\Uren\WeekController')->berekenOverzicht($m, $year, $userid)) as $week) {
        $uren[] = $week;
      }
    }


    return $uren;

  }
  public function uursoortDelete(Request $request){
    Uursoorten::where('id', $request->id)->update(['active' => 0]);
    return response(null, 200);
  }

  //cronjob
  public function standaardurenInvullen(){
    if(!getSettingCheckbox('urenregistratie_standaarduren_automatisch_registreren')) {
        return response(null, 200);
    }

    $today = Carbon();

     $users = User::where('active', 1)
       ->whereHas('standaarduren')
       ->with('standaarduren')
       ->get();

     $verlofPerUser = Verlof::where('akkoord', 1)
       ->whereDate('datum', $today)
       ->get()
       ->groupBy('medewerker_id');

    foreach($users as $user){
        $userVerlof = $verlofPerUser[$user->id] ?? collect();
        foreach($user->standaarduren ?? [] as $standaarduur){

          if (empty($standaarduur) || getWeekdagen(date('w')) != ucfirst($standaarduur->dag)) {continue;}

          $alreadyExists = UrenRegistratie::where('medewerker_id', $user->id)
              ->whereDate('datum', $today)
              ->exists();

          if ($alreadyExists){ continue; }

          $stdStart = CarbonTime($standaarduur->begintijd);
          $stdEnd = CarbonTime($standaarduur->eindtijd);

          $timeBlocks = [];

          $timeBlocks[] = [
              'start' => $stdStart,
              'end' => $stdEnd,
              'type' => 'work',
          ];

            foreach ($userVerlof as $verlof) {

                if (!$verlof->van || !$verlof->tot){ continue; };

                $verlofStart = CarbonTime($verlof->van);
                $verlofEnd = CarbonTime($verlof->tot);
                $newBlocks = [];

                foreach ($timeBlocks as $block) {
                    if ($block['type'] !== 'work') {
                        $newBlocks[] = $block;
                        continue;
                    }

                    if ($verlofEnd <= $block['start'] || $verlofStart >= $block['end']) {
                        $newBlocks[] = $block;
                        continue;
                    }

                    if ($verlofStart > $block['start']) {
                        $newBlocks[] = [
                            'start' => $block['start'],
                            'end' => $verlofStart,
                            'type' => 'work',
                        ];
                    }

                    $newBlocks[] = [
                        'start' => max($block['start'], $verlofStart),
                        'end' => min($block['end'], $verlofEnd),
                        'type' => 'verlof',
                        'verlofreden_id' => $verlof->reden_id,
                    ];

                    if ($verlofEnd < $block['end']) {
                        $newBlocks[] = [
                            'start' => $verlofEnd,
                            'end' => $block['end'],
                            'type' => 'work',
                        ];
                    }
                }

                $timeBlocks = $newBlocks;
            }

            foreach ($timeBlocks as $block) {
                $blockStart = Carbon::createFromFormat('H:i', $block['start']);
                $blockEnd = Carbon::createFromFormat('H:i', $block['end']);
                $duration = $blockStart->diffInMinutes($blockEnd) / 60;

                if ($duration <= 0){ continue; }

                $totalStandaardpauze = $standaarduur->pauze ?? 0;
                if(!empty($standaarduur->pauze)){
                    $standaardpauzes = json_decode($standaarduur->standaardpauzes, true) ?? [];

                    foreach($standaardpauzes as $pauze){
                        $begin = $pauze['begin'];
                        $eind = $pauze['eind'];

                        if ($begin && $eind){
                            $pauzeStart = Carbon::createFromFormat('H:i', $pauze['begin']);
                            $pauzeEnd = Carbon::createFromFormat('H:i', $pauze['eind']);

                            $totalStandaardpauze += $pauzeStart->diffInMinutes($pauzeEnd) / 60;
                        }
                    }
                }

                $duration -= $totalStandaardpauze;

                UrenRegistratie::insert([
                    'medewerker_id' => $user->id,
                    'datum' => $today->format('Y-m-d'),
                    'pauze' => $block['type'] === 'verlof' ? '0' : $totalStandaardpauze,
                    'gewerkte_uren' => $duration,
                    'begintijd' => CarbonTime($block['start']),
                    'eindtijd' => CarbonTime($block['end']),
                    'projectnummer' => '-',
                    'verlof' => $block['type'] === 'verlof' ? $duration : null,
                    'verlofreden_id' => $block['verlofreden_id'] ?? null,
                ]);
            }
        }
    }
    return response(null, 200);
  }


}
