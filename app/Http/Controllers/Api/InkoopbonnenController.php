<?php

namespace App\Http\Controllers\Api;

use App\AccorderenInkoopbonnen;
use App\AccorderenBudgetten;
use App\Classes\search\SearchManager;
use App\InkoopbonnenRegels;
use App\Klanten;
use App\Leveranciers;
use App\Mail\BlankMail;
use App\Project;
use App\Vestigingen;
use Auth;
use App\Inkoopbonnen;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class InkoopbonnenController extends Controller{

  private $relations = ['project', 'leverancier'];
  private $regels_relations = ['inkoopbon'];

  public function get(Request $request){
    try{
      $relations_build = generateLiteRelations(($request->relations ?? $this->relations), $request);
      $relations = $relations_build['relations'] ?? [];
      $relations_lite = $relations_build['relations_lite'] ?? [];

      $model = new Inkoopbonnen();
      $model = $model->with($relations)->with($relations_lite)->orderByRaw(($request->sort_by ?? 'created_at') . ' ' . ($request->sort_type ?? 'DESC'));

      //Filters
      if(isset($request->ids)){
        $model = $model->whereIn('id', $request->ids);

        $all_ids = $model->pluck('id')->toArray();
        if(isset($request->paginate) && isset($request->page)){
          $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
        }
        $inkoopbonnen = $model->get();
      }
      else{
	      if (isset($request->id)) {
		      $model = $model->where("id", $request->id);
	      }
        if(isset($request->project)){
          $model = $model->where('project_id', $request->project);
        }
        if(isset($request->active)){
          $model = $model->where('active', $request->active);
        }
        if(isset($request->start)){
          $model = $model->where('date', '>=', $request->start);
        }
        if(isset($request->end)){
          $model = $model->where('date', '<=', $request->end);
        }
        if(isset($request->leverancier)){
          $model = $model->where('leverancier_id', $request->leverancier);
        }
        if(isset($request->status)){
            if(is_string($request->status)){
                $model = $model->where('status', $request->status);
            }
            elseif(is_array($request->status)){
                $model = $model->whereIn('status', $request->status);
            }
        }
        if(isset($request->stadium)){
          $model = $model->whereIn('status', Inkoopbonnen::stadiumStatussen($request->stadium));
        }
        if(isset($request->bv)){
          $model = $model->where('bv', $request->bv);
        }
        if(isset($request->vestiging)){
          $model = $model->whereHas('project', function($query) use ($request){
            $query->where('vestiging_id', $request->vestiging);
          });
        }
        if(isset($request->zenvoice_kostendrager)){
            $model = $model->where('zenvoices_kostendrager', $request->zenvoice_kostendrager);
        }
        if(isset($request->target)){
          if($request->target == 'reporting_date'){
            if(isset($request->target_start)){
              $model = $model->whereHas('regels', function($query) use ($request){
                $query->where('reporting_date', '>=', $request->target_start);
              });
            }
            if(isset($request->target_end)){
              $model = $model->whereHas('regels', function($query) use ($request){
                $query->where('reporting_date', '<=', $request->target_end);
              });
            }
          }else{
            if(isset($request->target_start)){
              $model = $model->where($request->target, '>=', $request->target_start);
            }
            if(isset($request->target_end)){
              $model = $model->where($request->target, '<=', $request->target_end);
            }
          }

        }

        $all_ids = $model->pluck('id')->toArray();
        if(isset($request->paginate) && isset($request->page)){
          $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
        }
        $inkoopbonnen = $model->get();
      }

      foreach($inkoopbonnen as $i => $inkoopbon){
        $current_ids[] = $inkoopbon->id;

        foreach($relations_lite as $relation_key => $query){
          $inkoopbon->setRelation(str_replace('_lite', '', $relation_key), $inkoopbon[$relation_key]);
          unset($inkoopbon[$relation_key]);
        }

        if(isset($request->target_start) && isset($request->target_end)){
          $inkoopbon->statussen = $inkoopbon->statusByDateRange($request->target_start, $request->target_end);
          if(isset($request->target_status)){

            $target_statussen = is_array($request->target_status) ? $request->target_status : [$request->target_status];
            if ($inkoopbon->statussen == [] || !in_array(lastValue($inkoopbon->statussen)['status'], $target_statussen)){
              $inkoopbonnen->forget($i);
              continue;
            }
          }
        }

        $inkoopbon->accorderen = $inkoopbon->accorderen();
        $inkoopbon->has_budget = AccorderenBudgetten::hasBudget('inkoopbonnen', $inkoopbon);
        $inkoopbon->timeline = $inkoopbon->timeline();
        $inkoopbon->stadium = $inkoopbon->stadium();
        $inkoopbon->color = $inkoopbon->color();
        $inkoopbon->tot_incl = $inkoopbon->totaal()->incl;
        $inkoopbon->tot_excl = $inkoopbon->totaal()->excl;
        $inkoopbon->tot_btw = $inkoopbon->totaal()->btw;
      }

	    if(isset($request->id)){
		    return response($inkoopbonnen->first(), 200);
	    }

      $inkoopbonnen = resetIndex($inkoopbonnen);

      return response()->json([
        'inkoopbonnen' => $inkoopbonnen,
        'total_count' => count($all_ids ?? []),
        'all_ids' => $all_ids ?? [],
        'current_ids' => $current_ids ?? [],
      ], 201);
    }
    catch(\Exception $e){
      actError($e);
      return response(['message' => $e->getMessage()], 200);
    }
  }
  public function search(Request $request){
    $search = new SearchManager($request->search);
    return response($search->inkoopbon(), 200);
  }

  //Regels
  public function regelsGet(Request $request){
    try{
      $relations_build = generateLiteRelations(($request->relations ?? $this->regels_relations), $request);
      $relations = $relations_build['relations'] ?? [];
      $relations_lite = $relations_build['relations_lite'] ?? [];

      $model = new InkoopbonnenRegels();
      $model = $model->whereHas('inkoopbon')->with($relations)->with($relations_lite)->orderByRaw(($request->sort_by ?? 'created_at') . ' ' . ($request->sort_type ?? 'DESC'));

      //Filters
      if(isset($request->ids)){
        $model = $model->whereIn('id', $request->ids);

        $all_ids = $model->pluck('id')->toArray();
        if(isset($request->paginate) && isset($request->page)){
          $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
        }
        $regels = $model->get();
      }
      else{
        if (isset($request->id)) {
          $model = $model->where("id", $request->id);
        }
        if(isset($request->project)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->where('project_id', $request->project);
          });
        }
        if(isset($request->active)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->where('active', $request->active);
          });
        }
        if(isset($request->leverancier)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->where('leverancier_id', $request->leverancier);
          });
        }
        if(isset($request->status)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            if(is_string($request->status)){
              $query->where('status', $request->status);
            }
            elseif(is_array($request->status)){
              $query->whereIn('status', $request->status);
            }
          });
        }
        if(isset($request->stadium)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->where('status', Inkoopbonnen::stadiumStatussen($request->stadium));
          });
        }
        if(isset($request->bv)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->where('bv', $request->bv);
          });
        }
        if(isset($request->vestiging)){
          $model = $model->whereHas('inkoopbon', function($query) use ($request){
            $query->whereHas('project', function($query) use ($request){
              $query->where('vestiging_id', $request->vestiging);
            });
          });
        }
        if(isset($request->target)){
          if($request->target == 'reporting_date'){
            if(isset($request->target_start)){
              $model = $model->where($request->target, '>=', $request->target_start);
            }
            if(isset($request->target_end)){
              $model = $model->where($request->target, '<=', $request->target_end);
            }
          }
          else{
            if(isset($request->target_start)){
              $model = $model->wherehas('inkoopbon', function($query) use ($request){
                $query->where($request->target, '>=', $request->target_start);
              });
            }
            if(isset($request->target_end)){
              $model = $model->wherehas('inkoopbon', function($query) use ($request){
                $query->where($request->target, '<=', $request->target_end);
              });
            }
          }
        }
        if(isset($request->bc_accounts)){
          $model = $model->whereIn('business_central_account', $request->bc_accounts);
        }

        $all_ids = $model->pluck('id')->toArray();
        if(isset($request->paginate) && isset($request->page)){
          $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
        }
        $regels = $model->get();
      }

      foreach($regels as $i => $regel){
        $current_ids[] = $regel->id;

        foreach($relations_lite as $relation_key => $query){
          $regel->setRelation(str_replace('_lite', '', $relation_key), $regel[$relation_key]);
          unset($regel[$relation_key]);
        }

        if(isset($request->target_start) && isset($request->target_end)){
          if(!isset($regel->inkoopbon)){ dd($regel); }
          $regel->inkoopbon->statussen = $regel->inkoopbon->statusByDateRange($request->target_start, $request->target_end);
          if(isset($request->target_status)){

            $target_statussen = is_array($request->target_status) ? $request->target_status : [$request->target_status];
            if ($regel->inkoopbon->statussen == [] || !in_array(lastValue($regel->inkoopbon->statussen)['status'], $target_statussen)){
              $regels->forget($i);
              continue;
            }
          }
        }



        if(isset($regel->inkoopbon)){
          $regel->inkoopbon->stadium = $regel->inkoopbon->stadium();
          $regel->inkoopbon->color = $regel->inkoopbon->color();
        }

        $regel->totaal();
      }

      if(isset($request->id)){
        return response($regels->first(), 200);
      }

      $regels = resetIndex($regels);

      return response()->json([
        'regels' => $regels,
        'total_count' => count($all_ids ?? []),
        'all_ids' => $all_ids ?? [],
        'current_ids' => $current_ids ?? [],
      ], 201);
    }
    catch(\Exception $e){
      actError($e);
      return response(['message' => $e->getMessage()], 200);
    }
  }

  public function status(Request $request){
    try{
      $bon = Inkoopbonnen::where('id', $request->id)->with('leverancier._bv')->firstOrFail();

      if($request->status == 'Open'){
          AccorderenInkoopbonnen::where('inkoopbon_id', $bon->id)->update(['inkoopbon_id' => "-$bon->id"]);
      }
      else if($request->status == 'Accorderen'){
        AccorderenInkoopbonnen::where('inkoopbon_id', $bon->id)->update(['inkoopbon_id' => "-$bon->id"]);
        AccorderenInkoopbonnen::insert([
          "inkoopbon_id" => $bon->id,
          "process" => getSettingValue('accorderen_flow_inkoopbonnen'),
          "status" => null,
          "date" => Carbon::now(),
          "user_id" => User::id(),
        ]);

          $moduleInfo = AccorderenController::determineModule('inkoopbonnen', $bon->id);
          AccorderenController::sendAccordeerMail($moduleInfo->instance, $moduleInfo->module, $moduleInfo->onderwerp, $moduleInfo->msg, $request);
      }

      Inkoopbonnen::where('id', $bon->id)->update([
        'status' => $request->status,
        'active' => ($request->status == 'Verwijderd' ? 0 : 1),
        'completed_at' => ($request->status == 'Afgerond' ? Carbon::now() : null),
        'finished_at' => ($request->status == 'Voltooid' ? Carbon::now() : null),
        'deleted_at' => ($request->status == 'Verwijderd' ? Carbon::now() : null),
      ]);

      $log = User::name() . ' heft de status van de inkoopbon ' . $bon->bonnummer . ' gewijzigd naar ' . $request->status . '.';
      actLog($log, User::id(), 41);

      return response(null, 201);
    }
    catch(\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function active(Request $request){
    try{

      $bon = Inkoopbonnen::where('id', $request->id)->firstOrFail();
      Inkoopbonnen::where('id', $bon->id)->update([
        'active' => $request->active,
        'status' => $request->active === '1' ? 'Open' : 'Verwijderd'
      ]);

      $log = User::name() . ' heft van de inkoopbon ' . $bon->bonnummer . ($request->active === '1' ? ' teruggezet.' : 'verwijderd.');
      actLog($log, User::id(), 41);

      return response()->json();

    }
    catch(\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }

  public function send(Request $request){
    try{
      $temp = explode(",", str_replace(" ", "", $request->email));
      $mails = [];
      foreach($temp as $mail){
        if(!isset($mail) || $mail == ""){
          continue;
        }
        if(!verifyEmail($mail)){
          return response()->json(['message' => 'E-mailadres ' . $mail . ' is ongeldig, inkoopbon is niet verstuurd!']);
        }
        $mails[] = $mail;
      }
      if(!verifyEmail($request->sender)){
        return response()->json(['message' => 'E-mailadres ' . $request->sender . ' is ongeldig, inkoopbon is niet verstuurd!']);
      }

      $inkoopbon = Inkoopbonnen::where('id', $request->id)->with('project')->firstOrFail();

      $btnContent = emailBtnContent(getSubdomein(), "inkoopbonnen/open/$inkoopbon->token", 'Bekijk Inkoopbon');

      $div = '
            <div style="width: 100%;text-align: left; margin: 30px 0 0;">
              ' . $request->message . '
            </div>
            ' . $btnContent . '
        ';

      foreach($mails as $email){
        Mail::to($email)->send(
          new BlankMail(
            $inkoopbon->project->_bv ?? firstBvObject(),
            $request->sender,
            $inkoopbon->project->_bv->name ?? firstBvObject()->name,
            $request->subject,
            $div,
          ));
      }

      Inkoopbonnen::where('id', $inkoopbon->id)->update([
        'status' => 'Afgerond',
        'completed_at' => Carbon::now(),
        'sent_at' => Carbon::now(),
        'sent_to' => implode(', ', $mails),
      ]);
      $inkoopbon->refresh();

      $log = User::name() . ' heeft de inkoopbon ' . $inkoopbon->bonnummer . ' verstuurd aan ' . implode(", ", $mails) . '.';
      actLog($log, Auth::user()->id, 41);

      return response()->json([
          'inkoopbon' => $inkoopbon,
          'emails' => $mails,
      ], 200);
    }
    catch(\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
}
