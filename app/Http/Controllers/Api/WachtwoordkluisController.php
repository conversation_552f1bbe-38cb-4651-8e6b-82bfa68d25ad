<?php

namespace App\Http\Controllers\Api;

use App\Classes\search\SearchManager;
use App\RolePermission;
use App\Wachtwoordkluis;
use App\WachtwoordkluisUser;
use App\User;
use App\Mail\BlankMail;
use Auth;
use Storage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class WachtwoordkluisController extends Controller{
    public function get(Request $request){
        try{
            $model = new Wachtwoordkluis();
            $model = Wachtwoordkluis::where("active", 1)->orderBy($request->order_by ?? 'naam', $request->order_direction ?? 'asc')->with('users');

            //search
            if(isset($request->ids)){
                $model = $model->whereIn('id', $request->ids);
            }

            if(!hasPermission("Wachtwoorden inzien")){
                return response(null, 403);
            }

            //pagination
            $all_ids = $model->pluck('id')->toArray();
            if(isset($request->per_page) && isset($request->page)){
                $model->skip($request->per_page * ($request->page - 1))->take($request->per_page);
            }
            $kluisRegels = $model->get();
            $current_ids = $kluisRegels->pluck('id')->toArray();


            foreach($kluisRegels as $kluisRegel){
                $kluisRegel->kluisRows = $kluisRegel->kluisRows();
            }


            return response([
              'kluisRegels' => $kluisRegels,
              'all_ids' => $all_ids ?? [],
              'current_ids' => $current_ids ?? [],
            ], 200);
        }
        catch(\Exception $e){
            actError($e);
            return;
        }

    }
    public function search(Request $request){
        try{
            $search = new SearchManager($request->search);
            $kluisregels = $search->kluisregel();

            return response($kluisregels);
        }
        catch(\Exception $e){
            actError($e);
        }
    }

    public function decrypt(Request $request){
        try{
            return response()->json(["decPass" => tes_decrypt($request->password)], 201);
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }
    public function aanvragen(Request $request){
        try{
            $duplicate = WachtwoordkluisUser::where(["user_id" => Auth::user()->id, "password_id" => $request->passwordId])->first();

            if(!$duplicate){
                WachtwoordkluisUser::insert([
                  "user_id" => Auth::user()->id,
                  "password_id" => $request->passwordId
                ]);
            }

            return $request->passwordId;
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }
    public function aanvragenOverzicht(){
        $aanvragenLijst = WachtwoordkluisUser::whereNull("status")->orderBy("created_at", "DESC")->with("user", "password")->get();
        return response()->json(["aanvragenLijst" => $aanvragenLijst], 201);
    }
    public function judgeRequest(Request $request){
        try{
            WachtwoordkluisUser::where(['password_id' => $request->id, 'user_id' => $request->userId])->update(['status' => intval($request->allow)]);
            $this->notifyUser($request);
            return response()->json(["status" => 'succes'], 201);
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }
    public function usersByPassword(Request $request){
        try{
            $kluisUserRows = WachtwoordkluisUser::where(['password_id' => $request->id, 'status' => '1'])->with('user')->get();
            return $kluisUserRows;
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }
    public function deletePassword(Request $request){
        try{
            Wachtwoordkluis::where('id', $request->id)->update(['active' => 0]);
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }
    public function pushRequest(Request $request){
        try{
            $wachtwoordRegel = Wachtwoordkluis::where('id', $request->passwordId)->first();
            $perm = \DB::connection('tessa')->table('permissions')->where('permission', 'Wachtwoorden beheren')->first();
            $roles = RolePermission::select('role_id')->where('permission_id', $perm->id)->get()->toArray();

            $users = User::whereIn('role_id', $roles)->where('active', 1)->with('devices')->get();

            if(getSettingCheckbox("pushmelding_wachtwoordenkluis")){
                $tokens = [];
                foreach($users as $user){
                    foreach($user->devices ?? [] as $device){
                        $tokens[] = $device->device_id ?? 'no_token_given';
                    }
                }

                $aanvraagtekst = Auth::user()->name . " vraagt toegang tot wachtwoord " . $wachtwoordRegel->naam;
                pushToAll('Wachtwoord aanvraag', $aanvraagtekst, $tokens, ["route" => 'wachtwoordenkluis/aanvragen']);
            }

            if(getSettingCheckbox("mail_wachtwoordenkluis")){
                $bv = getBv();

                foreach($users as $user){

                    $message = 'Beste ' . $user->name . ', <br><br>'
                      . Auth::user()->name . ' heeft een aanvraag gedaan voor het wachtwoord ' . $wachtwoordRegel->naam
                      . '<div style="text-align: center;padding-top: 40px;">
                   <a href="https://infordb.ikbentessa.nl/wachtwoordenkluis/aanvragen"  class="email-btn">Beoordeel</a>
                 </div>'
                      . '<br><br> Met vriendelijke groet,'
                      . '<br> Tessa.';

                    if($user->email){
                        Mail::to($user->email)->send(new BlankMail(
                          $bv,
                          "<EMAIL>",
                          $bv->name,
                          'Wachtwoord aanvraag',
                          $message
                        ));
                    }
                }
            }
        }
        catch(\Exception $e){
            actError($e);
            return;
        }
    }

    private function notifyUser(Request $request){
        try{
            $wachtwoordRegelUser = WachtwoordkluisUser::where(['password_id' => $request->id, 'user_id' => $request->userId])->with('password')->first();
            $user = User::where('id', $wachtwoordRegelUser->user_id)->where('active', 1)->with('devices')->first();

            $tokens = [];

            foreach($user->devices ?? [] as $device){
                $tokens[] = $device->device_id ?? 'no_token_given';
            }

            if($request->allow){
                $aanvraagtekst = "Je aanvraag voor wachtwoord " . $wachtwoordRegelUser->password->naam . " is goedgekeurd";
            }
            else{
                $aanvraagtekst = "Je aanvraag voor wachtwoord " . $wachtwoordRegelUser->password->naam . " afgekeurd";
            }

            pushToAll('Wachtwoord aanvraag', $aanvraagtekst, $tokens);
        }
        catch(\Exception $e){
            actError($e);
        }
    }

    public function ontneemWachtwoordRechten(Request $request){
        $kluisRegels = WachtwoordkluisUser::where(["user_id" => $request->userId])->update(["status" => 0]);
        actError($kluisRegels);
    }

}
