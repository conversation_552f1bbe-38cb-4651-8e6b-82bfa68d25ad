<?php

namespace App\Http\Controllers\Api;

use App\Aanvragen;
use App\Classes\ProjectSearch;
use App\Classes\search\SearchManager;
use App\Klanten;
use App\KlantenContactpersonen;
use App\KlantenLocaties;
use App\ProjectCustomRows;
use App\ProjectenTakenTemplates;
use App\ProjectenTakenTemplatesTaken;
use App\ProjectenTakenTemplatesTakenCustomRows;
use App\ProjectTakenCustomRows;
use App\User;
use Auth;

use App\Project;
use App\ProjectTaken;
use App\KastvakOpmerkingen;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use phpDocumentor\Reflection\Utils;

class ProjectenController extends Controller
{
    private $relations = ['offerte', 'offertes', 'meerwerk_offertes', 'taken', 'completed_taken', 'contactpersoon', 'custom', 'vestiging', 'manager', 'werkbonnen', 'inkoopfacturen', 'opdrachtbon', 'planning', 'klant', '_bv', 'uncompleted_taken'];

    public function get(Request $request){
        try {
            $relations_build = generateLiteRelations(($request->relations ?? $this->relations), $request);
            $relations = $relations_build['relations'] ?? [];
            $relations_lite = $relations_build['relations_lite'] ?? [];

		        $exact_online_connected = exactOnlineLocal()->connected;

            $model = new Project();
            $model = $model->with($relations)->with($relations_lite)->orderByRaw(($request->sort_by ?? 'created_at') . ' ' . ($request->sort_type ?? 'DESC'));

            //Permissions
            if (hasPermission("Gekoppelde projecten inzien")){
              $model = $model->whereHas('custom', function ($query){$query->where(['type' => 'user', 'value' => Auth::user()->id]);});
            }

            //Select
            if (isset($request->select)) {
                $model = $model->select($request->select);
            }
            //Filters
            if (isset($request->ids)) {
                $model = $model->whereIn('id', $request->ids);
            }
            else {

                if (isset($request->id)) {
                    $model = $model->where("id", $request->id);
                }
                if(isset($request->bv)){
                    $model = $model->where("bv", $request->bv);
                }
                if(isset($request->klant)){
                    $model = $model->where("klant_id", $request->klant);
                }
                if(isset($request->gefactureerd)){
                    $model = $model->where('gefactureerd', $request->gefactureerd);
                }
                if(isset($request->vestiging)){
                    $model = $model->where("vestiging_id", $request->vestiging);
                }
                if(isset($request->custom)){
                  $model = $model->whereHas('custom', function ($query) use ($request){
                      $query->where('keyword', $request->custom['field'])->where('value', $request->custom['value']);
                  });
                }
                if(isset($request->status)){
	                if(is_array($request->status)){
		                $model = $model->whereIn('status', $request->status);
	                }
	                if(is_string($request->status)){
		                $model = $model->where('status', $request->status);
	                }
                }
                if(isset($request->active)){
                  $model = $model->where('active', $request->active);
                }
                if(isset($request->notstatus)){
	                if(is_array($request->notstatus)){
		                $model = $model->whereNotIn('status', $request->notstatus);
	                }
	                if(is_string($request->notstatus)){
		                $model = $model->where('status', '!=', $request->notstatus);
	                }
                }
                if(isset($request->must_have_taken)){
                    $model = $model->has('taken')->with('werkbonnen');
                }
                if(isset($request->start)){
                  $model = $model->where('created_at', '>=', $request->start);
                }
                if(isset($request->end)){
                  $model = $model->where('created_at', '<=', $request->end);
                }
                if(isset($request->reverse_filter)){
                  foreach ($request->reverse_filter as $column => $value){
                    $model = $model->where($column, '!=', $value);
                  }
                }

                if(isset($request->taken_planned)){
                    if($request->taken_planned == 'ingepland'){
                        $model = $model->whereHas('taken', function ($query){ $query->whereHas('planning'); })->with(['taken' => function ($query){ $query->whereHas('planning')->with('werkbonnen'); }]);
                    }
                    else if($request->taken_planned == 'niet_ingepland'){
                        $model = $model->whereHas('taken', function ($query){ $query->whereDoesntHave('planning'); })->with(['taken' => function ($query){ $query->whereDoesntHave('planning')->with('werkbonnen'); }]);
                    }
                }
            }

            $all_ids = $model->pluck('id')->toArray();
            if(isset($request->paginate) && isset($request->page)){
              $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
            }
            $projecten = $model->get();

            //Append data ( For JS ) & Replace lite relations
            foreach ($projecten as $project) {
                $current_ids[] = $project->id;

                foreach ($relations_lite as $relation_key => $query) {
                    $project->setRelation(str_replace('_lite', '', $relation_key), $project[$relation_key]);
                    unset($project[$relation_key]);
                }

								$project->explorerFiles();

                $project->background = $project->background();
                $project->facturen = $project->facturen($request->facturen_relations ?? []);
                $project->setRelation('custom', $project->custom->keyBy('keyword'));

                if(isset($request->append) && is_array($request->append)){
                    if(in_array('proformas', $request->append)){
                        $project->proformas = $project->proformas($request->proformas_relations ?? []);
                    }
                    if(in_array('proformabedrag', $request->append)){
                        $project->proformabedrag = $project->proformaBedrag();
                    }
                    if(in_array('factuurbedrag', $request->append)){
                        $project->factuurbedrag = $project->factuurBedrag();
                    }
                }

                if (in_array('uren', $relations) || isset($relations_lite['uren_lite'])) {
                    foreach($project->uren as $uur){
                        $uur->sumMachineUren();
                    }
                }
                if (in_array('offerte', $relations) || isset($relations_lite['offerte_lite'])) {
                    if (isset($project->offerte)) {
                        $project->offerte->color = $project->offerte->color();
                    }
                }
                if (in_array('offertes', $relations) || isset($relations_lite['offertes_lite'])) {
                    foreach ($project->offertes ?? [] as $offerte) {
                        $offerte->color = $offerte->color();
                    }
                }
                if (in_array('meerwerk_offertes', $relations) || isset($relations_lite['meerwerk_offertes_lite'])) {
                    foreach ($project->meerwerk_offertes ?? [] as $offerte) {
                        $offerte->color = $offerte->color();
                    }
                }
                if (in_array('werkbonnen', $relations) || isset($relations_lite['werkbonnen_lite'])) {
                    foreach ($project->werkbonnen as $werkbon) {
                        $werkbon->color = $werkbon->color();
                    }
                }

		            if ($exact_online_connected) {
			            $project->getExactOnlineMetadata();
		            }

		            //TODO: append facturen only based on append parameter, same as proformas
	              foreach ($project->facturen as $factuur) {
	                  $factuur->color = $factuur->color();
	                  if (!isset($request->factuur_bedrag)){ continue; }
	                  $factuur->tot_incl = $factuur->totaal()->incl;
	                  $factuur->tot_excl = $factuur->totaal()->excl;
	                  $factuur->tot_btw = $factuur->totaal()->btw;
	              }
		            if(isset($request->factuur_bedrag)){
			            $project->factuur_bedrag = $project->factuurBedrag()->excl;
		            }
		            if(isset($request->factuur_bedrag_betaald)){
			            $project->factuur_bedrag_betaald = $project->factuurBedragBetaald()->excl;
		            }

		            if(isset($request->offerte_bedrag)){
			            $project->offertebedrag();
		            }
            }

            if(isset($request->id)){
                return response($projecten->first(), 200);
            }

            $projecten = resetIndex($projecten);
            $velden = json_decode(getSettingValue("projecten_velden"), true);
            return response()->json([
                'projecten' => $projecten,
                'total_count' => count($all_ids ?? []),
                'all_ids' => $all_ids ?? [],
                'current_ids' => $current_ids ?? [],
                'fields' => $velden,
            ], 201);
        }
        catch (\Exception $e) {
          actError($e);
          return response(['message' => $e->getMessage(), 'line' => $e->getLine()], 500);
        }
    }
    public function search(Request $request){
      $search = new SearchManager($request->search);
      return response($search->project(), 200);
    }
    public function getRelation(Request $request){
      $project = Project::where('id', $request->id)->with($request->relation)->first();
      return response([ 'relation' => $project[$request->relation] ?? json_decode('{}') ], 200);
    }

  public function offerteBedragen(Request $request){
      $projecten = Project::whereIn('id', $request->ids ?? [])->get();

      $total = new \stdClass();
      $total->excl = 0;
      $total->incl = 0;
      $total->btw = 0;

      $response = [
        'projects' => [],
      ];

      foreach($projecten as $project){
        $offertebedrag = $project->offertebedrag();
        $response['projects'][$project->id] = $offertebedrag;
        $total->excl += $offertebedrag->texcl;
        $total->incl += $offertebedrag->tincl;
        $total->btw += $offertebedrag->tbtw;
      }

      $response ['total'] = $total;

      return response()->json($response, 200);
    }

    public function status(Request $request){
      try{
        Project::where('id', $request->id)->update([
          'status' => $request->status,
          'active' => ($request->status == 'Afgerond' ? 0 : 1),
          'completion_date' => $request->status == "Afgerond" ? Carbon::now()->format('Y-m-d') : null,
        ]);

        $project = Project::where("id", $request->id)->first();
        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de status van het project '$project->projectnr' veranderd naar '$project->status'.";
        actLog($log, User::id(), 7);

        return response(null, 201);
      }
      catch (\Exception $e){
        actError($e);
        return response(['message' => $e->getMessage()], 201);
      }
    }
    public function first(Request $request){
      try{
        $project = Project::where('id', $request->id)->with($this->relations)->first();
        return response()->json([
          'project' => $project,
        ], 201);
      }
      catch (\Exception $e){
        actError($e);
        return response(null, 500);
      }

    }
    public function verify(Request $request){
      $project = Project::where('projectnr', $request->project)->first();
      return response()->json([
        'status' => !isset($project),
      ]);
    }
    public function store(Request $request){
      $project = json_decode($request->project);

      if(isset($project->aanvraagnummer)){
        Aanvragen::where('aanvraagnummer', $project->aanvraagnummer)->update(["status" => "Afgerond"]);
      }

      $projectnummer = $project->projectnr;
      if($project->auto_nummer && !isset($project->id)){
        $projectnummer = Project::nummer($project->bv);
      }

      if(isset($project->projectleider_id)){
        $projectleider = (getUser($project->projectleider_id)->name ?? '').' '.(getUser($project->projectleider_id)->lastname ?? '');
      }

      $data = [
        "id" => $project->id,
        "bv" => $project->bv,
        "code" => $project->code,
        "opdrachtgever" => $project->opdrachtgever,
        "klant_id" => $project->klant_id,
        "vestiging_id" => $project->vestiging_id,
        "contactpersoon_id" => $project->contactpersoon_id,
        "projectnr" => $projectnummer,
        "projectnaam" => $project->projectnaam,
        "adres" => $project->adres,
        "huisnummer" => $project->huisnummer,
        "toevoeging" => $project->toevoeging,
        "postcode" => $project->postcode,
        "woonplaats" => $project->woonplaats,
        "projectleider" => $projectleider ?? null,
        "projectleider_id" => $project->projectleider_id,
        "reis" => $project->reis,
        "omschrijving" => $project->omschrijving,
        "opdrachtnummer" => $project->opdrachtnummer,
        "taaknummer" => $project->taaknummer,
        "files" => json_encode($project->files),
      ];

      if(isset($project->id)){
        $data["updated_at"] = Carbon::now();
        Project::where("id", $project->id)->update($data);
        $id = $project->id;
      }
      else{
        $data["created_at"] = Carbon::now();
        $data["status"] = 'Opdracht';
        $id = Project::insertGetId($data);
        Project::createDefaultFolders($id);
      }

      ProjectCustomRows::where("project_id", $id)->delete();
      foreach($project->custom ?? [] as $row){
        if(!isset($row->value) || !$row->value){continue;}
        if($row->type == "time"){$row->value = Carbon::parse($row->value)->format("H:i");}
        if($row->type == "date"){$row->value = Carbon::parse($row->value)->format("Y-m-d");}
        ProjectCustomRows::insert([
          'project_id' => $id,
          'keyword' => $row->keyword ?? '',
          'name' => $row->name ?? '',
          'type' => $row->type ?? '',
          'value' => $row->value,
        ]);
      }

      return response(null, 201);
    }

    public function switchPlanning(Request $request){
      Project::where("id", $request->project)->update(["planning_active" => $request->state]);
      return response(null, 201);
    }
    public function color(Request $request){
      Project::where("id", $request->id)->update(["planning_color" => $request->color, "planning_status" => $request->status]);
      return response(null, 201);
    }

    public function getTaken(Request $request){
      try{
        $relations_build = generateLiteRelations(($request->relations ?? ['project', 'planning', 'werkbonnen', 'subtaken', 'facturen', 'custom']), $request);
        $relations = $relations_build['relations'] ?? [];
        $relations_lite = $relations_build['relations_lite'] ?? [];

        $model = new ProjectTaken();
        $model = $model->where('active',$request->active ?? 1)->with($relations)->with($relations_lite)->orderByRaw(($request->sort_by ?? 'created_at') . ' ' . ($request->sort_type ?? 'DESC'));

        if (isset($request->select)) {
          $model = $model->select($request->select);
        }

        if (isset($request->ids)) {
          $model = $model->whereIn('id', $request->ids);

          $all_ids = $model->pluck('id')->toArray();
          if (isset($request->paginate) && isset($request->page)) {
              $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
          }
          $taken = $model->get();
        }
        else{
            if(isset($request->id)){
                $model = $model->where("id", $request->id);
            }
            if(isset($request->project)){
                $model = $model->where("project_id", $request->project);
            }
            if(isset($request->projecten)){
                $model = $model->whereIn("project_id", $request->projecten);
            }

            $all_ids = $model->pluck('id')->toArray();
            if(isset($request->paginate) && isset($request->page)){
                $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
            }
            $taken = $model->get();
        }

        foreach ($taken as $taak) {
          $current_ids[] = $taak->id;
          foreach ($relations_lite as $relation_key => $query) {
              $taak->setRelation(str_replace('_lite', '', $relation_key), $taak[$relation_key]);
              unset($taak[$relation_key]);
          }
        }

        if(isset($request->id)){
            return response()->json([
                'taak' => $taken->first(),
                'total_count' => count($all_ids ?? []),
                'all_ids' => $all_ids ?? [],
                'current_ids' => $current_ids ?? [],
            ], 201);
        }

        $taken = resetIndex($taken);
        return response()->json([
          'taken' => $taken,
          'total_count' => count($all_ids ?? []),
          'all_ids' => $all_ids ?? [],
          'current_ids' => $current_ids ?? [],
        ], 201);

      }catch (\Exception $e){
        actError($e);
        return response(null, 500);
      }
    }
    public function searchTaken(Request $request){
        $search = new SearchManager($request->search, ($request->active ?? 1), $request->ids);
        return response($search->taak(), 200);
    }

    public function storeTaken(Request $request){
      try{

        $color = $this->defineTaakColor($request);
        $name = $this->defineProjectTaakName($request);

        $id = ProjectTaken::insertGetId([
          "project_id" => $request->id,
          "user_id" => User::id(),
          "parent_id" => $request->parent_id ?? null,
          "name" => $name,
          "planning_color" => $color,
        ]);

        $project = Project::where('id', $request->id)->with('taken')->first();
        if(($project->projecttaken ?? 1) == 0){
          Project::where('id', $project->id)->update([
            'projecttaken' => 1
          ]);
        }

        $custom = json_decode((getSettingValue('projecten_taken_custom_rows') ?? '[]'), true);
        foreach($request->custom ?? [] as $key => $value){
          $setting = find('keyword', $key, $custom);
          if(!isset($setting)){continue;}

          ProjectTakenCustomRows::insert([
            'taak_id' => $id,
            'keyword' => $key,
            'name' => $setting['name'],
            'type' => $setting['type'],
            'value' => $value,
            'data' => $setting['data'],
          ]);
        }

        $this->storeBatchTaken($request, $id);

        $log = User::name()." Heeft een project ( ".($project->projectnr ?? 'Todolijst')." ) taak toegevoegd";
        actLog($log, User::id(), 7);

        return response(null, 201);
      }
      catch (\Exception $e){
        actError($e);
        return response(null, 500);
      }

    }
    public function editTaken(Request $request){
      $taak = ProjectTaken::where('id', $request->taak)->with('planning', 'custom')->firstOrFail();
      $project = Project::where('id', $taak->project_id)->with('taken')->first();


      $taak->planning_color = $taak->isPlanningColorCustom()
        ? $taak->planning_color
        : $this->defineTaakColor($request);


      $taak->name = $this->defineProjectTaakName($request);
      $taak->save();


      $custom = json_decode((getSettingValue('projecten_taken_custom_rows') ?? '[]'), true);
      ProjectTakenCustomRows::where('taak_id', $taak->id)->delete();
      foreach($request->custom ?? [] as $key => $value){
        $setting = find('keyword', $key, $custom);
        if(!isset($setting)){continue;}

        ProjectTakenCustomRows::insert([
          'taak_id' => $taak->id,
          'keyword' => $key,
          'name' => $setting['name'],
          'type' => $setting['type'],
          'value' => $value,
          'data' => $setting['data'],
        ]);
      }

      $taak->refresh();
      $project->refresh();

      $log = User::name()." Heeft een project ( ".$project->projectnr." ) taak gewijzigd";
      actLog($log, User::id(), 7);

      return response(null, 201);
    }
    public function completeTaken(Request $request){
      if(getSettingValue('projecten_taken_afronden_actie') == 'planning_kleur' && $request->fromSubTaak == 'true'){
        ProjectTaken::where("id", $request->id)->update(["planning_color" => getSettingValue('projecten_taken_afronden_kleur', '#5cb85c')]);
      }else{
        ProjectTaken::where("id", $request->id)->update(["completed" => 1]);
      }

      $taak = ProjectTaken::where("id", $request->id)->first();
      $log = User::name()." Heeft project taak '$taak->name' afgerond";
      actLog($log, User::id(), 7);

      return response(null,  201);
    }
    public function redoTaken(Request $request){
      ProjectTaken::where("id", $request->id)->update(["completed" => 0]);

      $row = ProjectTaken::where("id", $request->id)->first();
      $log = User::name()." Heeft project taak '$row->name' geactiveerd";
      actLog($log, User::id(), 7);

      return response(null, 201);
    }
    public function deleteTaken(Request $request){
      $projecttaak = ProjectTaken::where("id", $request->id)->with("subtaken")->first();
      ProjectTaken::where("id", $request->id)->update(["active" => 0]);

      foreach ($projecttaak->subtaken ?? [] as $subtaak){
        ProjectTaken::where("id", $subtaak->id)->update(["active" => 0]);
      }

      $projecttaken = ProjectTaken::where('project_id', $projecttaak->project_id)->where('active', 1)->get();
      if($projecttaken->isEmpty()){
        Project::where('id', $projecttaak->project_id)->update([
          'projecttaken' => 0,
        ]);
      }
      $row = ProjectTaken::where("id", $request->id)->first();
      $log = User::name()." Heeft project taak '$row->name' verwijderd";
      actLog($log, User::id(), 7);

      return response(null, 201);
    }
    public function copyTaken(Request $request){
      try{
        $project = Project::where('id', $request->id)->with('taken')->firstOrFail();

        $taken = ProjectenTakenTemplatesTaken::whereIn('id',$request->taken ?? [])->with('custom')->orderBy('id', 'DESC')->get();

        foreach ($taken as $taak){
          $id = ProjectTaken::insertGetId([
            "project_id" => $project->id,
            "user_id" => User::id(),
            "name" => $taak->name,
            "planning_color" => $taak->planning_color
          ]);

          foreach($taak->custom as $custom){
            ProjectTakenCustomRows::insert([
              'taak_id' => $id,
              'keyword' => $custom->keyword,
              'name' => $custom->name,
              'type' => $custom->type,
              'value' => $custom->value,
              'data' => $custom->data,
            ]);
          }

        }

        $log = User::name()." heeft nieuwe project ( $project->projectnr ) taken toegevoegd.";
        actLog($log, User::id(), 7);

        return response(null,  201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }
    public function checklistToggleTaken(Request $request){
      try{
        $custom = ProjectTakenCustomRows::where('id', $request->custom)->firstOrFail();

        if($custom->type != 'checklist'){ return catchResponse(); }
        $list = json_decode($custom->value ?? '[]');

        if(!isset($list[$request->index])){ return catchResponse(); }
        if($request->status == 'toggle'){
          if(!isset($list[$request->index]->status) && isset($list[$request->index]->checked)){
            if($list[$request->index]->checked){
              $list[$request->index]->status = 'Geen status';
              $list[$request->index]->statuscolor = '#999999';
            }else{
              $list[$request->index]->status = 'Afgerond';
              $list[$request->index]->statuscolor = '#5cb85c';
            }
          }
          elseif($list[$request->index]->status != 'Afgerond'){
            $list[$request->index]->status = 'Afgerond';
            $list[$request->index]->statuscolor = '#5cb85c';
          }
          else{
            $list[$request->index]->status = 'Geen status';
            $list[$request->index]->statuscolor = '#999999';
          }
        }
        elseif($request->status == 'delete'){
          unset($list[$request->index]);
        }
        else{
          $list[$request->index]->status = $request->status;
          $list[$request->index]->statuscolor = $request->statuscolor;
        }

        $list = resetIndex($list);

        ProjectTakenCustomRows::where('id', $request->custom)->update(['value' => json_encode($list)]);

        return response(null, 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }

    public function getTodoLijsten(){
      return ProjectTaken::where(['project_id' => 0, 'active' => 1, 'user_id' => Auth::id()])->with('custom')->get();
    }

    public function storeTemplateTaken(Request $request){
      try{
        $name = $this->defineProjectTaakName($request);
        $color = $this->defineTaakColor($request);

        $id = ProjectenTakenTemplatesTaken::insertGetId([
          "template_id" => $request->template,
          "user_id" => User::id(),
          "name" => $name,
          "planning_color" => $color,
        ]);

        $custom = json_decode((getSettingValue('projecten_taken_custom_rows') ?? '[]'), true);
        foreach($request->custom ?? [] as $key => $value){
          $setting = find('keyword', $key, $custom);
          if(!isset($setting)){continue;}

          ProjectenTakenTemplatesTakenCustomRows::insert([
            'taak_id' => $id,
            'keyword' => $key,
            'name' => $setting['name'],
            'type' => $setting['type'],
            'value' => $value,
            'data' => $setting['data'],
          ]);
        }

        $template = ProjectenTakenTemplates::where('id', $request->template)->with('taken')->first();

        return response()->json([
          'taken' => $template->taken,
        ], 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }
    public function activeTemplateTaken(Request $request){
      try{
        $taak = ProjectenTakenTemplatesTaken::where('id', $request->taak)->firstOrFail();
        ProjectenTakenTemplatesTaken::where('id', $request->taak)->update(['active' => $request->active]);

        $template = ProjectenTakenTemplates::where('id', $taak->template_id)->with('taken')->first();

        return response()->json([
          'taken' => $template->taken,
        ], 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }

    public function storeTakenTemplate(Request $request){
      try{
        ProjectenTakenTemplates::insert([
          'user_id' => User::id(),
          'name' => $request->name ?? '',
        ]);

        $templates = ProjectenTakenTemplates::where('active', 1)->orderBy('name', 'ASC')->get();

        $log = User::name()." heeft een project taken template ".($request->name ?? '')." aangemaakt.";
        actLog($log, User::id(), 7);

        return response()->json([
          'templates' => $templates,
        ], 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }
    public function activeTakenTemplate(Request $request){
      try{
        $template = ProjectenTakenTemplates::where('id', $request->template)->firstOrFail();

        ProjectenTakenTemplates::where('id', $request->template)->update(['active' => $request->active]);
        $templates = ProjectenTakenTemplates::where('active', 1)->orderBy('name', 'ASC')->get();

        $log = User::name().' heeft de taken template '.($template->name ?? '').' '.($request->active === '1' ? 'geactiveerd.' : 'verwijderd.');
        actLog($log, User::id(), 7);

        return response()->json([
          'templates' => $templates,
        ], 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }
    public function editTakenTemplate(Request $request){
      try{
        $template = ProjectenTakenTemplates::where('id', $request->template)->firstOrFail();

        ProjectenTakenTemplates::where('id', $request->template)->update(['name' => $request->name]);
        $templates = ProjectenTakenTemplates::where('active', 1)->orderBy('name', 'ASC')->get();

        $log = User::name().' heeft de taken template '.($template->name ?? '').' gewijzigd.';
        actLog($log, User::id(), 7);

        return response()->json([
          'templates' => $templates,
        ], 201);
      }
      catch (\Exception $e){ return catchResponse($e); }
    }

    private function storeBatchTaken($request, $ref_id){
      $ref = ProjectTaken::where('id', $ref_id)->with('custom_without_relations')->first();
      $primair = getSettingValue('projecten_taken_custom_rows_primair');

      // Loop through the copies
      foreach ($request->batch ?? [] as $batch){

        // Insert taak based of the reference
        $clone = $ref->replicate()->toArray();
        unset($clone['custom_without_relations']);
        $id = ProjectTaken::insertGetId($clone);

        // loop through the custom fields of the reference
        foreach ($ref->custom_without_relations as $custom){
          $custom_clone = $custom->replicate()->toArray();

          $custom_clone['taak_id'] = $id;
          $custom_clone['value'] = $batch[$custom->keyword] ?? $custom_clone['value'];

          // Only change if it's based on primair
          if ($primair && $custom->keyword == $primair){
            ProjectTaken::where('id', $id)->update(['name' => $custom_clone['value']]);
          }

          // insert a copy of the custom field
          ProjectTakenCustomRows::insert($custom_clone);
        }
      }

    }
    private function defineProjectTaakName($request){
      try{
        //Return posted naam if not disabled
        if(isset($request->name)){
          return $request->name;
        }

        //Return default if primari custom not selected
        $primair = getSettingValue('projecten_taken_custom_rows_primair');
        if(!isset($primair) || !$primair){
          return 'Geen naam';
        }

        foreach($request->custom ?? [] as $key => $value){
          if($key == $primair){
            return $value;
          }
        }

        return 'Geen naam';
      }
      catch (\Exception $e){
        actError($e);
        return 'Geen naam';
      }
    }
    private function defineTaakColor($request){
      $setting = json_decode(getSettingValue('projecten_taken_standard_colors') ?? '[]');

      foreach ($setting as $row){
        if(isset($row->name) && (strtolower($row->name) == strtolower($request->name))){ return $row->color; }

        if(isset($row->custom) && isset($request->custom[$row->custom]) && (strtolower($row->custom_value) == strtolower($request->custom[$row->custom]))){
          return $row->color;
        }
      }

      return '#55bfff';
    }

    public function updateTakenColor(Request $request){
      try{

        $taak = ProjectTaken::where('id', $request->id)->with('subtaken')->firstOrFail();
        ProjectTaken::where('id', $request->id)->update([ 'planning_color' => $request->color ]);

        $project = Project::where('id', $taak->project_id)->with('taken')->first();

        return response()->json([
          'taken' => $project->taken,
        ], 201);
      }
      catch(\Exception $e){ return catchResponse($e); }
    }

    public function woocommerceInsert(){
      try{
        $data = json_decode(file_get_contents('php://input') ?? "[]");
        $this->woocommerceStore($data);

        return response(null, 200);
      }catch (\Exception $e){
        actError($e);
        return response(null, 500);
      }
    }
    public function woocommerceUpdate(){
      try{
        $meta = json_decode(getSettingValue('woocommerce_metadata') ?? '[]');
        $data = json_decode(file_get_contents('php://input') ?? "[]");
        $key = ProjectCustomRows::where(['keyword' => 'woocommerce_id', 'value' => $data->id])->first();

        if(!isset($key)){
          $this->woocommerceStore($data);
          return response(null, 200);
        }

        $project = Project::where("id", $key->project_id)->firstOrFail();

        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_id',
          ], [
            'name' => 'Bestelling ID',
            'type' => 'text',
            'value' => $data->id,
          ]
        );
        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_customer_note',
          ], [
            'name' => 'Opmerking',
            'type' => 'text',
            'value' => $data->customer_note ?? '',
          ]
        );
        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_status',
          ], [
            'name' => 'Bestelling status',
            'type' => 'text',
            'value' => $data->status,
          ]
        );
        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_total',
          ],[
            'name' => 'Totaal',
            'type' => 'text',
            'value' => $data->total,
          ]
        );
        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_items',
          ],[
            'name' => 'Artikelen',
            'type' => 'text',
            'value' => json_encode($data->line_items),
          ]
        );
        ProjectCustomRows::updateOrInsert(
          [
            'project_id' => $project->id,
            'keyword' => 'woocommerce_order_key',
          ],[
            'name' => 'Bestelnummer',
            'type' => 'text',
            'value' => $data->order_key,
          ]
        );
        foreach($data->meta_data ?? [] as $row){
          foreach ($meta as $mrow){
            if ($row->key == $mrow){
              ProjectCustomRows::updateOrInsert(
                [
                  'project_id' => $project->id,
                  'keyword' => $row->key,
                ],[
                  'name' => $row->key,
                  'type' => 'text',
                  'value' => $row->value,
                ]
              );
            }
          }
        }

        actLog("WooCommerce project $project->projectnr ($project->projectnaam) is gewijzigd.", null, 7);

        return response(null, 200);
      }
      catch (\Exception $e){
        actError($e);
        return response(null, 500);
      }
    }
    public function woocommerceDelete(){
      $data = json_decode(file_get_contents('php://input') ?? "[]");
      $key = ProjectCustomRows::where(['keyword' => 'woocommerce_id', 'value' => $data->id])->firstOrFail();

      $project = Project::where('id', $key->project_id)->first();
      $project->active = 0;
      $project->save();

      actLog("WooCommerce project $project->projectnr ($project->projectnaam) is verwijderd.", null, 7);

      return response(null, 200);
    }
    private function woocommerceStore($data){
    try{
      $meta = json_decode(getSettingValue('woocommerce_metadata') ?? '[]');
      $shipping = $data->shipping;
      $billing = $data->billing;

      $klant = Klanten::where("email", $billing->email)->with('locaties')->first();
      $debiteurnummer = Klanten::debiteurnummer();
      if(!isset($klant)){
	      $klantId = Klanten::insertGetId([
		      'naam' => $billing->company ? $billing->company : (($billing->first_name ?? null) . ' ' . ($billing->last_name ?? null)),
		      'bv' => firstBv(),
		      'contactpersoon_voornaam' => $billing->first_name ?? null,
		      'contactpersoon_achternaam' => $billing->last_name ?? null,
		      'contactpersoon_email' => $billing->email ?? null,
		      'contactpersoon_telefoon' => $billing->email ?? null,
		      'telefoonnummer' => $billing->phone ?? null,
		      'email' => $billing->email ?? null,
		      'straat' => $billing->address_1 ?? null,
		      'postcode' => $billing->postcode ?? null,
		      'plaats' => $billing->city ?? null,
		      "vestiging_id" => getSettingValue('klanten_standaard_vestiging'),
		      "debiteurnummer" => $debiteurnummer['debiteurnummer'] ?? null,
		      "deb_prefix" => $debiteurnummer['prefix'] ?? null,
		      "deb_afterfix" => $debiteurnummer['afterfix'] ?? null,
	      ]);
	      if($shipping->first_name != $billing->first_name && $shipping->last_name != $billing->last_name){
		      KlantenContactpersonen::insert([
			      'klant_id' => $klantId,
			      'voornaam' => $shipping->first_name,
			      'achternaam' => $shipping->last_name,
			      'telefoon' => $shipping->phone,
		      ]);
	      }
	      if($shipping->address_1 != $billing->address_1){
		      KlantenLocaties::insert([
			      'klant_id' => $klantId,
			      'straat' => $shipping->address_1 ?? null,
			      'postcode' => $shipping->postcode ?? null,
			      'plaats' => $shipping->city ?? null,
		      ]);
	      }
        $klant = Klanten::where("id", $klantId)->with('locaties')->first();

	      $id = Project::insertGetId([
		      'status' => 'Opdracht',
		      'projectnaam' => 'Bestelling #' . $data->id,
		      'projectnr' => Project::nummer($klant->bv),
		      'klant_id' => $klant->id,
		      'opdrachtgever' => $billing->first_name . " " . $billing->last_name,
		      'adres' => $shipping->address_1,
		      'postcode' => $shipping->postcode,
		      'woonplaats' => $shipping->city,
	      ]);
	      Project::createDefaultFolders($id);
	      $project = Project::where('id', $id)->first();

	      ProjectCustomRows::insert([
		      [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_id',
			      'name' => 'Bestelling ID',
			      'value' => $data->id,
			      'type' => 'text',
		      ], [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_customer_note',
			      'name' => 'Opmerking',
			      'value' => $data->customer_note ?? '',
			      'type' => 'text',
		      ], [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_status',
			      'name' => 'Bestelling status',
			      'value' => $data->status,
			      'type' => 'text',
		      ], [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_total',
			      'name' => 'Totaal',
			      'value' => $data->total,
			      'type' => 'text',
		      ], [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_items',
			      'name' => 'Artikelen',
			      'value' => json_encode($data->line_items),
			      'type' => 'text',
		      ], [
			      'project_id' => $id,
			      'keyword' => 'woocommerce_order_key',
			      'name' => 'Bestelnummer',
			      'value' => $data->order_key,
			      'type' => 'text',
		      ]
	      ]);
	      foreach($data->meta_data ?? [] as $row){
		      foreach($meta as $mrow){
			      if($row->key == $mrow){
				      ProjectCustomRows::insert([
					      'project_id' => $id,
					      'keyword' => $row->key,
					      'name' => $row->key,
					      'value' => $row->value,
					      'type' => 'text',
				      ]);
			      }
		      }
	      }

	      actLog("WooCommerce project $project->projectnr ($project->projectnaam) is aangemaakt.", null, 7);
      }
      }catch (\Exception $e){
        actError($e);
      }
    }

    public function getKastvakken(Request $request){
      $taken = ProjectTaken::where('active', 1)->where('completed', 0)->with('project', 'custom')->get();
      foreach($taken as $taakid => $taak){
        foreach($taak->custom as $row){
          if($row->keyword == 'kastvak' && $row->value != null){
            $taak->kastvak = $row->value;
          }
        }
        if(!$taak->kastvak){
          $taken->forget($taakid);
        }
      }
      $opmerkingen = KastvakOpmerkingen::get();
      return response()->json([
        "taken" => resetIndex($taken),
        "opmerkingen" => $opmerkingen
      ], 201);
    }

    public function kastvakOpmerking(Request $request){
      KastvakOpmerkingen::updateOrInsert([
        'kastvaknummer' => $request->kastvak
      ],[
        'opmerking' => $request->opmerking
      ]);
      return response(null, 201);
    }
}
