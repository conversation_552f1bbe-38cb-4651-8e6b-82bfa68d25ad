<?php

namespace App\Http\Controllers\Api;

use App\ApiPosts;
use App\ChecklistsDetails;
use App\ChecklistsDetailsValues;
use App\ChecklistsFiles;
use App\ChecklistsKeywordsFiles;
use App\ChecklistsTemplatesKeywords;
use App\Classes\search\SearchManager;
use App\Device;
use App\ExplorerFiles;
use Storage;
use App\User;
use App\Aanvragen;
use App\Checklists;
use App\ChecklistsKeywords;
use App\ChecklistsTemplates;
use App\Klanten;
use App\OffertesDatasets;
use App\OffertesDatasetsItems;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ChecklistsController extends Controller{
  public function get(Request $request){
    try{
      $model = new Checklists();
      $model = $model->orderBy($request->order_by ?? 'checklistnummer', $request->order_direction ?? 'asc')->with('klant', 'user', 'keywords', 'template', 'project','_bv');

      //search
      if(isset($request->ids)){
        $model = $model->whereIn('id', $request->ids);
      }

      //Permissions
      if(!hasPermission("Alle checklists bekijken")){
        return response(null, 403);
      }

      //Filters
      if(isset($_GET['active'])){
          $model = $model->where(["active" => $_GET['active']]);
      }
      if (isset($request->id)) {
        $model = $model->where("id", $request->id);
      }
      if(isset($request->template)){
        $model = $model->where('template_id', $request->template);
      }
      if(isset($request->active)){
         $model = $model->where("active", $request->active);
      }
      if(isset($request->project)){
          if(is_array($request->project)){
              $model = $model->whereIn("project_id", $request->project);
          }
          else{
              $model = $model->where("project_id", $request->project);
          }
      }
      if(isset($request->klant)){
          if(is_array($request->klant)){
              $model = $model->whereIn("klant_id", $request->klant);
          }
          else{
              $model = $model->where("klant_id", $request->klant);
          }
      }
      if(isset($request->user)){
          if(is_array($request->user)){
              $model = $model->whereIn("user_id", $request->user);
          }
          else{
              $model = $model->where("user_id", $request->user);
          }
      }
      if(isset($request->bv)){
          if(is_array($request->bv)){
              $model = $model->whereIn("bv", $request->bv);
          }
          else{
              $model = $model->where("bv", $request->bv);
          }
      }
        if(isset($request->start)){
            $model = $model->where('datum', '>=', $request->start);
        }
        if(isset($request->end)){
            $model = $model->where('datum', '<=', $request->end);
        }

      //pagination
      $all_ids = $model->pluck('id')->toArray();
      if(isset($request->per_page) && isset($request->page)){
        $model->skip($request->per_page * ($request->page - 1))->take($request->per_page);
      }
      $checklists = $model->get();
      $current_ids = $checklists->pluck('id')->toArray();

      foreach($checklists as $checklist){
        $checklist->_locatie = $checklist->_locatie();
      }


      return response([
        'checklists' => $checklists,
        'all_ids' => $all_ids ?? [],
        'current_ids' => $current_ids ?? [],
      ], 200);
    }
    catch(\Exception $e){
      return catchResponse($e);
    }

  }
  public function search(Request $request){
    try{
      $search = new SearchManager($request->search);
      $checklists = $search->checklists();

      return response($checklists);
    }
    catch(\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
    public function templates(Request $request){
        $templates = ChecklistsTemplates::with('_bv')->where('active', 1)->orderBy('name')->get();

        $device = Device::where('api_token', $request->api_token)->firstOrFail();
        $checklists = Checklists::where(['active' => 1])->with('_bv', 'klant', 'user', 'keywords', 'template')->orderBy("created_at", "DESC")->get();

        if(!isset(userPermissions($device->user_id)['Alle checklists bekijken'])){
            $checklists = $checklists->where('uiser_id', $device->user_id);
        }

        foreach($checklists as $row){
            $row->keywords_by = keyBy($row->keywords, 'keyword');
        }

        return response()->json([
          'templates' => $templates,
          'checklists' => $checklists,
        ], 201);
    }
    public function show(Request $request){
        $checklist = Checklists::where('id', $request->checklist)->with("klant", "user", "files", "details", "files", "project", "klant_locatie", "template")->first();
        $keywords = ChecklistsKeywords::where('checklist_id', $checklist->id)->with('user', 'keyword_item', 'files')->get();
        $aanvragen = Aanvragen::get()->keyBy('id');

        $arr = [];
        foreach($keywords as $keyword){
            if(!isset($keyword->keyword_item) || !isset($keyword->keyword_item->parent_value)){
                $arr[] = $keyword;
                continue;
            }

            foreach($keywords as $row){
                if($row->keyword == $keyword->keyword_item->parent_keyword){
                    if($row->value == $keyword->keyword_item->parent_value){
                        $arr[] = $keyword;
                        continue 2;
                    }
                }
            }
        }
        $checklist->keywords = $arr;

        $checklist->_locatie = $checklist->_locatie();

        return response()->json([
            "checklist" => $checklist,
            "aanvragen" => $aanvragen,
          ], 201);
    }
    public function new(Request $request){
        $template = ChecklistsTemplates::where('id', $request->template)->with('_bv', 'keywords', 'details')->first();

        $klanten = Klanten::where('status', 1)->with('locaties')->get();
        $projecten = getProjecten();

        $aanvragen = Aanvragen::where('active', 1)->whereNotNull('klant_id')->get()->groupBy('klant_id');
        $datasets = OffertesDatasets::with('items')->get()->keyBy('id');
        $datasetItems = OffertesDatasetsItems::get();

        foreach($klanten as $klant){
            $titel = $klant->naam ?? $klant->contactpersoon_voornaam . " " . $klant->contactpersoon_achternaam;
            $klant->titel = $titel;

            foreach($klant->locaties as $locatie){
                $locatie->adres = $locatie->addressLine();
            }
        }

        return response()->json([
          "template" => $template,
          "klanten" => $klanten,
          "projecten" => $projecten,
          "aanvragen" => $aanvragen,
          "datasets" => $datasets,
          "datasetItems" => $datasetItems,
        ], 201);
    }
    public function store(Request $request){
        try{
            $user = User::where('id', $request->user)->first();

            if(!isset($request->checklist) || $request->checklist === 'null'){
                $template = ChecklistsTemplates::where('id', $request->template)->first();
                $count = Checklists::count();
                $nummer = 'CK' . date("Y") . str_pad($count + 1, 4, 0, STR_PAD_LEFT);
                $id = Checklists::insertGetId([
                  "template_id" => $template->id,
                  "klant_id" => $request->klant ?? null,
                  "klant_locatie_id" => $request->klant_locatie_id ?? null,
                  "project_id" => $request->project ?? null,
                  "user_id" => $request->user,
                  "aanvragen" => $request->aanvragen ?? null,
                  "checklistnummer" => $nummer,
                  "bv" => $template->bv,
                  "datum" => Carbon::now(),
                  "token" => randomString(25),
                  "created_at" => Carbon::now(),
                ]);
                $log = $user->name . " " . $user->lastname . "  heeft de checklist '" . $nummer . "' uitgebracht (App).";
            }
            else{
                $id = $request->checklist;
                $checklist = Checklists::where('id', $id)->with("details")->first();
                Checklists::where('id', $id)->update([
                  "klant_id" => $request->klant ?? $checklist->klant_id,
                  "klant_locatie_id" => $request->klant_locatie_id ?? $checklist->klant_locatie_id,
                  "project_id" => $request->project ?? $checklist->project_id,
                  "aanvragen" => $request->aanvragen ?? $checklist->aanvragen,
                ]);
                $log = $user->name . " " . $user->lastname . "  heeft de checklist '" . $checklist->checklistnummer . "' gewijzigd (App).";
            }

            $this->storeChecklistItems($id, $request);

            $this->clearDetails($id);
            foreach(json_decode($request->details ?? '[]') as $detail){
                $detailId = ChecklistsDetails::insertGetId([
                  "checklist_id" => $id,
                  "detail_template_id" => $detail->detailTemplate->id,
                  "keyword" => $detail->detailTemplate->keyword,
                  "name" => $detail->detailTemplate->name,
                  "title" => $detail->title,
                  "image" => $detail->image->src,
                  "description" => $detail->description,
                  "adjustment" => $detail->adjustment,
                  "price" => $detail->price,
                  "created_at" => Carbon::now(),
                ]);
                foreach($detail->fields as $row){
                    ChecklistsDetailsValues::insert([
                      "detail_id" => $detailId,
                      "name" => $row->name,
                      "keyword" => $row->keyword,
                      "value" => $row->value,
                      "created_at" => Carbon::now(),
                    ]);
                }
            }

            $this->clearOldFiles($id);
            foreach(json_decode($request->bestanden ?? '[]') as $file){
                if(!isset($file->src)){
                    continue;
                }
                ChecklistsFiles::insert([
                  "checklist_id" => $id,
                  "src" => $file->src,
                  "name" => $file->name,
                ]);
            }

            $checklist = Checklists::where('id', $id)->with('klant', 'user', '_bv', 'keywords', "details", "files", "project")->first();
            $checklist->setVervolgAfspraak();

          if(getSettingCheckbox('checklist_custom_project_flow') && $checklist->project){
            $stappen = getSettingJson('checklist_custom_project_flow_stappen');
            $laatstestap = end($stappen);

            if ($checklist->template_id == $laatstestap['template']){
              $checklist->finnishProjectFlow();
            }
          }

            actLog($log, $user->id, 35);
            return response()->json(['cknummer' => $nummer ?? null, 'checklist' => $checklist], 201);
        }
        catch(\Exception $e){
            return catchResponse($e);
        }
    }
    public function edit(Request $request){
        $checklist = Checklists::where('id', $request->checklist)->with('klant.locaties', 'klant_locatie', 'user', '_bv', 'keywords', "details", "files")->first();
        if(isset($checklist->klant)){
            $checklist->klant->titel = $checklist->klant->naam ?? $checklist->klant->contactpersoon_voornaam . " " . $checklist->klant->contactpersoon_achternaam;
        }
        return response()->json([
          "checklist" => $checklist,
        ], 201);
    }
    public function upload(Request $request){
        try{
            if($request->hasFile('file')){
                $file = $request->file('file');
                $string = randomString(10) . "." . $file->extension();

                Storage::disk('client')->putFileAs("checklists/files/", $file, $string);

                $success = true;
                $message = 'Upload gelukt.';
                $url = "checklists/files/" . $string;
            }
            else{
                $success = false;
                $message = 'Upload ging helaas mis, probeer opnieuw.';
                $url = '';
            }
            return response()->json(
              [
                'success' => $success,
                'message' => $message,
                'url' => $url
              ], 201);
        }
        catch(\Exception $e){
            actError($e);
            abort(500);
        }
    }
    public function complete(Request $request){
        try{
            $app = isset($request->api_token) ? ' (App)' : '';

            Checklists::where('id', $request->checklist)->update(['active' => 0]);
            $checklist = Checklists::where('id', $request->checklist)->first();

            $log = User::name() . "  heeft de checklist '" . $checklist->checklistnummer . "' afgerond$app.";
            actLog($log, User::id(), 35);


            return response(null, 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }
    public function activate(Request $request){
        try{
            $app = isset($request->api_token) ? ' (App)' : '';

            Checklists::where('id', $request->checklist)->update(['active' => 1]);
            $checklist = Checklists::where('id', $request->checklist)->first();

            $log = User::name() . "  heeft de checklist '" . $checklist->checklistnummer . "' teruggezet$app.";
            actLog($log, User::id(), 35);


            return response(null, 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }
    public function previousChecklistLocatie(Request $request){
        $checklist =  Checklists::where(['klant_locatie_id' => $request->locatie_id, 'klant_id' => $request->klant_id, 'template_id' => $request->template_id])->with('keywords')->orderBy('datum','DESC')->first();
        $template = ChecklistsTemplates::where('id', $checklist->template_id ?? null)->with('_bv', 'keywords', 'details')->first();
        return response()->json([
          "checklist" => $checklist ?? [],
          "template" => $template ?? [],
        ], 200);
    }

    private function storeChecklistItems($id, $request){
        ChecklistsKeywords::where('checklist_id', $id)->delete();
        ChecklistsKeywordsFiles::where('checklist_id', $id)->delete();

        $mailkeys = [];

        $items = json_decode($request->items);
        foreach($items ?? [] as $item){
          $key = ChecklistsTemplatesKeywords::where('keyword', $item->keyword)->first();

            $value = $item->value ?? null;

            if($item->type == 'date'){
                $value = Carbon::parse($value)->format("Y-m-d");
            }
            if($item->type == 'time'){
                $value = Carbon::parse($value)->format("H:i");
            }
            if($item->type == 'custom_row'){
                foreach($item->value as $row){
                    foreach($row as $element){
                        if($element->type == 'date'){
                            $element->value = Carbon::parse($element->value)->format("Y-m-d");
                        }
                    }
                }
                $value = json_encode($value);
            }
            if($item->type == 'signature'){
                if(substr($item->value, 0, 9) == 'signature'){
                    $value = $item->value;
                }
                else{
                    if(isset($item->value) && $item->value != null){
                        $string = str_replace("data:image/png;base64,", "", $item->value);
                        $string = str_replace(' ', '+', $string);
                        $base = base64_decode($string);
                        $path = randomString(25) . ".png";
                        Storage::disk("client")->put('signatures/' . $path, $base);
                        $value = 'signatures/' . $path;
                    }
                }
            }
            if($item->type == 'image'){
              $images = $value;
              $value = null;
            }

          $checklist_keyword_id = ChecklistsKeywords::insertGetId([
              "checklist_id" => $id,
              "name" => $item->name ?? null,
              "value" => $value ?? null,
              "keyword" => $item->keyword ?? null,
              "type" => $item->type,
              "data" => isset($item->data) ? json_encode($item->data) : null,
              "file" => $item->src ?? null,
              "opmerking" => $item->opmerking_value ?? null,
              "created_at" => Carbon::now(),
            ]);

          if($item->type == 'image'){
            foreach($images ?? [] as $file){
              ChecklistsKeywordsFiles::insert([
                "checklist_id" => $id,
                "checklist_keyword_id" => $checklist_keyword_id,
                "file_id" => $file->id,
              ]);
              $file = ExplorerFiles::where('id', $file->id)->first();
              if ($file->path == '/Canvas/temp' || $file->path == '/Checklists/temp'){
                $file->move("/Checklists/({$id}");
              }
              else{
                $file->copy("/Checklists/({$id}");
              }
            }
          }

          if($key->mail_on_value){
            $mailkeys[] = $checklist_keyword_id;
          }

        }

      foreach ($mailkeys as $mailkey){
        $keyword = ChecklistsKeywords::where('id', $mailkey)->first();
        $keyword->mailOnValue();
      }
    }
    private function clearDetails(int $id): void{
        $details = ChecklistsDetails::where("checklist_id", $id)->get();
        foreach($details ?? [] as $detail){
            if(isset($detail->image)){
                $request = (new Request())->merge([
                  'file' => $detail->file,
                ]);
                (new FileController())->delete($request);
            }
            ChecklistsDetailsValues::where("detail_id", $detail->id)->delete();
            ChecklistsDetails::where("id", $detail->id)->delete();
        }
    }
    private function clearOldFiles(int $id): void{
        ChecklistsFiles::where("checklist_id", $id)->delete();
    }

}
