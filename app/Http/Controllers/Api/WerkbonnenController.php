<?php

namespace App\Http\Controllers\Api;

use App\BV;
use App\Classes\search\SearchManager;
use App\ExplorerFiles;
use App\Klanten;
use App\Mail\BlankMail;
use App\Mail\NewWerkbon;
use App\OffertePlanning;
use App\Offertes;
use App\OffertesDatasetsItems;
use App\Offerteteksten;
use App\Planning;
use App\Project;
use App\ProjectTaken;
use App\User;
use App\Werkbonnen;
use App\WerkbonnenListInputTemplates;
use App\WerkbonnenProjectTaken;
use App\WerkbonnenTemplateKeywords;
use App\WerkbonnenTemplates;
use App\WerkbonRegels;
use App\WerkbonTeksten;
use http\Env\Response;
use http\Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Route;
use DB;
use Storage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\WerkbonOpvolgTeksten;

class WerkbonnenController extends Controller {
  private $relations = ['project', 'offerte', 'klant', 'user', 'template', 'vestiging'];
  private $select_finish = false;

  public function get(Request $request){
    try{
        $relations_build = generateLiteRelations(($request->relations ?? $this->relations), $request);
        $relations = $relations_build['relations'] ?? [];
        $relations_lite = $relations_build['relations_lite'] ?? [];

        $model = new Werkbonnen();
        $model = $model->with($relations)->with($relations_lite)->orderByRaw(($request->sort_by ?? 'created_at').' '.($request->sort_type ?? 'DESC'));

        //Select
        if (isset($request->select)){
            $model = $model->select($request->select);
        }

        //Permissions
        if(!hasPermission('Werkbonnen beheren')){
            $model = $model->where('user_id', User::id());
        }
        if (Auth::user()->hasPermissionTo("Werkbonnen inzien van gekoppelde projecten")) {
          $model = $model->whereHas('project.custom', function ($query) {
            $query->where(['type' => 'user', 'value' => User::id()]);
          });
        }

        //Filters
        if (isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);

            $all_ids = $model->pluck('id')->toArray();
            if(isset($request->paginate) && isset($request->page)) {
                $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
            }
            $werkbonnen = $model->get();
        }
        else{
            if(isset($request->bv)){
                $model = $model->where("bv", $request->bv);
            }
            if(isset($request->user)){
                $model = $model->where("user_id", $request->user);
            }
            if(isset($request->klant)){
                $model = $model->where("klant_id", $request->klant);
            }
            if(isset($request->vestiging)){
                $model = $model->where("vestiging_id", $request->vestiging);
            }
            if(isset($request->template)){
                $model = $model->where("template_id", $request->template);
            }
            if(isset($request->stadium)){
                $model = $model->whereIn('status', Werkbonnen::stadiumStatussen($request->stadium));
            }
            if(isset($request->status)){
                if(is_array($request->status)){
                    $model = $model->whereIn("status", $request->status);
                }
                else{
                    $model = $model->where("status", $request->status);
                }
            }
            if (isset($request->start)) {
                $model = $model->whereRaw("STR_TO_DATE(datum, '%d-%m-%Y') >= ?", [$request->start]);
            }
            if (isset($request->end)) {
                $model = $model->whereRaw("STR_TO_DATE(datum, '%d-%m-%Y') <= ?", [$request->end]);
            }
            if(isset($request->project)){
                $model = $model->where("project_id", $request->project);
            }

            $all_ids = $model->pluck('id')->toArray();
            if(isset($request->paginate) && isset($request->page)) {
                $model->skip($request->paginate * ($request->page - 1))->take($request->paginate);
            }
            $werkbonnen = $model->get();
        }

        //Append data ( For JS ) & Replace lite relations
        foreach ($werkbonnen as $werkbon){
            $current_ids[] = $werkbon->id;

            foreach ($relations_lite as $relation_key => $query){
                $werkbon->setRelation(str_replace('_lite', '', $relation_key),  $werkbon[$relation_key]);
                unset($werkbon[$relation_key]);
            }

            $werkbon->facturen = $werkbon->facturen();
            $werkbon->stadium = $werkbon->stadium();
            $werkbon->color = $werkbon->color();
            $werkbon->_contact = $werkbon->_contact();

            if($werkbon->project){
              $werkbon->project->fullAdres = $werkbon->project->projectAdres();
            }
        }

        $werkbonnen = resetIndex($werkbonnen);
        return response()->json([
            'werkbonnen' => $werkbonnen,
            'total_count' => count($all_ids ?? []),
            'all_ids' => $all_ids ?? [],
            'current_ids' => $current_ids ?? [],
        ], 201);
    }
    catch (\Exception $e){
        actError($e);
        return response(['message' => $e->getMessage()], 500);
    }
  }
  public function search(Request $request){
      $search = new SearchManager($request->search);
      return response($search->werkbon(), 200);
  }

  public function index(Request $request){
    try {
      $templates = new WerkbonnenTemplates();

      if(getSettingValue('werkbon_templates_weergeven_obv_geplande_activiteiten') == 'ja')
      {
        // haal dagplanning van deze user op en kijk welke activiteiten zijn ingepland
        $planning = getPlanning(["where" => ["user_id" => $request->user, "datum" => date('Y-m-d')]]);
        $plannedActiviteitIds = array_map(function ($planningRow) {
          return $planningRow['legenda_id'];
        }, $planning);

        // Templates filteren
        $templates = $templates->where(function ($query) use ($plannedActiviteitIds) {
          $query->whereNull('activiteit_id')
            ->orWhereIn('activiteit_id', $plannedActiviteitIds);
        });
      }

      $templates = $templates->get();

      $model = Werkbonnen::with('klant', 'user', 'project.custom', 'vestiging', 'template', 'opvolg_values')->orderBy("created_at", "DESC");
      $keywordSettings = getSettingJson('werkbonnen_show_keywords_app');
      if(!empty($keywordSettings)){
        $keywords = array_reduce($keywordSettings, function ($carry, $item) {
          return array_merge($carry, $item);
        }, []);

        if (!empty($keywords)) {
          $model = $model->with(['keywords' => function ($query) use ($keywords) {
            $query->whereIn('keyword', $keywords)->whereNotNull('value');
          }]);
        }
      }

      if ((!getSettingCheckbox('werkbonnen_show_all') || !hasPermission('Werkbonnen beheren')) && !hasPermission("Werkbonnen inzien van gekoppelde projecten")) {
        $model = $model->where('user_id', $request->user ?? 0);
      }

      if ($request->project_id) {
        $request_project_id = $request->project_id;

        $model = $model->whereHas('project', function ($query) use ($request_project_id) {
            $query->where('id', $request_project_id);
          }
        );
      }

      if (hasPermission("Werkbonnen inzien van gekoppelde projecten")) {
        $model = $model->whereHas('project.custom', function ($query) {
            $query->where('type', 'user')
              ->where('value', User::id());
          }
        );
      }

      $werkbonnen = $model->get();

      foreach($werkbonnen as $werkbon){
        $werkbon->stadium = $werkbon->stadium();
        $werkbon->color = $werkbon->color();
      }


      return response()->json([
        "templates" => $templates,
        "werkbonnen" => resetIndex($werkbonnen),
        "planning" => $planning ?? [],
      ], 201);
    }
    catch (\Exception $e) {
      return catchResponse($e);
    }
  }

  public function getWerkbon(Request $request){
    $werkbon = Werkbonnen::where("id", $request->id)->with("klant", "project", "regels", 'values')->first();
    return response()->json(["werkbon" => $werkbon], 201);
  }
  public function edit(Request $request){
    try{
      $werkbon = Werkbonnen::where('id', $request->id)->with('project', 'project_taken', 'keywords.input.listInputTemplates')->firstOrFail();
      return response()->json([
        'werkbon' => $werkbon,
      ], 201);
    }
    catch (\Exception $e){ actError($e); }
    return response(null, 500);
  }

  public function newWerkbon(Request $request){
    $template = WerkbonnenTemplates::where("id", $request->id)->firstOrFail();
    $keywords = WerkbonnenTemplateKeywords::where('template_id', $template->id)->orderBy('order_index', 'ASC')->get();

    $projecten = Project::where("active", 1)->with('offerte', 'klant', 'uncompleted_taken', 'contactpersoon')->orderBy('created_at', 'desc')->get();

    if(getSettingValue('werkbon_projecten_alleen_opdracht') == 'ja'){
      $projecten = resetIndex($projecten->where('status', 'Opdracht'));
    }

    $opties = $keywords->where("type", "optie")->groupBy("index");


//    Exclude keywords with own type
    $keywords = resetIndex($keywords->whereNotIn('type', ['optie']));

    $data = [
      "template" => $template,
      "keywords" => $keywords,
      "opties" => $opties,
      "projecten" => $projecten,
      "klanten" => $template->project != '1' ? getKlanten(['with' => ['contactpersonen']]) : [],
    ];
    return response()->json($data, 201);
  }

  public function createWerkbon(Request $request){
    $template = WerkbonnenTemplates::where("id", $request->id)->firstOrFail();
    $keywords = WerkbonnenTemplateKeywords::where('template_id', $template->id)->with('listInputTemplates')->orderBy('order_index', 'ASC')->get();

    $query = Project::where("active", 1)->with('offerte', 'klant', 'uncompleted_taken', 'contactpersoon')->orderBy('created_at', 'desc');

    if (getSettingValue('werkbon_projecten_alleen_opdracht') == 'ja') {
      $query->where('status', 'Opdracht');
    }

    if (hasPermission("Gekoppelde projecten inzien")) {
      $query->whereHas('custom', function ($query) {$query->where(['type' => 'user', 'value' => Auth::user()->id]);});
    }

    $projecten = $query->limit(50)->get();

    $opties = $keywords->where("type", "optie")->groupBy("index");


//    Exclude keywords with own type
    $keywords = resetIndex($keywords->whereNotIn('type', ['optie']));

    $data = [
      "template" => $template,
      "keywords" => $keywords,
      "opties" => $opties,
      "projecten" => $projecten,
      "klanten" => $template->project != '1' ? getKlanten(['with' => ['contactpersonen']]) : [],
    ];
    return response()->json($data, 201);
  }

  public function afronden(Request $request){
    try{
      Werkbonnen::where('id', $request->id)->update(['status' => 'Afgerond']);

      $user = User::where("id", $request->user)->first();
      $werkbon = Werkbonnen::where('id', $request->id)->first();

      $log = $user->name." ".$user->lastname."  heeft de werkbon '".$werkbon->werkbonnummer."'  afgerond (App).";
      actLog($log, $user->id, 5);

      return response(null, 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function status(Request $request){
      try{
          $status = $request->status;
          $werkbon = Werkbonnen::where(['id' => $request->id])->firstOrFail();
          $log = User::name()." heeft de status van de werkbon $werkbon->werkbonnummer veranderd naar ".strtolower($status).".";

          Werkbonnen::where('id', $request->id)->update([
              'status' => $status,
              'active' => ($status == 'Verwijderd') ? 0 : 1,
              'status_changed_at' => Carbon::now(),
          ]);

          if($status == 'Afgerond' && getSettingValue('last_proj_bon_mail', '') != ''){
            $setting = getSettingJson('werkbon_opvolgstappen');
            $openstaand = ['Uitgebracht'];
            foreach($setting as $set){
              $openstaand[] = $set['status'] ?? null;
              $openstaand[] = $set['statusgereed'] ?? null;
            }
            $project = Project::where('id', $werkbon->project_id)->with(['werkbonnen' => function ($query) use($openstaand){$query->where('active', 1)->whereIn('status', $openstaand);}])->first();
            if($project && !count($project->werkbonnen)){
              $alleBonnen = Werkbonnen::where('project_id', $werkbon->project_id)->with('user')->where('active', 1)->get();
              $bv = BV::where('id', $werkbon->bv)->first();

              $html = 'De laatste openstaande werkbon (' . $werkbon->werkbonnummer . ') van project '. $project->projectnr . ' - ' . $project->projectnaam . ' is zojuist afgerond. <br><br>';
              $html .= 'Alle werkbonnen van dit project:<br> <table>';
              foreach($alleBonnen as $bon){
                $html .= '<tr><td>Werkbon: ' . $bon->werkbonnummer . '</td><td>status: ' . $bon->status . '</td><td>Aangemaakt door: ' . $bon->user->name . ' ' . $bon->user->lastname . '</td></tr>';
              }
              $html .= '</table>';

              $html .= '<br><br>Xx,<br> Tessa!';

              Mail::to(getSettingValue('last_proj_bon_mail'))->send( new BlankMail(
                $bv,
                '<EMAIL>',
                $bv->name,
                'Laatste openstaande bon van project '.$project->projectnr. ' is afgerond',
                $html,
              ));
            }
          }

          actLog($log, User::id(), 5);
          return response(null, 200);
      }
      catch (\Exception $e){
          actError($e);
          return response(['message' => $e->getMessage()], 500);
      }
  }

  public function opvolgen(Request $request){
    try{
      $werkbon = Werkbonnen::where('id', $request->id)->firstOrFail();
      $edit = WerkbonOpvolgTeksten::where('werkbon_id', $werkbon->id)->exists();
      WerkbonOpvolgTeksten::where('werkbon_id', $werkbon->id)->delete();
      $setting = getSettingJson('werkbon_opvolgstappen')[$request->stapindex] ?? null;
      $datedone = find('type', 'datedone', $setting['velden'])['naam'] ?? null;
      foreach($request->data as $row){
        $name = find('keyword', $row['name'], $setting['velden'])['naam'] ?? null;
        WerkbonOpvolgTeksten::insert([
          'werkbon_id' => $werkbon->id,
          'titel' => $name,
          'value' => $row['value'],
          'date_done' => $name == $datedone,
        ]);
      }
      $user = Auth::user();
      WerkbonOpvolgTeksten::insert([
        'werkbon_id' => $werkbon->id,
        'titel' => 'Opgevolgd door',
        'value' => $user->name .' '. $user->lastname,
        'date_done' => 0,
      ]);
      WerkbonOpvolgTeksten::insert([
          'werkbon_id' => $werkbon->id,
          'titel' => 'Opgevolgd op',
          'value' => Carbon()->format('d-m-Y'),
          'date_done' => 0,
        ]);
      $werkbon->opvolgMail($edit);
      $this->status($request);
      return response(null, 200);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }


  public function offerteContent(Request $request){
    $teksten = Offerteteksten::where("offerte_id", $request->offerteId)->get()->keyBy("keyword");
    $planning = OffertePlanning::where("offerte_id", $request->offerteId)->get();
    $data = [
      "planning" => $planning,
      "teksten" => $teksten
    ];
    return response()->json($data, 201);
  }

  public function store(Request $request){
    try{
      $handtekeningen = [];
      $project = Project::where("id", $request->projectId)->first();
      $template = WerkbonnenTemplates::where('id', $request->template)->first();

      $bv = BV::where("id", $request->bv)->first();

      $werkbonnummer = Werkbonnen::werkbonnummer(($bv ?? firstBvObject()), ($project->id ?? null));

      if(isset($request->canvas)){
        foreach(json_decode($request->canvas) as $canvas){

          if(isset($canvas->url)){
            $handtekeningen[] = $canvas->url;
            continue;
          }

          $base = str_replace("data:image/png;base64,","",$canvas->src);
          $data = base64_decode($base);
          $string = randomString(7).".png";
          $handtekeningen[] = $string;
          Storage::disk('client')->put("werkbonnen/handtekeningen/".$string, $data);

        }
      }

      $betalingsmethode = null;
      if(isset($request->betalingsoptie) && $request->betalingsoptie != "null"){
        $betalingsmethode = $request->betalingsoptie;
      }
      if($betalingsmethode == 'Pin' || $betalingsmethode == 'Contant' || $betalingsmethode == 'Online betaald'){
        $gefactureerd = 1;
        $status = 'Afgerond';
      }

      $klantSignature = null;
      if(isset($request->klantSignature) && $request->klantSignature != 'null'){
        $klantSignature = storeBaseSignature($request->klantSignature);
      }

      if(isset($request->edit) && $request->edit != 'null'){
        $werkbon = Werkbonnen::where("id", $request->edit)->with("project", "user")->first();
        Werkbonnen::where('id', $werkbon->id)->update([
            "user_id" => $request->user ?? null,
            "klant_id" => json_decode($request->klantId),
            "contactpersoon_id" => json_decode($request->contactId),
            "vestiging_id" => json_decode($request->vestigingId),
            "handtekeningen" => json_encode($handtekeningen) ?? null,
            "files" => $request->images ?? '[]',
            "betalingsoptie" => $betalingsmethode,
            "datum" => Carbon::now()->format("d-m-Y"),
            "opmerking" => (isset($request->opmerking) && $request->opmerking != "null") ? $request->opmerking : null,
            "gefactureerd" => $gefactureerd ?? 0,
            "status" => $status ?? 'Uitgebracht',
            "klant_signature" => $klantSignature,
            "signed_at" => isset($klantSignature) ? Carbon::now() : null,
            "updated_at" => Carbon::now(),
        ]);
        $werkbon->refresh();
      }
      else{
        $werkbonId = Werkbonnen::insertGetId([
          "werkbonnummer" => $werkbonnummer,
          "template_id" => $request->template,
          "bv" => $request->bv ?? firstBv(),
          "user_id" => $request->user ?? null,
          "klant_id" => json_decode($request->klantId),
          "contactpersoon_id" => json_decode($request->contactId),
          "project_id" => json_decode($request->projectId),
          "vestiging_id" => json_decode($request->vestigingId),
          "handtekeningen" => json_encode($handtekeningen) ?? null,
          "files" => $request->images ?? '[]',
          "betalingsoptie" => $betalingsmethode,
          "datum" => Carbon::now()->format("d-m-Y"),
          "opmerking" => (isset($request->opmerking) && $request->opmerking != "null") ? $request->opmerking : null,
          "gefactureerd" => $gefactureerd ?? 0,
          "status" => $status ?? 'Uitgebracht',
          "klant_signature" => $klantSignature,
          "signed_at" => isset($klantSignature) ? Carbon::now() : null,
          "token" => randomString(25),
          "created_at" => Carbon::now(),
        ]);
        $werkbon = Werkbonnen::where("id", $werkbonId)->with("project", "user")->first();
      }

      $requestfiles = json_decode($request->images);
      foreach($requestfiles as $file){
        $explorerfile = ExplorerFiles::where('src', $file)->first();
        ExplorerFiles::moveFile($explorerfile->id, "werkbonnen/($werkbon->id)");
      }

      $this->clearWerkbon($werkbon->id);
      $this->storeKeywords($request, $werkbon->id);
      $werkbon->finishWerkbon($project, $this->select_finish);

      if($request->offerteAantalPersonen){
        WerkbonTeksten::insert([
          "werkbon_id" => $werkbon->id,
          "input_id" => null,
          "keyword" => "offerteAantalPersonen",
          "titel" => null,
          "type" => "offerte_aantal_personen",
          "value" => $request->offerteAantalPersonen,
          "created_at" => Carbon::now(),
        ]);
      }
      if(isset($request->planningRegels)){
        $row = json_decode($request->planningRegels);
        WerkbonRegels::insert([
          "werkbon_id" => $werkbon->id,
          "naam" => $row->naam ?? "",
          "aantal" => $row->aantal ?? 0,
          "prijs" => $row->prijs ?? 0,
          "btw" => $row->btw ?? 0,
          "created_at" => Carbon::now(),
        ]);
      }
      if(isset($project)){
        if(getSettingValue('werkbon_project_complete') != 'nee'){
          $project->active = 0;
          $project->status = 'Afgerond';
          $project->save();
        }
        foreach(json_decode($request->projectTaken ?? '[]') as $id){
          if(getSettingValue('werkbon_projecttaken_complete') != 'nee'){
            ProjectTaken::where('id', $id)->update(['completed' => 1]);
          }
          WerkbonnenProjectTaken::insert([
            'werkbon_id' => $werkbon->id,
            'taak_id' => $id,
          ]);
        }
      }
      if(json_decode($request->planningId ?? null)){
        Planning::where('id', $request->planningId)->update(['completed' => 1]);
      }

      $this->sendmail($werkbon);

      $user = User::where("id", $request->user)->first();
      $log = $user->name." ".$user->lastname."  heeft de werkbon '".$werkbon->werkbonnummer."'  uitgebracht (App).";
      actLog($log, $user->id, 5);

      return response()->json([],201);
    }
    catch (\Exception $e){ return catchResponse($e); }
  }

  public function clearWerkbon($id){
    WerkbonTeksten::where('werkbon_id', $id)->update(['werkbon_id' => '-'.$id]);
    WerkbonRegels::where('werkbon_id', $id)->update(['werkbon_id' => '-'.$id]);
  }
  public function storeKeywords($request, $id){
    foreach(json_decode($request->inputs ?? '[]') as $row){

      $value = $row->value;

      if($row->type == 'list_select'){$value = json_encode($row->value);}
      if($row->type == 'list_input'){$value = json_encode($row->value);}
      if($row->type == 'images'){
        $value = json_encode($row->value);
        foreach($row->value as $file){
          $explorerfile = ExplorerFiles::where('src', $file)->first();
          ExplorerFiles::moveFile($explorerfile->id, "/werkbonnen/($id)");
        }
      }
      if($row->type == 'image'){
        $explorerfile = ExplorerFiles::where('src', $value)->first();
        ExplorerFiles::moveFile($explorerfile->id, "/werkbonnen/($id)");
      }
      if($row->type == 'signature'){
        $value = storeBaseSignature($row->value);
      }
      if($row->type == 'time'){$value = Carbon::parse($row->value)->format("H:i");}
      if($row->type == 'date'){$value = Carbon::parse($row->value)->format("Y-m-d");}

      if($row->type == 'select_finish'){
        $keyword = WerkbonnenTemplateKeywords::where('id', $row->db)->first();

        if($value == json_decode($keyword->data)->finishValue){
          $this->select_finish = "finish";
        }elseif ($value == json_decode($keyword->data)->opvolgValue){
          $this->select_finish = "opvolgen";
        }
      }

      WerkbonTeksten::insert([
        "werkbon_id" => $id,
        "input_id" => $row->db ?? null,
        "keyword" => $row->keyword ?? null,
        "titel" => $row->titel ?? null,
        "type" => $row->type ?? null,
        "value" => $value,
        "created_at" => Carbon::now(),
      ]);
    }
  }

  public function storeListInputTemplate(Request $request){
    $keyword = WerkbonnenTemplateKeywords::where('id', $request->keyword_id)->first();
    $data = json_decode($keyword->data ?? '[]');
    $data->rows = [];

    foreach (($request->items[$keyword->id] ?? []) as $string => $v){
      if(isset($request->list_input_dataset_item_id[$string])){
        $datasetitem = OffertesDatasetsItems::where('id', $request->list_input_dataset_item_id[$string])->first();
        if($datasetitem){
          $datasetitem->value = json_decode($datasetitem->value);
        }
      }
      $data->rows[] = [
        'datasetitem' => $datasetitem ?? '',
        'name' => $request->list_input_name[$string] ?? '',
        'values' => $v,
      ];
    }

    $templateId = WerkbonnenListInputTemplates::insertGetId([
      'keyword_id' => $keyword->id,
      'name' => $request->name,
      'data' => json_encode($data),
    ]);

    return response()->json([
      'data' => $data,
      'keyword' => $keyword,
      'name' => $request->name,
      'templateId' => $templateId
      ], 201);
  }
  public function deleteListInputTemplate(Request $request) {
    WerkbonnenListInputTemplates::where('id', $request->id)->update(['active' => 0]);
    return response()->json(['status' => 'success']);
  }
  public function getListInputTemplate(Request $request){
    $templates = WerkbonnenListInputTemplates::where('id', $request->id)->first();
    return response()->json($templates, 201);
  }

  public function inzien(Request $request){
    try{
      $offerteTeksten = [];

      $werkbon = Werkbonnen::with('regels', 'project_taken', 'vestiging', 'template')->where("id", $request->werkbonId)->first();
      $keywords = WerkbonTeksten::where('werkbon_id', $werkbon->id)->with('input')->get();
      $opties = $keywords->where('type', 'optie');

      //    Exclude keywords with own var
      $keywords = resetIndex($keywords->whereNotIn('type', ['optie']));

      $db = DB::table("werkbon_template_inputs")->get()->keyBy("id");
      $klant = Klanten::where("id", $werkbon->klant_id)->first();
      $project = Project::where("id", $werkbon->project_id)->first();
      $df = WerkbonTeksten::where("werkbon_id", $werkbon->id)->where("type", "offerte_aantal_personen")->first();
      $user = User::where("id", $werkbon->user_id)->first();
      $bvs = BV::get()->keyBy("id");



      foreach ($opties as $optie){
        $temp[$optie->input->index][] = $optie;
      }
      $opties = $temp ?? [];

      if($project){
        $offerte = Offertes::where("offertenummer", $project->projectnr)->with("template")->with("activiteiten")->first();
      }
      if(isset($offerte)){
        $offerteTeksten = Offerteteksten::where("offerte_id", $offerte->id)->get()->keyBy("keyword");
      }
      if(isset($df)){
        $defPersonen = json_decode($df->value);
      }

      $data = [
        "werkbon" => $werkbon,
        "keywords" => $keywords,
        "opties" => $opties,
        "db" => $db,
        "klant" => $klant,
        "project" => $project,
        "user" => $user,
        "offerte" => $offerte ?? null,
        "offerteTeksten" => $offerteTeksten ?? [],
        "bvs" => $bvs,
        "customInfo" => json_decode(getSettingValue("werkbon_offerte_info")) ?? [],
        "defPersonen" => $defPersonen ?? null,
      ];
      return response()->json($data, 201);
    }
    catch (\Exception $e){ return catchResponse($e); }

  }
  public function upload(Request $request){
    try{
      if(!$request->hasFile('file')){
        return response()->json(
          [
            'success' => false,
            'message' => 'Upload ging helaas mis, probeer opnieuw.',
            'url' => '',
          ], 201);
      }
      $file = $request->file('file');
      $file = ExplorerFiles::insertFromRequest([
        'file' => $file,
        'name' => randomString(5).'_'.$file->getClientOriginalName(),
        'path' => "/werkbonnen/temp",
        'description' => null,
      ]);

      return response()->json([
          'success' => true,
          'message' => 'Upload gelukt.',
          'url' => $file->src,
      ], 201);
    }
    catch (\Exception $e){
      actError($e);
      return response()->json([
          'success' => false,
          'message' => 'Upload ging helaas mis, probeer opnieuw.',
          'url' => '',
      ], 201);
    }
  }
  public function urenregistratie(Request $request){
    $werkbonnen = Werkbonnen::whereDate('created_at', $request->date)->where('user_id', $request->user)->with('keywords', 'project', 'project_taken')->get();
    return response()->json([
      'werkbonnen' => $werkbonnen,
    ], 201);
  }

  public function sendSignature(Request $request){
    try{
      $temp = explode(",",str_replace(" ","",$request->emails));
      $mails = [];
      foreach($temp as $mail){
        if(!isset($mail) || $mail == ""){ continue; }

        if(!verifyEmail($mail)){
          return response()->json([
            'message' => "Ongeldige e-mailadres '".$mail."', werkbon is niet verstuurd!"
          ], 202);
        }
        $mails[] = $mail;
      }

      $werkbon = Werkbonnen::where('id', $request->id)->with('_bv', 'project', 'template', 'user', 'klant')->firstOrFail();
      $werkbon->status == 'Afgerond' ? $status = 'Afgerond' : $status = 'Verzonden';
      Werkbonnen::where('id', $werkbon->id)->update([
        'sent_to' => implode(', ', $mails),
        'sent_at' => Carbon::now(),
        'status' => $status,
      ]);

      if(!isset($werkbon->token)){
        $werkbon->token = randomString(25);
        $werkbon->save();
      }
      $btnContent = emailBtnContent(getSubdomein(), 'werkbonnen/ondertekenen/'.$werkbon->token, 'Bekijk werkbon');
      $message = $request->message ?? getSettingValue('werkbon_signature_content');

      if (strpos($request->message, '{btn-content}') === false){
        $message = $message.$btnContent;
      }
      else{
        $message = str_replace('{btn-content}', $btnContent, $message);
      }

      $message = str_replace("&lt;{klant}&gt;", ($werkbon->klant->contactpersoon_voornaam ?? '').' '.($werkbon->klant->contactpersoon_achternaam ?? ''), $message);
      $message = str_replace("&lt;{bv}&gt;", ($werkbon->_bv->name ?? ''), $message);
      $message = str_replace("&lt;{user}&gt;", ($werkbon->user->name ?? '').' '.($werkbon->user->lastname ?? ''), $message);

      $templateSubject = $werkbon->template->mail_onderwerp;
      $templateSubject = str_replace('{werkbon-werkbonnummer}', ($werkbon->werkbonnummer ?? ''), $templateSubject);
      $templateSubject = str_replace('{werkbon-projectnaam}', ($werkbon->project->projectnaam ?? ''), $templateSubject);

      $subject = $request->subject ?? $templateSubject;

      foreach ($mails as $mail){
        Mail::to($mail)->send(
          new BlankMail(
            $werkbon->_bv ?? firstBv(),
            $request->sender ?? getSettingValue('werkbon_email_sender', '<EMAIL>'),
            $werkbon->_bv->name ?? firstBv()->name,
            $subject,
            "<div class='werkbon-content' >$message</div>",
          )
        );
      }

      return response($mails, 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }

  public function sendMail($werkbon){
    try {
      $mail = getSettingValue('werkbon_email_new');
      $ccMail = (getSettingValue('werkbon_aanmaker_cc') == 'ja') ? $werkbon->user->email : null;

      $mail = trim($mail, " \t\n\r\0\x0B\"'");

      $emails = array_map('trim', explode(',', $mail));

      foreach($emails as $key => $email){
        if(!verifyEmail($email)){
          unset($emails[$key]);
        }
      }

      if (!$werkbon->template->klant_signature || ($werkbon->template->klant_signature && isset($werkbon->klant_signature))) {
        $mailBuilder = Mail::to($emails);

        if (verifyEmail($ccMail)) {
          $mailBuilder->cc($ccMail);
        }

        $mailBuilder->send(new NewWerkbon($werkbon, getSubdomein()));
      }
    }catch(\Exception $e){
      actError($e);
    }

  }

  //cronjob
    public function reminders() {
      if(!getSettingCheckbox('werkbonnen_reminder_mail')){return;}

      $setting['termijn'] = getSettingJson('werkbonnen_reminder_mail_termijn');

      $werkbonnen = Werkbonnen::where(['status' => 'Verzonden', 'active' => 1])->whereNull('reminded_at')
          ->whereDate('sent_at', '<=', Carbon::now()->subDays($setting['termijn']))
          ->with('klant', 'user')->get();

      foreach($werkbonnen as $werkbon){
          $this->sendReminderMail($werkbon, $setting);
          $werkbon->reminded_at = Carbon::now();
          $werkbon->save();
      }
    }

    private function sendReminderMail($werkbon, $setting){
        $setting['inhoud'] = getSettingValue('werkbon_mail_reminder_inhoud');

        $message = $setting['inhoud'];
        $message = str_replace("&lt;{werkbonnummer}&gt;", ($werkbon->werkbonnummer ?? ''), $message);
        $message = str_replace("&lt;{sent_at}&gt;", (CarbonDmy($werkbon->sent_at) ?? ''), $message);
        $message = str_replace("&lt;{gebruiker}&gt;", ($werkbon->user->fullName() ?? ''), $message);

        $btnContent = emailBtnContent(getSubdomein(), "werkbonnen/token/$werkbon->token", 'Bekijk Werkbon');
        $message .= $btnContent;

        Mail::to($werkbon->sent_to)->send(
            new BlankMail(
                $werkbon->_bv ?? firstBv(),
                getSettingValue('werkbonnen_reminder_mail_afzender' ) ?? '<EMAIL>',
                $werkbon->_bv->name ?? firstBv()->name,
                'Werkbon: '.$werkbon->werkbonnummer,
                    $message,
            )
        );
    }


}
