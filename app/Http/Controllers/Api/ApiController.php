<?php

namespace App\Http\Controllers\Api;
use App\Facturen;
use App\Landen;
use App\Project;
use App\Werkbonnen;
use App\Offertes;
use Auth;
use App\Calls;
use App\Clients;
use App\Klanten;
use App\MainSettings;
use App\Settings;
use App\SmsLog;
use Carbon\Carbon;
use Config;
use App\User;
use Route;
use App\News;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ApiController extends Controller {

  public function getSubdomain(Request $request){
    $subdomain = Clients::where('subdomain', $request->subdom)->first();
    if(!$subdomain){
      return response()->json(["status" => 0], 201);
    }
    return response()->json(["client" => $subdomain], 201);
  }

  public function all() {
    return News::orderBy('id', 'DESC')->with('files')->get();
  }

  public function data(Request $request){
    $data = [];
    $target = $request->target;

    if($target == "projecten"){
      foreach ($request->ids ?? [] as $id){
        $data[] = Project::where('id', $id)->with('vestiging')->first();
      }
    }
    if($target == "offertes"){
      foreach ($request->ids ?? [] as $id){
        $data[] = Offertes::where('id', $id)->with('project')->first();
      }
    }
    if($target == "werkbonnen"){
      foreach ($request->ids ?? [] as $id){
        $data[] = Werkbonnen::where('id', $id)->with('project', 'template')->first();
      }
    }
    if($target == "facturen"){
      foreach ($request->ids ?? [] as $id){
        $data[] = Facturen::where('id', $id)->with(['sign_files', 'klant'])->first();
      }
    }

    return response()->json(["data" => $data], 201);

  }

  public function storeSms(Request $request){
    SmsLog::insert([
      "user_id" => $request->user,
      "klant_id" => $request->klant,
      "number" => $request->number,
      "content" => $request->message,
      "created_at" => Carbon::now(),
    ]);

    $klant = Klanten::where("id", $request->klant)->with("offertes", "aanvragen", "projecten", "calls", "_bv", "werkbonnen", "rapporten", "sms")->first();
    $user = User::where("id", $request->user)->first();
    $titel = $klant->name ?? $klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam;
    $log = $user->name." ".$user->lastname."  een sms aan ".$titel." verzonden (App).";
    actLog($log, 29, $request->user);

    return response()->json([
      "klant" => $klant
    ], 201);
  }

  public function storeCall(Request $request){
    $id = Calls::insertGetId([
      "user_id" => $request->user,
      "klant_id" => $request->klantId,
      "nummer" => $request->number,
      "response" => $request->response,
      "created_at" => Carbon::now(),
    ]);
    $call = Calls::where("id", $id)->first();

    $klant = Klanten::where("id", $request->klantId)->first();
    $user = User::where("id", $request->user)->first();
    $titel = $klant->name ?? $klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam;
    $log = $user->name." ".$user->lastname." heeft de klant ".$titel." gebeld (App).";
    actLog($log, 29, $request->user);

    return response()->json(["call" => $call], 201);
  }

  public function storeCallOpmerking(Request $request){
    Calls::where("id", $request->callId)->update(["opmerking" => $request->opmerking]);
    return response(null, 201);
  }

  public function settings(){
    $settings = MainSettings::get()->keyBy('name');
    return response()->json(['settings' => $settings], 201);
  }

  public function storeSetting(Request $request){
    Settings::updateOrInsert(["naam" => $request->name], ["value" => $request->value]);
    return response(null, 201);
  }

  public function validateSession(){
    return response()->json([
      'auth' => Auth::check(),
    ], 201);
  }

  public function location(Request $request){
    if(!isset($request->postcode)){
      return response()->json(["status" => 0]);
    }

//    Define vars and response data
    $key = env('GOOGLE_MAPS');
    $address = urlencode($request->postcode);
    $url = "https://maps.googleapis.com/maps/api/geocode/json?language=nl&key=".$key;

    $response = [
      "status" => null,
      "data" => [
        "straat" => null,
        "huisnummer" => $request->huisnummer,
        "postcode" => $request->postcode,
        "plaats" => null,
        "gemeente" => null,
        "provincie" => null,
        "land" => null,
        "land_code" => null,
        "lat" => null,
        "lng" => null,
      ]
    ];

//    First curl => fetch all data except street name
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url."&address=".$address);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $data = json_decode(curl_exec($ch), true);
    curl_close($ch);

    if(!count($data["results"])){
      return response()->json(["status" => 0]);
    }

    $components = $data["results"][0]["address_components"];

//    Define lat and lng.
    $lat = $data["results"][0]["geometry"]["location"]["lat"];
    $lng = $data["results"][0]["geometry"]["location"]["lng"];
    $response["data"]["lat"] = $lat;
    $response["data"]["lng"] = $lng;

//    Put data into response
    foreach($components ?? [] as $component){
      $type = $component["types"][0] ?? null;
      if($type == "locality"){
        $response["data"]["plaats"] = $component["long_name"] ?? null;
      }
      elseif($type == "administrative_area_level_2"){
        $response["data"]["gemeente"] = $component["long_name"] ?? null;
      }
      elseif($type == "administrative_area_level_1"){
        $response["data"]["provincie"] = $component["long_name"] ?? null;
      }
      elseif($type == "country"){
        $response["data"]["land"] = $component["long_name"] ?? null;
      }
    }

    if(isset($lat) && isset($lng)){
//      Second curl => fetch street name from lat and lng.
      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, $url."&latlng=".$lat.",".$lng);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_POST, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
      $data = json_decode(curl_exec($ch), true);
      curl_close($ch);

//      Put street name into response data
      foreach($data["results"][0]["address_components"] ?? [] as $component){
        $type = $component["types"][0] ?? null;
        if($type == "route"){
          $response["data"]["straat"] = $component["long_name"] ?? null;
        }
      }
    }

	  if($response['data']['land']){
		 $land = Landen::where('naam', $response['data']['land'])->first();
		 $response['data']['land_code'] = $land->code ?? null;
	  }

    $response["status"] = 1;

    return response()->json($response, 201);
  }

}
