<?php

namespace App\Http\Controllers\Api;

use App\Inkoopbonnen;
use App\Leveranciers;
use App\Zenvoices\BtwCodes;
use App\Zenvoices\Kostendragers;
use App\Zenvoices\Producten;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;

class ZenvoicesController extends Controller{

    //general
    public function getAdministration(Request $request){
        try{
            $api = zenvoices();
            $response = $api->administrations($request->search);
            $this->handleResponse($response);

            return response([
                'suppliers' => $response->data
            ], 200);
        }
        catch(\Exception $e){ return catchResponse($e); }
    }

    //Leveranciers
    public function syncAccounts($bv_id = null){
        try {
            if (!$bv_id || !is_int($bv_id)) {
                $bv_id = Auth::user()->bv_id;
            }

            $api = zenvoices($bv_id);

            $done = false;
            $params = [
                'maxResultCount' => 250,
                'skipCount' => 0,
                'administrationId' => getSettingJson('zenvoices_credentials')[$bv_id]['administration']
            ];

            $response = new \stdClass();
            $response->data = [];

            while (!$done) {
                $apiResponse = $api->accountsGet($params);
                if (isset($apiResponse->data->items) && is_array($apiResponse->data->items)) {
                    $response->data = array_merge($response->data, $apiResponse->data->items);
                }

                if (!isset($response->status) && isset($apiResponse->status)) {
                    $response->status = $apiResponse->status;
                }

                if (count($apiResponse->data->items ?? []) < $params['maxResultCount']) {
                    $done = true;
                } else {
                    $params['skipCount'] += 250;
                }
            }

            $this->handleResponse($response);
            foreach($response->data as $account){
                $this->storeAccount($account, $bv_id);
            }

            return response([
                'suppliersAmount' => count($response->data ?? [])
            ], 200);
        }
        catch(\Exception $e) {
            return catchResponse($e);
        }
    }
    private function storeAccount($account, $bvId = null){
        //split straat en huisnummer
        $address = $account->accountAddress->addressLine1 ?? '';
        preg_match('/^(.+?)\s(\d+.*)$/', $address, $matches);
        $street = $matches[1] ?? '';
        $number = $matches[2] ?? '';


        Leveranciers::updateOrInsert([
            'zenvoices_id' => $account->externalId
        ], [
            'naam' => $account->name ?? null,
            'straat' => $street ?? null,
            'huisnummer' => $number ?? null,
            'plaats' => $account->accountAddress->city ?? null,
            'postcode' => $account->accountAddress->postalCode ?? null,
            'land' => $account->accountAddress->countryCode ?? null,
            'telefoonnummer' => $account->accountAddress->telephone ?? null,
            'email' => $account->accountAddress->emailAddress ?? null,
            'kvk' => $account->chamberOfCommerceNumber ?? null,
            'btw_nr' => $account->vatNumber ?? null,
            'crediteurnummer' => $account->code ?? null,
            'bv' => $bvId ?? getBv()->id ?? null,
        ]);
    }

    //producten
    public function syncProducten(){
        try {
            $api = zenvoices();
            $done = false;
            $params = [
                'maxResultCount' => 250,
                'skipCount' => 0,
                'administrationId' => getSettingJson('zenvoices_credentials')[Auth::user()->bv_id]['administration']
            ];

            $response = new \stdClass();
            $response->data = [];

            while (!$done) {
                $apiResponse = $api->productsGet($params);
                if (isset($apiResponse->data->items) && is_array($apiResponse->data->items)) {
                    $response->data = array_merge($response->data, $apiResponse->data->items);
                }

                if (!isset($response->status) && isset($apiResponse->status)) {
                    $response->status = $apiResponse->status;
                }

                if (count($apiResponse->data->items ?? []) < $params['maxResultCount']) {
                    $done = true;
                } else {
                    $params['skipCount'] += 250;
                }
            }

            $this->handleResponse($response);
            foreach($response->data as $product){
                $this->storeProduct($product);
            }
        }
        catch(\Exception $e) {
            return catchResponse($e);
        }
    }
    private function storeProduct($product){
        Producten::updateOrInsert([
            'zenvoice_id' => $product->externalId
        ], [
            'code' => $product->code,
            'naam' => $product->name,
            'eenheid' => $product->salesUnit,
            'prijs' => $product->salesPrice,
            'btw_code' => $product->purchaseTaxCode,
            'grootboek' => $product->purchaseLedgerAccountCode,
            'active' => $product->isActive
        ]);
    }

    //kostendragers
    public function syncCostUnits(){
        $api = zenvoices();
        $params = [
            'maxResultCount' => 250,
            'skipCount' => 0,
            'administrationId' => getSettingJson('zenvoices_credentials')[Auth::user()->bv_id]['administration']
        ];

        $apiResponse = $api->costUnitsGet($params);

        foreach($apiResponse->data->items as $costUnit){
            $this->storeCostUnit($costUnit);
        }
    }
    private function storeCostUnit($costUnit){
        Kostendragers::updateOrInsert([
            'external_id' => $costUnit->externalId
        ], [
            'naam' => $costUnit->name,
            'code' => $costUnit->code,
            'administration_id' => getSettingJson('zenvoices_credentials')[Auth::user()->bv_id]['administration'],
            'bv' => getBv()->id,
        ]);
    }

    //Btw codes
    public function syncBtwCodes(){
        $api = zenvoices();
        $params = [
            'maxResultCount' => 250,
            'skipCount' => 0,
            'administrationId' => getSettingJson('zenvoices_credentials')[Auth::user()->bv_id]['administration']
        ];

        $apiResponse = $api->taxCodesGet($params);

        foreach($apiResponse->data->items as $taxCode){
            $this->storeBtwCode($taxCode);
        }
    }
    private function storeBtwCode($taxCode){
        BtwCodes::updateOrInsert([
            'zenvoice_id' => $taxCode->externalId
        ], [
            'naam' => $taxCode->name,
            'code' => $taxCode->code,
            'soort' => $taxCode->type,
            'percentage' => ($taxCode->percentages[0]->rate * 100),
            'bv' => getBv()->id,
        ]);
    }

    //Inkoopbonnen
    //commitments
    public function uploadCommitment(Request $request){
        try{
            $inkoopbon = Inkoopbonnen::where('id', $request->id)->with('leverancier', 'regels', '_zenvoice_kostendrager')->firstOrFail();
            $description = $inkoopbon->regels->first()->naam ?? 0;

            if(isset($request->bv_id)){
                $bv_id = $request->bv_id;
            }else{
                $bv_id = Auth::user()->bv_id;
            }

            $data = [
                "code" => $inkoopbon->bonnummer,
                "reference" => $inkoopbon->bonnummer,
                "accountCode" => $inkoopbon->leverancier->crediteurnummer,
                "accountType" => "1",
                "administrationId" => (int)getSettingJson('zenvoices_credentials')[$bv_id]['administration'],
                "description" => $description,
                "currencyCode" => "EUR",
                "amount" => $inkoopbon->totaal()->excl ?? 0,
                "openAmount" => $inkoopbon->totaal()->excl ?? 0,
                "externalId" => (string)$inkoopbon->id ?? null,
            ];
            $api = zenvoices($bv_id);

            $response = $api->uploadCommitment($data);
            $this->handleResponse($response);

            $this->updateCommitmentZenvoiceId($inkoopbon, $response->data);

            return response([
                'inkoopbon' => $response->data
            ], 200);
        }
        catch(\Exception $e){return catchResponse($e);}
    }
    private function updateCommitmentZenvoiceId($inkoopbon, $zenvoiceBon){
        Inkoopbonnen::where('id', $inkoopbon->id)->update([
            'zenvoices_id' => $zenvoiceBon->reference
        ]);
    }

    //Utility
    private function handleResponse($response){
        if($response->status){
            return;
        }

        $response->data->error->message = $response->data->error->message . ' (' . $response->data->error->details  . ')';
        abort($response->code, $response->data->error->message);
    }

    //cronjob

    public function cronjobCommitmentSync(){
        try{
            if(!getSettingCheckbox('zenvoices_auto_sync_inkoopbonnen')){
                return;
            }

            foreach(getSettingJson('zenvoices_credentials') as $bv_id => $credentials){
                if($credentials['state'] != 'on'){
                    continue;
                }
                $inkoopbonnen = Inkoopbonnen::whereNull('zenvoices_id')->where(['status' => 'Afgerond', 'bv' => $bv_id])->get();
                foreach($inkoopbonnen as $inkoopbon){
                    $this->uploadCommitment(new Request(['id' => $inkoopbon->id, 'bv_id' => $inkoopbon->bv]));
                }
            }

        } catch(\Exception $e){
            return catchResponse($e);
        }
    }
    public function cronSyncAccounts(){
        if(!getSettingCheckbox('zenvoices_auto_sync_leverancier')){return;}

        foreach(getSettingJson('zenvoices_credentials') as $bv_id => $credentials){
            if($credentials['state'] != 'on'){continue;}
            $this->syncAccounts($bv_id);
        }
    }

}
