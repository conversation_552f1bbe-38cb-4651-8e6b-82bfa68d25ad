<?php

namespace App\Http\Controllers\Api;

use App\Verlof;
use App\Verlofreden;
use App\StatistiekenFacturatieGroepen;
use App\StatistiekenFacturatieItems;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\UrenRegistratie;

class StatistiekenController extends Controller
{
  public function getFacturatieGroepen()
  {
    $groepen = StatistiekenFacturatieGroepen::with("items")->get()->keyBy("id");
    return response()->json([
      'groepen' => $groepen,
    ], 202);
  }
  public function insertFacturatieGroepen(Request $request)
  {
    StatistiekenFacturatieGroepen::insert([
      "naam" => $request->name,
      "created_at" => Carbon::now(),
    ]);
    return response([], 201);
  }
  public function deleteFacturatieGroepen(Request $request)
  {
    StatistiekenFacturatieGroepen::where("id", $request->id)->delete();
    StatistiekenFacturatieItems::where("groep_id", $request->id)->delete();
    return response([], 201);
  }
  public function addItemsFacturatieGroepen(Request $request)
  {
    StatistiekenFacturatieItems::where("groep_id", $request->id)->delete();
    foreach ($request->items ?? [] as $row) {
      StatistiekenFacturatieItems::insert([
        "groep_id" => $request->id,
        "naam" => $row,
        "created_at" => Carbon::now(),
      ]);
    }
    return response([], 201);
  }

  private function calculateVerlofHours($userId, $redenId, $start, $end)
  {
      $query = Verlof::where('beoordeeld', 1)
          ->where('akkoord', 1)
          ->where('reden_id', $redenId)
          ->whereBetween('datum', [$start, $end]);
      if ($userId) {
          $query->where('medewerker_id', $userId);
      }
      $verlofRecords = $query->get();
      return $verlofRecords->reduce(function($carry, $verlof) {
          return $carry + $this->getVerlofHours($verlof);
      }, 0);
  }

  private function getVerlofHours($verlof)
  {
      if ($verlof->van && $verlof->tot) {
          $start = Carbon::parse($verlof->datum . ' ' . $verlof->van);
          $end = Carbon::parse($verlof->datum . ' ' . $verlof->tot);
          return $start->floatDiffInHours($end);
      } else {
          $hours = 0;
          $standaarduren = $verlof->user && $verlof->user->standaarduren ? $verlof->user->standaarduren : null;
          if ($standaarduren && count($standaarduren)) {
              $dagNamen = [
                  'monday' => 'maandag',
                  'tuesday' => 'dinsdag',
                  'wednesday' => 'woensdag',
                  'thursday' => 'donderdag',
                  'friday' => 'vrijdag',
                  'saturday' => 'zaterdag',
                  'sunday' => 'zondag',
              ];
              $englishDay = strtolower(Carbon::parse($verlof->datum)->format('l'));
              $dagNaam = $dagNamen[$englishDay] ?? $englishDay;
              $std = $standaarduren->where('dag', $dagNaam)->first();
              if ($std) {
                  $hours = max(0, $std->standaarduren);
              }
          }
          return $hours;
      }
  }
}
