<?php

namespace App\Http\Controllers\Api;

use DB;
use App\User;
use App\Device;
use App\Verlof;
use App\Verlofreden;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Mail\VerlofAanvraag;

use DatePeriod;
use DateInterval;

class VerlofController extends Controller {

  public function get(Request $request) {
    $device = Device::where('api_token', $request->token)->firstOrFail();
    $user = User::where('id', $device->user_id)->firstOrFail();

    $query = Verlof::where('medewerker_id', $user->id)->where('datum', '>=', Carbon::now()->startOfDay())->with('door')->orderBy('datum');

    if(isset($request->date)) {
      $query->whereDate('datum', Carbon::parse($request->date)->toDateString());
    }

    $verlof = $query->get();
    $verlofRedenen = Verlofreden::where('active', 1)
    ->where(function ($query) use ($user) {
      $query->where('role_id', 0)
        ->orWhere('role_id', $user->role_id);
    })->get();

    foreach($verlof as &$ver) {
      $ver->datum = Carbon::parse($ver->datum)->format('d-m-Y');
      if(isset($ver->van)) { $ver->van = Carbon::parse($ver->van)->format('H:i'); }
      if(isset($ver->tot)) { $ver->tot = Carbon::parse($ver->tot)->format('H:i'); }
    }

    return response()->json([
        'verlof' => $verlof,
        'verlofRedenen' => $verlofRedenen
    ], 200);
  }

  public function store(Request $request) {
    $dagen = ["zondag", "maandag", "dinsdag", "woensdag", "donderdag", "vrijdag", "zaterdag", "zondag"];

    $device = Device::where('api_token', $request->token)->firstOrFail();
    $user = User::where('id', $device->user_id)->with('role')->firstOrFail();

    if($request->tot && !$request->totTijd) {
      if ($request->van !== $request->tot) {
        $interval = DateInterval::createFromDateString('1 day');

        $period = new DatePeriod(Carbon::parse($request->van), $interval, Carbon::parse($request->tot)->addDay());

        foreach ($period as $dt) {

          $standaarduren = DB::table('standaarduren')->where('medewerkers_id', $user->id)->where('dag', $dagen[$dt->dayOfWeek])->first();

          if (!empty($standaarduren->standaarduren) && $standaarduren->standaarduren > 0) {

            DB::table('verlof')->insert([
              'datum' => $dt,
              'opmerkingen' => $request->opmerking,
              'reden_id' => $request->reden_id ?? null,
              'medewerker_id' => $user->id,
              'akkoord' => 0
            ]);
          }
        }
      } else {
        $datum = Carbon::parse($request->van);

        $standaarduren = DB::table('standaarduren')->where('medewerkers_id', $user->id)->where('dag', $dagen[$datum->dayOfWeek])->first();

        if (!empty($standaarduren->standaarduren) && $standaarduren->standaarduren > 0) {
          DB::table('verlof')->insert([
            'datum' => $request->van,
            'opmerkingen' => $request->opmerking,
            'reden_id' => $request->reden_id ?? null,
            'medewerker_id' => $user->id,
            'akkoord' => 0
          ]);
        }

      }
    } else {
      // totTijd
      DB::table('verlof')->insert([
        'datum' => $request->van,
        'van' => $request->vanTijd,
        'tot' => $request->totTijd,
        'reden_id' => $request->reden_id ?? null,
        'opmerkingen' => $request->opmerking,
        'medewerker_id' => $user->id,
        'akkoord' => 0
      ]);
    }
    try{
      if(!empty($user->role->verlof_mail )){Mail::to($user->role->verlof_mail )->send(new VerlofAanvraag($user->name . ' ' . $user->lastname, $request->van, $request->tot, $request->vanTijd, $request->totTijd, $request->opmerking));}
      if(!empty($user->role->verlof_mail2)){Mail::to($user->role->verlof_mail2)->send(new VerlofAanvraag($user->name . ' ' . $user->lastname, $request->van, $request->tot, $request->vanTijd, $request->totTijd, $request->opmerking));}
      if(!empty($user->role->verlof_mail3)){Mail::to($user->role->verlof_mail3)->send(new VerlofAanvraag($user->name . ' ' . $user->lastname, $request->van, $request->tot, $request->vanTijd, $request->totTijd, $request->opmerking));}
    }catch (\Exception $e) {
      actError($e);
    }
    return response()->json(["message" => "Ingevoerd!", "status" => "success"], 201);
  }
}
