<?php

namespace App\Http\Controllers\api;

use App\Classes\search\SearchManager;
use DB;
use App\ExplorerFiles;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ExplorerController extends Controller{

	public $relations = ['user'];
	public $relations_lite = [];

	//Base
	public function get(Request $request){
		try{
			$relations_build = generateLiteRelations(($request->relations ?? $this->relations), $request);
			$this->relations = $relations_build['relations'] ?? [];
			$this->relations_lite = $relations_build['relations_lite'] ?? [];

			$model = new ExplorerFiles();
			$model = $model->with($this->relations)->with($this->relations_lite)->where('active', $request->active ?? 1)->orderBy($request->order_by ?? 'name', $request->order_direction ?? 'ASC');

			//Permissions
			$model = $this->getPermissions($model);

			//search
			if(isset($request->ids)){
				$model = $model->whereIn('id', $request->ids);
			}
			else{
				if(isset($request->id)){
					$model = $model->where('id', $request->id);
				}
				if(isset($request->path)){
					$model = $model->where('path', $request->path);
					ExplorerFiles::createFoldersByPath($request->path);
				}
				if(isset($request->type)){
					$model = $model->where('type', $request->type);
				}
				if(isset($request->extension)){
                    if(is_array($request->extension)){
                        $model = $model->whereIn('extension', $request->extension);
                    }
                    else{
                        $model = $model->where('extension', $request->extension);
                    }
				}
			}

			$files = $model->get();
			$files->map([$this, 'mapFiles']);

			if(isset($request->id)){
				return response($files->first(), 200);
			}

			return response([
				'files' => resetIndex($files->where('type', 'file')),
				'folders' => resetIndex($files->where('type', 'folder')),
				'path_folders' => $this->pathFolders($request->path),
			], 200);
		}
		catch(\Exception $e){ return catchResponse($e); }

	}
	public function search(Request $request){
		try {
			$search = new SearchManager($request->search);
			$toolbox = $search->explorer();

			return response($toolbox, 200);
		}
		catch (\Exception $e) { return catchResponse($e); }
	}

	//Permissions
	private function getPermissions($model){
		if(!hasPermission('Alle bestanden bekijken') && !hasPermission('Eigen bestanden bekijken')) {
			return $model->where('path', 'LIKE', '/Gebruikers/('.User::id().')%');
		}

		if(!hasPermission('Alle bestanden bekijken')){
			return $model->where('user_id', User::id());
		}

		return $model;
	}

	//Utility
	public function mapFiles($file){
		$file->hasContent();
		$file->subPath();
		$file->size();
		$file->name();

		foreach($this->relations as $relation_key => $query){
			$file->setRelation(str_replace('_lite', '', $relation_key), $file[$relation_key]);
			unset($file[$relation_key]);
		}

		return $file;
	}
	public function pathFolders($path){
		if(!$path){ return null; }

		ExplorerFiles::createFoldersByPath($path);

		$folders = explode('/', $path);

		$path = '/';
		$path_folders = [];

		foreach($folders as $folder_name){
			if(!$folder_name){ continue; }

			$folder = ExplorerFiles::where(['path' => $path, 'name' => $folder_name, 'type' => 'folder'])->first();
			if(!$folder){ continue; }

			$path_folders[] = $this->mapFiles($folder);

			if($path != '/'){ $path .= '/'; }
			$path .= $folder_name;
		}

		return $path_folders;

	}

	//Data Get
	public function storage(){

		//Eloquent groupBy now working with 2 parameters
		$types = DB::select("	SELECT extension, SUM(size) as size
				FROM (
				    SELECT extension, size, src
				    FROM explorer_files
				    WHERE type = 'file' AND size > 0
				    GROUP BY src, extension
				) as unique_files
				GROUP BY extension
				ORDER BY size DESC;");

		$types = collect($types);
		$types->map(function($type){
			$type->size = intval($type->size);
			$type->extension = strtoupper($type->extension);
			return $type;
		});

		$total = $types->sum('size');
		$total = bytesToFormats($total);

		return response([
			'types' => $types,
			'total' => $total,
		], 200);
	}
	public function pathStorage(Request $request){
		$size = ExplorerFiles::where(['type' => 'file', 'active' => 1])->where('path', 'LIKE', $request->path.'%')->sum('size');
		return response([
			'size' => bytesToFormats(intval($size))
		], 200);
	}
	public function pathExists(Request $request){
		$exists = ExplorerFiles::where('path', $request->path)->exists();
		return response(['exists' => $exists ? 1 : 0], 200);
	}

	//Data Set
  public function upload(Request $request){

    $file = ExplorerFiles::insertFromRequest([
      'file' => $request->file('file'),
      'name' => $request->name ?? (randomString(5) . '_' . $request->file('file')->getClientOriginalName()),
      'path' => $request->path ?? '/',
      'description' => $request->description ?? null,
    ]);

    return response([ 'file' => $file ], 200);

  }
	public function update(Request $request){
		try{
			$data = [];
			if($request->has('active')){ $data['active'] = $request->active; }
			if($request->has('view_name')){ $data['view_name'] = $request->view_name; }

			ExplorerFiles::where('id', $request->id)->update($data);

			return response(null, 200);
		}
		catch(HttpException $e){ return catchResponse($e); }
	}
	public function storeFolder(Request $request){
		try{
			$path = $request->path;
			if(substr($path, -1) != '/'){ $path .= "/"; }

			$path .= $request->folder;

			ExplorerFiles::createFoldersByPath($path);

			return response(null, 200);
		}
		catch(HttpException $e){
			return catchResponse($e);
		}
	}

}
