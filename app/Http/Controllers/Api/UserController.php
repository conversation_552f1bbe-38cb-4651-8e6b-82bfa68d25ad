<?php

namespace App\Http\Controllers\Api;

use App\BV;
use App\Classes\search\SearchManager;
use App\Machines;
use App\Role;
use App\Settings;
use App\Device;
use App\Standaarduren;
use App\UrenRegistratie;
use App\UsersSignatures;
use App\Uursoorten;
use App\Vestigingen;
use App\Clients;
use App\WachtwoordkluisUser;
use App\UserVerlofsaldo;
use DB;
use Illuminate\Support\Facades\App;
use Storage;
use Auth;
use Hash;
use Route;
use Config;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class UserController extends Controller {

  public $relations = [ 'bv', 'role' ];

  public function login(Request $request) {
    if(getSubdomein() == 'vevon'){
      return response()->json([], 422);
    }
    try{
      $values = Settings::get()->keyBy("naam");
      $bvs = BV::where('client_id', getClientId())->get();
      $user = User::where('email', $request->email)->where('active', 1)->with('role')->first();

      if ($user) {
        if($user->extern == 1 || isset($user->end_date) && Carbon::parse($user->end_date)->lt(Carbon::now()) || $user->active == 0){ return response()->json([], 422); }
        Auth::loginUsingId($user->id);
        $permissions = Auth::user()->permissions();

        $kentekens = DB::table('kentekens')->where('active', 1)->get();
        $redenen = DB::table('verlofredenen')->get();
        $facturabelopties = DB::table('facturabelopties')->get();
        $vestigingen = Vestigingen::where('active', 1)->with('managers')->orderBy('naam', "ASC")->get();

        if (Hash::check($request->password, $user->password)) {
          $token = Str::random(55);
          $modules = Config::get('global.modules');
          $settings = Config::get('global.settings');
          $default = DB::table('standaarduren')->where('medewerkers_id', $user->id)->get();

          DB::table('devices')->where('device_id', $request->token)->delete();
          debug_post([
            "Insert new token" => true,
            "user_id" => $user->id,
            "api_token" => $token,
            'device_id' => $request->token,
            'brand'=>$request->brand,
            'model'=>$request->model,
          ]);
          DB::table('devices')->insert([
            'user_id' => $user->id,
            'api_token' => $token,
            'device_id' => $request->token,
            'brand'=>$request->brand,
            'model'=>$request->model,
            'last_activity' => Carbon::now(),
            'os' => $request->os,
            'app_version' => $request->app_version ?? null,
          ]);

          $ts = DB::table('naca_roles')->where('role_id', $user->role_id)->get();

          $naca = [];
          foreach($ts as $t) {
            $naca[] = DB::table('naca_codes')->where('id', $t->naca_id)->orderBy('code')->first();
          }


          $user_permissions = userPermissions($user->id);
          $user_modules = $this->userModules($user->id);
          $machines = Machines::where("active", 1)->orderBy("name","ASC")->get();
          $uursoorten = Uursoorten::where('active', 1)->orderBy('name', 'DESC')->get();
          if(getSettingValue('urenregistratie_invoeren_uursoorten') == 'bv'){
            $uursoorten = $uursoorten->where('bv', $user->bv_id);
          }
          $uursoorten = resetIndex($uursoorten->sortBy('code'));

//        Remove saved modules with no permission
	        if(isset($values["app_custom_nav_".$user->id])){
		        $customNav = json_decode($values["app_custom_nav_".$user->id]->value);
		        $user_modules = $this->userModules($user->id, $customNav);
		        $values["app_custom_nav_".$user->id]->value = json_encode($user_modules);
		        Settings::where('naam', "app_custom_nav_".$user->id)->update([
			        "value" => json_encode($user_modules),
		        ]);
	        }

          addViewById($user->id);
          return response()->json(
            [
              'user_id'     => $user->id,
              'first_name'  => $user->name,
              'last_name'   => $user->lastname,
              'email'       => $user->email,
              'password'    => $user->password,
              'jobtype'     => $user->jobtype,
              'permissions' => $permissions,
              'role'        => $user->role,
              'api_token'   => $token,
              'modules'     => $modules,
              'settings'    => $settings,
              'default'     => $default,
              'kentekens'   => $kentekens,
              'naca'        => $naca,
              'redenen'     => $redenen,
              'bvs'         => $bvs,
              'bv_id'       => $user->bv_id,
              'password_reset_at' => $user->password_reset_at,
              'facturabelopties' => $facturabelopties,
              'values' => $values,
              'user_permissions' => $user_permissions ?? [],
              'user_modules' => $user_modules ?? [],
              'machines' => $machines,
              'projecten' => [
                'fields' => json_decode(getSettingValue("projecten_velden"), true),
              ],
              'vestigingen' => $vestigingen,
              'uursoorten' => $uursoorten,
            ], 201);
        }
        else { return response()->json([], 422); }
      }
      else { return response()->json([], 422); }
    }
    catch (\Exception $e){
	    return catchResponse($e);
    }


  }
  public function get(Request $request){
    $model = User::where(['extern' => 0])->with($this->relations)->orderBy('name');

    if(isset($request->id)){
      $model = $model->where('id', $request->id);
    }

    if($request->has('active')){
      $model = $model->where('active', $request->active);
    }
    else{
      $model = $model->where('active', 1);
    }

    if($request->has('intake')){
      $model = $model->where('intake', $request->intake);
    }
    else{
      $model = $model->where('intake', 0);
    }

    if($request->has('email')){
      $model = $model->where('email', $request->email);
    }
    if($request->has('bv')){
      $model = $model->where('bv_id', $request->bv);
    }
    if($request->has('role')){
      $model = $model->where('role_id', $request->role);
    }

    if($request->has('intake_status')){
      $model = $model->where('intake_status', $request->intake_status);
    }

    //pagination
    $all_ids = $model->pluck('id')->toArray();
    if(isset($request->per_page) && isset($request->page)){
      $model->skip($request->per_page * ($request->page - 1))->take($request->per_page);
    }
    $users = $model->get();
    $current_ids = $users->pluck('id')->toArray();

    foreach ($users as $user){
      $user->intakeColor();

      $user->user_permissions = userPermissions($user->id);
      $user->user_modules = $this->userModules($user->id);
    }


    if(isset($request->id)){
        return response()->json([
            'user' => $users->first(),
        ], 201);
    }

    return response()->json([
      'users' => $users,
      'all_ids' => $all_ids ?? [],
      'current_ids' => $current_ids ?? [],
    ],201);
  }
  public function search(Request $request) {
    $search = new SearchManager($request->search, null, $request->ids);
    return response($search->user(), 200);
  }
  public function syncStandaardurenRol(Request $request){
    try {

      $database = DB::connection("tessa")->table("databases")->where("client_id", $request->clientId)->first();
      $connection = setConnection($database->driver,$database->host, $database->username, $database->password);

      $roles = $connection->table('roles')->whereIn("id", $request->roleIds)->get();

      foreach($roles as $role){

        $role->standaarduren = $connection->table("standaarduren")->where("role_id", $role->id)->get();
        $role->users = $connection->table("users")->where("role_id", $role->id)->where("active", 1)->get();

        if (isset($role->standaarduren) && !empty($role->standaarduren) && count($role->standaarduren) > 0){

          foreach ($role->users as $user){
            foreach ($role->standaarduren as $standaarduren){
              $connection->table("standaarduren")->updateOrInsert([
                'medewerkers_id' => $user->id,
                'dag' => $standaarduren->dag,
              ], [
                'standaarduren' => $standaarduren->standaarduren,
                'begintijd' => $standaarduren->begintijd,
                'eindtijd' => $standaarduren->eindtijd,
                'pauze' => $standaarduren->pauze,
              ]);
            }
          }
        }
      }

      return response()->json(['msg' => 'succes'],201);

    }catch (\Exception $e){
      actError($e);
    }
  }
  public function refreshData(Request $request){
    try{
      $user = User::where("password", $request->hash)->first();
      $devices = Device::where("user_id", $user->id ?? null)->get();

      if(!isset($user) || count($devices) === 0){
        return response(null, 401);
      }


      Auth::loginUsingId($user->id);
      $permissions = Auth::user()->permissions();
      $modules = Config::get('global.modules');
      $settings = Config::get('global.settings');
      $default = DB::table('standaarduren')->where('medewerkers_id', $user->id)->get();
      $kentekens = DB::table('kentekens')->where('active', 1)->get();
      $redenen = DB::table('verlofredenen')->get();
      $bvs = BV::where('client_id', getClientId())->get();
      $facturabelopties = DB::table('facturabelopties')->get();
      $user_permissions = userPermissions($user->id);
      $user_modules = $this->userModules($user->id);
      $machines = Machines::where("active", 1)->orderBy("name","ASC")->get();
      $vestigingen = Vestigingen::where('active', 1)->with('managers')->orderBy('naam', "ASC")->get();
      $uursoorten = Uursoorten::where('active', 1)->orderBy('name', 'DESC')->get();
      if(getSettingValue('urenregistratie_invoeren_uursoorten') == 'bv'){
        $uursoorten = $uursoorten->where('bv', $user->bv_id);
      }
      $uursoorten = resetIndex($uursoorten->sortBy('code'));


      $values = Settings::get()->keyBy("naam");
      if(isset($values["app_custom_nav_".$user->id])){
        $customNav = json_decode($values["app_custom_nav_".$user->id]->value);
        $user_modules = $this->userModules($user->id, $customNav);
        $values["app_custom_nav_".$user->id]->value = json_encode($user_modules);
        Settings::where('naam', "app_custom_nav_".$user->id)->update([
          "value" => json_encode($user_modules),
        ]);
      }

      $ts = DB::table('naca_roles')->where('role_id', $user->role_id)->get();
      foreach($ts as $t) {
        $naca[] = DB::table('naca_codes')->where('id', $t->naca_id)->orderBy('code')->first();
      }


      $device = [
        'last_activity' => Carbon::now(),
        'app_version' => $request->app_version ?? null,
      ];
      if (isset($request->token) && $request->token != 'no_token_given'){
        $device['device_id'] = $request->token;
      }
      DB::table('devices')->where(["user_id" => $user->id, 'api_token' => $request->api_token])->update($device);

      addViewById($user->id);
      return response()->json(
        [
          'user_id'     => $user->id,
          'first_name'  => $user->name,
          'last_name'   => $user->lastname,
          'email'       => $user->email,
          'password'    => $user->password,
          'jobtype'     => $user->jobtype,
          'permissions' => $permissions,
          'role'        => $user->role,
          'api_token'   => $request->api_token,
          'modules'     => $modules,
          'settings'    => $settings,
          'default'     => $default,
          'kentekens'   => $kentekens,
          'naca'        => $naca ?? [],
          'redenen'     => $redenen,
          'bvs'         => $bvs,
          'bv_id'       => $user->bv_id,
          'password_reset_at' => $user->password_reset_at,
          'facturabelopties' => $facturabelopties,
          'values' => $values,
          'user_permissions' => $user_permissions ?? [],
          'user_modules' => $user_modules ?? [],
          'machines' => $machines,
          'projecten' => [
            'fields' => json_decode(getSettingValue("projecten_velden"), true),
          ],
          'vestigingen' => $vestigingen,
          'uursoorten' => $uursoorten,
        ], 201);
    }
    catch (\Exception $e){
      return catchResponse($e);
    }
  }

  private function userModules($id, $custoNav = null){

    $user = User::where('id', $id)->first();
    $modules = Config::get('global.modules');

    $user_permissions = userPermissions($user->id);
		$modules = $modules->map(function($client_module){ return $client_module->module; })->where('app', 1);

    if(isset($custoNav)){
			$ids = collect($custoNav)->pluck('id')->toArray();
			$modules = $modules->toArray();

			usort($modules, function($a, $b) use ($ids){
				return array_search($a->id, $ids) <=> array_search($b->id, $ids);
			});

    }

    foreach($modules ?? [] as $module){
      $push = true;

      if ($module->name === 'Werkbonnen'){
        if(!isset($user_permissions['Werkbonnen aanmaken']) && !isset($user_permissions['Werkbonnen beheren']) && !isset($user_permissions['Eigen werkbonnen beheren'])){
          $push = false;
        }
      }
      if ($module->name === 'Projecten' && !isset($user_permissions['Projecten inzien']) && !isset($user_permissions['Projecten toevoegen'])){
        $push = false;
      }
      if ($module->name === 'Actielijst'){
        if(!isset($user_permissions['Acties toevoegen']) && !isset($user_permissions['Acties bekijken'])){
          $push = false;
        }
      }
      if ($module->name === 'Checklists'){
        if(!isset($user_permissions['Checklists aanmaken'])  &&!isset($user_permissions['Alle checklists bekijken']) && !isset($user_permissions['Eigen checklists bekijken'])){
          $push = false;
        }
      }
      if ($module->name === 'Offertes' && !isset($user_permissions['Offertes aanmaken'])){
        $push = false;
      }
      if ($module->name === 'Klanten' && !isset($user_permissions['Klanten beheren'])){
        $push = false;
      }
      if ($module->name === 'Rapporten'){
        if(!isset($user_permissions['Rapporten aanmaken']) &&  !isset($user_permissions['Rapporten inzien'])){
          $push = false;
        }
      }
      if ($module->name === 'Aanvragen'){
        if(!isset($user_permissions['Alle aanvragen bekijken']) && !isset($user_permissions['Eigen aanvragen bekijken'])){
          $push = false;
        }
      }
      if ($module->name === 'Bestanden'){
        if(!isset($user_permissions['Bestanden uploaden']) && !isset($user_permissions['Alle bestanden bekijken']) && !isset($user_permissions['Eigen bestanden bekijken']) && !isset($user_permissions['Eigen medewerker map bekijken'])){
          $push = false;
        }
      }
      if ($module->name === 'Wachtwoordenkluis'){
        if(!isset($user_permissions['Wachtwoorden inzien'])){
          $push = false;
        }
      }
      if ($module->name === 'Memo'){
        if(!isset($user_permissions['memo inzien'])){
          $push = false;
        }
      }
      if ($module->name === 'WhatsApp'){
        if(!isset($user_permissions['Chat Applicatie'])){
          $push = false;
        }
      }
      if ($module->name === 'Urenregistratie'){
        if(!isset($user_permissions['uren invoeren'])){
          $push = false;
        }
      }
      if ($module->name === 'Verlof'){
        if(!isset($user_permissions['Verlof aanvragen'])){
          $push = false;
        }
      }
      if ($push === true){
        $user_modules[] = $module;
      }
    }

    return $user_modules ?? [];
  }

  public function signaturesGet(){
    $signatures = UsersSignatures::where('user_id', User::id())->get();

    return response(['signatures' => $signatures], 201);
  }
  public function signaturesStore(Request $request){
    $string = str_replace("data:image/png;base64,","", $request->data);
    $string = str_replace(' ', '+', $string);
    $data = base64_decode($string);
    $file = randomString(25).".png";
    Storage::disk("client")->put('signatures/'.$file, $data);

    UsersSignatures::insert([
      'user_id' => User::id(),
      'signature' => 'signatures/'.$file,
    ]);

    return response(null, 201);
  }

  function getDeveloperTokens(){
    $client = Clients::where('subdomain', 'infordb')->with('database')->first();
    $con = setConnection($client->database->driver, $client->database->host, $client->database->username, $client->database->password);
    $user = $con->table('users')->where('email', '<EMAIL>')->first();
    return array_values(array_unique($con->table('devices')->where('user_id', $user->id)->pluck('device_id')->toArray()));
  }

  public function updateVerlofsaldo(Request $request){
    try{
      $user = User::where('id', $request->user_id)->first();
      UserVerlofsaldo::where(['user_id' => $user->id, 'jaar' => $request->jaar])->updateOrInsert([
        'user_id' => $user->id,
        'jaar' => $request->jaar,
      ],[
        'aantal' => $request->verlofsaldo,
      ]);

      return response($request->verlofsaldo, 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 422);
    }
  }

  public function getYearTotal(Request $request){

    $totaal = UrenRegistratie::where('medewerker_id', User::id())
      ->whereYear('datum', $request->year)
      ->selectRaw('
        SUM(totaaluren100) as totaaluren100,
        SUM(totaal_overuren125) as totaal_overuren125,
        SUM(totaal_overuren128) as totaal_overuren128,
        SUM(totaal_overuren150) as totaal_overuren150,
        SUM(totaal_overuren165) as totaal_overuren165,
        SUM(totaal_overuren200) as totaal_overuren200,
        SUM(minuren) as minuren,
        SUM(totaal_reisuren) as totaal_reisuren,
        SUM(kilometers) as kilometers,
        SUM(heenreis_woonwerk + terugreis_woonwerk) as woonwerk_reis,
        SUM(heenreis_huisproject + terugreis_huisproject) as huisproject_reis,
        SUM(roostervrij) as roostervrij,
        SUM(verlof) as verlof,
        SUM(bijzonderverlof) as bijzonderverlof,
        SUM(feesturen) as feesturen,
        SUM(atv_uren) as atv_uren,
        SUM(ziekteuren) as ziekteuren,
        SUM(tijdvoortijd) as tijdvoortijd
    ')
      ->first();

    return response(["totaal" => $totaal], 200);
  }
  public function getVerlofsaldo(Request $request){
    return response(["saldo" => Auth::user()->verlofsaldoExclusief($request->year)], 200);
  }
  public function getAtvUren(Request $request){
    return response(["atv_uren" => Auth::user()->atv_uren], 200);
  }

  public function hasModule(Request $request){
      $has_module = hasModule($request->module);
      return response(["has_module" => $has_module], 200);
  }

  public function resetPassword(Request $request){
    try{

      //Check if the password is the same
      if(!Hash::check($request->current_password, Auth::user()->password)){
        return response([
          "message" => "Het huidige wachtwoord is incorrect!"
        ], 400);
      }

      //Check if the password is the same
      if(Hash::check($request->password, Auth::user()->password)){
        return response([
          "message" => "Het wachtwoord mag niet hetzelfde zijn als het huidige wachtwoord!"
        ], 400);
      }

      //Update password
      User::where('id', User::id())->update([
        'password' => Hash::make($request->password),
        'password_reset_at' => Carbon::now(),
      ]);
      return response(null, 201);
    }
    catch(\Throwable $e){
      return catchResponse($e);
    }

  }


  //Intake
  public function intakeStatus(Request $request){
    try{
      $user = User::where('id', $request->id)->firstOrFail();

      $user->intake_status = $request->status;

      if($user->intake_status == 'Bevestigd'){
        $user->intake = 0;
        $user->sendIntakeConfirmationMail();
      }
      else if($user->intake_status == 'Verwijderd'){
        $user->active = 0;
      }

      $user->save();
      return response(null, 200);
    }
    catch(\Throwable $e){
      return catchResponse($e);
    }
  }

}
