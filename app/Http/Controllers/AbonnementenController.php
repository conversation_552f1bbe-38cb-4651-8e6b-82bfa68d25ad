<?php

namespace App\Http\Controllers;

use App\AanmeldingenKortingSelect;
use App\AanmeldingItems;
use App\Abonnementen;
use App\AbonnementenFormulieren;
use App\AbonnementenFormulierItems;
use App\AbonnementenItems;
use App\AbonnementenItemsUitgaves;
use App\AbonnementenKortingsgroepen;
use App\AbonnementenVerenigingen;
use App\AbonnementFacturen;
use App\AbonnementItems;
use App\BV;
use App\Exports\AbonnementExport;
use App\Facturen;
use App\Factuurregels;
use App\Imports\AbonnementenImport;
use App\KlantenContactpersonen;
use App\Mail\abonnementAccess;
use App\Mail\abonnementCredentials;
use App\Mail\abonnementPasswordReset;
use App\Mail\BlankMail;
use App\Mail\sendFactuur;
use App\MollieAttempts;
use App\Vestigingen;
use DB;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use Hash;
use Auth;
use App\CDN;
use App\FacturenAdres;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use MikeMcLin\WpPassword\Facades\WpPassword;
use Mollie\Laravel\Facades\Mollie;

class AbonnementenController extends Controller
{

    public function index(){
	    $credentials_check = Abonnementen::where(['imported' => 1])->whereNull('wachtwoord')->whereHas('items', function($query) {
		    $query->where('form_type', 'digitaal');
	    })->first();

      $abonnementen = Abonnementen::with("facturen", "items", "_bv", "attempt", "vereniging")->get()->keyBy("id");
      $group = AbonnementItems::whereHas('abonnement')->where("active", 1)->with("abonnement", "item")->get()->groupBy("item_id");
      $items = AbonnementenItems::where("active", 1)->with('uitgaves')->get()->keyBy("id");
      $facturen = Facturen::with("klant", "all_emails")->get()->keyBy("id");

      $aFacturen = AbonnementFacturen::with("factuur")->with("abonnement")->orderBy("id", "DESC")->get();
      $kortingsgroepen = AbonnementenKortingsgroepen::get()->keyBy("id");
      $formulieren = AbonnementenFormulieren::where('active', 1)->orderBy('naam', 'DESC')->get();
      $verenigingen  = AbonnementenVerenigingen::where(['active' => 1])->orderBy('name', 'ASC')->get();

      foreach($aFacturen as $f){
        foreach(json_decode($f->items, true) as $i => $p){
          if($f->factuur->status == "Betaald"){
            $decode = clone $f;
            $decode->items = json_decode($f->items, true);
            $geldigheid[$f->abonnement_id][$i][] = $decode;

          }
          if($f->factuur->active == 1){
            $activeFacturen[$f->abonnement_id][$i][] = $f->factuur;
          }
        }
      }

      foreach($items as $item){
        if(!isset($group[$item->id])){
          $group[$item->id] = [];
        }
      }

      return view("abonnementen.index",[
        "abonnementen" => $abonnementen,
        "group" => $group,
        "items" => $items,
        "facturen" => $facturen,
        "aFacturen" => $aFacturen,
        "geldigheid" => $geldigheid ?? [],
        "kortingsgroepen" => $kortingsgroepen,
        "activeFacturen" => $activeFacturen ?? [],
        "formulieren" => $formulieren,
        "verenigingen" => $verenigingen,
        "credentials_check" => $credentials_check,
        "bvs" => getBvs(),
      ]);
    }
    public function expired(){
      $data = $this->index()->getData();
      $data['expired'] = true;

      return view("abonnementen.index",$data);
    }
    public function active(){
      $data = $this->index()->getData();
      $data['active'] = true;

      return view("abonnementen.index",$data);
    }
    public function deleted(){
      $abonnementen = Abonnementen::with("facturen")->with("items")->with("_bv")->with("attempt")->get()->keyBy("id");
      $group = AbonnementItems::where("active", 0)->with("abonnement")->get()->groupBy("item_id");
      $items = AbonnementenItems::where("active", 1)->get()->keyBy("id");
      $facturen = Facturen::with("klant")->get()->keyBy("id");
      $aFacturen = AbonnementFacturen::with("factuur")->with("abonnement")->orderBy("id", "DESC")->get();
      $kortingsgroepen = AbonnementenKortingsgroepen::get()->keyBy("id");
      $bvs = BV::where("client_id", getClientId())->get()->keyBy("id");
      foreach($aFacturen as $f){
        foreach(json_decode($f->items, true) as $i => $p){
          if($f->factuur->status == "Betaald"){
            $decode = clone $f;
            $decode->items = json_decode($f->items, true);
            $geldigheid[$f->abonnement_id][$i][] = $decode;
          }
          if($f->factuur->active == 1){
            $activeFacturen[$f->abonnement_id][$i][] = $f->factuur;
          }
        }
      }
      foreach($items as $item){
        if(!isset($group[$item->id])){
          $group[$item->id] = [];
        }
      }
      return view("abonnementen.deleted",[
        "abonnementen" => $abonnementen,
        "group" => $group,
        "items" => $items,
        "facturen" => $facturen,
        "aFacturen" => $aFacturen,
        "geldigheid" => $geldigheid ?? [],
        "kortingsgroepen" => $kortingsgroepen,
        "activeFacturen" => $activeFacturen ?? [],
        "bvs" => $bvs,
      ]);
    }

		public function create($subdomain){
			$abonnement = Abonnementen::find($_GET['edit'] ?? null);
      $items = AbonnementenItems::where("active", 1)->get();
			return view('abonnementen.create', [
				'abonnement' => $abonnement ?? null,
        'items' => $items ?? null,
			]);
		}

    public function store(Request $request){
      if(!isset($_GET['edit'])){
        if (count($request->items) == 1){
          $formId = AbonnementenFormulierItems::where('item_id', $request->items[0])->first()->formulier_id ?? 0;
        }
        $items = AbonnementItems::whereIn('id', $request->items)->get();
        $id = Abonnementen::insertGetId([
          "bv" => $items[0]->bv ?? getBv()->id,
          "formulier_id" => $formId ?? 0,
          "methode" => "Factuur",
          "token" => randomString(10),
          "created_at" => Carbon::now(),
          "datum" => Carbon::now(),
          "soort_klant" => $request->soort_klant,
          "klant_id" => $request->klant_id,
          "aanhef" => $request->aanhef,
          "voornaam" => $request->voornaam,
          "tussenvoegsel" => $request->tussenvoegsel,
          "achternaam" => $request->achternaam,
          "straat" => $request->straat,
          "huisnummer" => $request->huisnummer . $request->toevoeging,
          "postcode" => $request->postcode,
          "plaats" => $request->plaats,
          "land" => $request->land,
          "email" => $request->email,
          "telefoonnummer" => $request->telefoonnummer,
          "organisatie" => $request->organisatie,
          "afdeling" => $request->afdeling,
          "factuur_referentie" => $request->factuur_referentie,
          "factuur_contactpersoon" => $request->contactpersoon,
        ]);
        $this->storeItems(Abonnementen::where('id', $id)->with('klant', 'items')->first(), $request->items);
        $this->storeFactuur(Abonnementen::where('id', $id)->with('klant', 'items')->first());
      }else{
        $id = $_GET['edit'];
        Abonnementen::where('id', $id)->update([
          "formulier_id" => 0,
          "methode" => "Factuur",
          "datum" => Carbon::now(),
          "soort_klant" => $request->soort_klant,
          "aanhef" => $request->aanhef,
          "voornaam" => $request->voornaam,
          "tussenvoegsel" => $request->tussenvoegsel,
          "achternaam" => $request->achternaam,
          "straat" => $request->straat,
          "huisnummer" => $request->huisnummer . $request->toevoeging,
          "postcode" => $request->postcode,
          "plaats" => $request->plaats,
          "land" => $request->land,
          "email" => $request->email,
          "telefoonnummer" => $request->telefoonnummer,
          "organisatie" => $request->organisatie,
          "afdeling" => $request->afdeling,
          "factuur_referentie" => $request->factuur_referentie,
        ]);
      }
      return redirect('abonnementen')->with('status', 'Abonnement aangepast!');
    }
    private function storeItems($abonnement, $items){
      foreach($items as $item){
        AbonnementItems::updateOrInsert([
          "abonnement_id" => $abonnement->id,
          "item_id" => $item,
        ],
        [
          "abonnement_id" => $abonnement->id,
          "item_id" => $item,
          "created_at" => Carbon::now(),
          "toegang" => ($abonnement->soort_klant == 'ip') ? 1 : 0,
        ]);
      }
    }
    private function storeFactuur($abonnement){
      $contactnaam = $abonnement->klant->contactpersoon_voornaam." ".$abonnement->klant->contactpersoon_achternaam;
      if($abonnement->factuur_contactpersoon && $abonnement->factuur_contactpersoon != 'klant_contactpersoon'){
        $cp = KlantenContactpersonen::where('id', $abonnement->factuur_contactpersoon)->first();
        $contactnaam = $cp->voornaam." ".$cp->achternaam;
      }
      $factuurId = Facturen::insertGetId([
        "klant_id" => $abonnement->klant_id ?? null,
        "bv" => $abonnement->bv,
        "datum" => Carbon::now()->format("Y-m-d"),
        "betalingstermijn" => Carbon::now()->addDays(getSettingValue("factuur_betalingstermijn") ?? 30)->format("Y-m-d"),
        "status" => 'Uitgebracht',
        "inleiding" => getSettingValue("factuur_inleiding"),
        "slot" => getSettingValue("factuur_slot"),
        "token" => randomString(25),
        "referentie" => $abonnement->factuur_referentie ?? null,
        "created_at" => Carbon::now()
      ]);
      FacturenAdres::insert([
        "factuur_id" => $factuurId,
        "klant" => $abonnement->klant->title(),
        "straat" => $abonnement->klant->postadres_straat,
        "huisnummer" => $abonnement->klant->postadres_huisnummer,
        "postcode" => $abonnement->klant->postadres_postcode,
        "plaats" => $abonnement->klant->postadres_plaats,
        "land" => $abonnement->klant->postadres_land,
        "tav" => $contactnaam ?? null,
      ]);
      $items = [];
      foreach($abonnement->items as $item){
        $initialtermijn = $item->initialTermijn();
        $items[$item->id] = $initialtermijn;
        $factuurRegel = [
          "factuur_id" => $factuurId,
          "naam" => $abonnement->soort_klant == 'ip' ? ('IP-Abonnement website: '.$item->naam) : $item->naam,
          "aantal" => 1,
          "prijs" => $item->getPrice($abonnement->id),
          "btw" => $item->getBtwPerc($abonnement->id),
          "incl" => $item->incl_btw,
          "created_at" => Carbon::now(),
        ];

        $factuurRegel["beschrijving"] = $abonnement->soort_klant == 'ip' ? '1 jaar' : ($item->geldigheid == 'Uitgaves' ? 'Uitgaves: ' : '').CarbonDmy($initialtermijn['start'])." t/m ".CarbonDmy($initialtermijn['end']);
        Factuurregels::insert($factuurRegel);
      }
      if($abonnement->soort_klant == 'ip'){
        $items['ip'] = ['start' => Carbon::now()->format("Y-m-d"), 'end' => Carbon::now()->addYear()->format("Y-m-d")];
      }
      AbonnementFacturen::insert([
        "abonnement_id" => $abonnement->id,
        "factuur_id" => $factuurId,
        "items" => json_encode($items),
        "created_at" => Carbon::now(),
      ]);
      $factuur = Facturen::where("id", $factuurId)->first();
      $formulier = AbonnementenFormulieren::where("id", $abonnement->formulier_id)->first();
      $prefix = $abonnement->soort_klant == 'ip' ? ('IP'.Carbon::now()->format('Y')) : ($formulier->factuur_prefix ?? ('KLO'.Carbon::now()->format('Y')));
      $factuur->setFactuurnummer(['force' => true, 'prefix' => $prefix]);
    }

    public static function checkItemUitgave($addUitgavesToCheck = 0){
      $items = AbonnementenItems::where('active', 1)->with('uitgaves')->get();
      $problematicItemReleases = [];
      foreach ($items as $item){
        if($item->geldigheid != "Uitgaves") { continue; }
        $uitgaveDatums = [];
        foreach ($item->uitgaves as $uitgave ){
          if (Carbon::parse($uitgave->date)->lt(Carbon::now()) || Carbon::parse($uitgave->date)->eq(Carbon::now())){ continue; }
          $uitgaveDatums[] = Carbon::parse($uitgave->date);
        }
        if (count($uitgaveDatums) < $item->aantal_uitgaves + $addUitgavesToCheck){
          $problematicItemReleases[] = $item;
        }
      }
      return $problematicItemReleases;
    }

    public function items(){
      $items = AbonnementenItems::where("active", 1)->with("user", "uitgaves")->get()->keyBy("id");
      $bvs = BV::where("client_id", getClientId())->get()->keyBy("id");
      return view("abonnementen.items",[
        "items" => $items,
        "bvs" => $bvs,
      ]);
    }

    public function storeItem(Request $request){
      if(!$request->hasFile("img") && !$request->itemId){
        return redirect('abonnementen/items')->with("warning", "Preview bestand is verplicht!");
      }
      if($request->hasFile("img")){
        $string = randomString(10).".png";
        Storage::disk("client")->putFileAs("abonnementen/items/", $request->file("img"), $string);
      }

      if($request->itemId){
        AbonnementenItems::where("id", $request->itemId)->update([
          "user_id" => Auth::user()->id,
          "naam" => $request->naam ?? "",
          "bv" => $request->bv,
          "omschrijving" => $request->omschrijving ?? null,
          "preview" => $string ?? $request->hiddenImg,
          "prijs" => $request->prijs ?? 0,
          "prijs_zakelijk" => $request->prijs_zakelijk ?? 0,
          "prijs_binnen_eu" => $request->prijs_binnen_eu ?? 0,
          "prijs_buiten_eu" => $request->prijs_buiten_eu ?? 0,
          "prijs_ip" => $request->prijs_ip ?? 0,
          "btw" => $request->btw,
          "incl_btw" => $request->inclBtw ?? 0,
          "geldigheid" => $request->geldigheid ?? "Jaar",
          "aantal_uitgaves" => $request->aantalUitgaves ?? null,
          "website" => $request->website ?? null,
          "db" => $request->db ?? null,
          "db_name" => $request->db_name ?? null,
          "db_login" => $request->db_login ?? null,
          "db_pass" => $request->db_pass ?? null,
          "db_prefix" => $request->db_prefix ?? null,
          "role" => $request->role ?? null,
          "created_at" => Carbon::now(),
          "form_type" => $request->form_type,
        ]);
      }
      else{
        AbonnementenItems::insert([
          "user_id" => Auth::user()->id,
          "naam" => $request->naam ?? "",
          "bv" => $request->bv,
          "omschrijving" => $request->omschrijving ?? null,
          "preview" => $string ?? $request->hiddenImg,
          "prijs" => $request->prijs ?? 0,
          "prijs_zakelijk" => $request->prijs_zakelijk ?? 0,
          "prijs_binnen_eu" => $request->prijs_binnen_eu ?? 0,
          "prijs_buiten_eu" => $request->prijs_buiten_eu ?? 0,
          "prijs_ip" => $request->prijs_ip ?? 0,
          "btw" => $request->btw,
          "incl_btw" => $request->inclBtw ?? 0,
          "geldigheid" => $request->geldigheid ?? "Jaar",
          "aantal_uitgaves" => $request->aantalUitgaves ?? null,
          "db" => $request->db ?? null,
          "db_name" => $request->db_name ?? null,
          "db_login" => $request->db_login ?? null,
          "db_pass" => $request->db_pass ?? null,
          "db_prefix" => $request->db_prefix ?? null,
          "role" => $request->role ?? null,
          "created_at" => Carbon::now(),
          "form_type" => $request->form_type,
        ]);
      }

      return redirect('abonnementen/items')->with("status", "Item opgeslagen");
    }

    public function storeKorting(Request $request, $redirect = "abonnementen/korting"){
      if(isset($request->code)){
        $code = $request->code;
        $try = AbonnementenKortingsgroepen::where("code", $code)->first();
        if(isset($try)){
          $code = randomString(10);
        }
      }
      else{
        $code = randomString(10);
      }

      $type = $request->discountType;
      $value = $request->discount;

      if(isset($request->id)){
        AbonnementenKortingsgroepen::where("id", $request->id)->update([
          "naam" => $request->naam,
          "percentage" => $value,
          "type" => $type,
          "code" => $code,
          "updated_at" => Carbon::now(),
        ]);
      }
      else{
        AbonnementenKortingsgroepen::insert([
          "naam" => $request->naam,
          "percentage" => $value,
          "type" => $type,
          "code" => $code,
          "created_at" => Carbon::now(),
        ]);
      }
      return redirect("abonnementen/korting")->with("status", "Kortingscode toegevoegd!");
    }

    public function storeUitgaves(Request $request){
      AbonnementenItemsUitgaves::where('item_id', $request->item)->delete();
      foreach($request->dates as $date){
        AbonnementenItemsUitgaves::insert([
          "item_id" => $request->item,
          "date" => $date,
          "created_at" => Carbon::now(),
        ]);
      }
      return response(null, 201);
    }

    public function deleteItem($subdomein, $id){
      AbonnementenItems::where("id", $id)->update(["active" => 0]);
      return redirect("abonnementen/items")->with("status", "Item verwijderd");
    }
    public function deleteCdn($subdomain, $token){
      CDN::where("client_id", getClientId())->where("token", $token)->delete();
      $formulier = AbonnementenFormulieren::where("token", $token)->first();
      AbonnementenFormulieren::where("token", $token)->delete();
      AbonnementenFormulierItems::where("formulier_id", $formulier->id)->delete();
      return redirect("abonnementen/formulieren")->with("status", "Formulier verwijderd!");
    }
    public function delete(Request $request){
      AbonnementItems::where("abonnement_id", $request->abonnement)->where("item_id", $request->item)->update(["active" => 0]);
      return response(null. 201);
    }
    public function deleteKorting($subdomain, $id){
    AbonnementenKortingsgroepen::where("id", $id)->update(["active" => 0]);
    AbonnementItems::where("kortingsgroep", $id)->update(["kortingsgroep" => null]);
    return redirect("abonnementen/korting")->with("status", "Kortingsgroep verwijderd!");
  }

    public function newCdn($subdomein, Request $request){
      $token = randomString(10);
      AbonnementenFormulieren::insert([
        "user_id" => Auth::user()->id,
        "bv" => $request->bv,
        "naam" => $request->naam,
        "token" => $token,
        "voorwaarden" => $request->voorwaarden ?? null,
        "created_at" => Carbon::now(),
      ]);
      CDN::insert([
        "client_id" => getClientId(),
        "name" => $request->naam,
        "subdomain" => $subdomein,
        "bv" => $request->bv,
        "token" => $token,
        "module" => "Abonnementen",
      ]);
      return redirect("abonnementen/formulieren")->with("status", "Token ".$token." aangemaakt!");
    }
    public function newFactuur(Request $request)
    {
      try {
        $abonnement = Abonnementen::where('id', $request->abonnement)->with('formulier')->first();
        $item = AbonnementenItems::where('id', $request->item)->with("uitgaves")->first();
        $prijs = $item->getPrice($abonnement->id);
        $cItem = AbonnementItems::where(['abonnement_id' => $abonnement->id, 'item_id' => $item->id])->with(
          '_kortingsgroep'
        )->first();

        $lastFactuurCon = $cItem->lastFactuurCon();
        $lastDates = json_decode($lastFactuurCon->items, true)[$item->id];
        $s = Carbon::parse($lastDates['end'])->format("Y-m-d");
        $e = Carbon::parse($lastDates['end'])->addYear()->format("Y-m-d");
        if ($item->geldigheid == "Maand") {
          $e = Carbon::parse($lastDates['end'])->addMonth()->format("Y-m-d");
        } elseif ($item->geldigheid == "Week") {
          $e = Carbon::parse($lastDates['end'])->addWeek()->format("Y-m-d");
        } elseif ($item->geldigheid == "Uitgaves") {
          $uitgaves = AbonnementenItemsUitgaves::where('item_id', $item->id)->whereDate(
            'date',
            '>',
            $lastDates['end']
          )->orderBy('date', "ASC")->take($item->aantal_uitgaves)->get();
          $s = $uitgaves[0]->date ?? null;
          $e = $uitgaves[($item->aantal_uitgaves - 1)]->date ?? null;
          if ($s == null || $e == null){
            $facturen = Facturen::with("klant")->get()->keyBy("id");
            $cFacturen = AbonnementFacturen::with("factuur")->with("abonnement")->orderBy("id", "DESC")->get();
            return response()->json([
              "cFacturen" => $cFacturen,
              "facturen" => $facturen,
              "factuur" => null,
            ], 201);
          }
        }

        $dates[$item->id] = [
          "start" => $s,
          "end" => $e
        ];

        $factuurId = Facturen::insertGetId([
          "klant_id" => $abonnement->klant_id,
          "bv" => $abonnement->bv,
          "datum" => Carbon::now()->format("Y-m-d"),
          "betalingstermijn" => Carbon::now()->addDays(getSettingValue("factuur_betalingstermijn") ?? 30)->format(
            "Y-m-d"
          ),
          "inleiding" => getSettingValue("factuur_inleiding"),
          "slot" => getSettingValue("factuur_slot"),
          "referentie" => $abonnement->factuur_referentie,
          "created_at" => Carbon::now()
        ]);
        $factuur = Facturen::find($factuurId);
        $factuur->setFactuurnummer(['force' => true, 'prefix' => ($abonnement->formulier->factuur_prefix ?? '')]);

        $factuurRegelPrijs = $prijs;
        if (isset($cItem->_kortingsgroep)) {
          if ($cItem->_kortingsgroep->type == "exact_amount") {
            $factuurRegelPrijs = $item->getPriceDiscounted($abonnement->id);
          }
        }

        $uitgaveDates = $item->getUitgaveDatesForPeriod($dates[$item->id]['start'], $dates[$item->id]['end']);

        $factuurRegel = [
          "factuur_id" => $factuurId,
          "naam" => $item->naam,
          "aantal" => 1,
          "prijs" => $factuurRegelPrijs,
          "btw" => $item->getBtwPerc($abonnement->id),
          "incl" => $item->incl_btw,
          "created_at" => Carbon::now(),
        ];
        if (!empty($uitgaveDates)){
          $factuurRegel["beschrijving"] = "Uitgaves: " . implode(" / ", $uitgaveDates);
        }
        Factuurregels::insert($factuurRegel);

        if (isset($cItem->_kortingsgroep)) {
          if ($cItem->_kortingsgroep->type == "bedrag") {
            $incl = 0;
            $facValue = "-" . $cItem->_kortingsgroep->percentage;
          }
          else if($cItem->_kortingsgroep->type == "percentage"){
            $incl = $item->incl_btw;
            $facValue = "-" . ($prijs * ($cItem->_kortingsgroep->percentage / 100));
          }
          else{
            $korting = $prijs - $cItem->_kortingsgroep->percentage;
            $incl = $item->incl_btw;
            $facValue = "-" . $korting;
          }
          if ($cItem->_kortingsgroep->type != "exact_amount"){
            Factuurregels::insert([
              "factuur_id" => $factuurId,
              "naam" => $cItem->_kortingsgroep->type == "percentage" ? ($cItem->_kortingsgroep->percentage . "% Korting") : "Korting",
              "aantal" => 1,
              "prijs" => $facValue,
              "btw" => $item->getBtwPerc($abonnement->id),
              "incl" => $incl,
              "created_at" => Carbon::now(),
            ]);
          }
        }

          AbonnementFacturen::insert([
            "abonnement_id" => $abonnement->id,
            "factuur_id" => $factuurId,
            "items" => json_encode($dates),
            "created_at" => Carbon::now(),
          ]);

          $facturen = Facturen::with("klant", "all_emails")->get()->keyBy("id");
          $cFacturen = AbonnementFacturen::with("factuur")->with("abonnement")->orderBy("id", "DESC")->get();
          $factuur = Facturen::where("id", $factuurId)->first();

          if (isset($request->paid)) {
            Facturen::where('id', $factuur->id)->update([
              'betaald_op' => Carbon::now()->format("Y-m-d"),
              'status' => "Betaald",
              'exact_globe_update_required' => 1,
            ]);
          }
          return response()->json([
            "cFacturen" => $cFacturen,
            "facturen" => $facturen,
            "factuur" => $factuur,
          ], 201);

      } catch (\Exception $e) {
        actError($e);
        return response(null, 500);
      }
    }
    public function sendFactuur(Request $request){
      $factuur = Facturen::findOrFail($request->factuur);
      $abonnement = Abonnementen::findOrfail($request->abonnement);
      $item = AbonnementenItems::findOrFail($request->item);

      Abonnementen::sendFactuur($abonnement, $factuur);

      $facturen = Facturen::with("klant", "all_emails")->get()->keyBy("id");
      $cFacturen = AbonnementFacturen::with("factuur")->orderBy("id", "DESC")->get();

      return response()->json([
        "facturen" => $facturen,
        "cFacturen" => $cFacturen,
      ],201);
    }

    public function formulieren(){
      $formulieren = AbonnementenFormulieren::with('korting_selects')->get()->keyBy("token");
      $tokens = CDN::where("client_id", getClientId())->where("module", "Abonnementen")->get()->keyBy("token");
      $items = AbonnementenItems::where("active", 1)->get()->keyBy("id");
      $kortingen = AanmeldingenKortingSelect::where('active', 1)->get();

      $fi = AbonnementenFormulierItems::get();
      $bvs = BV::where("client_id", getClientId())->get()->keyBy("id");

      $formulierItems = [];
      foreach ($fi as $row){
        $formulierItems[$row->formulier_id][$row->item_id] = true;
      }

      return view("abonnementen.formulieren",[
        "formulieren" => $formulieren,
        "tokens" => $tokens,
        "items" => $items,
        "bvs" => $bvs,
        "kortingen" => $kortingen,
        "formulierItems" => $formulierItems,
      ]);
    }
    public function reloadCdn($subdomain, $token){
      $nToken = randomString(10);
      CDN::where("client_id", getClientId())->where("token", $token)->update(["token" => $nToken]);
      AbonnementenFormulieren::where("token", $token)->update(["token" => $nToken]);
      return redirect("abonnementen/formulieren")->with("status", "Token gewijzigd!");
    }
    public function setItem(Request $request){
      if($request->state == 1){
        $status = "isnert";
        AbonnementenFormulierItems::insert([
          "formulier_id" => $request->formulier,
          "item_id" => $request->item
        ]);
      }
      else{
        $status = "delete";
        AbonnementenFormulierItems::where("formulier_id", $request->formulier)->where("item_id", $request->item)->delete();
      }
      $fi = AbonnementenFormulierItems::get();
      $formulierItems = [];
      foreach ($fi as $row){
        $formulierItems[$row->formulier_id][$row->item_id] = true;
      }
      return response($formulierItems, 201);
    }
    public function refresh(Request $request){
      $abonnement = Abonnementen::where("token", $request->token)->first();
      return response()->json($abonnement, 201);
    }
    public function enable(Request $request){
      AbonnementItems::where("abonnement_id", $request->abonnement)->where("item_id", $request->item)->update(["active" => 1]);
      return response(null. 201);
    }

    public function betaald($subdomein, $id, $date = null){
      $password = randomString();
      $hash = WpPassword::make($password);

      Abonnementen::where("id", $id)->update([
        "betaald" => 1,
        "betaaldatum" => Carbon::parse($date)->format("Y-m-d"),
        "wachtwoord" => $hash,
      ]);
      $abonnement = Abonnementen::where("id", $id)->with('items', 'facturen', '_bv')->first();
      $abonnement->betaald(Carbon::parse($date)->format("Y-m-d"));
      if($abonnement->soort_klant != 'ip'){
        $abonnement->toegang([
          'hash' => $hash
        ]);
      }
      $this->sendPaymentConfirmationMail($abonnement, getSettingValue("abonnement_email_betaling_voldaan"));
      if($abonnement->soort_klant == 'ip' || $abonnement->items[0]->form_type == "digitaal"){
        Mail::to($abonnement->email)->send(new abonnementCredentials($subdomein, $abonnement, $password));
      }
      return redirect("abonnementen")->with("status", "Status gewijzigd!");
    }

    public function sendPaymentConfirmationMail($abonnement, $message){
      if ($abonnement->lastFactuurCon()->factuur->token == null){
        $factuur = $abonnement->lastFactuurCon()->factuur;
        $factuur->token = randomString(25);
        $factuur->save();
      }

      $afzender = getSettingValue("abonnement_email_afzender") ?? "<EMAIL>";
      if($afzender == ''){
        $afzender = '<EMAIL>';
      }
      $_bv = $abonnement->_bv;
      Mail::to($abonnement->betalerMail())->send(new BlankMail(
        $_bv,
        $afzender,
        $_bv->name,
        'Betaling Voldaan',
        $this->formatEmailBody($message, $abonnement),
        [$abonnement->lastFactuurCon()->factuur->factuurnummer .".pdf" => url("/facturatie/facturen/open/" .$abonnement->lastFactuurCon()->factuur->token) . ".pdf"]
      ));
    }

    private function formatEmailBody($message, $abonnement){
      $keyWords = [
        "{last_name}" => $abonnement->achternaam
      ];

      foreach ($keyWords as $key => $value){
        $message = str_replace($key,$value,$message);
      }
      return $message;
    }

    public function user($subdomein, $token){
      $abonnement = Abonnementen::where("token", $token)->with("items")->with("_bv")->with("facturen")->firstOrFail();
      $cItems = AbonnementItems::where("abonnement_id", $abonnement->id)->with("item")->get()->keyBy("item_id");
      $conFacturen = AbonnementFacturen::where("abonnement_id", $abonnement->id)->with("factuur")->orderBy("id", "DESC")->get()->keyBy("factuur_id");
      foreach($conFacturen as $con){
        $clone = clone $con;
        $items = json_decode($clone->items, true);
        $clone->items = $items;
        foreach($items as $i => $p){
          $cFacturen[$i][] = $clone;
        }
      }

      return view("abonnementen.password",[
        "abonnement" => $abonnement,
        "cItems" => $cItems,
        "cFacturen" => $cFacturen ?? [],
        "conFacturen" => $conFacturen,
      ]);
    }
    public function account(Request $request){
      $abonnement = Abonnementen::where("token", $request->token)->with('items')->firstOrFail();
      if(!WpPassword::check($request->password, $abonnement->wachtwoord)){
        return redirect("abonnementen/user/".$abonnement->token."?message=".urlencode('<b class="text-danger" >Onjuiste wachtwoord!</b>'));
      }

      Abonnementen::where("token", $request->token)->update([
        "voornaam" => $request->voornaam,
        "achternaam" => $request->achternaam,
        "email" => $request->email,
        "telefoonnummer" => $request->telefoonnummer,
        "straat" => $request->straat,
        "huisnummer" => $request->huisnummer,
        "postcode" => $request->postcode,
        "plaats" => $request->plaats,
      ]);

      $abonnement->refresh();
      $abonnement->updateInfo();

      return redirect("abonnementen/user/".$abonnement->token."?message=".urlencode('<b class="text-success" >Gegevens gewijzigd!</b>'));
    }
    public function passwordStore(Request $request){
      $abonnement = Abonnementen::where("token", $request->token)->with("items")->first();
      if(!WpPassword::check($request->oldPassword, $abonnement->wachtwoord)){
        return redirect("abonnementen/user/".$abonnement->token."?message=".urlencode('<b class="text-danger" >Onjuiste wachtwoord!</b>'));
      }

      $pass = WpPassword::make($request->newPassword);
      Abonnementen::where("token", $abonnement->token)->update([
        "wachtwoord" => $pass,
      ]);

      $abonnement->refresh();
      $abonnement->updateInfo();

      return redirect("abonnementen/user/".$abonnement->token."?message=".urlencode('<b class="text-success" >Wachtwoord gewijzigd!</b>'));
    }
    public function passwordReset($subdomein, $token){
      $abonnement = Abonnementen::where("token", $token)->with("items")->first();

      $password = randomString(10);
      $hash = WpPassword::make($password);

      Abonnementen::where("token", $abonnement->token)->update([
        "wachtwoord" => $hash,
      ]);


      $abonnement->refresh();
      $abonnement->updateInfo();

      Mail::to($abonnement->email)->send(new abonnementPasswordReset($subdomein, $abonnement, $password));

      return redirect("abonnementen/user/".$abonnement->token."?message=".urlencode('<b class="text-success" >Wachtwoord succesvol gereset, Controleer uw email voor nieuwe inloggegevens</b>'));
    }

    public function confirmFactuur(Request $request){
      $date = Carbon::now()->format("Y-m-d");
      if(isset($request->date)){
        $date = Carbon::parse($request->date)->format("Y-m-d");
      }
      Facturen::where("id", $request->factuur)->update([
        "status" => "Betaald",
        "betaald_op" => $date,
        'exact_globe_update_required' => 1,
      ]);
      $cFactuur = AbonnementFacturen::where("factuur_id", $request->factuur)->with("factuur", "abonnement")->first();
      $items = json_decode($cFactuur->items, true);
      $nItems = [];
      foreach($items as $i => $periode){
        $item = AbonnementenItems::where("id", $i)->first();
        $s = Carbon::now()->format("Y-m-d");
        if($item->geldigheid == "Jaar"){
          $e = Carbon::now()->addYear()->format("Y-m-d");
        }
        elseif($item->geldigheid == "Maand"){
          $e = Carbon::now()->addMonth()->format("Y-m-d");
        }
        elseif($item->geldigheid == "Week"){
          $e = Carbon::now()->addWeek()->format("Y-m-d");
        }
        elseif ($item->geldigheid == "Uitgaves"){
          $e = $periode['end'];
          $s = $periode['start'];
        }
        else{
          $e = Carbon::now()->addWeek()->format("Y-m-d");
        }
        $nItems[$i] = ["start" => $s, "end" => $e];
      }
      $cFactuur->items = json_encode($nItems);
      $cFactuur->save();

      $facturen = Facturen::with("klant", "all_emails")->get()->keyBy("id");
      $cFacturen = AbonnementFacturen::with("factuur", "abonnement")->orderBy("id", "DESC")->get();

      $this->sendPaymentConfirmationMail($cFactuur->abonnement, getSettingValue("abonnement_email_betaling_voldaan"));

      return response()->json([
        "facturen" => $facturen,
        "cFacturen" => $cFacturen,
        "cFactuur" => $cFactuur
      ], 201);
    }


    public function wpLevels(Request $request){
      $con = setHostConnection("mysql", $request->db, $request->db_login, $request->db_pass, $request->db_name);
      try {
        $con->getPdo();
        $roles = $con->table($request->db_prefix.'_options')->where('option_name', $request->db_prefix.'_user_roles')->first();
        $roles = unserialize($roles->option_value);

        return response()->json(['roles' => $roles], 201);
      }
      catch(Exception $e){
        return response()->json(false, 500);
      }
    }
    public function toegang(Request $request){
      $abonnement = Abonnementen::where("id", $request->abonnement)->with("items")->with("_bv")->first();
      $item = AbonnementenItems::where("id", $request->item)->first();
      $state = $request->state;

      $cItem = AbonnementItems::where("abonnement_id", $abonnement->id)->where("item_id", $item->id)->first();

      $data = [
        'hash' => $abonnement->wachtwoord,
        'abonnement' => $abonnement,
        'abonnementItem' => $abonnement,
      ];

      if($state == 1){ $item->giveToegang($data); }
      else{$item->noToegang($data); }

      if($request->send == 1){
        if($state == 1){
          Mail::to($abonnement->email)->send(new abonnementAccess(getSubdomein(), $abonnement,$item, "Abonnement vernieuwd", $request->sender, $request->mess ));
        }
        else{
          Mail::to($abonnement->email)->send(new abonnementAccess(getSubdomein(), $abonnement,$item, "Abonnement beëindigd", $request->sender, $request->mess ));
        }
      }

      $group = AbonnementItems::where("active", 1)->with("abonnement")->get()->groupBy("item_id");
      return response()->json(["group" => $group], 201);
    }
    public function kortingIndex(){
      $korting = AbonnementenKortingsgroepen::where('active', 1)->get()->keyBy("id");
      $cItems = AbonnementItems::whereNotNull("kortingsgroep")->get()->groupBy("kortingsgroep");
      return view("abonnementen.korting",[
        "korting" => $korting,
        "cItems" => $cItems,
      ]);
    }
    public function selectKorting($subdomain, $kortingId, $abonnementId, $itemId){
      if($kortingId == 0){
        AbonnementItems::where("abonnement_id", $abonnementId)->where("item_id", $itemId)->update(["kortingsgroep" => null]);
        return redirect("abonnementen")->with("status", "Kortingscode verwijderd!");
      }
      else{
        AbonnementItems::where("abonnement_id", $abonnementId)->where("item_id", $itemId)->update(["kortingsgroep" => $kortingId]);
        return redirect("abonnementen")->with("status", "Kortingscode toegepast!");
      }
    }
    public function prefixStore(Request $request){
      AbonnementenFormulieren::where('token', $request->token)->update([
        'factuur_prefix' => $request->prefix,
      ]);
      return response(null, 201);
    }

    public function upload(Request $request){
      $request->validate([
        'file' => 'required|mimes:xls,xlsx'
      ]);

      Excel::import(new AbonnementenImport([
				'formulier_id' => $request->form,
	      'item_id' => $request->item,
	      'vereniging_id' => $request->vereniging,
	      'remove_inactive' => isset($request->remove_inactive),
	      'paid' => isset($request->paid)
      ]),$request->file('file'));
      return redirect('/abonnementen')->with('status', 'Abonnementen succesvol toegevoegd.');
    }

    public function verenigingen(){
      $verenigingen = AbonnementenVerenigingen::where(['active' => 1])->get();

      return view('abonnementen.verenigingen', [
        'verenigingen' => $verenigingen,
      ]);
    }
    public function verenigingenStore(Request $request){
      // ::update() zonder where() werkt niet.
      AbonnementenVerenigingen::where(['active' => 1])->update(['active' => 0]);

      foreach ($request->verenigingen ?? [] as $vereniging){
        if(!isset($vereniging['id'])){
          AbonnementenVerenigingen::insert(['name' => $vereniging['name']]);
          continue;
        }

        AbonnementenVerenigingen::where(['id' => $vereniging['id']])->update([
          'name' => $vereniging['name'],
          'active' => 1,
        ]);
      }

      return redirect('iframe/abonnementen/verenigingen');
    }

  public function downloadUitgave($subdomain, $itemId, $date){
    return Excel::download(new AbonnementExport(['item_id' => $itemId, "date" => $date]), "Uitgave " . Carbon::parse($date)->format('Y-m-d') .'.xlsx');
  }
}
