<?php

namespace App\Http\Controllers;

use App\Aanvragen;
use App\Checklists;
use App\ChecklistsDetails;
use App\ChecklistsDetailsTemplate;
use App\ChecklistsDetailsValues;
use App\ChecklistsFiles;
use App\ChecklistsKeywords;
use App\ChecklistsKeywordsFiles;
use App\ChecklistsTemplates;
use App\ChecklistsTemplatesKeywords;
use App\ExplorerFiles;
use App\Http\Controllers\Api\FileController;
use App\Klanten;
use App\OffertesDatasets;
use App\Project;
use App\Templates;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Auth;
use Storage;
use PDF;

class ChecklistsController extends Controller
{

    private $relations = ['user', 'klant', '_bv', 'template', 'keywords'];

  public function index() {
    return view('checklists.index');
  }

    public function indexByKlant($subdomein, $klant){
      $klanten = Klanten::where('status', 1)->orderBy('naam')->get()->keyBy('id');
      $templates = Templates::get()->groupBy('bv');
      $checklists = Checklists::with('user', 'klant', '_bv', 'template', 'keywords')->where('klant_id', $klant)->orderBy('created_at', 'DESC')->get();

      foreach($checklists as $checklist){
        $checklist->keywords = keyBy($checklist->keywords, 'keyword');
      }

      return view('checklists.index', [
        'checklists' => $checklists,
        'klanten' => $klanten,
        'templates' => $templates,
      ]);
    }

    public function create($subdomain, $template, $id = null){
      $template = ChecklistsTemplates::where('id', $template)->with('_bv', 'keywords', 'details')->first();
      $klanten = Klanten::where('status', 1)->where('bv', $template->bv)->with('locaties')->get();
      $aanvragen = Aanvragen::where('active', 1)->whereNotNull('klant_id')->get()->groupBy('klant_id');
      $datasets = OffertesDatasets::with('items')->get()->keyBy('id');
      $detailTemplates = ChecklistsDetailsTemplate::with("velden")->get()->keyBy("id");
      $explorerFiles = ExplorerFiles::where("active", 1)->get()->keyBy("id");

      if(isset($id)){
        $checklist = Checklists::where('id', $id)->with("keywords", "details", "files", "klant", "klant_locatie")->firstOrFail();

        foreach($checklist->details as $detail){
          $detail->values = keyBy($detail->values, 'keyword');
        }
        foreach (json_decode($checklist->aanvragen) as $id){
          $selectedAnv[$id] = true;
        }
        foreach ($checklist->keywords as $item){
          foreach($template->keywords as $key){
            if($key->keyword == $item->keyword){

              $value = $item->value;
              if($item->type == 'image'){
                $ids = ChecklistsKeywordsFiles::where('checklist_keyword_id', $item->id)->pluck('file_id')->toArray();
                $value = ExplorerFiles::whereIn('id', $ids )->get();
              }

              $key->value = $value;
              $key->file_src = $item->file;
              $key->opmerking_value = $item->opmerking;
            }
          }
        }
      }

      foreach($klanten as $klant){
        $klant->titel = $klant->naam ?? $klant->contactpersoon_voornaam." ".$klant->contactpersoon_achternaam;
      }

      return view('checklists.create',[
        "template" => $template,
        "klanten" => $klanten,
        "aanvragen" => $aanvragen,
        "datasets" => $datasets,
        "checklist" => $checklist ?? null,
        "selectedAnv" => $selectedAnv ?? [],
        "detailTemplates" => $detailTemplates,
        "explorerFiles" => $explorerFiles,
        "users" => getUsers(),
      ]);

    }
    public function store($subdomein, $template, $id = null, Request $request){
      if(!isset($request->checklist)){
        $template = ChecklistsTemplates::where('id', $request->template)->first();
        $count = Checklists::count();
        $nummer = 'CK'.date("Y").str_pad($count + 1,4, 0, STR_PAD_LEFT);
        $id = Checklists::insertGetId([
          "template_id"  => $template->id,
          "klant_id" => $request->klant ?? null,
          "user_id" => Auth::user()->id,
          "klant_locatie_id" => $request->klant_locatie ?? null,
          "aanvragen" => json_encode($request->aanvragen ?? []),
          "checklistnummer" => $nummer,
          "bv" => $template->bv,
          "datum" => Carbon::now(),
          "project_id" => $request->project ?? null,
          "token" => randomString(25),
          "created_at" => Carbon::now(),
        ]);

        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de checklist '".$nummer."' uitgebracht.";
        actLog($log, Auth::user()->id, 35);
      }
      else{
        $id = $request->checklist;
        $checklist = Checklists::where('id', $request->checklist)->first();
        Checklists::where('id', $request->checklist)->update([
          "klant_id" => $request->klant ?? $checklist->klant_id,
          "klant_locatie_id" => $request->klant_locatie ?? $checklist->klant_locatie_id,
          "aanvragen" => isset($request->aanvragen) ? json_encode($request->aanvragen) : $checklist->aanvragen,
          "project_id" => $request->project ?? null,
        ]);

        $log = Auth::user()->name." ".Auth::user()->lastname." heeft de checklist '".$checklist->checklistnummer."' gewijzigd.";
        actLog($log, Auth::user()->id, 35);
      }

      $this->storeChecklistItems($id, $request);

      $this->clearDetails($id);
      foreach ($request->detail_title ?? [] as $index => $title){
        $detailTemplate = ChecklistsDetailsTemplate::where('id', $request->detail_template_id[$index])->with('velden')->first();
        $detailImages = $request->file('detail_img') ?? [];

        if(isset($detailImages[$index])){
          $file = $detailImages[$index];
          $path = 'checklists/files/';
          $string = randomString(15).".".$file->extension();
          $fullPath = $path.$string;
          Storage::disk('client')->putFileAs($path, $file, $string);
        }
        else{
          $fullPath = $request->detail_img_hidden[$index] ?? null;
        }

        $detailId = ChecklistsDetails::insertGetId([
          "checklist_id" => $id,
          "detail_template_id" => $detailTemplate->id,
          "keyword" => $detailTemplate->keyword ?? '',
          "name" => $detailTemplate->name ?? '',
          "title" => $request->detail_title[$index] ?? '',
          "image" => $fullPath,
          "description" => $request->detail_description[$index] ?? '',
          "adjustment" => $request->detail_adjustment[$index] ?? '',
          "price" => $request->detail_price[$index] ?? '',
          "created_at" => Carbon::now(),
        ]);
        foreach ($detailTemplate->velden as $row){
          ChecklistsDetailsValues::insert([
            "detail_id" => $detailId,
            "name" => $row->name,
            "keyword" => $row->keyword,
            "value" => $request->detail[$index][$row->keyword] ?? null,
            'created_at' => Carbon::now(),
          ]);
        }
      }

      $this->clearOldFiles($id);
      foreach($request->explorer_files ?? [] as $i => $src){
        ChecklistsFiles::insert([
          "checklist_id" => $id,
          "src" => "explorer/files/".$src,
          "name" => $request->explorer_files_names[$i] ?? "",
        ]);
      }
      foreach($request->old_files ?? [] as $i => $src){
        ChecklistsFiles::insert([
          "checklist_id" => $id,
          "src" => $src,
          "name" => $request->old_files_names[$i] ?? "",
        ]);
      }
      foreach($request->bijlage ?? [] as $file){
        $path = "checklists/files/";
        $string = randomString(20).".".$file->extension();

        Storage::disk("client")->putFileAs($path, $file, $string);
        ChecklistsFiles::insert([
          "checklist_id" => $id,
          "src" => $path.$string,
          "name" => $file->getClientOriginalName(),
        ]);

      }

      $checklist = Checklists::where('id', $id)->with('project')->first();
      $checklist->setVervolgAfspraak();

      if(getSettingCheckbox('checklist_custom_project_flow') && $checklist->project){
        $stappen = getSettingJson('checklist_custom_project_flow_stappen');
        $laatstestap = end($stappen);

        if ($checklist->template_id == $laatstestap['template']){
          $checklist->finnishProjectFlow();
        }
      }

      return redirect('checklists')->with('status', 'Checklist opgeslagen!');
    }

    public function show($subdomein, $id){
      $checklist = Checklists::where('id', $id)->with("user", "klant", "_bv", "keywords", "details", "project")->firstOrFail();
      $aanvragen = Aanvragen::get()->keyBy('id');
      if(!Auth::user()->hasPermissionTo('Alle checklists bekijken')){
        if(!Auth::user()->hasPermissionTo('Eigen checklists bekijken') || $checklist->user_id != Auth::user()->id){
          return redirect('checklists');
        }
      }

      return view('checklists.show',[
        'checklist' => $checklist,
        'aanvragen' => $aanvragen,
      ]);

    }
    public function preview($subdomein, $template_id, $id){
        $data = $this->create($subdomein, $template_id, $id)->getData();
        $data['preview'] = true;

        return view('checklists.create', $data);
    }
    public function confirmKlant(Request $request){
      Checklists::where('id', $request->checklist)->update(['klant_id' => $request->klant]);
      return response(null, 201);
    }

    public function pdf($subdomein, $token){
      $checklist = Checklists::where('token', $token)->with('user', 'klant', '_bv', 'template', 'klant_locatie')->firstOrFail();
      $keywords = ChecklistsKeywords::where('checklist_id', $checklist->id)->with('user', 'keyword_item')->get()->keyBy('keyword');


      $pdf = PDF::loadView("checklists.pdf.".getClientId()."template".$checklist->template_id, [
        'checklist' => $checklist,
        'keywords' => $keywords,
      ]);
      return $pdf->stream( $checklist->checklistnummer.".pdf");
    }
    public function projectPdf($subdomein, $projectid){
      $project = Project::where('id', $projectid)->with('checklists')->firstOrFail();

      $pdf = PDF::loadView("checklists.pdf.".getClientId()."projecttemplate", [
        'project' => $project,
      ]);
      return $pdf->stream( $project->projectnr.".pdf");
    }

    private function storeChecklistItems($id, $request){
      ChecklistsKeywords::where('checklist_id', $id)->delete();
      ChecklistsKeywordsFiles::where('checklist_id', $id)->delete();

      $mailkeys = [];

      $images = $request->img ?? [];
      foreach($request->items as $i => $value){
        $key = ChecklistsTemplatesKeywords::where('id', $i)->first();

        $file = $request->img_hidden[$i] ?? null;
        if(isset($images[$i])){
          $file = $this->storeImage($images[$i]);
        }

        if($key->type == 'custom_row'){
          $rows = [];
          foreach($value ?? [] as $row){
            $temp = json_decode($key->data, true)['rows'];
            foreach ($temp as $x => $e){
              $temp[$x]['value'] = $row[$e['keyword']] ?? null;
            }
            $rows[] = $temp;
          }
          $value = json_encode($rows);
        }
        if($key->type == 'signature'){
          if(mb_substr($value, 0, 9) == 'existing_'){
            $value = str_replace('existing_', '', $value);
          }
          else{
            if(isset($value)){
              $string = str_replace("data:image/png;base64,","", $value);
              $string = str_replace(' ', '+', $string);
              $base = base64_decode($string);
              $path = randomString(25).".png";
              Storage::disk("client")->put('signatures/'.$path, $base);
              $value = 'signatures/'.$path;
            }
          }
        }
        if($key->type == 'image'){
          $images = $value;
          $value = null;
        }

        $checklist_keyword_id = ChecklistsKeywords::insertGetId([
          "checklist_id" => $id,
          "name" => $key->name,
          "value" => $value,
          "keyword" => $key->keyword,
          "type" => $key->type,
          "data" => $key->data,
          "file" => $file,
          "opmerking" => $request->opmerking[$i] ?? null,
          "created_at" => Carbon::now(),
        ]);

        if($key->type == 'image'){
          foreach($images ?? [] as $file_id){
            ChecklistsKeywordsFiles::insert([
              "checklist_id" => $id,
              "checklist_keyword_id" => $checklist_keyword_id,
              "file_id" => $file_id,
            ]);
            ExplorerFiles::moveFile($file_id, "/Checklists/({$id})");
          }
        }

        if($key->mail_on_value){
          $mailkeys[] = $checklist_keyword_id;
        }

      }

      foreach ($mailkeys as $mailkey){
        $keyword = ChecklistsKeywords::where('id', $mailkey)->first();
        $keyword->mailOnValue();
      }

    }
    private function storeImage($file){
      $path = 'checklists/img/';
      $string = randomString(15).".".$file->extension();
      $fullPath = $path.$string;
      Storage::disk('client')->putFileAs($path, $file, $string);
      return $fullPath;
    }
    private function clearDetails($id){
      $details = ChecklistsDetails::where("checklist_id", $id)->get();
      foreach($details ?? [] as $detail){
        if(isset($detail->image)){
          $request = (new Request())->merge([
            'file' => $detail->file,
          ]);
          (new FileController())->delete($request);
        }
        ChecklistsDetailsValues::where("detail_id", $detail->id)->delete();
        ChecklistsDetails::where("id", $detail->id)->delete();
      }
    }

    private function clearOldFiles(int $id): void{
      ChecklistsFiles::where("checklist_id", $id)->delete();
    }
}
