<?php

namespace App\Http\Controllers;

use App\Aanvragen;
use App\Beschikbaarheid;
use App\BV;
use App\Device;
use App\ExplorerFiles;
use App\Exports\ProjectPlanningExport;
use App\Feestdagen;
use App\Http\Middleware\GoogleCalendarApi;
use App\Machines;
use App\Mail\BlankMail;
use App\OffertePlanning;
use App\Offertes;
use App\PlanningFiles;
use App\PlanningProjecttaken;
use App\PlanningMachines;
use App\Project;
use App\UrenRegistratie;
use App\Verlof;
use App\Verlofreden;
use App\WerkbonnenTemplates;
use App\Werkuren;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Storage;
use PDF;
use DB;
use Route;
use App\Legenda;
use App\Offerteteksten;
use App\Planning;
use App\User;
use App\Klanten;
use App\PlanningCustomRows;
use App\PlanningReleaseWerkbon;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Symfony\Component\VarDumper\Dumper\DataDumperInterface;
use Illuminate\Support\Collection;

class PlanningController extends Controller{

    public static $relations = [];

    public function inplannen($subdomein, $type, $y, $p){

      injectSettings('planning.planning');

      $projectId = $_GET['projectid'] ?? null;

      if($type == "dag"){
        $date = Carbon::parse($y.'-'.$p);
        $period = CarbonPeriod::create($date->format('Y-m-d'), $date->format('Y-m-d'));
      }
      elseif($type == "week"){
        $date = Carbon::now()->setISODate($y, $p);
        $period = CarbonPeriod::create($date->startOfWeek()->format('Y-m-d'), $date->endOfWeek()->format('Y-m-d'));
      }
      elseif($type == "maand"){
        $date = Carbon::now()->startOfMonth()->setYear($y)->setMonth($p);
        $period = CarbonPeriod::create($date->startOfMonth()->format('Y-m-d'), $date->endOfMonth()->format('Y-m-d'));
      }
      elseif($type == "kwartaal"){
        $date = Carbon::now()->setYear($y)->setMonth($p);
        $period = CarbonPeriod::create($date->firstOfQuarter()->format('Y-m-d'), $date->lastOfQuarter()->format('Y-m-d'));
      }
      elseif($type == "jaar"){
        $date = Carbon::now()->setYear($y);
        $period = CarbonPeriod::create($date->firstOfYear()->format('Y-m-d'), $date->lastOfYear()->format('Y-m-d'));
      }
      elseif($type == "periode"){
        $date = Carbon::parse($y);
        $date_end = Carbon::parse($p);
        $period = CarbonPeriod::create($date->format('Y-m-d'), $date_end->format('Y-m-d'));
      }
      else{
        abort(404);
      }

      $full = ($_GET['full'] ?? null) == 'true';
      if(!$full && Carbon::now()->between($period->start, $period->end) && $type != "periode"){
          $period->setStartDate(date('Y-m-d'));
      }

      $verlofRedenen = Verlofreden::get();
      $klanten = Klanten::orderBy('naam')->get()->keyBy("id");
      $legenda = Legenda::where('active', 1)->orderBy("naam","ASC")->get()->keyBy("id");
      $ver = Verlof::where("Akkoord", 1)->orderBy("datum")->get();
      $feestdag = Feestdagen::where("disabled", 0)->get()->groupBy('datum');

      $beschikbaarheid = groupByGroupBy(Beschikbaarheid::where("date", ">=", $period->start->format('Y-m-d'))->where('date', "<=", $period->end->format('Y-m-d'))->get(), 'user_id', 'date');
      $machines = Machines::where("active", 1)->orderBy("name", "ASC")->with('group')->get();
      $werkbonTemplates = WerkbonnenTemplates::with('keywords')->get();

      $users = $this->usersOrderedByRoleBySetting();

      $verlof = [];
      $verlofDag = [];

      foreach($ver as $row){
        $verlof[$row->datum][$row->medewerker_id][] = $row;
        if($row->van == null && $row->tot == null){
          $verlofDag[$row->datum][$row->medewerker_id] = true;
        }
      }
      $options = json_decode(getSettingValue('planning_elementen'), true);
      if(isset($options['aanvragen'])){
          $aanvragen = Aanvragen::where("status", "In behandeling")->with("klanten", "planning")->get()->keyBy("id");
      }
      if(isset($options['offerteActiviteiten'])){
          $offertes = Offertes::with("klanten", "locatie")->get()->keyBy("id");
          $ofr = OffertePlanning::where("begin", ">=", $period->start->format("Y-m-d"))->where("eind", "<=", $period->end->format("Y-m-d"))->with("offerte")->orderBy("begin", "ASC")->get();
          foreach($ofr as $row){
              if($row->offerte->type == 1 && $row->offerte->active == 1){
                  $offertePlanning[$row->id] = $row;
              }
          }
      }
      if(isset($options['projecten'])){
          $planningByProject = Planning::whereNotNull("project_id")->with("machines")->get()->keyBy("project_id");
          $projects = Project::where('active', 1)->with("taken", "completed_taken", "planning", "werkbonnen.values")->orderBy("created_at", "DESC")->get()->keyBy("id");
          $projects = $this->werkbonnenOpvolgen($projects);
        }

      if(Auth::user()->google_token){
        $agendas = getGoogleAgendas(Auth::user()->google_token);
      }

      return view("planning.planning",[
        "full" => $full,
        "date" => $date,
        "type" => $type,
        "period" => $period,
        "year" => $y,
        "target" => $p,
        "maanden" => getMaanden(),
        "users" => $users,
        "legenda" => $legenda,
        "planning" => [],
        "klanten" => $klanten,
        "bvs" => Auth::user()->bvs()->keyBy("id"),
        "projectId" => $projectId,

        "verlof" => $verlof,
        "verlofDag" => $verlofDag,
        "verlofRedenen" => $verlofRedenen,
        "subdomein" => $subdomein,
        "weekdagen" => getWeekdagen(),
        "agendas" => $agendas ?? [],
        "beschikbaarheid" => $beschikbaarheid,
        "machines" => $machines,
        "werkbonTemplates" => $werkbonTemplates,

        "planningByProject" => $planningByProject ?? [],
        "projecten" => $projects ?? [],
        "offertes" => isset($offertes) ? $offertes->where('type', 1) : [],
        "allOffertes" => $offertes ?? [],
        "aanvragen" => $aanvragen ?? [],
        "offertePlanning" => $offertePlanning ?? [],

        "feestdag" => $feestdag,

        "planningDatasets" => getPlanningSettingsDatasets(),

      ]);
    }
    public function index($subdomein, $year, $month){
        $data = $this->inplannen($subdomein, "maand", $year, $month)->getData();
        $data["year"] = $year;
        $data["month"] = $month;
        $data["preview"] = true;

        return view("planning.planning", $data);
    }
    public function overzicht($subdomain, $type, $year, $period){
      $data = $this->inplannen($subdomain, $type, $year, $period)->getData();
      $data["preview"] = true;

      return view("planning.planning", $data);
    }
    public function project(){
        return view("planning.project");
    }

    public function dagOverzicht($subdomein, $date){
        $date = Carbon::parse($date);

        if(isset(userPermissions(Auth::user()->id)["Volledige planning bekijken"])){
            $planning = Planning::where('datum', $date)->with('legenda', 'aanvraag', 'offerte', 'project', 'klant', 'user')->orderBy('begin', "ASC")->get()->keyBy("id");
            $werkuren = Werkuren::where("active", 1)->whereDate("date", $date)->orderBy("start", "ASC")->with("user")->get()->groupBy("user_id");
        }
        else{
            $planning = Planning::where(['datum' => $date, "user_id" => Auth::user()->id])->with('legenda', 'aanvraag', 'offerte', 'project', 'klant', 'user')->orderBy('begin', "ASC")->get()->keyBy("id");
            $werkuren = Werkuren::where(["active" => 1, "user_id" => Auth::user()->id])->whereDate("date", $date)->orderBy("start", "ASC")->with("user")->get()->groupBy("user_id");
        }


        return view("planning.dagoverzicht", [
          "maanden" => getMaanden(),
          "date" => $date,
          "planning" => $planning,
          "werkuren" => $werkuren,
        ]);
    }
    public function selectMonth(){

        return view("planning.inplannen", [
          "maanden" => getMaanden(),
        ]);
    }

    public function werkuren($subdomein, $type, $date){
        $date = Carbon::parse($date);
        $beschikbaarheid = groupByGroupBy(Beschikbaarheid::whereYear('date', $date->year)->orderBy("start", "ASC")->get(), "date", "user_id");
        $werkuren = groupByGroupBy(Werkuren::where("active", 1)->whereYear("date", $date->year)->orderBy("start", "ASC")->with('user')->get(), "date", "user_id");

        if($type == "week"){
            $period = CarbonPeriod::create($date->startOfWeek()->format('Y-m-d'), $date->endOfWeek()->format('Y-m-d'));
        }
        elseif($type == "maand"){
            $period = CarbonPeriod::create($date->startOfMonth()->format('Y-m-d'), $date->endOfMonth()->format('Y-m-d'));
        }
        elseif($type == "kwartaal"){
            $period = CarbonPeriod::create($date->firstOfQuarter()->format('Y-m-d'), $date->lastOfQuarter()->format('Y-m-d'));
        }
        else{
            $period = CarbonPeriod::create($date->firstOfYear()->format('Y-m-d'), $date->lastOfYear()->format('Y-m-d'));
        }

        return view('planning.werkuren', [
          "date" => Carbon::parse($date),
          "type" => $type,
          "period" => $period,
          "werkuren" => $werkuren,
          "users" => getUsers(),
          "beschikbaarheid" => $beschikbaarheid,
        ]);
    }
    public function dagplanningPdf($subdomain, $date){
      if(!view()->exists("planning.pdf.C".getClientId()."dagplanning")){
        $cdate = Carbon::parse($date)->format('Y-m-d');
        return redirect('/iframe/planning/dagplanning/'.$cdate);
      }
      $pdf = $this->generatePdf([
        'begin' => $date,
        'eind' => $date,
      ]);

      return $pdf->stream( Carbon::parse($date)->format('d-m-Y').".pdf");
    }

    public function dagplanningPeriodePdf($subdomain, $periode){
      $periode = json_decode($periode,1);
      if(!view()->exists("planning.pdf.C".getClientId()."dagplanning")){
        $cdate = Carbon::parse($periode['start'])->format('Y-m-d');
        return redirect('/iframe/planning/dagplanning/'.$cdate);
      }
      $pdf = $this->generatePdf([
        'begin' => $periode['start'],
        'eind' => $periode['eind'],
      ]);

      return $pdf->stream( Carbon::parse($periode['start'])->format('d-m-Y'). "_" . Carbon::parse($periode['eind'])->format('d-m-Y') . ".pdf");
    }

    public function generatePdf($options){
      $blade = "planning.pdf.C".getClientId()."dagplanning";
      $planning = Planning::where('datum', '>=', $options['begin'])->where('datum', '<=', $options['eind'])->with('project', 'user', 'taken', 'legenda', 'custom')->get();
      $groupedPlanning = $planning->groupBy('datum');

      $released = $planning->pluck('released')->toArray();
      $allReleased = !in_array(0, $released);

      $data = [
        'planning' => $groupedPlanning,
        'allReleased' => $allReleased,
      ];

      $pdf = PDF::loadView($blade, $data);

      $pages = getPDFPages($pdf);
      $data['pages'] = $pages;

      return PDF::loadView($blade, $data);
    }

    public function storeWerkuren($subdomein, $type, $date, Request $request){
        try{
            if(Carbon::parse($request->start)->gte(Carbon::parse($request->end))){
                return redirect("/planning/werkuren/inplannen/$type/$date")->with("warning", "Ongeldige eindtijd");
            }

            $user = User::where("id", $request->user)->first();

            $uren = Carbon::parse($request->start)->diffInMinutes(Carbon::parse($request->end)) / 60;

            $data = [
              "user_id" => $request->user,
              "date" => $request->date,
              "start" => $request->start,
              "end" => $request->end,
              "opmerking" => $request->opmerking,
              "werkuren" => $uren,
            ];

            if(isset($request->werkuren_id)){
                Werkuren::where("id", $request->werkuren_id)->update($data);
            }
            else{
                Werkuren::insert($data);
            }

            actLog(
              Auth::user()->name . " " . Auth::user()->lastname . " heeft werkuren van $user->name $user->lastname ingevoerd",
              Auth::user()->id,
              9,
            );

            return redirect("/planning/werkuren/inplannen/$type/$date")->with("status", "Werkuren opgeslagen");
        }
        catch(\Exception $e){
            actError($e);
            return redirect("/planning/werkuren/inplannen/$type/$date")->with("warning", "Er is iets foutgegaan!");
        }


    }
    public function deleteWerkuren(Request $request){
        $werkuren = Werkuren::where("id", $request->id)->with("user")->firstOrFail();

        $werkuren->active = 0;
        $werkuren->save();

        actLog(
          Auth::user()->name . " " . Auth::user()->lastname . " heeft werkuren van " . $werkuren->user->name . " " . $werkuren->user->lastname . " verwijderd",
          Auth::user()->id,
          9,
        );

        return response(null, 201);
    }

    public function storeLegenda(Request $request){
        $id = Legenda::insertGetId([
          "naam" => $request->legendaNaam,
          "kleur" => $request->legendaHex,
          "omschrijving" => $request->legendaOmschrijving,
          "aanvraag_id" => $request->legendaAanvraagId ?? null,
          "offerte_id" => $request->legendaOfferteId ?? null,
          "created_at" => Carbon::now(),
        ]);

        $legenda = Legenda::where("id", $id)->first();
        $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de activiteit '" . $legenda->naam . "' (" . $legenda->id . ") aan de legenda toegevoegd";
        actLog($log, Auth::user()->id, 9);

        return redirect($request->url)->with("status", "Activiteit toegevoegd");
    }
    public function deleteLegenda($subdomein, $id, $type, $year, $target){
        $legenda = Legenda::where("id", $id)->first();
        Legenda::where('id', $legenda->id)->update(['active' => 0]);

        $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de activiteit '" . $legenda->naam . "' verwijderd";
        actLog($log, Auth::user()->id, 9);

        return redirect("planning/inplannen/" . $type . "/" . $year . "/" . $target)->with("status", "Activiteit verwijderd");
    }
    public function updateLegenda(Request $request){
        Legenda::where("id", $request->id)->update([
          "naam" => $request->legendaNaam,
          "omschrijving" => isset($request->legendaOmschrijving) ? $request->legendaOmschrijving : "",
          "kleur" => $request->legendaHex,
          "aanvraag_id" => $request->legendaAanvraagIdUpdate ?? null,
          "offerte_id" => $request->legendaOfferteIdUpdate ?? null,
        ]);

        $legenda = Legenda::where("id", $request->id)->first();
        $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de legenda activiteit '" . $legenda->naam . "' (" . $legenda->id . ") gewijzigd";
        actLog($log, Auth::user()->id, 9);

        return redirect($request->url)->with("status", "Legenda gewijzigd");
    }

    public function update(Request $request){
        try{
          $planning = Planning::where("id", $request->id)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->firstOrFail();
          $machines_ids = $planning->machines->pluck('id')->toArray();
          $explorer_ids = $planning->explorer->pluck('id')->toArray();

          foreach($request->oldBijlagen ?? [] as $string){
              $file = explode("::", $string);
              $arr = [];
              $arr["name"] = $file[0];
              $arr["src"] = $file[1];
              $files[] = $arr;

          }
          foreach($request->bijlagen ?? [] as $file){
              $string = randomString(15) . "." . $file->extension();
              $obj = [];
              $obj["name"] = $file->getClientOriginalName();
              $obj["src"] = $string;
              $files[] = $obj;
              Storage::disk("client")->putFileAs("planning/files/", $file, $string);
          }

          foreach(json_decode($planning->files) ?? [] as $oFile){
              $delete = true;
              foreach($files ?? [] as $file){
                  if($oFile->src == $file["src"]){
                      $delete = false;
                  }
              }
              if($delete == true){
                  Storage::disk("client")->delete("planning/files/" . $oFile->src);
              }
          }
          $data = [
            "datum" => $request->datum,
            "begin" => $request->begin,
            "eind" => $request->eind,
            "opmerking" => $request->opmerking,
            "aantal_personen" => $request->updateAantal,
            "aantal_medewerkers" => $request->updateAantalMedewerkers,
            "prijs" => $request->updatePrijs,
            "btw" => $request->updateBtw,
            "files" => json_encode($files ?? []),
            "straat" => $request->straat ?? null,
            "huisnummer" => $request->huisnummer ?? null,
            "postcode" => $request->postcode ?? null,
            "plaats" => $request->plaats ?? null,
            "completed" => isset($request->completed) ? 1 : 0,
            "released" => isset($request->released) ? 1 : 0,
            "klant_id" => $planning->klant_id,
            "legenda_id" => $planning->legenda_id,
            "type" => $planning->type,
            "offerte_id" => $planning->offerte_id,
            "aanvraag_id" => $planning->aanvraag_id,
            "project_id" => $planning->project_id,
            "google_agenda" => $planning->google_agenda,
            "reserveerplein_id" => $planning->reserveerplein_id,
            "werkbon_keywords" => $planning->werkbon_keywords,
          ];
          $updPlanning = [];

          if(count($request->updateUser ?? []) <= 1){
            $data["user_id"] = $request->updateUser[0];
            Planning::where("id", $request->id)->update($data);
            $updPlanning[] = $request->id;
          }
          else{
              $orguser = Planning::where("id", $request->id)->first()->user_id;
              Planning::where("id", $request->id)->delete();
              foreach($request->updateUser as $userId){
                  $data["user_id"] = $userId;
                  $planning_id = Planning::insertGetId($data);

                  foreach ($machines_ids as $id){ PlanningMachines::insert(['machine_id' => $id, 'planning_id' => $planning_id]); }
                  foreach ($request->taken ?? [] as $id){ PlanningProjecttaken::insert(['taak_id' => $id, 'planning_id' => $planning_id]); }
                  foreach ($explorer_ids as $id){ PlanningFiles ::insert(['file_id' => $id, 'planning_id' => $planning_id]); }

                  $updPlanning[] = $planning_id;
              }
              if($request->updateUser[0] == $orguser){
                PlanningReleaseWerkbon::where("planning_id", $request->id)->update(["planning_id" => $updPlanning[0]]);
              }
          }
          if(count($request->sameProjectPlannen ?? [])){
            foreach($request->sameProjectPlannen as $id){
              $plan = Planning::where("id", $id)->first();
              $data["user_id"] = $plan->user_id;
              Planning::where("id", $id)->update($data);
              $updPlanning[] = $id;
            }
          }

          PlanningMachines::where("planning_id", $request->id)->delete();
          $this->storeMachines($request, $request->id);
          PlanningCustomRows::where("planning_id", $request->id)->delete();
          $this->storeCustomRows($request, $request->id);
          $this->storeDatasetItems($request, $request->id);
          PlanningProjecttaken::where("planning_id", $request->id)->delete();
          foreach($request->taken ?? [] as $id){ PlanningProjecttaken::insert(['taak_id' => $id, 'planning_id' => $request->id]); }

          $updPlanning = Planning::whereIn("id", $updPlanning)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer", "checklist")->get();

          foreach($updPlanning as $item){
              if($item->checklist){
                $item->checklist->description = $item->checklist->vervolgAfspraakDescription();
              }
              $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de planning (" . $item->id . ") op " . Carbon::parse($item->begin)->format("d-m-Y") . " gewijzigd.";
              actLog($log, Auth::user()->id, 9);
          }

          if(isset($request->pushmelding)){
              $this->pushmelding($request->updateUser);
          }
          if(isset($request->klantMail)){
              $this->klantMailUpdate($planning, $updPlanning);
          }

          $this->storeFiles($request, [$planning]);

          return response()->json(['planning' => $planning, 'updPlanning' => $updPlanning], 201);
        }catch(\Exception $e){
            actError($e);
            return response()->json(null, 500);
        }
    }
    public function delete(Request $request){
        try{
            $planning = Planning::where("id", $request->id)->with('legenda', 'aanvraag', 'project')->firstOrFail();


            OffertePlanning::where("status", $planning->id)->update(["status" => null]);
            Planning::where("id", $planning->id)->delete();

            $name = 'Activiteit: ' . ($planning->legenda->naam ?? '');
            if($planning->type == 'aanvraag'){
                $name = 'Aanvraag: ' . ($planning->aanvraag->aanvraagnummer ?? '');
            }
            elseif($planning->type == 'project'){
                $name = 'Project: ' . ($planning->project->projectnr ?? '');
            }


            $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de planning van de activiteit '" . $name . "' op " . Carbon::parse($planning->begin)->format("d-m-Y") . " verwijderd.";
            actLog($log, Auth::user()->id, 9);

            return response(null, 201);
        }catch(\Exception $e){
            actError($e);
            return response()->json(['message' => 'Element niet gevonden, probeer de planning te herladen'], 500);
        }

    }

    public function storePlanning(Request $request){
      foreach($request->begin ?? [] as $d => $begin){
        if(Carbon::parse($request->eind[$d]) < Carbon::parse($request->begin[$d])){
          return response()->json(['message' => 'Eindtijd ligt voor de begintijd'], 500);
        }
      }

        try{
            $legenda = Legenda::where("id", $request->legendaId)->first();
            $createdAt = Carbon::now();
            $klantId = null;
            $projectId = null;

            $adres = [
              'adres' => null,
              'huisnummer' => null,
              'toevoeging' => null,
              'postcode' => null,
              'plaats' => null,
            ];

            if(isset($request->klantId) && $request->klantId != 0){
                $klantId = $request->klantId;
                $klant = Klanten::where("id", $klantId)->first();

                $adres['adres'] = $klant->straat;
                $adres['huisnummer'] = $klant->huisnummer;
                $adres['toevoeging'] = $klant->toevoeging;
                $adres['postcode'] = $klant->postcode;
                $adres['plaats'] = $klant->plaats;
            }
            if(isset($request->straat) || isset($request->postcode) || isset($request->plaats)){
                $adres['adres'] = $request->straat;
                $adres['huisnummer'] = $request->huisnummer;
                $adres['toevoeging'] = null;
                $adres['postcode'] = $request->postcode;
                $adres['plaats'] = $request->plaats;
            }
            if(isset($request->project)){
                $projectId = Project::insertGetId([
                  "status" => 'Opdracht',
                  "bv" => $klant->bv ?? null,
                  "opdrachtgever" => $klant->naam ?? null,
                  "klant_id" => $klantId,
                  "projectnr" => Project::nummer($klant->bv ?? getBv()->id),
                  "projectnaam" => $legenda->naam,
                  "adres" => $adres['adres'],
                  "huisnummer" => $adres['huisnummer'],
                  "toevoeging" => $adres['toevoeging'],
                  "postcode" => $adres['postcode'],
                  "woonplaats" => $adres['plaats'],
                  "projectleider" => Auth::user()->name . " " . Auth::user()->lastname,
                  "projectleider_id" => Auth::user()->id,
                  "omschrijving" => $legenda->omschrijving,
                  "created_at" => Carbon::now(),
                ]);

                Project::createDefaultFolders($projectId);
            }

          foreach($request->begin as $d => $begin) {
            if (Carbon::parse($request->begin[$d])->day == Carbon::parse($request->eind[$d])->day) {
              foreach ($request->users ?? [] as $user) {
                $planning_id = Planning::insertGetId([
                  "user_id" => $user,
                  "klant_id" => $klantId,
                  "datum" => Carbon::parse($request->begin[$d])->format("Y-m-d"),
                  "begin" => Carbon::parse($request->begin[$d])->format("H:i"),
                  "eind" => Carbon::parse($request->eind[$d])->format("H:i"),
                  "opmerking" => $request->inplannenOpmerking ?? "",
                  "type" => "legenda",
                  "legenda_id" => $request->legendaId,
                  "project_id" => $projectId,
                  "created_at" => $createdAt,
                  "aantal_personen" => $request->actAantal,
                  "prijs" => $request->actPrijs,
                  "btw" => $request->actBtw,
                  "files" => json_encode($files ?? []),
                  "straat" => $request->straat ?? null,
                  "huisnummer" => $request->huisnummer ?? null,
                  "postcode" => $request->postcode ?? null,
                  "plaats" => $request->plaats ?? null,
                ]);

                if($request->opslaanUrenreg){
                    $legendaItem = Legenda::where("id", $request->legendaId)->first();
                    UrenRegistratie::insert([
                        "medewerker_id" => $user,
                        "projectnummer" => '-',
                        "datum" => Carbon::parse($request->begin[$d])->format("Y-m-d"),
                        "begintijd" => Carbon::parse($request->begin[$d])->format("H:i"),
                        "eindtijd" => Carbon::parse($request->eind[$d])->format("H:i"),
                        "gewerkte_uren" => Carbon::parse($request->begin[$d])->diffInMinutes(Carbon::parse($request->eind[$d])) / 60,
                        "opmerkingen" => $legendaItem->naam ?? '',
                        "created_at" => Carbon::now(),
                    ]);
                }

                $this->storeMachines($request, $planning_id);
                $this->storeWerkbonKeywords($request, $planning_id);
                $this->storeCustomRows($request, $planning_id);
                $this->storeDatasetItems($request, $planning_id);
                $herhalingen = $this->storeHerhalingen($request, $planning_id);

                $planning[] = Planning::where('id', $planning_id)->with(
                  "legenda",
                  "klanten",
                  "aanvraag",
                  "project",
                  "machines",
                  "taken",
                  "explorer"
                )->first();

                $planning = array_merge($planning, $herhalingen ?? []);
              }
            }else{
                $diff = CarbonPeriod::create($request->begin[$d], $request->eind[$d]);
                foreach($diff as $row){
                    if(CarbonDmy($request->begin[$d]) == $row->format("d-m-Y")){
                        $bt = Carbon($request->begin[$d])->format("H:i");
                        $et = "23:59";
                    }
                    elseif(CarbonDmy($request->eind[$d]) == $row->format("d-m-Y")){
                        $bt = "00:00";
                        $et = Carbon($request->eind[$d])->format("H:i");
                    }
                    else{
                        $bt = "00:00";
                        $et = "23:59";
                    }

                    foreach ($request->users ?? [] as $user) {
                        $planning_id = Planning::insertGetId([
                            "user_id" => $user,
                            "klant_id" => $klantId,
                            "datum" => $row->format("Y-m-d"),
                            "begin" => $bt,
                            "eind" => $et,
                            "opmerking" => $request->inplannenOpmerking ?? "",
                            "type" => "legenda",
                            "legenda_id" => $request->legendaId,
                            "project_id" => $projectId,
                            "created_at" => $createdAt,
                            "aantal_personen" => $request->actAantal,
                            "prijs" => $request->actPrijs,
                            "btw" => $request->actBtw,
                            "files" => json_encode($files ?? []),
                            "straat" => $request->straat ?? null,
                            "huisnummer" => $request->huisnummer ?? null,
                            "postcode" => $request->postcode ?? null,
                            "plaats" => $request->plaats ?? null,
                        ]);

                        if($request->opslaanUrenreg){
                            $legendaItem = Legenda::where("id", $request->legendaId)->first();
                            UrenRegistratie::insert([
                                "medewerker_id" => $user,
                                "projectnummer" => '-',
                                "datum" => $row->format("Y-m-d"),
                                "begintijd" => $bt,
                                "eindtijd" => $et,
                                "gewerkte_uren" => Carbon::parse($request->begin[$d])->diffInMinutes(Carbon::parse($request->eind[$d])) / 60,
                                "opmerkingen" => $legendaItem->naam ?? '',
                                "created_at" => Carbon::now(),
                            ]);
                        }

                        $this->storeMachines($request, $planning_id);
                        $this->storeWerkbonKeywords($request, $planning_id);
                        $this->storeCustomRows($request, $planning_id);
                        $this->storeDatasetItems($request, $planning_id);
                        $herhalingen = $this->storeHerhalingen($request, $planning_id);

                        $planning[] = Planning::where('id', $planning_id)->with(
                            "legenda",
                            "klanten",
                            "aanvraag",
                            "project",
                            "machines",
                            "taken",
                            "explorer"
                        )->first();
                        $planning = array_merge($planning, $herhalingen ?? []);
                    }
                }
            }
          }

            $logDate = Carbon::parse($request->date)->format("d-m-Y");
            $legenda = Legenda::where("id", $request->legendaId)->first();
            $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft de activiteit '" . $legenda->naam . "' (" . $legenda->id . ") ingepland op " . $logDate;

            actLog($log, Auth::user()->id, 9);

            if(isset($request->google_agenda_id) && isset(Auth::user()->google_token) && isset($planning_id)){
                $this->googleCalendarEvent($request->google_agenda_id, Auth::user()->google_token, [$planning_id]);
            }
            if(isset($request->pushmelding)){
                $this->pushmelding($request->users);
            }
            if(isset($request->klantMail)){
                $this->klantMail([$planning_id]);
            }

            $this->storeFiles($request, $planning ?? []);
            $planning = $this->refreshPlanning($planning ?? []);

            return response()->json(['planning' => $planning ?? []], 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }
    public function storeRowPlanning(Request $request){
        if(Carbon::parse($request->rowEind) < Carbon::parse($request->rowBegin)){
            return response()->json(['message' => 'Eindtijd ligt voor de begintijd'], 500);
        }

        try{
            $offerte = Offertes::where("id", OffertePlanning::where("id", $request->rowId)->first()->offerte_id)->first();

            if(Carbon::parse($request->rowBegin)->day == Carbon::parse($request->rowEind)->day){
                foreach($request->rowUsers ?? [] as $user){
                    $planningId = Planning::insertGetId([
                      "user_id" => $user,
                      "klant_id" => $request->klantId,
                      "datum" => Carbon::parse($request->rowBegin)->format("Y-m-d"),
                      "begin" => Carbon::parse($request->rowBegin)->format("H:i"),
                      "eind" => Carbon::parse($request->rowEind)->format("H:i"),
                      "opmerking" => isset($request->rowOpmerking) ? $request->rowOpmerking : "",
                      "type" => "legenda",
                      "legenda_id" => $request->legendaId,
                      "offerte_id" => $offerte->id,
                      "files" => json_encode($files ?? []),
                      "straat" => $request->straat ?? null,
                      "huisnummer" => $request->huisnummer ?? null,
                      "postcode" => $request->postcode ?? null,
                      "plaats" => $request->plaats ?? null,
                      "created_at" => Carbon::now(),

                    ]);

                    $this->storeMachines($request, $planningId);
                    $this->storeWerkbonKeywords($request, $planningId);
                    $this->storeCustomRows($request, $planningId);
                    $this->storeDatasetItems($request, $planningId);
                    $herhalingen = $this->storeHerhalingen($request, $planningId);

                    $planning[] = Planning::where('id', $planningId)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                    $planning = array_merge($planning, $herhalingen ?? []);

                    $planningIds[] = $planningId;
                    OffertePlanning::where("id", $request->rowId)->update([
                      "status" => $planningId,
                    ]);
                }
                $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->rowBegin)->format("d-m-Y");
                actLog($log, Auth::user()->id, 9);
            }
            else{
                $diff = CarbonPeriod::create($request->rowBegin, $request->rowEind);
                foreach($diff as $row){
                    if(Carbon::parse($request->rowBegin)->format("d-m-Y") == $row->format("d-m-Y")){
                        $bt = Carbon::parse($request->rowBegin)->format("H:i");
                        $et = "23:59";
                    }
                    elseif(Carbon::parse($request->rowEind)->format("d-m-Y") == $row->format("d-m-Y")){
                        $bt = "00:00";
                        $et = Carbon::parse($request->rowEind)->format("H:i");
                    }
                    else{
                        $bt = "00:00";
                        $et = "23:59";
                    }
                    foreach($request->rowUsers as $user){
                        $planningId = Planning::insertGetId([
                          "user_id" => $user,
                          "klant_id" => $request->klantId,
                          "datum" => $row->format("Y-m-d"),
                          "begin" => $bt,
                          "eind" => $et,
                          "opmerking" => isset($request->rowOpmerking) ? $request->rowOpmerking : "",
                          "type" => "legenda",
                          "legenda_id" => $request->legendaId,
                          "offerte_id" => $offerte->id,
                          "files" => json_encode($files ?? []),
                          "straat" => $request->straat ?? null,
                          "huisnummer" => $request->huisnummer ?? null,
                          "postcode" => $request->postcode ?? null,
                          "plaats" => $request->plaats ?? null,
                        ]);

                        $this->storeMachines($request, $planningId);
                        $this->storeWerkbonKeywords($request, $planningId);
                        $this->storeCustomRows($request, $planningId);
                        $this->storeDatasetItems($request, $planningId);
                        $herhalingen = $this->storeHerhalingen($request, $planningId);

                        $planning[] = Planning::where('id', $planningId)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                        $planning = array_merge($planning, $herhalingen ?? []);

                        $planningIds[] = $planningId;
                        OffertePlanning::where("id", $request->rowId)->update([
                          "status" => $planningId,
                        ]);
                    }


                }
                $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->rowBegin)->format("d-m-Y") . " t/m " . Carbon::parse($request->rowEind)->format("d-m-Y");
                actLog($log, Auth::user()->id, 9);
            }

            if(isset($request->google_agenda_id) && isset(Auth::user()->google_token) && isset($planning_id)){
                $this->googleCalendarEvent($request->google_agenda_id, Auth::user()->google_token, $planningIds ?? []);
            }
            if(isset($request->pushmelding)){
                $this->pushmelding($request->rowUsers);
            }
            if(isset($request->klantMail)){
                $this->klantMail($planningIds ?? []);
            }

            $this->storeFiles($request, $planning ?? []);
            $planning = $this->refreshPlanning($planning ?? []);

            return response()->json(['planning' => $planning ?? []], 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }
    public function storeAanvraagPlanning(Request $request){
        if(Carbon::parse($request->eind) < Carbon::parse($request->begin)){
            return response()->json(['message' => 'Eindtijd ligt voor de begintijd'], 500);
        }

        try{
            if(Carbon::parse($request->eind) < Carbon::parse($request->begin)){
                return redirect("planning/inplannen/" . $request->url)->with("warning", "Onjuiste Eindtijd");
            }

            $aanvraag = Aanvragen::where("id", $request->aanvraagId)->first();
            $aanvraag->ingepland = 1;
            $aanvraag->save();

            if(Carbon::parse($request->begin)->day == Carbon::parse($request->eind)->day){
                foreach($request->users ?? [] as $user){
                    $planning_id = Planning::insertGetId([
                      "user_id" => $user,
                      "klant_id" => $request->klantId ?? null,
                      "datum" => Carbon::parse($request->begin)->format("Y-m-d"),
                      "begin" => Carbon::parse($request->begin)->format("H:i"),
                      "eind" => Carbon::parse($request->eind)->format("H:i"),
                      "opmerking" => isset($request->opmerking) ? $request->opmerking : "",
                      "type" => "aanvraag",
                      "aanvraag_id" => $aanvraag->id,
                      "files" => json_encode($files ?? []),
                      "straat" => $request->straat ?? null,
                      "huisnummer" => $request->huisnummer ?? null,
                      "postcode" => $request->postcode ?? null,
                      "plaats" => $request->plaats ?? null,
                      "created_at" => Carbon::now(),
                    ]);
                    $this->storeMachines($request, $planning_id);
                    $this->storeWerkbonKeywords($request, $planning_id);
                    $this->storeCustomRows($request, $planning_id);
                    $this->storeDatasetItems($request, $planning_id);
                    $herhalingen = $this->storeHerhalingen($request, $planning_id);

                    if(isset($request->pushmelding)){
                        $this->pushmelding($user);
                    }
                    $planning[] = Planning::where('id', $planning_id)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                    $planning = array_merge($planning, $herhalingen ?? []);
                }
                $log = Auth::user()->name . "(" . Auth::user()->id . ") Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->begin)->format("d-m-Y");
                actLog($log, Auth::user()->id, 9);
                $planningIds[] = $planning_id;
            }
            else{
                $diff = CarbonPeriod::create($request->begin, $request->eind);
                foreach($diff as $row){
                    if(Carbon::parse($request->begin)->format("d-m-Y") == $row->format("d-m-Y")){
                        $bt = Carbon::parse($request->begin)->format("H:i");
                        $et = "23:59";
                    }
                    elseif(Carbon::parse($request->eind)->format("d-m-Y") == $row->format("d-m-Y")){
                        $bt = "00:00";
                        $et = Carbon::parse($request->eind)->format("H:i");
                    }
                    else{
                        $bt = "00:00";
                        $et = "23:59";
                    }
                    foreach($request->users ?? [] as $user){
                        $planning_id = Planning::insertGetId([
                          "user_id" => $user,
                          "klant_id" => $request->klantId ?? null,
                          "datum" => $row->format("Y-m-d"),
                          "begin" => $bt,
                          "eind" => $et,
                          "opmerking" => isset($request->opmerking) ? $request->opmerking : "",
                          "type" => "aanvraag",
                          "files" => json_encode($files ?? []),
                          "straat" => $request->straat ?? null,
                          "huisnummer" => $request->huisnummer ?? null,
                          "postcode" => $request->postcode ?? null,
                          "plaats" => $request->plaats ?? null,
                          "aanvraag_id" => $aanvraag->id,
                          "created_at" => Carbon::now(),
                        ]);
                        $this->storeMachines($request, $planning_id);
                        $this->storeWerkbonKeywords($request, $planning_id);
                        $this->storeCustomRows($request, $planning_id);
                        $this->storeDatasetItems($request, $planning_id);
                        $herhalingen = $this->storeHerhalingen($request, $planning_id);

                        if(isset($request->pushmelding)){
                            $this->pushmelding($user);
                        }

                        $planning[] = Planning::where('id', $planning_id)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                        $planning = array_merge($planning, $herhalingen ?? []);
                    }
                    $planningIds[] = $planning_id;
                }
                $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->begin)->format("d-m-Y") . " t/m " . Carbon::parse($request->eind)->format("d-m-Y");
                actLog($log, Auth::user()->id, 9);
            }


            if(isset($request->google_agenda_id) && isset(Auth::user()->google_token) && isset($planning_id)){
                $this->googleCalendarEvent($request->google_agenda_id, Auth::user()->google_token, $planningIds ?? []);
            }
            if(isset($request->klantMail)){
                $this->klantMail($planningIds ?? []);
            }

            $this->storeFiles($request, $planning ?? []);
            $planning = $this->refreshPlanning($planning ?? []);

            return response()->json(['planning' => $planning ?? []], 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }
    public function storeProjectPlanning(Request $request){
        if(isset($request->pushmelding)){
            $this->pushmelding($request->rowUsers);
        }

        foreach($request->begin ?? [] as $d => $begin){
            if(Carbon::parse($request->eind[$d]) < Carbon::parse($request->begin[$d])){
                return response()->json(['message' => 'Eindtijd ligt voor de begintijd'], 500);
            }
        }

        try{
            $project = Project::where("id", $request->projectId)->first();

            foreach($request->begin as $d => $begin){
                if(Carbon::parse($request->begin[$d])->day == Carbon::parse($request->eind[$d])->day){
                    foreach($request->rowUsers ?? [] as $user){
                        $planning_id = Planning::insertGetId([
                          "user_id" => $user,
                          "klant_id" => $request->klantId ?? null,
                          "datum" => Carbon::parse($request->begin[$d])->format("Y-m-d"),
                          "begin" => Carbon::parse($request->begin[$d])->format("H:i"),
                          "eind" => Carbon::parse($request->eind[$d])->format("H:i"),
                          "opmerking" => isset($request->opmerking) ? $request->opmerking : "",
                          "type" => "project",
                          "project_id" => $project->id,
                          "files" => json_encode($files ?? []),
                          "straat" => $request->straat ?? null,
                          "huisnummer" => $request->huisnummer ?? null,
                          "postcode" => $request->postcode ?? null,
                          "plaats" => $request->plaats ?? null,
                          "aantal_personen" => $request->actAantal ?? null,
                          "prijs" => $request->actPrijs ?? null,
                          "btw" => $request->actBtw ?? null,
                          "created_at" => Carbon::now(),
                          "aantal_medewerkers" => $request->projectInplannenAantalMedewerkers,
                          "werkbon_id" => $request->werkbonId ?? null,
                        ]);
                        $this->storeMachines($request, $planning_id);
                        $this->storeWerkbonKeywords($request, $planning_id);
                        $this->storeCustomRows($request, $planning_id);
                        $this->storeDatasetItems($request, $planning_id);

                        foreach($request->taken ?? [] as $taak){
                            PlanningProjecttaken::insert([
                              "planning_id" => $planning_id,
                              "taak_id" => $taak,
                            ]);
                        }
                        $herhalingen = $this->storeHerhalingen($request, $planning_id);

                        $planning[] = Planning::where('id', $planning_id)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                        $planning = array_merge($planning, $herhalingen ?? []);
                    }
                    $planningIds[] = $planning_id;
                    $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->begin[$d])->format("d-m-Y");
                    actLog($log, Auth::user()->id, 9);;
                }
                else{
                    $diff = CarbonPeriod::create($request->begin[$d], $request->eind[$d]);
                    foreach($diff as $row){
                        if(Carbon::parse($request->begin[$d])->format("d-m-Y") == $row->format("d-m-Y")){
                            $bt = Carbon::parse($request->begin[$d])->format("H:i");
                            $et = "23:59";
                        }
                        elseif(Carbon::parse($request->eind[$d])->format("d-m-Y") == $row->format("d-m-Y")){
                            $bt = "00:00";
                            $et = Carbon::parse($request->eind[$d])->format("H:i");
                        }
                        else{
                            $bt = "00:00";
                            $et = "23:59";
                        }
                        foreach($request->rowUsers ?? [] as $user){
                            $planning_id = Planning::insertGetId([
                              "user_id" => $user,
                              "klant_id" => $request->klantId ?? null,
                              "datum" => $row->format("Y-m-d"),
                              "begin" => $bt,
                              "eind" => $et,
                              "opmerking" => isset($request->opmerking) ? $request->opmerking : "",
                              "type" => "project",
                              "project_id" => $project->id,
                              "straat" => $request->straat ?? null,
                              "huisnummer" => $request->huisnummer ?? null,
                              "postcode" => $request->postcode ?? null,
                              "plaats" => $request->plaats ?? null,
                              "aantal_personen" => $request->actAantal ?? null,
                              "prijs" => $request->actPrijs ?? null,
                              "btw" => $request->actBtw ?? null,
                              "files" => json_encode($files ?? []),
                              "created_at" => Carbon::now(),
                              "werkbon_id" => $request->werkbonId ?? null,
                            ]);
                            $this->storeMachines($request, $planning_id);
                            $this->storeWerkbonKeywords($request, $planning_id);
                            $this->storeCustomRows($request, $planning_id);
                            $this->storeDatasetItems($request, $planning_id);
                            $herhalingen = $this->storeHerhalingen($request, $planning_id);

                            foreach($request->taken ?? [] as $taak){
                                PlanningProjecttaken::insert([
                                  "planning_id" => $planning_id,
                                  "taak_id" => $taak,
                                ]);
                            }

                            $planning[] = Planning::where('id', $planning_id)->with("legenda", "klanten", "aanvraag", "project", "machines", "taken", "explorer")->first();
                            $planning = array_merge($planning, $herhalingen ?? []);
                        }
                        $planningIds[] = $planning_id;
                    }
                    $log = Auth::user()->name . " " . Auth::user()->lastname . " Heeft een nieuwe activiteit ingepland op " . Carbon::parse($request->begin[$d])->format("d-m-Y") . " t/m " . Carbon::parse($request->eind[$d])->format("d-m-Y");
                    actLog($log, Auth::user()->id, 9);
                }
            }

            if(isset($request->google_agenda_id) && isset(Auth::user()->google_token) && isset($planning_id)){
                $this->googleCalendarEvent($request->google_agenda_id, Auth::user()->google_token, $planningIds ?? []);
            }
            if(isset($request->klantMail)){
                $this->klantMail($planningIds ?? []);
            }
            if(isset($request->pushmelding)){
                $this->pushmelding($request->rowUsers);
            }

            $this->storeFiles($request, $planning ?? []);
            $planning = $this->refreshPlanning($planning ?? []);

            return response()->json(['planning' => $planning ?? []], 201);
        }
        catch(\Exception $e){
            actError($e);
            return response(null, 500);
        }
    }

    public function createCalendar($subdomein, $planning_id){

        if(!isset(Auth::user()->google_token)){
            return redirect('planning/inplanntn/' . date('Y/n'))->with("warning", "Google account niet gevonden!");
        }

        $capi = new GoogleCalendarApi();

        $data = $capi->refreshAccessToken(Auth::user()->google_token);
        $access_token = $data['access_token'];
        $calendars = $capi->GetCalendarsList($access_token);
        $planning = Planning::where("id", $planning_id)->with("klanten")->with("offerte")->with("project")->with("aanvraag")->first();

        $description = $this->planningDescription($planning);
        $adres = $this->planningAdres($planning);


        return view('planning.calendar', [
          'calendars' => $calendars,
          'planning' => $planning,
          'google_info' => json_decode(Auth::user()->google_info),
          'description' => $description,
          'adres' => $adres,
        ]);
    }
    public function storeCalendar(Request $request){
        $capi = new GoogleCalendarApi();
        $data = $capi->refreshAccessToken(Auth::user()->google_token);
        $access_token = $data['access_token'];
        $timezone = $capi->GetUserCalendarTimezone($access_token);

        Planning::where("id", $request->planning)->update(['google_agenda' => 1]);
        $planning = Planning::where("id", $request->planning)->first();

        foreach($request->attendee_mail ?? [] as $i => $mail){
            if(isset($mail)){
                $attendees[] = ['displayName' => $request->attendee_name[$i] ?? '', "email" => $mail];
            }
        }

        $datum = Carbon::parse($planning->datum)->format("Y-m-d");
        $event_time = ['start_time' => $datum . 'T' . $request->begin, 'end_time' => $datum . 'T' . $request->eind];
        $data = $capi->CreateCalendarEvent($request->agenda, $request->titel, $request->description, $request->adres, $attendees ?? [], 0, $event_time, $timezone, $access_token);

        return redirect("planning/inplannen/" . date("Y/n"))->with("status", "<a class='text-white' target='_blank' href='" . $data["htmlLink"] . "' ><b>Activiteit</b></a> opgeslagen!");
    }

    public function projecten($subdomein, $type, $y = null, $p = null){
      if($y == null){
        $y = Carbon::now()->format('Y');
        if($type == "week"){
          $p = Carbon::now()->format('W');
        }elseif($type == "maand" || $type == "kwartaal"){
          $p = Carbon::now()->format('m');
        }elseif($type == "jaar"){
          $p = Carbon::now()->format('Y');
        }
      }
        if(getSettingValue('projectplanning_layout') == 'week'){
            if($type == "week"){
                $date = Carbon::now()->setISODate($y, $p);
                $period = CarbonPeriod::create($date->startOfWeek()->format('Y-m-d'), '1 week', $date->endOfWeek()->format('Y-m-d'));
            }
            elseif($type == "maand"){
                $date = Carbon::now()->setYear($y)->setMonth($p);
                $period = CarbonPeriod::create($date->startOfMonth()->format('Y-m-d'), '1 week', $date->endOfMonth()->format('Y-m-d'));
            }
            elseif($type == "kwartaal"){
                $date = Carbon::now()->setYear($y)->setMonth($p);
                $period = CarbonPeriod::create($date->firstOfQuarter()->format('Y-m-d'), '1 week', $date->lastOfQuarter()->format('Y-m-d'));
            }
            elseif($type == "jaar"){
                $date = Carbon::now()->setYear($y);
                $period = CarbonPeriod::create($date->firstOfYear()->format('Y-m-d'), '1 week', $date->lastOfYear()->format('Y-m-d'));
            }
            else{
                abort(404);
            }
        }
        else{
            if($type == "week"){
                $date = Carbon::now()->setISODate($y, $p);
                $period = CarbonPeriod::create($date->startOfWeek()->format('Y-m-d'), $date->endOfWeek()->format('Y-m-d'));
            }
            elseif($type == "maand"){
                $date = Carbon::now()->setYear($y)->setMonth($p);
                $period = CarbonPeriod::create($date->startOfMonth()->format('Y-m-d'), $date->endOfMonth()->format('Y-m-d'));
            }
            elseif($type == "kwartaal"){
                $date = Carbon::now()->setYear($y)->setMonth($p);
                $period = CarbonPeriod::create($date->firstOfQuarter()->format('Y-m-d'), $date->lastOfQuarter()->format('Y-m-d'));
            }
            elseif($type == "jaar"){
                $date = Carbon::now()->setYear($y);
                $period = CarbonPeriod::create($date->firstOfYear()->format('Y-m-d'), $date->lastOfYear()->format('Y-m-d'));
            }
            else{
                abort(404);
            }
        }

        $machines = Machines::where("active", 1)->orderBy("name", "ASC")->get();
        $projecten = Project::where("active", 1)->with("_bv", "offerte", "taken", "completed_taken", "planning")->orderBy("created_at", "DESC")->get()->keyBy("id");
        $projectenAll = Project::with("_bv", "offerte", "taken")->get()->keyBy("id");
        $planning = Planning::whereNotNull("project_id")->where("datum", ">=", $period->start->format('Y-m-d'))->where('datum', "<=", $period->end->format('Y-m-d'))->with("user", "taken", "machines", "project")->orderBy("begin", "ASC")->get();
        $werkbonTemplates = WerkbonnenTemplates::with('keywords')->get();

        foreach($projecten as $project){
            if(!isset($project->offerte)){
                continue;
            }
            $project->offerte_keywords = Offerteteksten::where("offerte_id", $project->offerte->id)->get()->keyBy("keyword");
        }


        if(getSettingValue('projectplanning_layout') == 'week'){
            $planning = groupByGroupBy($planning, "datum", "project_id");
        }
        else{
            $planning = groupByGroupBy($planning, "project_id", "datum");
        }

        return view('planning.projecten', [
          "type" => $type,
          "date" => $date,
          "period" => $period,
          "startDate" => $period->startDate,
          "endDate" => $period->endDate,
          "users" => activeUsers(),
          "projecten" => $projecten,
          "projectenAll" => $projectenAll,
          "planning" => $planning,
          "machines" => $machines,
          "werkbonTemplates" => $werkbonTemplates,
        ]);
    }
    public function projectenStore($subdomein, $type, $y, $w, Request $request){
        $project = Project::where("id", $request->projectId)->first();

        foreach($request->date ?? [] as $i => $date){
            if(!isset($date) || !isset($request->start[$i]) || !isset($request->end[$i])){
                continue;
            }

            foreach($request->users ?? [] as $user){
                if(!isset($user)){
                    continue;
                }
                $id = Planning::insertGetId([
                  "user_id" => $user,
                  "klant_id" => $project->klant_id,
                  "datum" => $date,
                  "begin" => $request->start[$i],
                  "eind" => $request->end[$i],
                  "opmerking" => $request->opmerking ?? "",
                  "type" => "project",
                  "project_id" => $project->id,
                  "created_at" => Carbon::now(),
                  "files" => json_encode($files ?? []),
                  "straat" => $request->straat ?? null,
                  "huisnummer" => $request->huisnummer ?? null,
                  "postcode" => $request->postcode ?? null,
                  "plaats" => $request->plaats ?? null,
                  "aantal_medewerkers" => $request->aantalmedewerkers ?? null,
                  "aantal_personen" => $request->actAantal ?? null,
                  "prijs" => $request->actPrijs ?? null,
                  "btw" => $request->actBtw ?? null,
                ]);
                $this->storeWerkbonKeywords($request, $id);
                $this->storeCustomRows($request, $id);
                $this->storeDatasetItems($request, $id);
                $herhalingen = $this->storeHerhalingen($request, $id);

                foreach($request->taken ?? [] as $taak){
                    PlanningProjecttaken::insert([
                      "planning_id" => $id,
                      "taak_id" => $taak,
                    ]);
                }
                foreach($request->machines ?? [] as $machine){
                    if(!isset($machine)){
                        continue;
                    }
                    PlanningMachines::insert([
                      "planning_id" => $id,
                      "machine_id" => $machine,
                    ]);
                }

            }
        }

        return redirect("planning/projecten/" . $type . "/" . $y . "/" . $w)->with("status", "planning opgeslagen");
    }
    public function projectenUpdate($subdomein, $type, $y, $w, Request $request){
        foreach($request->editId ?? [] as $i => $id){
            if(!isset($request->editUser[$i]) || !isset($request->editDate[$i]) || !isset($request->editStart[$i]) || !isset($request->editEnd[$i])){
                continue;
            }

            Planning::where("id", $id)->update([
              "user_id" => $request->editUser[$i],
              "datum" => $request->editDate[$i],
              "begin" => $request->editStart[$i],
              "eind" => $request->editEnd[$i],
              "opmerking" => $request->editOpmerking[$i],
            ]);

            PlanningProjecttaken::where("planning_id", $id)->delete();
            foreach(($request->taken[$id] ?? []) as $taak){
                PlanningProjecttaken::insert([
                  "planning_id" => $id,
                  "taak_id" => $taak,
                ]);
            }

            PlanningMachines::where("planning_id", $id)->delete();
            foreach(($request->machines[$id] ?? []) as $machine){
                if(!isset($machine)){
                    continue;
                }
                PlanningMachines::insert([
                  "planning_id" => $id,
                  "machine_id" => $machine,
                ]);
            }

        }

        return redirect("planning/projecten/" . $type . "/" . $y . "/" . $w)->with("status", "planning gewijzigd");
    }
    public function projectenDelete($subdomein, $type, $y, $w, $id){
        Planning::where("id", $id)->delete();

        return redirect("planning/projecten/" . $type . "/" . $y . "/" . $w)->with("status", "Planning verwijderd");
    }

    public function reserveerplein(Request $request){
        $data = json_decode($request->data, true)["reserveringen"];
        $new = 0;
        foreach($data as $row){
            $check = Planning::where("reserveerplein_id", $row["id"])->first();
            if(!isset($check)){
                $legenda = Legenda::where("naam", $row["assetnaam"])->first();
                if(!isset($legenda)){
                    $legendaId = Legenda::insertGetId([
                      "naam" => $row["assetnaam"],
                      "kleur" => randomHex(),
                      "created_at" => Carbon::now(),
                    ]);
                    $legenda = Legenda::where("id", $legendaId)->first();
                }
                $projectId = Project::insertGetId([
                  "status" => 'Opdracht',
                  "opdrachtgever" => $row["naam"],
                  "projectnr" => "RP" . $row["id"],
                  "projectnaam" => "RP Reservering " . $row["id"],
                  "adres" => $row["straat"],
                  "huisnummer" => $row["huisnummer"],
                  "postcode" => $row["postcode"],
                  "woonplaats" => $row["plaats"],
                  "Omschrijving" => "Reserveerplein reservering: " . $row["assetnaam"],
                  "created_at" => Carbon::now(),

                ]);

                Project::createDefaultFolders($projectId);

                $string = ", Aantal: " . $row["aantalpersonen"];
                if(!empty($row["reserveringen_producten"])){
                    $string = "";
                    foreach($row["reserveringen_producten"] as $product){
                        $string .= ", Product: " . $product["productnaam"] . " aantal: " . $product["aantal"];
                    }

                }
                Planning::insert([
                  "user_Id" => 0,
                  "klant_id" => null,
                  "datum" => $row["datum"],
                  "begin" => $row["begintijd"],
                  "eind" => $row["eindtijd"],
                  "opmerking" => "Email: " . $row["emailadres"] . ", Reserveringnummer: " . $row["reserveringsnummer"] . ", Betaalmethode: " . $row["betaalmethode"] . $string,
                  "type" => "legenda",
                  "legenda_id" => $legenda->id,
                  "project_id" => $projectId,
                  "created_at" => Carbon::now(),
                  "reserveerplein_id" => $row["id"],
                ]);
                $new++;
            }
        }
        return response()->json($new, 201);
    }
    public function calcRoute(Request $request){
        $url = "https://maps.googleapis.com/maps/api/distancematrix/json?units=metric&origins=" . $request->origin . "&destinations=" . $request->destination . "&key=AIzaSyAOo20gZOetftvgtbu9NYFKt3-e1h7xajs";
        $xml = json_decode(file_get_contents($url));

        return response()->json($xml->rows[0]->elements[0], 201);
    }

    public function preview($subdomain, $id){
        $dag = Planning::where('id', $id)->with('legenda', 'klant', 'project', 'user', 'offerte', 'explorer', 'user', 'taken', 'machines')->firstOrFail();

        return view('planning.preview', [
          'dag' => $dag,
        ]);
    }

    private function storeWerkbonKeywords($request, $id){
        if(!count($request->werkbonPrefillKeywords ?? [])){
            return;
        };

        Planning::where('id', $id)->update(['werkbon_keywords' => json_encode($request->werkbonPrefillKeywords)]);
    }
    private function storeMachines($request, $id){
        foreach($request->machines ?? [] as $machine){
            if(!isset($machine)){
                continue;
            }
            PlanningMachines::insert([
              "planning_id" => $id,
              "machine_id" => $machine,
            ]);
        }
        foreach($request->usermachines ?? [] as $userid => $machine){
            if(!isset($userid) || !isset($machine)){
                continue;
            }
            PlanningMachines::insert([
              "planning_id" => $id,
              "machine_id" => $machine,
              "user_id" => $userid,
            ]);
        }
    }
    private function storeCustomRows($request, $id){
        try{
            foreach($request->custom ?? [] as $keyword => $value){
                if(!isset($value)){
                    continue;
                }

                $setting = json_decode((getSettingValue('planning_custom_rows') ?? '[]'), true);
                $key = find('keyword', $keyword, $setting);

                PlanningCustomRows::insert([
                  'planning_id' => $id,
                  'keyword' => $keyword,
                  'name' => $key['name'] ?? '',
                  'type' => $key['type'] ?? '',
                  'data' => $key['data'] ?? '',
                  'value' => $value,
                ]);
            }
        }
        catch(\Exception $e){
            actError($e);
        }
    }

    private function storeDatasetItems($request, $id){
      try{
        if(isset($request->datasetItemAantal)){
          $setting = getPlanningSettingsDatasets();
          foreach($request->datasetItemAantal ?? [] as $setId => $items){
              $dataset = $setting[$setId]['dataset'];
              $value = [];
              foreach($items ?? [] as $itemid => $aantal){
                  $item = findInObj('id', $itemid, $dataset->items);
                  $value[] = [
                      'dataset_id' => $setId,
                      'item_id' => $itemid,
                      'Aantal' => $aantal,
                      'name' => $item->name,
                  ];
              }
              PlanningCustomRows::insert([
                  'planning_id' => $id,
                  'keyword' => $dataset->naam,
                  'name' => $dataset->naam,
                  'type' => 'dataset',
                  'data' => '',
                  'value' => json_encode($value),
              ]);
          }
        }
      }catch(\Exception $e){
        actError($e);
      }
    }

    private function storeHerhalingen($request, $planningid){
      if(!isset($request->herhalen) || !isset($request->herhalen_freq) || !isset($request->herhalen_eind)){
        return;
      }
      try{
        $arr = [];
        $planning = Planning::where('id', $planningid)->with("taken", "explorer", "custom", "machines")->first();
        $dayofweek = Carbon($planning->datum)->dayOfWeek;
        $weekday = false;
        if(str_contains($request->herhalen_freq, "weekday")){
          $request->herhalen_freq = str_replace(" weekday", "", $request->herhalen_freq);
          $weekday = true;
        }
        $periode = CarbonPeriod($planning->datum, $request->herhalen_eind, $request->herhalen_freq);
        foreach($periode as $p){
          if($weekday){
            $startofweek = Carbon($p)->startOfWeek();
            $date = Carbon($startofweek)->addDays(($dayofweek - 1))->format("Y-m-d");
          }else{
            $date = $p->format("Y-m-d");
          }

          if($date == $planning->datum){continue;}

          $newplan = $planning->replicate();
          $newplan->datum = $date;

          $newplan->save();

          $relations = [$planning->planningProjecttaken, $planning->PlanningFiles, $planning->custom, $planning->planningMachines];
          foreach($relations as $relation){
            if($relation instanceof Collection){
              foreach($relation as $row){

                if(empty($row)){continue;}

                $rclone = $row->replicate();
                $rclone->planning_id = $newplan->id;
                $rclone->save();
              }
              continue;
            }
            if(!$relation || !count($relation)){continue;}
            $rclone = $relation->replicate();
            $rclone->planning_id = $newplan->id;
            $rclone->save();
          }
          $newplan->refresh();
          $newplan->load("legenda", "klanten", "aanvraag", "project.taken", "machines", "taken", "explorer");
          $arr[] = $newplan;
        }
        return $arr;
      }catch(\Exception $e){
        actError($e);
      }
    }

    private function googleCalendarEvent($calendar, $google_token, $planning_ids){
        $capi = new GoogleCalendarApi();
        $token = $capi->refreshAccessToken($google_token);
        $timezone = $capi->GetUserCalendarTimezone($token['access_token']);

        foreach($planning_ids ?? [] as $id){
            $planning = Planning::where("id", $id)->with('legenda', 'project', 'aanvraag')->first();
            $event_time = ['start_time' => $planning->datum . 'T' . $planning->begin, 'end_time' => $planning->datum . 'T' . $planning->eind];

            if($planning->type == "legenda"){
                $titel = 'Activiteit: ' . $planning->legenda->naam;
            }
            elseif($planning->type == "project"){
                $titel = 'Project: ' . $planning->project->projectnr;
            }
            elseif($planning->type == "aanvraag"){
                $titel = 'Aanvraag: ' . $planning->aanvraag->aanvraagnummer;
            }

            $description = $this->planningDescription($planning);
            $adres = $this->planningAdres($planning);
            $attendee = $this->planningAttendee($planning);

            $capi->CreateCalendarEvent($calendar, $titel ?? '', $description, $adres, [$attendee], 0, $event_time, $timezone, $token['access_token']);
        }

    }
    private function refreshPlanning($arr){
        $planning = [];
        foreach($arr ?? [] as $row){
            $planning[] = $row->refresh();
        }
        return $planning;
    }
    public function pushmelding($users){
        if(!is_array($users)){ $users = [$users]; }

        $tokens = Device::select('device_id')
            ->whereIn("user_id", $users)
            ->where('device_id', '!=', 'no_token_given')
            ->pluck('device_id')
            ->toArray();

        pushToAll("Planning update!", "Hi, er staat nieuwe planning voor je klaar!", $tokens ?? []);
    }
    private function storeFiles($request, $planning){
        foreach($planning as $row){
            PlanningFiles::where('planning_id', $row->id)->delete();
            foreach($request->explorer_files ?? [] as $id){
                PlanningFiles::insert([
                  'planning_id' => $row->id,
                  'file_id' => $id,
                ]);
            }
        }
    }
    private function planningDescription($planning){
        $description = '';
        $klant = '';
        if(isset($planning->klanten)){
            $klant = "\r\n" . "Klant: ";
            $klant .= $planning->klanten->naam ?? $planning->klanten->contactpersoon_voornaam . " " . $planning->klanten->contactpersoon_achternaam;
        }


        if($planning->type == 'legenda'){
            $description = "Activiteit: " . $planning->legenda->naam . $klant;
        }
        elseif($planning->type == "project"){
            $description = "Project: " . $planning->project->projectnaam . " (" . $planning->project->projectnr . ")" . $klant;
        }
        elseif($planning->type == "aanvraag"){
            $description = "Aanvraag: " . $planning->aanvraag->aanvraagnummer . $klant;
        }

        return $description;
    }
    private function planningAdres($planning){
        $adres = '';
        if($planning->straat || $planning->postcode || $planning->plaats){
            $adres = $planning->straat . " " . $planning->huisnummer . ", " . $planning->postcode . " " . $planning->plaats;
        }
        return $adres;
    }
    private function planningAttendee($planning){
        if(isset($planning->klanten)){
            $name = $planning->klanten->name ?? $planning->klanten->contactpersoon_voornaam . ' ' . $planning->klanten->contactpersoon_achternaam;
            $attendee = ['displayName' => $name, "email" => $planning->klanten->email, "role" => "reader"];
        }
        return $attendee ?? [];
    }

    private function usersOrderedByRoleBySetting(){
        $users = getUsers();

        if(getSettingValue('planning_roles_order') === null){
            return $users;
        }

        $setting = json_decode((getSettingValue('planning_roles_order') ?? '[]'), true);

        asort($setting);

        $temp = [];
        foreach($setting as $r => $sort){
            if(!isset($sort)){
                continue;
            }

            // array index must be string!
            foreach($users->where('role_id', $r) as $user){
                $temp[strval($user->id)] = $user;
            }
        }

        return $temp;

    }

    private function klantMail(array $ids): void{
        $sender = getSettingValue("planning_mail_afzender");
        $subject = getSettingValue("planning_mail_onderwerp");

        foreach($ids as $id){
            $content = getSettingValue("planning_mail");
            $planning = Planning::where("id", $id)->with("klant", "legenda", "project", "aanvraag")->first();

            $bv = $planning->klant->_bv ?? BV::where("client_id", getClientId())->first();

            if(!isset($planning->klant) || !isset($planning->klant->email) || !verifyEmail($planning->klant->email)){
                continue;
            }
            if(!isset($sender) || !isset($content) || !verifyEmail($sender)){
                continue;
            }

            if(isset($planning->legenda)){
                $activiteit = $planning->legenda->naam;
            }
            elseif(isset($planning->project)){
                $activiteit = "Project: " . $planning->project->projectnr . ", " . $planning->project->projectnaam;
            }
            elseif(isset($planning->aanvraag)){
                $activiteit = "Aanvraag: " . $planning->aanvraag->aanvraagnummer;
            }
            elseif(isset($planning->offerte)){
                $activiteit = "Offerte: " . $planning->offerte->offertenummer;
            }

            $content = str_replace("{activiteit}", $activiteit ?? '', $content);
            $content = str_replace("{datum}", Carbon::parse($planning->datum)->format("d-m-Y"), $content);
            $content = str_replace("{begintijd}", Carbon::parse($planning->begin)->format("H:i"), $content);
            $content = str_replace("{eindtijd}", Carbon::parse($planning->eind)->format("H:i"), $content);
            $content = str_replace("{opmerking}", $planning->opmerking, $content);

            Mail::to($planning->klant->email)->send(new BlankMail($bv, $sender, $bv->name, $subject, $content));
        }

    }
    private function klantMailUpdate(object $old, object $new): void{
        $sender = getSettingValue("planning_mail_afzender");
        $subject = getSettingValue("planning_mail_update_onderwerp");
        $content = getSettingValue("planning_mail_update");
        $bv = $new->klant->_bv ?? BV::where("client_id", getClientId())->first();

        if(!isset($new->klant) || !isset($new->klant->email) || !verifyEmail($new->klant->email)){
            return;
        }
        if(!isset($sender) || !isset($content) || !verifyEmail($sender)){
            return;
        }


        if(isset($new->legenda)){
            $activiteit = $new->legenda->naam;
        }
        elseif(isset($new->project)){
            $activiteit = "Project: " . $new->project->projectnr . ", " . $new->project->projectnaam;
        }
        elseif(isset($new->aanvraag)){
            $activiteit = "Aanvraag: " . $new->aanvraag->aanvraagnummer;
        }
        elseif(isset($new->offerte)){
            $activiteit = "Offerte: " . $new->offerte->offertenummer;
        }

        $content = str_replace("{datum_old}", Carbon::parse($old->datum)->format("d-m-Y"), $content);
        $content = str_replace("{begintijd_old}", Carbon::parse($old->begin)->format("H:i"), $content);
        $content = str_replace("{eindtijd_old}", Carbon::parse($old->eind)->format("H:i"), $content);

        $content = str_replace("{activiteit}", $activiteit ?? '', $content);
        $content = str_replace("{datum}", Carbon::parse($new->datum)->format("d-m-Y"), $content);
        $content = str_replace("{begintijd}", Carbon::parse($new->begin)->format("H:i"), $content);
        $content = str_replace("{eindtijd}", Carbon::parse($new->eind)->format("H:i"), $content);
        $content = str_replace("{opmerking}", $new->opmerking, $content);

        Mail::to($new->klant->email)->send(new BlankMail($bv, $sender, $bv->name, $subject, $content));
    }


    public function webhookBriq(Request $request){
        foreach($request->allFiles() as $file){
            $files[] = [
              "name" => $file->getClientOriginalName(),
              "path" => $file->getPathname(),
              "size" => $file->getSize(),
            ];
        }
        $data = [
          "headers" => $request->header(),
          "POST" => $request->all(),
          "GET" => $_GET,
          "Files" => $files ?? [],
          "Json" => json_decode(file_get_contents('php://input') ?? "[]"),
        ];
        return response()->json($data, 201);
    }

  public function werkbonnenOpvolgen($projecten) {
    foreach ($projecten as $project) {
      foreach ($project->werkbonnen as $werkbon) {
        $werkbon->opvolgen = $werkbon->opvolgen();
      }
    }
    return $projecten;
  }

  public function resources($sub, $id, $type, $y, $p) {
    if($type == "dag"){
      $date = Carbon::parse($y.'-'.$p);
      $period = CarbonPeriod::create($date->format('Y-m-d'), $date->format('Y-m-d'));
    }
    elseif($type == "week"){
      $date = Carbon::now()->setISODate($y, $p);
      $period = CarbonPeriod::create($date->startOfWeek()->format('Y-m-d'), $date->endOfWeek()->format('Y-m-d'));
    }
    elseif($type == "maand"){
      $date = Carbon::now()->startOfMonth()->setYear($y)->setMonth($p);
      $period = CarbonPeriod::create($date->startOfMonth()->format('Y-m-d'), $date->endOfMonth()->format('Y-m-d'));
    }
    elseif($type == "kwartaal"){
      $date = Carbon::now()->setYear($y)->setMonth($p);
      $period = CarbonPeriod::create($date->firstOfQuarter()->format('Y-m-d'), $date->lastOfQuarter()->format('Y-m-d'));
    }
    elseif($type == "jaar"){
      $date = Carbon::now()->setYear($y);
      $period = CarbonPeriod::create($date->firstOfYear()->format('Y-m-d'), $date->lastOfYear()->format('Y-m-d'));
    }
    elseif($type == "periode"){
      $date = Carbon::parse($y);
      $date_end = Carbon::parse($p);
      $period = CarbonPeriod::create($date->format('Y-m-d'), $date_end->format('Y-m-d'));
    }
    else{
      abort(404);
    }
    $dataset = getDataset($id);
    $groupedItems = [];
    foreach ($dataset->items as $item) {
      $itemvals = json_decode($item->value);
      if (!isset($groupedItems[$itemvals->categorie])) {
        $groupedItems[$itemvals->categorie] = [];
      }
      $groupedItems[$itemvals->categorie][] = $item;
    }

    $dataset->groupedItems = $groupedItems;

    return view('planning.resources', [
      "type" => $type,
      "date" => $date,
      "period" => $period,
      "dataset" => $dataset,
    ]);
  }

    public function projectExport($subdomain, $projectId) {
        $log = Auth::user()->fullName()."  heeft de gebruikers gedownload.";
        actLog($log, Auth::user()->id, 7);

        return Excel::download(new ProjectPlanningExport($projectId), 'Project_planning.xlsx');
    }

}
