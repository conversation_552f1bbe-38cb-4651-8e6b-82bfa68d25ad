<?php

namespace App\Http\Controllers;

use App\ActivityLog;
use App\ExplorerFiles;
use App\Leveranciers;
use App\UsersCustomFields;
use DB;
use Auth;
use Hash;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Route;
use Config;
use Carbon\Carbon;
use App\User;
use App\Exports\UserExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;

class UserController extends Controller {

  public function index() {
    return view('users.index');
  }

  public function create($sub, $id = null) {
    $bvs   = DB::connection('tessa')->table('bv')->where('client_id', Config::get('filesystems.disks.client.client_id'))->get();
    $roles = DB::table('roles')->get();
    $user = User::find($id);
    $users = getUsers();

    return view('users.create', [
      'user' => $user,
      'users' => $users,
      'roles' => $roles,
      'bvs' => $bvs,
    ]);
  }
//  public function edit($sub, $id = false) {
//    $bvs   = DB::connection('tessa')->table('bv')->where('client_id', Config::get('filesystems.disks.client.client_id'))->get();
//    $roles = DB::table('roles')->get();
//    if(!$id){return view('users.edit', ['user' => null, 'roles' => $roles, 'bvs' => $bvs]);}
//
//    $user  = User::where('id', $id)->firstOrFail();
//    return view('users.create', ['user' => $user, 'roles' => $roles, 'bvs' => $bvs]);
//  }
  public function store(Request $request) {
    $request->validate([
        'name' => 'required',
        'lastname' => 'required',
        'email' => 'required',
        'password' => 'required',
        'role' => 'required',
        'bv' => 'required'
    ]);
    $mail = User::where(['email' => $request->email, 'active' => 1])->first();
    if(isset($mail)){
      return redirect("users/create")->with("warning", "Email niet beschikbaar");
    }

    if($request->document) {
      $filename = $request->document->getClientOriginalName() . md5(time()) . '.' . $request->document->getClientOriginalExtension();
      $request->document->move(storage_path('app/public/uploads'.request()->subdomain . '/'), $filename);
    }
    if($request->hasFile("handtekening")){
      $hString = randomString(10).".png";
      Storage::disk("image")->put("/handtekeningen/".$hString, file_get_contents($request->file("handtekening")));
    }
    if($request->hasFile("footer")){
      $footer_src = randomString(10).".png";
      Storage::disk("client")->put("/medewerkers/footers/".$footer_src, file_get_contents($request->file("footer")));
    }

    $userid = User::insertGetId([
      'name'                     => $request->name,
      'lastname'                 => $request->lastname,
      'email'                    => $request->email,
      'voorletters'              => $request->voorletters,
      'password'                 => Hash::make($request->password),
      'role_id'                  => $request->role,
      'end_date'                 => $request->enddate,
      'pnumber'                  => $request->pnumber,
      'jobtype'                  => $request->jobtype,
      'functie'                  => $request->function,
      'phone'                    => $request->phone,
      'phone_private'            => $request->phoneprivate,
      'email_private'            => $request->emailprivate,
      'dateofbirth'              => $request->dateofbirth ? Carbon::parse($request->dateofbirth) : '',
      'start_date'               => $request->date_start  ? Carbon::parse($request->date_start) : '',
      'uurtarief'                => $request->uurtarief,
      'reistarief'               => $request->reistarief,
      'bsn'                      => $request->bsn,

      'bhv'                      => $request->bhv,
      'expiration_date_bhv'      => Carbon::parse($request->expiration_bhv),
      'vca'                      => $request->vca,
      'expiration_date_vca'      => Carbon::parse($request->expiration_vca),
      'expiration_date_limosa'   => $request->expiration_limosa  ? Carbon::parse($request->expiration_limosa) : null,
      'driver_license'           => json_encode($request->driver ?? []),
      'bv_id'                    => $request->bv,
      'leverancier_id'           => $request->leverancier,
      'vestiging_id'             => $request->vestiging ?? null,
      'created_at'               => Carbon::now(),
      'nul_uren'                 => $request->nul_uren,
      'opmerking'                => $request->opmerking,
      'document'                 => isset($filename) ? $filename : null,
      'handtekening'             => $hString ?? null,
      'footer'                   => $footer_src ?? null,
      'all_in'                   => $request->all_in ?? 0,
      'disable_reisuren'         => $request->disable_reisuren ?? 0,
      'exact_globe_id'           => $request->exact_resource ?? null,
      'caogroep'                 => $request->caogroep,
      'uurreistarief'            => $request->uurreistarief,
      'telefoonthuis'            => $request->telefoonthuis,
      'straat'                   => $request->straat,
      'huisnummer'               => $request->huisnummer,
      'toevoeging'               => $request->toevoeging,
      'postcode'                 => $request->postcode,
      'woonplaats'               => $request->woonplaats,
      'geboorteplaats'           => $request->geboorteplaats,
      'onkostenvergoeding'       => $request->onkostenvergoeding,
      'iban'                     => $request->iban,
      'geslacht'                 => $request->geslacht,
      'burgerlijkestaat'         => $request->burgerlijkestaat,
      'autovdzaak'               => $request->autovdzaak ?? 0,
      'loonheffingskorting'      => $request->loonheffingskorting ?? 0,
      'contractduur'             => $request->contractduur,
      'functieloonheffing'       => $request->functieloonheffing,
      'dagenurenperweek'         => $request->dagenurenperweek,
      'reiskosten'               => $request->reiskosten ?? 0,
      'be'                       => $request->be,
      'calamiteiten_form'        => $request->calamiteiten_form,
      'personeeldossier_d13'     => $request->personeeldossier_d13,
      'pbm_d8'                   => $request->pbm_d8,
      'inwerkchecklist_d9'       => $request->inwerkchecklist_d9,
      'ttg'                      => $request->ttg,
      'kvknr'                    => $request->kvknr,
      'initialen'                => $request->initialen,
      'link_vca'                 => $request->link_vca,
      'link_kvk'                 => $request->link_kvk,
      'leidinggevende'           => $request->leidinggevende,
      'onderaannemer'           => $request->onderaannemer,
      'standaard_verlofsaldo'           => $request->standaard_verlofsaldo,
      'atv_uren'                        => $request->atv_uren,
      'veilig_werken_langs_de_weg'      => $request->veilig_werken_langs_de_weg  ? Carbon::parse($request->veilig_werken_langs_de_weg) : null,
      'expiration_date_hoogwerker'      => $request->expiration_date_hoogwerker  ? Carbon::parse($request->expiration_date_hoogwerker) : null,
      'expiration_date_driver_license'  => $request->expiration_date_driver_license  ? Carbon::parse($request->expiration_date_driver_license) : null,
      'overeengekomenbrutosalaris'      => $request->overeengekomenbrutosalaris,
      'loonperiode'                     => $request->loonperiode,
      'subcontractor_employment_type'   => $request->subcontractor_employment_type,

      'identity_document_type'          => $request->identity_document_type,
      'id_card_number'                  => $request->id_card_number,
      'expiration_date_id_card'         => $request->expiration_date_id_card,
      'id_card_file_id'                 => $request->id_card_file_id,
      'passport_number'                 => $request->passport_number,
      'expiration_date_passport'        => $request->expiration_date_passport,
      'passport_file_id'                => $request->passport_file_id,
    ]);

    $this->storeExplorerFiles($userid, $request);
    $this->storeCustomFields($userid, $request);

    $roles = DB::table('standaarduren')->where('role_id', $request->role)->whereNull('medewerkers_id')->get();
    foreach($roles as $role) {
      DB::table('standaarduren')->insert([
        'medewerkers_id'               => $userid,
        'dag'                          => $role->dag,
        'standaarduren'                => $role->standaarduren,
        'begintijd'                    => $role->begintijd,
        'eindtijd'                     => $role->eindtijd,
        'pauze'                        => $role->pauze,
        'ladenlossen_begintijd'        => $role->ladenlossen_begintijd,
        'ladenlossen_eindtijd'         => $role->ladenlossen_eindtijd,
        'ladenlossen_middag_begintijd' => $role->ladenlossen_eindtijd,
        'ladenlossen_middag_eindtijd'  => $role->ladenlossen_eindtijd,
        'reisuren'                     => $role->reisuren,
        'heenreis_woon_werk'           => $role->heenreis_woon_werk,
        'terugreis_woon_werk'          => $role->terugreis_woon_werk,
        'heenreis_huis_project'        => $role->heenreis_huis_project,
        'terugreis_huis_project'       => $role->terugreis_huis_project,
        'bestuurder'                   => $role->bestuurder,
        'bijrijder'                    => $role->bijrijder,
        'kenteken_id'                  => $role->kenteken_id,
        'opmerkingen'                  => $role->opmerkingen
      ]);
    }

    if (hasModule("Bestanden")){
			ExplorerFiles::createFoldersByPath("/Gebruikers/($userid)");
    }

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft een nieuw medewerker toegevoegd.";
    ActivityLog::insert([
      "client_id" => getClientId(),
      "user_id" => Auth::user()->id,
      "log" => $log,
      "created_at" => Carbon::now(),
    ]);

    return redirect('/users')->with('status', 'Werknemer '. $request->name .' '. $request->lastname .' is toegevoegd!')->with('warning', 'Zijn er standaarduren ingevoerd?');
  }
  public function storeExplorerFiles($user_id, $request){
    $files = [
      $request->passport_file_id,
      $request->id_card_file_id,
    ];

    foreach($files as $file_id){
      $file = ExplorerFiles::find($file_id);
      if(!$file){ continue; }

      $file->move("/Gebruikers/({$user_id})");
    }
  }
  public function storeCustomFields($user_id, $request){
    $setting = resetIndex(getSettingJson('users_custom_fields'));

    foreach($request->custom_fields ?? [] as $custom_keyword => $custom_value){
      $setting_record = find('keyword', $custom_keyword, $setting);
      if(!$setting_record){ continue; }

      UsersCustomFields::updateOrInsert([
        'user_id' => $user_id,
        'keyword' => $custom_keyword,
      ], [
        'value' => $custom_value
      ]);

      if($setting_record['type'] == 'file'){
        $file = ExplorerFiles::find($custom_value);
        if($file){
          $file->move("/Gebruikers/({$user_id})");
        }
      }
    }
  }

  public function update($sub, $id, Request $request) {
    $user = User::where('id', $id)->firstOrFail();
    if ($request->document) {
      $filename = $request->document->getClientOriginalName() . md5(time()) . '.' . $request->document->getClientOriginalExtension();
      $request->document->move(storage_path('app/public/uploads'.request()->subdomain . '/'), $filename);
    }

    $hString = $user->handtekening;
    if($request->hasFile("handtekening")){
      $hString = randomString(10).".png";
      Storage::disk("image")->put("/handtekeningen/".$hString, file_get_contents($request->file("handtekening")));
    }

    $fotoString = $user->foto;
    if($request->hasFile("foto")){
      $fotoString = randomString(10).".png";
      Storage::disk("client")->put("users/".$id."/headshot/".$fotoString, file_get_contents($request->file("foto")));
    }

    $footer_src = $user->footer;
    if($request->hasFile("footer")){
      $footer_src = randomString(10).".png";
      Storage::disk("client")->put("/medewerkers/footers/".$footer_src, file_get_contents($request->file("footer")));
    }

    User::where('id', $id)->update([
      'name'                     => $request->name,
      'lastname'                 => $request->lastname,
      'email'                    => $request->email,
      'voorletters'              => $request->voorletters,
      'password'                 => $request->password ? Hash::make($request->password) : $user->password,
      'role_id'                  => $request->role,
      'end_date'                 => $request->enddate,
      'pnumber'                  => $request->pnumber,
      'jobtype'                  => $request->jobtype,
      'functie'                  => $request->function,
      'phone'                    => $request->phone,
      'phone_private'            => $request->phoneprivate,
      'email_private'            => $request->emailprivate,
      'dateofbirth'              => $request->dateofbirth ? Carbon::parse($request->dateofbirth) : null,
      'start_date'               => $request->date_start  ? Carbon::parse($request->date_start) : null,
      'uurtarief'                => $request->uurtarief,
      'reistarief'               => $request->reistarief,
      'bsn'                      => $request->bsn,
      'bhv'                      => $request->bhv,
      'expiration_date_bhv'      => $request->expiration_bhv ? Carbon::parse($request->expiration_bhv) : null,
      'vca'                      => $request->vca,
      'expiration_date_vca'      => $request->expiration_vca ? Carbon::parse($request->expiration_vca) : null,
      'expiration_date_limosa'   => $request->expiration_limosa ? Carbon::parse($request->expiration_limosa) : null,
      'driver_license'           => json_encode($request->driver ?? []),
      'bv_id'                    => $request->bv,
      'leverancier_id'           => $request->leverancier,
      'vestiging_id'             => $request->vestiging ?? null,
      'updated_at'               => Carbon::now(),
      'nul_uren'                 => $request->nul_uren,
      'opmerking'                => $request->opmerking,
      'document'                 => isset($filename) ? $filename : $user->document,
      "handtekening"             => $hString,
      "footer"                   => $footer_src,
      'all_in'                   => $request->all_in ?? 0,
      'disable_reisuren'         => $request->disable_reisuren ?? 0,
      'exact_globe_id'           => $request->exact_resource ?? null,
      'caogroep'                 => $request->caogroep,
      'telefoonthuis'            => $request->telefoonthuis,
      'straat'                   => $request->straat,
      'huisnummer'               => $request->huisnummer,
      'toevoeging'               => $request->toevoeging,
      'postcode'                 => $request->postcode,
      'uurreistarief'            => $request->uurreistarief,
      'woonplaats'               => $request->woonplaats,
      'geboorteplaats'           => $request->geboorteplaats,
      'onkostenvergoeding'       => $request->onkostenvergoeding,
      'iban'                     => $request->iban,
      'geslacht'                 => $request->geslacht,
      'burgerlijkestaat'         => $request->burgerlijkestaat,
      'autovdzaak'               => $request->autovdzaak ?? 0,
      'loonheffingskorting'      => $request->loonheffingskorting ?? 0,
      'contractduur'             => $request->contractduur,
      'functieloonheffing'       => $request->functieloonheffing,
      'dagenurenperweek'         => $request->dagenurenperweek,
      'reiskosten'               => $request->reiskosten ?? 0,
      'be'                       => $request->be,
      'calamiteiten_form'        => $request->calamiteiten_form,
      'personeeldossier_d13'     => $request->personeeldossier_d13,
      'pbm_d8'                   => $request->pbm_d8,
      'inwerkchecklist_d9'       => $request->inwerkchecklist_d9,
      'ttg'                      => $request->ttg,
      'kvknr'                    => $request->kvknr,
      'initialen'                => $request->initialen,
      'foto'                     => $fotoString,
      'link_vca'                 => $request->link_vca,
      'link_kvk'                 => $request->link_kvk,
      'leidinggevende'           => $request->leidinggevende,
      'onderaannemer'            => $request->onderaannemer,
      'standaard_verlofsaldo'    => $request->standaard_verlofsaldo,
      'atv_uren'                        => $request->atv_uren,
      'veilig_werken_langs_de_weg'      => $request->veilig_werken_langs_de_weg  ? Carbon::parse($request->veilig_werken_langs_de_weg) : null,
      'expiration_date_hoogwerker'      => $request->expiration_date_hoogwerker  ? Carbon::parse($request->expiration_date_hoogwerker) : null,
      'expiration_date_driver_license'  => $request->expiration_date_driver_license  ? Carbon::parse($request->expiration_date_driver_license) : null,
      'overeengekomenbrutosalaris'      => $request->overeengekomenbrutosalaris,
      'loonperiode'                     => $request->loonperiode,
      'subcontractor_employment_type'   => $request->subcontractor_employment_type,

      'identity_document_type'          => $request->identity_document_type,
      'id_card_number'                  => $request->id_card_number,
      'expiration_date_id_card'         => $request->expiration_date_id_card,
      'id_card_file_id'                 => $request->id_card_file_id,
      'passport_number'                 => $request->passport_number,
      'expiration_date_passport'        => $request->expiration_date_passport,
      'passport_file_id'                => $request->passport_file_id,

    ]);

    $this->storeCustomFields($id, $request);

    if ($user->role_id != $request->role) {
      DB::table('standaarduren')->where('medewerkers_id', $id)->delete();
      $roles = DB::table('standaarduren')->where('role_id', $request->role)->whereNull('medewerkers_id')->get();
      foreach($roles as $role) {
        DB::table('standaarduren')->insert([
          'medewerkers_id'               => $id,
          'dag'                          => $role->dag,
          'standaarduren'                => $role->standaarduren,
          'begintijd'                    => $role->begintijd,
          'eindtijd'                     => $role->eindtijd,
          'pauze'                        => $role->pauze,
          'ladenlossen_begintijd'        => $role->ladenlossen_begintijd,
          'ladenlossen_eindtijd'         => $role->ladenlossen_eindtijd,
          'ladenlossen_middag_begintijd' => $role->ladenlossen_eindtijd,
          'ladenlossen_middag_eindtijd'  => $role->ladenlossen_eindtijd,
          'reisuren'                     => $role->reisuren,
          'heenreis_woon_werk'           => $role->heenreis_woon_werk,
          'terugreis_woon_werk'          => $role->terugreis_woon_werk,
          'heenreis_huis_project'        => $role->heenreis_huis_project,
          'terugreis_huis_project'       => $role->terugreis_huis_project,
          'bestuurder'                   => $role->bestuurder,
          'bijrijder'                    => $role->bijrijder,
          'kenteken_id'                  => $role->kenteken_id,
          'opmerkingen'                  => $role->opmerkingen
        ]);
      }
    }

    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de medewerker '".$user->name." ".$user->lastname."' (".$user->id.") gewijzigd.";
    actLog($log, User::id(), null);

    return redirect('/users')->with('status', 'Werknemer '. $request->name .' is gewijzigd!');
  }


  public function overview() {
    $id       = Route::current()->parameter('id');
    $user     = User::where('id', $id)->firstOrFail();
    $dagen    = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
    $response = [];

    $page = Request()->query('page');
    if ($page == 1) {
      $min = 0;
      $max = 60;
    } else {
      $pageMinEen = $page - 1;
      $min = (60 * $pageMinEen);
      $max = (60 + $min);
    }

    for ($i = $min; $i < $max; $i++) {
      $today                 = Carbon::now()->subDays($i)->format('Y-m-d');
      $response[$i]["datum"] = Carbon::parse($today)->format('d-m-Y');
      $urenregistratie       = DB::table('urenregistratie')->select('datum')->where('medewerker_id', $id)->where('datum', $today)->first();
      $verlof                = DB::table('verlof')->where('medewerker_id', $id)->where('datum', $today)->where('akkoord', 1)->pluck('datum')->first();
      $feestdag              = DB::table('feestdagen')->select("datum", "feestdag")->where('datum', $today)->where('disabled', 0)->pluck("feestdag")->first();

      $response[$i]["dag"]        = $dagen[Carbon::parse($today)->dayOfWeek];
      $response[$i]["feestdagen"] = $feestdag        ? true : false;
      $response[$i]["verlof"]     = $verlof          ? true : false;
      $response[$i]["ingevoerd"]  = $urenregistratie ? true : false;
    }

    return view('users.overview', ['user' => $user, 'response' => $response, 'page' => $page]);
  }
  public function default() {
    $id = Route::current()->parameter('id');
    $user = User::where('id', $id)->first();
    $standaarduren = DB::table('standaarduren')->where('medewerkers_id', $id)->get();
    $kentekens = DB::table('kentekens')->get();
    $weekdagen = ["maandag", "dinsdag", "woensdag", "donderdag", "vrijdag", "zaterdag", "zondag"]; // 0 is maandag, 7 is zondag.
    $dagen = [];
    $dagen["maandag"]   = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'maandag')->first()    ?: [];
    $dagen["dinsdag"]   = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'dinsdag')->first()   ?: [];
    $dagen["woensdag"]  = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'woensdag')->first()  ?: [];
    $dagen["donderdag"] = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'donderdag')->first() ?: [];
    $dagen["vrijdag"]   = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'vrijdag')->first()   ?: [];
    $dagen["zaterdag"]  = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'zaterdag')->first()  ?: [];
    $dagen["zondag"]    = DB::table('standaarduren')->where('medewerkers_id', $id)->where('dag', 'zondag')->first()    ?: [];
    return view('users.default', ['standaarduren' => $standaarduren, 'kentekens' => $kentekens, 'dagen' => $dagen, "weekdagen" => $weekdagen, "user" => $user]);
  }
  public function storedefault(Request $request) {
    $userrole = DB::table('users')->select('role_id')->where('id', Route::current()->parameter('id'))->first();
    foreach ($request->dagen as $dag) {
      $row = DB::table('standaarduren')->where('medewerkers_id', Route::current()->parameter('id'))->where('dag', $dag['dag'])->first();
      if ($row) {
        DB::table('standaarduren')->where('medewerkers_id', Route::current()->parameter('id'))->where('dag', $dag['dag'])->update([
          'standaarduren'                => isset($dag['standaarduren'])                ? $dag['standaarduren']                : null,
          'maximale_uren'                => isset($dag['maximale_uren'])                ? $dag['maximale_uren']                : null,
          'begintijd'                    => isset($dag['begintijd'])                    ? $dag['begintijd']                    : null,
          'eindtijd'                     => isset($dag['eindtijd'])                     ? $dag['eindtijd']                     : null,
          'pauze'                        => isset($dag['pauze'])                        ? $dag['pauze']                        : null,
          'ladenlossen_begintijd'        => isset($dag['ladenlossen_begintijd'])        ? $dag['ladenlossen_begintijd']        : null,
          'ladenlossen_eindtijd'         => isset($dag['ladenlossen_eindtijd'])         ? $dag['ladenlossen_eindtijd']         : null,
          'ladenlossen_middag_begintijd' => isset($dag['ladenlossen_middag_begintijd']) ? $dag['ladenlossen_middag_begintijd'] : null,
          'ladenlossen_middag_eindtijd'  => isset($dag['ladenlossen_middag_eindtijd'])  ? $dag['ladenlossen_middag_eindtijd']  : null,
          'reisuren'                     => isset($dag['reistijd'])                     ? $dag['reistijd']                     : null,
          'heenreis_woon_werk'           => isset($dag['heenreis_woonwerk'])            ? $dag['heenreis_woonwerk']            : null,
          'terugreis_woon_werk'          => isset($dag['terugreis_woonwerk'])           ? $dag['terugreis_woonwerk']           : null,
          'heenreis_huis_project'        => isset($dag['heenreis_woonproject'])         ? $dag['heenreis_woonproject']         : null,
          'terugreis_huis_project'       => isset($dag['terugreis_woonproject'])        ? $dag['terugreis_woonproject']        : null,
          'bestuurder'                   => isset($dag['bestuurder'])                   ? $dag['bestuurder']                   : null,
          'bijrijder'                    => isset($dag['bijrijder'])                    ? $dag['bijrijder']                    : null,
          'kenteken_id'                  => isset($dag['kenteken'])                     ? $dag['kenteken']                     : null,
          'opmerkingen'                  => isset($dag['opmerkingen'])                  ? $dag['opmerkingen']                  : null,
        ]);
        $insertId = $row->id;
      }
      else {
        $insertId = DB::table('standaarduren')->insertGetId([
          'dag'                          => $dag['dag'],
          'medewerkers_id'               => Route::current()->parameter('id'),
          'standaarduren'                => isset($dag['standaarduren'])                       ? $dag['standaarduren']                : null,
          'maximale_uren'                => isset($dag['maximale_uren'])                       ? $dag['maximale_uren']                : null,
          'begintijd'                    => isset($dag['begintijd'])                           ? $dag['begintijd']                    : null,
          'eindtijd'                     => isset($dag['eindtijd'])                            ? $dag['eindtijd']                     : null,
          'pauze'                        => isset($dag['pauze'])                               ? $dag['pauze']                        : null,
          'ladenlossen_begintijd'        => isset($dag['ladenlossen_begintijd'])               ? $dag['ladenlossen_begintijd']        : null,
          'ladenlossen_eindtijd'         => isset($dag['ladenlossen_eindtijd'])                ? $dag['ladenlossen_eindtijd']         : null,
          'ladenlossen_middag_begintijd' => isset($dag['ladenlossen_middag_begintijd'])        ? $dag['ladenlossen_middag_begintijd'] : null,
          'ladenlossen_middag_eindtijd'  => isset($dag['ladenlossen_middag_eindtijd'])         ? $dag['ladenlossen_middag_eindtijd']  : null,
          'reisuren'                     => isset($dag['reistijd'])                            ? $dag['reistijd']                     : null,
          'heenreis_woon_werk'           => isset($dag['heenreis_woonwerk'])                   ? $dag['heenreis_woonwerk']            : null,
          'terugreis_woon_werk'          => isset($dag['terugreis_woonwerk'])                  ? $dag['terugreis_woonwerk']           : null,
          'heenreis_huis_project'        => isset($dag['heenreis_woonproject'])                ? $dag['heenreis_woonproject']         : null,
          'terugreis_huis_project'       => isset($dag['terugreis_woonproject'])               ? $dag['terugreis_woonproject']        : null,
          'bestuurder'                   => isset($dag['bestuurder'])                          ? $dag['bestuurder']                   : null,
          'bijrijder'                    => isset($dag['bijrijder'])                           ? $dag['bijrijder']                    : null,
          'kenteken_id'                  => isset($dag['kenteken'])                            ? $dag['kenteken']                     : null,
          'opmerkingen'                  => isset($dag['opmerkingen'])                         ? $dag['opmerkingen']                  : null,
        ]);
      }
      DB::table('standaarduren')->where('id', $insertId)->update(['standaardpauzes' => json_encode($dag['standaardpauzes'] ?? [])]);
    }
    $user = User::where('id', Route::current()->parameter('id'))->first();
    $log = Auth::user()->name." ".Auth::user()->lastname." heeft de standaarduren van '".$user->name." ".$user->lastname."' (".$user->id.") ingesteld.";
    ActivityLog::insert([
      "client_id" => getClientId(),
      "user_id" => Auth::user()->id,
      "module_id" => 2,
      "log" => $log,
      "created_at" => Carbon::now(),
    ]);
    return redirect('/users')->with('status', 'Standaarduren zijn ingesteld!');
  }
  public function delete(Request $request) {
    try{
      User::where('id', $request->user)->update(['active' => '0']);

      $user = User::where('id', $request->user)->firstOrFail();
      $log = User::name()." heeft de medewerker '".$user->name." ".$user->lastname."' verwijderd.";
      actLog($log, User::id(), 40);

      return response(null, 201);
    }
    catch (\Exception $e){
      actError($e);
      return response(null, 500);
    }
  }
  public function resetPassword(){
    $requirements = getSettingJson('password_reset_requirements');
    return view('users.reset-password', [
      'requirements' => $requirements
    ]);
  }

  public function downloadExcel() {
    $log = Auth::user()->name." ".Auth::user()->lastname."  heeft de gebruikers gedownload.";
    actLog($log, Auth::user()->id, 7);

    return Excel::download(new UserExport, 'Gebruikers.xlsx');
  }

  //Intake
  public function intakes(){
    return view('users.intake-index');
  }
  public function createIntake(){
    return view('users.intake-create');
  }
  public function storeIntake(Request $request){
    try{
      $uid = User::insertGetId([
        'name' => $request->name,
        'lastname' => $request->lastname,
        'email' => $request->email,
        'bv_id' => $request->bv,
        'role_id' => $request->role,
        'intake' => 1,
        'intake_status' => 'Concept',
        'intake_email' => $request->intake_email,
        'intake_token' => randomString(32),
      ]);

      $user = User::find($uid);
      $user->sendIntakeMail();

      actLog(User::name().' heeft een intakeformulier aangemaakt.', User::id(), 40);

      return redirect('users/intake');
    }
    catch(\Throwable $e){
      actError($e);
      return redirect('users/intake')->with('warning', 'Er is iets foutgegaan!');
    }
  }
  public function fillIntake($subdomain, $token){
    $user = User::where('intake_token', $token)->firstOrFail();
    if(!$user->intake){ abort(400); }

    $fillable = $user->intake_status === 'Verzonden';

    return view('users.intake-fill', [
      'user' => $user,
      'fillable' => $fillable,
    ]);
  }
  public function previewIntake($subdomain, $token){
    $user = User::where('intake_token', $token)->firstOrFail();
    if(!$user->intake){ abort(400); }

    return view('users.intake-fill', [
      'user' => $user,
      'fillable' => false,
    ]);
  }
  public function editIntake($subdomain, $token){
    $user = User::where('intake_token', $token)->firstOrFail();
    if(!$user->intake || $user->intake_status !== 'Ingevuld'){ abort(400); }

    return view('users.intake-fill', [
      'user' => $user,
      'fillable' => true,
    ]);
  }
  public function storeFilledIntake(Request $request){
    try{
      $user = User::where('intake_token', $request->intake_token)->firstOrFail();
      $is_edit = $user->intake_status == 'Ingevuld';

      $leverancier_id = $this->storeFilledIntakeLeverancier($request, $user->bv_id);
      if($leverancier_id){
        $user->leverancier_id = $leverancier_id;
        $user->save();
      }

      $this->storeFilledIntakeUser($request);

      if(!$is_edit){
        $user->sendIntakeFilledMail();
      }

      $this->storeCustomFields($user->id, $request);
      $user->refresh();

      return redirect("users/intake/preview/{$user->intake_token}")->with('status', 'Het intakeformulier is succesvol ingevuld!');
    }
    catch(\Throwable $e){
      actError($e);
      return redirect("users/intake/fill/{$user->intake_token}")->with('warning', 'Er is iets foutgegaan!');
    }
  }
  private function storeFilledIntakeLeverancier($request, $bv_id){
    $leverancier_data = $request->get('leverancier');

    //Fetch leverancier if already exists
    if(isset($leverancier_data['kvk'])){
      $leverancier = Leveranciers::whereNotNull('kvk')->where(['kvk' => $leverancier_data['kvk']])->first();
      if($leverancier){
        return $leverancier->id;
      }
    }

    //Preprocess fields
    $fields = getSettingJson('users_intake_fields');
    $fields = Arr::flatten([
      ($fields[$request->employment_type]['leverancier'] ?? []),
      ($fields['general']['leverancier'] ?? [])
    ]);

    //Process request data
    $data = [
      'bv' => $bv_id,
    ];
    foreach($fields as $field_key){
      $data[$field_key] = $request->get('leverancier')[$field_key] ?? null;
    }

    //Abort if kvk is not provided
    if(empty($data) || !isset($data['kvk'])){ return null; }

    //Create leverancier by kvk
    Leveranciers::insert($data);

    $leverancier = Leveranciers::where(['kvk' => $data['kvk']])->first();
    return optional($leverancier)->id;
  }
  private function storeFilledIntakeUser($request){
    $user = User::where('intake_token', $request->intake_token)->firstOrFail();

    //Preprocess fields
    $fields = getSettingJson('users_intake_fields');
    $fields = Arr::flatten([
      ($fields[$request->employment_type]['user'] ?? []),
      ($fields['general']['user'] ?? [])
    ]);

    //Process request data
    $data = [
      'employment_type' => $request->employment_type,
      'intake_status' => 'Ingevuld',
    ];

    //Hash Password
    if($request->has('password')){
      $data['password'] = Hash::make($request->password);
    }

    foreach($fields as $field_key){
      $value = $request->get($field_key);
      $is_file = $request->hasFile($field_key);

      //Store non explorer files
      if($is_file){
        $value = $user->storeFile($field_key, $request->file($field_key));
      }

      //Handle data exceptions
      if($field_key == 'driver_license'){
        $value = json_encode($value);
      }

      $data[$field_key] = $value;
    }

    //Update user
    User::where('intake_token', $request->intake_token)->update($data);
  }

}
