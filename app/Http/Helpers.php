<?php

use App\ActivityLog;
use App\ApiPosts;
use App\ChecklistsTemplatesKeywords;
use App\Classes\FCMApi;
use App\Device;
use App\ExplorerFiles;
use App\Facturen;
use App\Imports;
use App\ImportTemplates;
use App\Inkoopfacturen;
use App\Http\Controllers\Api\ApiController;
use App\Http\Middleware\GoogleCalendarApi;
use App\Landen;
use App\OffertesDatasetsItems;
use App\Planning;
use App\Templates;
use App\ChecklistsTemplates;
use App\User;
use App\Role;
use App\Clients;
use App\ClientSettings;
use App\Eenheden;
use App\Klanten;
use App\Leveranciers;
use App\Project;
use App\Tarieven;
use App\Machines;
use App\OffertesDatasets;
use App\SupportForms;
use App\Permission;
use App\RolePermission;
use App\Uursoorten;
use App\ProjectTaken;
use App\WerkbonnenTemplateKeywords;
use App\WerkbonnenTemplates;
use App\Module;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

function _username(){
    return Auth::user()->name ?? null;
}

function _get($key, $default = null){
    return $_GET[$key] ?? $default;
}
function _req($key){
    return request()->get($key) ?? '';
}
function _header($key){
    return request()->headers->get($key) ?? '';
}
function _browser() {
    $userAgent = _header('User-Agent');

    $browsers = [
        'Edge' => 'Edg',
        'Chrome' => 'Chrome',
        'Firefox' => 'Firefox',
        'Safari' => 'Safari',
        'IE' => 'MSIE',
    ];

    $browserName = 'Unknown';

// Detect the browser
    foreach ($browsers as $browser => $pattern) {
        if (stripos($userAgent, $pattern) !== false) {
            $browserName = $browser;
            break;
        }
    }

    return $browserName;
}
function _platform() {
    $userAgent = _header('User-Agent');

    $platforms = [
        'Windows 10 / 11' => 'Windows NT 10.0',
        'Windows 8.1' => 'Windows NT 6.3',
        'Windows 8' => 'Windows NT 6.2',
        'Windows 7' => 'Windows NT 6.1',
        'Windows Vista' => 'Windows NT 6.0',
        'Windows XP' => 'Windows NT 5.1',
        'Android' => 'Android',
        'iOS' => 'iPhone|iPod|iPad',
        'Linux' => 'Linux',
    ];

    $platformName = 'Unknown';

    foreach ($platforms as $platform => $pattern) {
        if (stripos($userAgent, $pattern) !== false) {
            $platformName = $platform;
            break;
        }
    }

    return $platformName;
}
function _cookie($key, $default = null){
    return $_COOKIE[$key] ?? $default;
}

function getPDFPages($pdf){
    $pdf->output();
    return $pdf->getDomPDF()->getCanvas()->get_page_count();
}

function isAdmin(){
    if(!Auth::check()){return false;}
    if(Auth::user()->email == '<EMAIL>'){ return true; }
    if(Auth::user()->is_admin === '1'){ return true; }
    if(Auth::user()->is_admin){ return true; }

    return false;
}

function storeQueries(){
    $queries = Session::get('queries_log');
    if (isset($queries) && !empty($queries)){
        DB::table('query_log')->insert([
            'data' => json_encode($queries)
        ]);
    }
    Session::put('queries_log', []);

    DB::listen(function($query) {
        $queries = Session::get('queries_log');
        $queries[$query->sql] = [
            'bindings' => $query->bindings,
            'sql' => $query->sql,
            'route' => currentRoute(),
        ];
        Session::put('queries_log', $queries);
    });
}
function queryFromBuilder($model){
    $sql = $model->toSql();
    foreach($model->getBindings() as $binding){
        if (is_string($binding)) {
            $binding = "'".addslashes($binding)."'";
        }
        $sql = preg_replace('/\?/', $binding, $sql, 1);
    }
    return $sql;
}
function isDate($val){
    return (bool)strtotime($val);
}
function isJustDate($val) {
  $timestamp = strtotime($val);
  if ($timestamp === false) {
    return false;
  }

  $dateOnly = date('Y-m-d', $timestamp);
  return $dateOnly === $val;
}
function Carbon($date = null){
    if($date){
        return Carbon::parse($date);
    }

    return new Carbon();
}
function CarbonFromTimestamp($timestamp = null, $milliseconds = true) {
  if ($timestamp !== null) {
    if ($milliseconds) {
      $timestamp = intval($timestamp / 1000);
    }
    return Carbon::createFromTimestamp($timestamp);
  }

  return Carbon::now();
}
function CarbonDmy($date = null){
  if($date){
    return Carbon::parse($date)->format('d-m-Y');
  }

  return Carbon::parse(new Carbon())->format('d-m-Y');
}
function CarbonTime($date = null){
  if($date){
    return Carbon::parse($date)->format('H:i');
  }

  return Carbon::parse(new Carbon())->format('H:i');
}
function CarbonPeriod($start, $end, $interval){
    $start = Carbon::parse($start);
    $end = Carbon::parse($end);
    return CarbonPeriod::create($start, $end)->every($interval);
}
function formatTimeDifference(int $diffSeconds): string {
    $hours = round($diffSeconds / 3600);
    $minutes = round(($diffSeconds % 3600) / 60);

    $timeString = '';

    if ($hours > 0) {
        $timeString .= $hours . ' uur';
    }

    if ($minutes > 0) {
        if ($timeString !== '') {
            $timeString .= ' en ';
        }
        $timeString .= $minutes . ' ' . ($minutes > 1 ? 'minuten' : 'minuut');
    }

    if ($hours === 0 && $minutes === 0) {
        $timeString = '0 minuten';
    }

    return $timeString;
}

function determinePeriodStart($selectedPeriod){
    switch($selectedPeriod){
        case 'dag':
            $periodStart = Carbon::now()->startOfDay();
            break;
        case 'week':
            $periodStart = Carbon::now()->startOfWeek();
            break;
        case 'maand':
            $periodStart = Carbon::now()->startOfMonth();
            break;
        case 'kwartaal':
            $periodStart = Carbon::now()->startOfQuarter();
            break;
        case 'half_jaar':
            $month = Carbon::now()->month;
            $periodStart = ($month <= 6)
                ? Carbon::now()->startOfYear()
                : Carbon::now()->startOfYear()->addMonths(6);
            break;
        case 'jaar':
            $periodStart = Carbon::now()->startOfYear();
            break;
        default:
            $periodStart = null;
    }

    return $periodStart;
}
function cacheClear(){
    return '?'.randomString().'='.randomString();
}

function removeCookie($name){
    setcookie($name, null, time()-1, "/");
}
function getUnseenModules(){
    $lastViewedTime = Carbon::parse(_cookie('store_last_viewed'));
    return Module::where('updated_at', '>', $lastViewedTime)->get();
}
function removeSpecialCharacters($string){
    return preg_replace('/[`!@#$%^&*()_+\=\[\]{};\'":\\\\|,.<>\/?~]/', '', $string);
}
function strContains($needle, $haystack){
    return strpos($haystack, $needle) !== false;
}
function find($index, $search, $arr){
  foreach(is_array($index) ? $index : [$index] as $i){
      foreach($arr ?? [] as $row){
          $x = $row[$i] ?? null;
          if($x == $search){
              return $row;
          }
      }
  }
  return null;
}

function bytesToFormats($bytes){
    $size = new \stdClass();

    $size->B = intval($bytes);
    $size->KB = round($size->B / 1024, 2);
    $size->MB = round($size->KB / 1024, 2);
    $size->GB = round($size->MB / 1024, 2);

    if($size->GB >= 1){
        $size->display = "{$size->GB}GB";
    }
    elseif($size->MB >= 1){
        $size->display = "{$size->MB}MB";
    }
    elseif($size->KB >= 1){
        $size->display = "{$size->KB}KB";
    }
    else{
        $size->display ="{$size->B}B";
    }

    return $size;
}

function findInObj($index, $search, $obj){
    foreach($obj ?? [] as $row){
        $x = $row->$index ?? null;
        if($x == $search){
            return $row;
        }
    }
    return null;
}
function firstBv(){
    return \App\BV::where('client_id', getClientId())->first()->id;
}
function firstBvObject(){
    return \App\BV::where('client_id', getClientId())->first();
}
function debug_post($data){
    ApiPosts::insert([
        "post" => json_encode($data),
    ]);
}
function hasAccessTo($permission) {
    foreach(\Config::get('global.settings') as $setting) {
        if($permission == $setting->permission->setting) {
            return true;
        }
    }
    return false;
}
function clientSetting($setting, $default = null){
    foreach(\Config::get('global.settings') as $global_setting) {
        if($setting == ($global_setting->permission->setting ?? null)) {
            return $global_setting->value ?? $default;
        }
    }
    return $default;
}
function permissionValue($permission) {
    foreach(\Config::get('global.settings') as $setting) {
        if($permission == $setting->permission->setting) {
            return $setting->value;
        }
    }
    return false;
}
function hasPermission($permission, $module = null){
    if(app()->runningInConsole()){ return true; }
    if(!Auth::check()){ return false; }

    if($module){
        return Auth::user()->hasModulePermission($module, $permission);
    }

    return Auth::user()->hasPermissionTo($permission);
}
function bladeExists($blade){

    $blade = str_replace('.', '/', $blade) . ".blade.php";
    $blade = str_replace('{client}', getClientId(), $blade);

    return file_exists('../resources/views/'.$blade);
}
function clientSettingValue($clientId, $settingId) {
    return ClientSettings::where('client_id', $clientId)->where('setting_id', $settingId)->first()->value ?? null;
}
function getMaanden($maand = null){
    $maanden = [];
    $maanden[1] = "Januari";
    $maanden[2] = "Februari";
    $maanden[3] = "Maart";
    $maanden[4] = "April";
    $maanden[5] = "Mei";
    $maanden[6] = "Juni";
    $maanden[7] = "Juli";
    $maanden[8] = "Augustus";
    $maanden[9] = "September";
    $maanden[10] = "Oktober";
    $maanden[11] = "November";
    $maanden[12] = "December";
    if($maand !== null){
        return $maanden[intval($maand)] ?? '';
    }
    return $maanden;
}
function getShortMaanden($maand = null){
    $maanden = [];
    $maanden[1] = "Jan";
    $maanden[2] = "Feb";
    $maanden[3] = "Mrt";
    $maanden[4] = "Apr";
    $maanden[5] = "Mei";
    $maanden[6] = "Jun";
    $maanden[7] = "Jul";
    $maanden[8] = "Aug";
    $maanden[9] = "Sept";
    $maanden[10] = "Okt";
    $maanden[11] = "Nov";
    $maanden[12] = "Dec";
    if($maand !== null){
        return $maanden[$maand] ?? '';
    }
    return $maanden;
}
function getFourWeeklyPeriods($year = null){
    $year = $year ?? date('Y');

    $periods = [];

    for($i = 1; $i <= 13; $i++){
        $period = new \stdClass();
        $period->index = $i;
        $period->start = Carbon()->now()->setISODate($year, $i * 4 - 3)->startOfWeek();
        $period->end = Carbon()->now()->setISODate($year, $i * 4)->endOfWeek();
        $period->is_current = Carbon()->now()->between($period->start, $period->end);

        $periods[] = $period;
    }

    return $periods;
}
function getMonthlyPeriods($year = null) {
  $year = $year ?? date('Y');

  $periods = [];
  $carbonPeriod = CarbonPeriod::create("$year-01-01", "1 month", "$year-12-31");

  foreach ($carbonPeriod as $startDate) {
      $period = new \stdClass();
      $period->start = $startDate;
      $period->end = $startDate->copy()->endOfMonth();
      $period->index = $startDate->month;
      $period->is_current = Carbon::now()->between($period->start, $period->end);

      $periods[] = $period;
  }

  return $periods;
}
function getMaandIndex($maand){
    switch ($maand){
        case  ($maand == "januari" || $maand == "Januari") : return 1;
        case  ($maand == "Februari" || $maand == "februari") : return 2;
        case  ($maand == "Maart" || $maand == "maart") : return 3;
        case  ($maand == "April" || $maand == "april") : return 4;
        case  ($maand == "Mei" || $maand == "mei") : return 5;
        case  ($maand == "Juni" || $maand == "juni") : return 6;
        case  ($maand == "Juli" || $maand == "juli") : return 7;
        case  ($maand == "Augustus" || $maand == "augustus") : return 8;
        case  ($maand == "September" || $maand == "september") : return 9;
        case  ($maand == "Oktober" || $maand == "oktober") : return 10;
        case  ($maand == "November" || $maand == "november") : return 11;
        case  ($maand == "December" || $maand == "december") : return 12;
    }
}
function getWeekdagen($dow = null){
    $dagen = [];
    $dagen[0] = 'Zondag';
    $dagen[1] = 'Maandag';
    $dagen[2] = 'Dinsdag';
    $dagen[3] = 'Woensdag';
    $dagen[4] = 'Donderdag';
    $dagen[5] = 'Vrijdag';
    $dagen[6] = 'Zaterdag';

    if ($dow !== null && isset($dagen[$dow])){ return $dagen[$dow]; }

    return $dagen;
}
function pushToUser($title, $message, $user, $phone = null){
    $devices = \App\Device::where('user_id', $user)->get();
    foreach($devices as $device){
        if(isset($phone)){
            if($device->brand == $phone){
                $tokens[] = $device->device_id;
            }
        }
        else{
            $tokens[] = $device->device_id;
        }
    }
    pushToAll($title, $message, $tokens ?? []);
}
function pushToAll($title, $message, $tokens, $data = []) {
    try {
        $FCM = new FCMApi();
        $FCM->sendNotification($title, $message, $tokens, $data);
    }
    catch (\Exception $e) {
        actError($e);
    }
}
function randomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyz';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
function randomStringLU($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
function randomStringLetters($length = 10) {
    $characters = 'abcdefghijklmnopqrstuvwxyz';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
function randomStringCijfers($length = 10) {
    $characters = '1234567890';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

function randomHex($l = 6) {
    $characters = '123456789ABCDEF';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $l; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return "#".$randomString;
}
function generateHexId(){
    return '0x'.bin2hex(\Carbon()->now()->format('Y-m-d H:i:s:v'));
}
function decodeHexId($id){
    $id = str_replace('0x', '', $id);
    return hex2bin($id);
}

function ISDK($blade){
    //InjectedSettingsDirectoryKeys

    $ISD = new \App\Classes\InjectedSettingsDirectory();
    return $ISD->bladeKeys($blade);
}
function injectSettings($blade){
    $keys = ISDK($blade);
    $_SERVER['_SETTINGS'] = \App\Settings::select('naam', 'value')->whereIn('naam', $keys)->get()->keyBy('naam')->toArray();
}

function getSettingValue($key, $default = null){
    if (isset($_SERVER['_SETTINGS'][$key])) {
        return $_SERVER['_SETTINGS'][$key]['value'] ?? $default;
    }

    //To display the settings on the current page:
    //in the api_posts, change the post column to [] on the record with with route _settings_debug and run the snippet below
    //  $post = ApiPosts::where('route', '_settings_debug')->first();
    //  $arr = json_decode($post->post, true);
    //  $arr[] = $key;
    //  $post->post = json_encode($arr);
    //  $post->save();

    $setting =  \App\Settings::where("naam", $key)->first();
    return $setting->value ?? $default;
}
function getSettingCheckbox($key){
    $setting =  \App\Settings::where("naam", $key)->first();
    return isset($setting);
}
function getSettingJson($key, $default = null, $array = true){
    return json_decode(getSettingValue($key, $default) ?? '[]', $array);
}

function getSubdomein(){
  if(app()->runningInConsole()){
    return Session::get('mock_subdomain') ?? 'Onbekend';
  }
    return Route::current()->parameter('subdomain') ?? 'Onbekend';
}
function getClientId(){
    return getClient()->id;
}
function getClient(){
    return Clients::where('subdomain', getSubdomein())->first();
}
function getClients(){
    $clients = Clients::get();
    foreach ($clients as $client){
        $client->logo = $client->logo();
    }
    return $clients;
}
function checkSubdomein(){
    if(getSubdomein() != "beheer"){
        header("location: ".url("/"));
    }
}
function addView(){
    $datum = \carbon\Carbon::now()->format("Y-m-d");
    $viewLog = DB::connection("tessa")->table("log_view")->where("datum", $datum)->where("client_id", getClientId())->where("type","alle")->first();

    if(!empty($viewLog)){
        $currentView = $viewLog->views + 1;
    }
    else{
        $currentView = 1;
    }

    DB::connection("tessa")->table("log_view")->updateOrInsert([
        "client_id" => getClientId(),
        "datum" => $datum,
        "type" => "alle"
    ],
        [
            "views" => $currentView,
            "updated_at" => Carbon::now(),
        ]);

    DB::connection("tessa")->table("log_online")->updateOrInsert([
        "client_id" => getClientId(),
        "user_id" => Auth::user()->id,
        "date" => $datum,
    ],[
        "updated_at" => Carbon::now(),
    ]);



//  First login
    if (empty(\App\ActivityLog::where("client_id",getClientId())->where("user_id",Auth::user()->id)->where("type", "login")->whereDate("created_at", "=", \Carbon\Carbon::now())->first())) {
        $client = \App\Clients::where("id", getClientId())->first();
        $log = Auth::user()->name ." heeft zich vandaag voor het eerst ingelogd.";
        \App\ActivityLog::insert([
            "client_id" => getClientId(),
            "user_id" => Auth::user()->id,
            "type" => "login",
            "log" => $log,
            "created_at" => \Carbon\Carbon::now(),

        ]);
    }

}
function addViewById($id){
    $user = \App\User::where("id", $id)->first();
    $datum = \carbon\Carbon::now()->format("Y-m-d");
    $viewLog = DB::connection("tessa")->table("log_view")->where("datum", $datum)->where("client_id", getClientId())->where("type","alle")->first();
    if(isset($viewLog)){
        $currentView = $viewLog->views + 1;
    }
    else{
        $currentView = 1;
    }

    DB::connection("tessa")->table("log_view")->updateOrInsert([
        "client_id" => getClientId(),
        "datum" => $datum,
        "type" => "alle"
    ], [
        "views" => $currentView,
        "updated_at" => Carbon::now(),
    ]);

    DB::connection("tessa")->table("log_online")->updateOrInsert([
        "client_id" => getClientId(),
        "user_id" => $user->id,
        "date" => $datum,
    ],[
        "updated_at" => Carbon::now(),
    ]);

//  First login
    if (empty(\App\ActivityLog::where("client_id",getClientId())->where("user_id",$user->id)->where("type", "login")->whereDate("created_at", "=", \Carbon\Carbon::now())->first())) {
        $log = $user->name . " " . $user->lastname . " heeft zich vandaag voor het eerst ingelogd (App).";
        \App\ActivityLog::insert([
            "client_id" => getClientId(),
            "user_id" => $user->id,
            "type" => "login",
            "log" => $log,
            "created_at" => \Carbon\Carbon::now(),

        ]);
    }

}
function currentPermissions(){
    $modules = \DB::connection("tessa")->table("client_modules")->where("client_id", getClientId())->get();
    $permissions = [];
    foreach ($modules as $row){
        $prm = \DB::connection("tessa")->table("permissions")->where("module_id", $row->module_id)->get();
        foreach ($prm as $permission){
            $permissions[] = $permission;
        }
    }
    return $permissions;
}
function setConnection($driver, $host, $username, $password)
{
    $string = randomString(5);
    config(['database.connections.'.$string => [
        'driver' => $driver,
        'host' => $host,
        'username' => $username,
        'password' => tes_decrypt($password),
        'database' => $username,
    ]]);
    return DB::connection($string);
}
function setHostConnection($driver, $host, $username, $password, $database)
{
    $string = randomString(5);
    config(['database.connections.'.$string => [
        'driver' => $driver,
        'host' => $host,
        'username' => $username,
        'password' => $password,
        'database' => $database,
    ]]);
    return DB::connection($string);
}
function setDisk($driver, $root, $host, $username, $password)
{
    config(['filesystems.disks.dynamic' => [
        'driver' => $driver,
        "root" => $root,
        'host' => $host,
        'username' => $username,
        'password' => $password,
    ]]);
    return Storage::disk('dynamic');
}
function getKlantSettingValue($klantid, $setting){
    $settings =  \App\KlantenSettings::where("klant_id", $klantid)->where("naam", $setting)->first();
    if(isset($settings) && isset($settings->value)){
        return $settings->value;
    }

    return null;
}
function getStringBetween($str,$from,$to){
    $sub = substr($str, strpos($str,$from)+strlen($from),strlen($str));
    return substr($sub,0,strpos($sub,$to));
}
function hasModule($naam){
    $module = DB::connection("tessa")->table("modules")->where("name", $naam)->first();
    $clientModule = DB::connection("tessa")->table("client_modules")->where("client_id", getClientId())->where("module_id", $module->id ?? 0)->first();
    if($clientModule == null){
        return false;
    }
    else{
        return true;
    }
}
function someoneHasPermission($permission){
    $perm = Permission::where('permission', $permission)->first();
    $hasPermission = RolePermission::where('permission_id', $perm->id)->first();
    if(!$hasPermission){
        return false;
    }else{
        return true;
    }
}
function getWeekByDate($y){
    $period = \Carbon\CarbonPeriod::create($y."-01"."-01", $y."-12"."-31");
    $dates = [];
    foreach ($period as $p){
        $dates[$p->format("Y-m-d")] = $p->week;
    }
    return $dates;
}
function getDateByWeek($y){
    $period = \Carbon\CarbonPeriod::create($y."-01"."-01", $y."-12"."-31");
    $dates = [];
    foreach ($period as $p){
        $dates[$p->week][$p->format("Y-m-d")] = $p->format("Y-m-d");
    }
    return $dates;
}
function userPermissions($id){
    $permissions = [];
    $user = \App\User::where("id", $id)->first();
    $rolePermissions = \App\RolePermission::where("role_id", $user->role_id)->get();
    foreach($rolePermissions as $row){
        $per = DB::connection("tessa")->table("permissions")->where("id", $row->permission_id)->first();
        if(!isset($per)){ continue; }
        $permissions[$per->permission] = $per;
    }
    return $permissions;


}
function externHasModule($module){
    $klant = \App\ExternenKlanten::where("user_id", Auth::user()->id)->first();
    $specific = \App\ExternenModule::where("user_id", Auth::user()->id)->where("module", $module)->first();
    if(isset($specific) || isset($klant)){
        return true;
    }
    else{
        return false;
    }
}
function pushToDevelopers($title, $message){
    try{
        $client = Clients::where('subdomain', 'infordb')->with('database')->first();
        $con = setConnection($client->database->driver, $client->database->host, $client->database->username, $client->database->password);
        $user = $con->table('users')->where('email', '<EMAIL>')->first();

        $tokens = $con->table('devices')
            ->whereNotNull('device_id')
            ->where('device_id', '!=', 'no_token_given')
            ->where('user_id', $user->id)
            ->where('last_activity', '>=', Carbon::now()->subDays(30))
            ->orderBy('last_activity', 'DESC')
            ->pluck('device_id')->toArray();

        $tokens = array_values(array_unique($tokens));

        return pushToAll($title, $message, $tokens);

    }
    catch (Exception $e){ actError($e); return null; }
}
function actError(Exception $e){
    actErrorLog($e);

    try {
        $client = Clients::where('subdomain', 'infordb')->with('database')->first();
        $con = setConnection($client->database->driver, $client->database->host, $client->database->username, $client->database->password);
        $user = $con->table('users')->where('email', '<EMAIL>')->first();
        $tokens = array_values(array_unique($con->table('devices')->where('user_id', $user->id)->pluck('device_id')->toArray()));

        pushToAll('Er is een fout opgetreden!', "Subdomein: ".getSubdomein()."\r\nPagina: ".currentRoute()."\r\nFoutmelding: ".$e->getMessage()."\r\nUser: ".User::name()."\r\nPlatform: "._platform()." - "._browser()."\r\nBestand: ".lastValue(explode('/', $e->getFile()))."\r\nRegel: ".$e->getLine(), $tokens);
    }
    catch(Exception $ex){}
}
function actErrorLog($e){
    actLog("
        <div class='text-danger' >Er is een fout opgetreden!</div>
        <ul class='text-danger d-flex flex-column'>
            <li><b>Pagina:</b> '".currentRoute()."'</li>
            <li><b>Foutmelding:</b> ".$e->getMessage()."</li>
            <li><b>User:</b> ".User::name()."</li>
            <li><b>Platform:</b> "._platform()." - "._browser()."</li>
            <li><b>Bestand:</b> ".lastValue(explode('/', $e->getFile()))."</li>
            <li><b>Regel:</b> ".$e->getLine()."</li>
        <ul>",
        User::id(), null, 'error'
    );
}
function actErrorFront($post, $prefix){
    try{
        actLog("
        <div class='text-danger' >$prefix Er is een fout opgetreden!</div>
        <ul class='text-danger d-flex flex-column'>
            <li><b>Route:</b> ".$post['route']." </li>
            <li><b>Foutmelding:</b> ".$post['message']."</li>
            <li><b>User:</b> ".User::name()."</li>
            <li><b>Platform:</b> "._platform()." - "._browser()."</li>
            <li><b>Request Status:</b> ".$post['status']."</li>
            <li><b>Stack:</b> ".$post['stack']."</li>
            <li><b>Opmerking:</b> ".$post['opmerking']."</li>
        <ul>",
            User::id(), null, 'error'
        );
        pushToDevelopers($prefix.' Er is een fout opgetreden!',"Subdomein: ".getSubdomein()."\r\nRoute: ".$post['route']."\r\nFoutmelding: ".$post['message']."\r\nUser: ".User::name()."\r\nPlatform: "._platform()." - "._browser()."\r\nRequest status: ".($post['status'] ?? 'Geen request')."\r\nStack: ".$post['stack']."\r\nOpmerking: ".$post['opmerking']);
    }
    catch(Exception $ex){}
}

function actLog($log, $userId, $moduleId, $type = null){
  ActivityLog::insert([
        "client_id" => getClientId(),
        "user_id" => $userId,
        "module_id" => $moduleId,
        "log" => $log,
        "created_at" => Carbon::now(),
        "type" => $type,
    ]);
}
function actLogBeheer($log, $userId, $moduleId){
    ActivityLog::insert([
        "client_id" => 1,
        "user_id" => $userId,
        "module_id" => $moduleId,
        "log" => $log,
        "created_at" => Carbon::now(),
    ]);
}
function actLogByClient($client, $log, $userId, $moduleId){
    ActivityLog::insert([
        "client_id" => $client,
        "user_id" => $userId,
        "module_id" => $moduleId,
        "log" => $log,
        "created_at" => Carbon::now(),
    ]);
}
function getGoogleAgendas($google_token){
    $capi = new GoogleCalendarApi();
    $token = $capi->refreshAccessToken($google_token);
    if(isset($token)){
        return $capi->GetCalendarsList($token['access_token']);
    }
    else{
        return [];
    }

}
function keyBy($arr, $key){
    $new = [];
    foreach ($arr as $row){
        $new[$row[$key]] = $row;
    }
    return $new;
}
function groupBy($arr, $key){
    $new = [];
    foreach ($arr as $row){
        $new[$row[$key]][] = $row;
    }
    return $new;
}
function groupByKeyBy($arr, $group, $key){
    $new = [];
    foreach ($arr as $row){
        $new[$row[$group]][$row[$key]] = $row;
    }
    return $new;
}
function groupByGroupBy($arr, $group, $key){
    $new = [];
    foreach ($arr as $row){
        $new[$row[$group]][$row[$key]][] = $row;
    }
    return $new;
}
function getSeasonLogo(){


    $logo = "client/img/logo.png";
    if(urlExists(url("client/img/logos/tessa/client".getClientId().".png"))){
        $logo = "client/img/logos/tessa/client".getClientId().".png";
    }


    $date = Carbon::now();
    $year = $date->year;

    $zomer = ['start' => $year.'-07-01', 'end' => $year.'-08-31', 'asset' => '/client/public/img/tessa_seasons/tessa_zomer.png'];
    $kerst = ['start' => $year.'-12-20', 'end' => ($year+1).'-01-01', 'asset' => '/client/public/img/tessa_seasons/tessa_kerst.png'];
    $koningsdag = ['start' => $year.'-04-23', 'end' => $year.'-04-23', 'asset' => '/client/public/img/tessa_seasons/tessa_koningsdag.png'];
    $pasen = ['start' => $year.'-03-25', 'end' => $year.'-04-01', 'asset' => '/client/public/img/tessa_seasons/tessa_pasen.png'];
    $halloween = ['start' => $year.'-10-27', 'end' => $year.'-10-31', 'asset' => '/client/public/img/tessa_seasons/tessa_halloween.png'];
    $sinterklaas = ['start' => $year.'-11-29', 'end' => $year.'-12-06', 'asset' => '/client/public/img/tessa_seasons/tessa_sinterklaas.png'];
    $valentijnsdag = ['start' => $year.'-02-10', 'end' => $year.'-02-14', 'asset' => '/client/public/img/tessa_seasons/tessa_valentijnsdag.png'];

    $seasons = [$zomer, $kerst, $koningsdag, $pasen, $halloween, $sinterklaas, $valentijnsdag];

    foreach($seasons as $season){
        if(isDateBetween(date("Y-m-d"), $season['start'], $season['end'])){
            $logo = $season['asset'];
        }
    }

    return asset($logo);

}
function isDateBetween($t, $a, $b){
    return  ( Carbon::parse($t)->gte(Carbon::parse($a)) && Carbon::parse($t)->lte(Carbon::parse($b)) );
}
function dateRangesOverlap($s1, $e1, $s2, $e2){
    $s1 = Carbon::parse($s1);
    $e1 = Carbon::parse($e1);
    $s2 = Carbon::parse($s2);
    $e2 = Carbon::parse($e2);

    if($s1->between($s2, $e2)){ return true; }
    if($e1->between($s2, $e2)){ return true; }
    if($s1->lte($s2) && $e1->gte($e2)){ return true; }
    if($s1->gte($s2) && $e1->lte($e2)){ return true; }

    return false;
}
function extensions(){
    return $extensions = [
        'img' => ['jpg' => true, 'jpeg' => true, 'png' => true, 'gif' => true, 'svg' => true, 'webp' => true, 'tiff' => true, 'Bitmap' => true, 'EPS' => true],
        'mp3' => ['mp3' => true, 'aac' => true, 'flac' => true, 'alac' => true, 'wav' => true, 'aiff' => true, 'dsd' => true, 'pcm' => true],
        'mp4' => ['mp4' => true, 'mov' => true, 'wmv' => true, 'avi' => true, 'avchd' => true, 'flv' =>true,  'f4v' => true, 'swf' => true, 'mkv' => true, 'webm' => true],
        'pdf' => ['pdf' => true],
        'stat' => ['csv' => true, 'dat' => true, 'db' => true, 'dbf' => true, 'log' => true, 'mdb' => true, 'sav' => true, 'sql' => true, 'tar' => true, 'xml' => true],
        'txt' => ['doc' => true, 'docx' => true, 'odt' => true, 'rtf' => true, 'tex' => true, 'txt' => true, 'wpd' => true],
        'xlsx' => ['ods' => true, 'xls' => true, 'xlsm' => true, 'xlsx' => true],
        'zip' => ['7z' => true, 'arj' => true, 'deb' => true, 'pkg' => true, 'rar' => true, 'rpm' => true, 'tar' => true, 'gz' => true, 'z' => true, 'zip' => true]
    ];
}
function mimeToExtension($mime) {
    $mime_map = [
        'video/3gpp2'                                                               => '3g2',
        'video/3gp'                                                                 => '3gp',
        'video/3gpp'                                                                => '3gp',
        'application/x-compressed'                                                  => '7zip',
        'audio/x-acc'                                                               => 'aac',
        'audio/ac3'                                                                 => 'ac3',
        'application/postscript'                                                    => 'ai',
        'audio/x-aiff'                                                              => 'aif',
        'audio/aiff'                                                                => 'aif',
        'audio/x-au'                                                                => 'au',
        'video/x-msvideo'                                                           => 'avi',
        'video/msvideo'                                                             => 'avi',
        'video/avi'                                                                 => 'avi',
        'application/x-troff-msvideo'                                               => 'avi',
        'application/macbinary'                                                     => 'bin',
        'application/mac-binary'                                                    => 'bin',
        'application/x-binary'                                                      => 'bin',
        'application/x-macbinary'                                                   => 'bin',
        'image/bmp'                                                                 => 'bmp',
        'image/x-bmp'                                                               => 'bmp',
        'image/x-bitmap'                                                            => 'bmp',
        'image/x-xbitmap'                                                           => 'bmp',
        'image/x-win-bitmap'                                                        => 'bmp',
        'image/x-windows-bmp'                                                       => 'bmp',
        'image/ms-bmp'                                                              => 'bmp',
        'image/x-ms-bmp'                                                            => 'bmp',
        'application/bmp'                                                           => 'bmp',
        'application/x-bmp'                                                         => 'bmp',
        'application/x-win-bitmap'                                                  => 'bmp',
        'application/cdr'                                                           => 'cdr',
        'application/coreldraw'                                                     => 'cdr',
        'application/x-cdr'                                                         => 'cdr',
        'application/x-coreldraw'                                                   => 'cdr',
        'image/cdr'                                                                 => 'cdr',
        'image/x-cdr'                                                               => 'cdr',
        'zz-application/zz-winassoc-cdr'                                            => 'cdr',
        'application/mac-compactpro'                                                => 'cpt',
        'application/pkix-crl'                                                      => 'crl',
        'application/pkcs-crl'                                                      => 'crl',
        'application/x-x509-ca-cert'                                                => 'crt',
        'application/pkix-cert'                                                     => 'crt',
        'text/css'                                                                  => 'css',
        'text/x-comma-separated-values'                                             => 'csv',
        'text/comma-separated-values'                                               => 'csv',
        'application/vnd.msexcel'                                                   => 'csv',
        'application/x-director'                                                    => 'dcr',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'   => 'docx',
        'application/x-dvi'                                                         => 'dvi',
        'message/rfc822'                                                            => 'eml',
        'application/x-msdownload'                                                  => 'exe',
        'video/x-f4v'                                                               => 'f4v',
        'audio/x-flac'                                                              => 'flac',
        'video/x-flv'                                                               => 'flv',
        'image/gif'                                                                 => 'gif',
        'application/gpg-keys'                                                      => 'gpg',
        'application/x-gtar'                                                        => 'gtar',
        'application/x-gzip'                                                        => 'gzip',
        'application/mac-binhex40'                                                  => 'hqx',
        'application/mac-binhex'                                                    => 'hqx',
        'application/x-binhex40'                                                    => 'hqx',
        'application/x-mac-binhex40'                                                => 'hqx',
        'text/html'                                                                 => 'html',
        'image/x-icon'                                                              => 'ico',
        'image/x-ico'                                                               => 'ico',
        'image/vnd.microsoft.icon'                                                  => 'ico',
        'text/calendar'                                                             => 'ics',
        'application/java-archive'                                                  => 'jar',
        'application/x-java-application'                                            => 'jar',
        'application/x-jar'                                                         => 'jar',
        'image/jp2'                                                                 => 'jp2',
        'video/mj2'                                                                 => 'jp2',
        'image/jpx'                                                                 => 'jp2',
        'image/jpm'                                                                 => 'jp2',
        'image/jpeg'                                                                => 'jpeg',
        'image/pjpeg'                                                               => 'jpeg',
        'application/x-javascript'                                                  => 'js',
        'application/json'                                                          => 'json',
        'text/json'                                                                 => 'json',
        'application/vnd.google-earth.kml+xml'                                      => 'kml',
        'application/vnd.google-earth.kmz'                                          => 'kmz',
        'text/x-log'                                                                => 'log',
        'audio/x-m4a'                                                               => 'm4a',
        'audio/mp4'                                                                 => 'm4a',
        'application/vnd.mpegurl'                                                   => 'm4u',
        'audio/midi'                                                                => 'mid',
        'application/vnd.mif'                                                       => 'mif',
        'video/quicktime'                                                           => 'mov',
        'video/x-sgi-movie'                                                         => 'movie',
        'audio/mpeg'                                                                => 'mp3',
        'audio/mpg'                                                                 => 'mp3',
        'audio/mpeg3'                                                               => 'mp3',
        'audio/mp3'                                                                 => 'mp3',
        'video/mp4'                                                                 => 'mp4',
        'video/mpeg'                                                                => 'mpeg',
        'application/oda'                                                           => 'oda',
        'audio/ogg'                                                                 => 'ogg',
        'video/ogg'                                                                 => 'ogg',
        'application/ogg'                                                           => 'ogg',
        'font/otf'                                                                  => 'otf',
        'application/x-pkcs10'                                                      => 'p10',
        'application/pkcs10'                                                        => 'p10',
        'application/x-pkcs12'                                                      => 'p12',
        'application/x-pkcs7-signature'                                             => 'p7a',
        'application/pkcs7-mime'                                                    => 'p7c',
        'application/x-pkcs7-mime'                                                  => 'p7c',
        'application/x-pkcs7-certreqresp'                                           => 'p7r',
        'application/pkcs7-signature'                                               => 'p7s',
        'application/pdf'                                                           => 'pdf',
        'application/octet-stream'                                                  => 'pdf',
        'application/x-x509-user-cert'                                              => 'pem',
        'application/x-pem-file'                                                    => 'pem',
        'application/pgp'                                                           => 'pgp',
        'application/x-httpd-php'                                                   => 'php',
        'application/php'                                                           => 'php',
        'application/x-php'                                                         => 'php',
        'text/php'                                                                  => 'php',
        'text/x-php'                                                                => 'php',
        'application/x-httpd-php-source'                                            => 'php',
        'image/png'                                                                 => 'png',
        'image/x-png'                                                               => 'png',
        'application/powerpoint'                                                    => 'ppt',
        'application/vnd.ms-powerpoint'                                             => 'ppt',
        'application/vnd.ms-office'                                                 => 'ppt',
        'application/msword'                                                        => 'doc',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
        'application/x-photoshop'                                                   => 'psd',
        'image/vnd.adobe.photoshop'                                                 => 'psd',
        'audio/x-realaudio'                                                         => 'ra',
        'audio/x-pn-realaudio'                                                      => 'ram',
        'application/x-rar'                                                         => 'rar',
        'application/rar'                                                           => 'rar',
        'application/x-rar-compressed'                                              => 'rar',
        'audio/x-pn-realaudio-plugin'                                               => 'rpm',
        'application/x-pkcs7'                                                       => 'rsa',
        'text/rtf'                                                                  => 'rtf',
        'text/richtext'                                                             => 'rtx',
        'video/vnd.rn-realvideo'                                                    => 'rv',
        'application/x-stuffit'                                                     => 'sit',
        'application/smil'                                                          => 'smil',
        'text/srt'                                                                  => 'srt',
        'image/svg+xml'                                                             => 'svg',
        'application/x-shockwave-flash'                                             => 'swf',
        'application/x-tar'                                                         => 'tar',
        'application/x-gzip-compressed'                                             => 'tgz',
        'image/tiff'                                                                => 'tiff',
        'font/ttf'                                                                  => 'ttf',
        'text/plain'                                                                => 'txt',
        'text/x-vcard'                                                              => 'vcf',
        'application/videolan'                                                      => 'vlc',
        'text/vtt'                                                                  => 'vtt',
        'audio/x-wav'                                                               => 'wav',
        'audio/wave'                                                                => 'wav',
        'audio/wav'                                                                 => 'wav',
        'application/wbxml'                                                         => 'wbxml',
        'video/webm'                                                                => 'webm',
        'image/webp'                                                                => 'webp',
        'audio/x-ms-wma'                                                            => 'wma',
        'application/wmlc'                                                          => 'wmlc',
        'video/x-ms-wmv'                                                            => 'wmv',
        'video/x-ms-asf'                                                            => 'wmv',
        'font/woff'                                                                 => 'woff',
        'font/woff2'                                                                => 'woff2',
        'application/xhtml+xml'                                                     => 'xhtml',
        'application/excel'                                                         => 'xl',
        'application/msexcel'                                                       => 'xls',
        'application/x-msexcel'                                                     => 'xls',
        'application/x-ms-excel'                                                    => 'xls',
        'application/x-excel'                                                       => 'xls',
        'application/x-dos_ms_excel'                                                => 'xls',
        'application/xls'                                                           => 'xls',
        'application/x-xls'                                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'         => 'xlsx',
        'application/vnd.ms-excel'                                                  => 'xlsx',
        'application/xml'                                                           => 'xml',
        'text/xml'                                                                  => 'xml',
        'text/xsl'                                                                  => 'xsl',
        'application/xspf+xml'                                                      => 'xspf',
        'application/x-compress'                                                    => 'z',
        'application/x-zip'                                                         => 'zip',
        'application/zip'                                                           => 'zip',
        'application/x-zip-compressed'                                              => 'zip',
        'application/s-compressed'                                                  => 'zip',
        'multipart/x-zip'                                                           => 'zip',
        'text/x-scriptzsh'                                                          => 'zsh',
    ];

    return $mime_map[$mime] ?? false;
}
function ddd($var){
    if((Auth::user()->email ?? null) !== '<EMAIL>'){ return; }
    dd($var);
}
function sdd($sub, $var){
    if(getSubdomein() == $sub){
        dd($var);
    }
}
function sdump($sub, $var){
    if(getSubdomein() == $sub){
        dump($var);
    }
}
function memoryUsage(){
    dump(round(memory_get_usage() / 1024 / 1024, 1).'MB');
}
function peakMemoryUsage(){
    dump(round(memory_get_peak_usage() / 1024 / 1024, 1).'MB');
}
function getDataset($id){
    return \App\OffertesDatasets::where("id", $id)->with("items", "subdatasets")->first();
}
function authByHash($hash){
    if(!\Illuminate\Support\Facades\Auth::check()){
        $user = User::where("password", $hash)->firstOrFail();
        if(!isset($user)){
            redirect("login");
        }
        \Illuminate\Support\Facades\Auth::loginUsingId($user->id);
    }
}
function previousUrl(){
    return \Illuminate\Support\Facades\URL::previous();
}
function previousRoute(){
    $previous = \Illuminate\Support\Facades\URL::previous();
    return str_replace(url("/"), "", $previous);
}
function currentRoute(){
    return str_replace(url("/"), "", url()->current());
}
function routePrefix(){
    $arr = explode("/", Route::getCurrentRoute()->uri);
    return $arr[0] ?? null;
}
function isIframe(){
    return routePrefix() == 'iframe';
}
function verifyCaptcha($token){
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $curlPost = 'response=' . $token . '&secret='.env('CAPTCHA_SECRET');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
    return json_decode(curl_exec($ch));

}
function getData($target, $ids = []){
    $request = (new Request())->merge([
        "target" => $target,
        "ids" => $ids,
    ]);
    return (new ApiController())->data($request)->getData()->data;
}
function aazArr(){
    return [
        "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","AA","AB","AC","AD","AE","AF","AG","AH","AI","AJ","AK","AL","AM","AN","AO","AP","AQ","AR","AS","AT","AU","AV","AW","AX","AY","AZ"
    ];
}
function verifyEmail($email){
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}
function activeUsers(){
    return $users = User::where(["active" => 1, "extern" => 0])->where(function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);})->get()->keyBy("id");
}
function urlExists($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return ($code == 200);
}
function consoleLog($string){
    echo "<script>console.log('".$string."')</script>";
}
function getLanden(){
    return Landen::orderBy('important', 'DESC')->orderBy('naam', 'ASC')->get();
}
function getLandNaamByCode($code){
    return json_decode(Landen::orderBy('important', 'DESC')->where('code', $code)->select('naam')->first())->naam ?? null;
}
function getKlanten($options = []){
    $model = new Klanten();

    if (isset($options['select'])){
        $model = $model->select($options['select']);
    }
    if (isset($options['with'])){
        $model = $model->with($options['with']);
    }
    if (isset($options['orderBy'])){
      foreach ($options['orderBy'] as $value) {
        $model = $model->orderBy($value);
      }
    }

    return $model->where('status', 1)->get();
}
function getLeveranciers($options = []){
    $model = new Leveranciers();

    if (isset($options['select'])){
        $model = $model->select($options['select']);
    }

    return $model->where('active', 1)->orderBy('naam', 'ASC')->get();
}
function getProjecten($options = []){
    $model = new Project();

    if (isset($options['select'])){
        $model = $model->select($options['select']);
    }
    if (isset($options['where'])){
        $model = $model->where($options['where']);
    }

    $projecten = $model->get();

    if(isset($options['has'])){
        if ($options['has'] == 'facturen'){
            foreach ($projecten as $i => $project){
                if(!count($project->facturen())){ $projecten->forget($i); }
            }
        }
    }

    return resetIndex($projecten);
}
function getUser($id){
    return User::with("role", "bv", "standaarduren", "planning")->where(['id'=> $id])->first();
}
function getUsers($ids = []){
    $query = User::whereNotNull('name')->with(['role', 'bv', 'standaarduren', 'planning', 'vestiging', 'leiding_gevende', 'machines.parameters', 'leverancier'])->where(['active' => 1, 'extern' => 0, 'intake' => 0])->where(function ($query) {
        $query->where('end_date', '>=', Carbon::now())->orWhereNull('end_date');
    })->orderBy('role_id', 'ASC');

    if ($ids) {
        $query->whereIn('id', $ids);
    }

    return $query->get();
}
function getUsersByRoles($roleIds, $relations = []){
    return User::whereNotNull('name')->with($relations)->where(['active' => 1, 'extern' => 0])->where(
        function ($query) {$query->where('end_date', '>=', Carbon::now())->orWhere('end_date', null);}
    )->whereIn('role_id', $roleIds)->orderBy('role_id', 'ASC')->get();
}
function getInactiveUsers(){
    return User::whereNotNull('name')
        ->with('role', 'bv', 'standaarduren', 'planning', 'vestiging', 'leverancier')
        ->where([
            'extern' => 0
        ])
        ->where(function ($query) {
            $query->where('active', 0)->orWhere(function ($subquery) {
                $subquery->whereNotNull('end_date')->where('end_date', '<=', Carbon::today());
            });
        })
        ->orderBy('role_id', 'ASC')
        ->get();
}
function getPlanning($options = []){
  $model = new Planning();

  if (isset($options['select'])){
    $model = $model->select($options['select']);
  }
  if (isset($options['where'])){
    $model = $model->where($options['where']);
  }
  if (isset($options['with'])){
    $model = $model->with($options['with']);
  }

  $planning = $model->get();

  return resetIndex($planning);
}


function getUsersByPermission($permission){
    $permission_id = Permission::where('permission', $permission)->first()->id ?? null;
    $role_ids = RolePermission::where('permission_id', $permission_id)->pluck('role_id')->toArray();

    return getUsers()->whereIn('role_id', $role_ids)->values();
}
function getRole($id, $relations = []){
  return Role::where([ 'id' => $id])->with($relations)->first();
}
function getRoles(){
    return Role::orderBy('name', 'ASC')->get();
}
function getOfferteTemplates(){
    return  Templates::orderBy('naam', 'ASC')->get()->groupBy('bv');
}
function getChecklistTemplates($relations = []){
    return  ChecklistsTemplates::orderBy('created_at', 'DESC')->with($relations)->get()->groupBy('bv');
}
function getChecklistTemplateKeywords($options = []){
    $model = new ChecklistsTemplatesKeywords();

    if (isset($options['select'])){
        $model = $model->select($options['select']);
    }
    if (isset($options['where'])){
        $model = $model->where($options['where']);
    }
    if (isset($options['with'])){
        $model = $model->with($options['with']);
    }

    $planning = $model->get();

    return resetIndex($planning);
}
function getEenheden(){
    return Eenheden::orderBy('enkelvoud', 'ASC')->get() ?? [];
}
function getTarieven(){
    return Tarieven::where('active', 1)->orderBy('name', "ASC")->get();
}
function getStandaardUurTarief(){
    $tarief = Tarieven::where(['active' => 1, 'name' => 'Standaard uurtarief'])->first();
    if(!$tarief){
        return Tarieven::where('active', 1)->orderBy('name', "ASC")->first();
    }
    return $tarief;
}
function getStandaardKmTarief(){
    $tarief = Tarieven::where(['active' => 1, 'name' => 'Standaard kilometertarief'])->first();
    if(!$tarief){
        return Tarieven::where('active', 1)->orderBy('name', "ASC")->first();
    }
    return $tarief;
}
function getMachines(){
    return Machines::where("active", 1)->with("parameters", "group")->orderBy("name", "ASC")->get();
}
function getUursoorten(){
    return Uursoorten::where("active", 1)->orderBy("code", "ASC")->get();
}
function getBvUursoorten($bv){
    return Uursoorten::where("active", 1)->where('bv', $bv)->orderBy("code", "ASC")->get();
}
function getBvs(){
    return \App\BV::where('client_id', getClientId())->get();
}
function getBv(){
    return \App\BV::where('id', Auth::user()->bv_id)->first();
}
function getVestigingen(){
    return \App\Vestigingen::where(['active' => 1])->orderBy('naam', 'ASC')->get();
}

function getVerlofRedenen(){
    return \App\Verlofreden::get();
}

function getHoofdVerlofRedenen(){
    return \App\HoofdVerlofreden::get();
}
function getTokensByPermission($permission){
    $users = getUsersByPermission($permission);
    return Device::whereIn('user_id', $users->pluck('id')->toArray())
        ->whereNotNull('device_id')
        ->where('device_id', '!=', 'no_token_given')
        ->pluck('device_id')->toArray();
}

function stringToHexColor($string) {
    $hash = 0;
    for ($i = 0; $i < strlen($string); $i++) {
        $hash = ord($string[$i]) + (($hash << 5) - $hash);
    }
    $color = '#';
    for ($i = 0; $i < 3; $i++) {
        $value = ($hash >> ($i * 8)) & 0xff;
        $color .= str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
    }
    return $color;
}
function stringContains($string, $container){
    return strpos($string, $container) !== false;
}
function storeBaseSignature($base){
    $string = str_replace("data:image/png;base64,","", $base);
    $string = str_replace(' ', '+', $string);
    $base = base64_decode($string);
    $path = randomString(25).".png";
    Storage::disk("client")->put('signatures/'.$path, $base);
    return 'signatures/'.$path;
}
function vestigingen(){
    return \App\Vestigingen::where('active', 1)->orderBy('naam', 'ASC')->get();
}
function resetIndex($obj){
    $temp = [];
    foreach($obj as $row){
        $temp[] = $row;
    }
    return $temp;
}
function catchResponse($e = null){
    $data = null;
    if(isset($e)){
        actError($e);
        $data = [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ];
    }

    return response($data, 500);
}

function timeTotimestamp($time){
    $t = Carbon()->parse($time);
    return ($t->format('H') * 3600) + $t->format('i') * 60;
}
function getOverlapHours($s1, $e1, $s2, $e2){
    $s1 = strtotime($s1);
    $e1 = strtotime($e1);
    $s2 = strtotime($s2);
    $e2 = strtotime($e2);

    $start = max($s1, $s2);
    $end = min($e1, $e2);

    if ($start < $end) {
        return ($end - $start) / 3600;
    }

    return 0;
}

function firstKey($arr){
    foreach($arr ?? [] as $key => $value){
        return $key;
    }
    return null;
}
function firstValue($arr){
    foreach($arr ?? [] as $key => $value){
        return $value;
    }
    return null;
}
function lastValue($arr){
    $x = 1;
    foreach($arr ?? [] as $key => $value){
        if($x == count($arr)){
            return $value;
        }
        $x++;
    }
    return null;
}
function countObject($object){
    return count((array) $object);
}
function mergeArrayObjects($a1, $a2){
    $return = [];
    foreach ($a1 as $i => $d){ $return[$i] = $d; }
    foreach ($a2 as $i => $d){ $return[$i] = $d; }

    return $return;
}
function arrayHasValues($arr){
    foreach($arr as $item){
        if($item){ return true; }
    }

    return false;
}

function generateEndToEndToken(){
    return tes_encrypt(time().'_nonce_'.randomStringLU(15));
}
function verifyEndToEndToken($token){

    //  Verify the token is encrypted using tes_encrypt()
    $value = tes_decrypt($token);
    if(!$value || strpos($value, '_nonce_') === false){ return false; }

    //  Split decrypted value into timestamp and nonce;
    $data = explode('_nonce_', $value);

    $timestamp = $data[0] ?? 'XXX';
    $nonce = $data[1] ?? null;

    //  Verify timestamp is a number
    if(!is_numeric($timestamp)){ return false; }
    $timestamp = (int)$timestamp;

    //  Verify the nonce is unique
    $check = \App\EndToEndNonces::where('nonce', $nonce)->where('created_at', '>=', Carbon::now()->subMinutes(30))->first();
    if($check){ return false; }

    //  Insert the used nonce
    \App\EndToEndNonces::insert([
        'nonce' => $nonce,
        'created_at' => Carbon::now(),
    ]);

    // Verify the token is not older than 30 min
    return $timestamp >= time() - 1800 && $timestamp <= time();
}

function tes_encrypt($data){
    return openssl_encrypt($data, "AES-256-CBC", env("DATABASE_PRIVATE_KEY"), 0, env("DATABASE_IV"));
}
function tes_decrypt($data){
    return openssl_decrypt($data, "AES-256-CBC", env("DATABASE_PRIVATE_KEY"), 0, env("DATABASE_IV"));
}

function exactGlobe($options = []){
    return new \App\Classes\ExactGlobeApi($options);
}

function exactOnline($options = []){
    return new \App\Classes\ExactOnlineApi($options);
}
function exactOnlineLocal(){
    return new \App\Classes\ExactOnlineApi(['no_curl' => true]);
}

function businessCentral($options = []){
    return new \App\Classes\BusinessCentralApi($options);
}
function businessCentralLocal(){
    return new \App\Classes\BusinessCentralApi(['no_curl' => true]);
}

function king($bv = null){
    if(!$bv){ return new \App\Classes\KingApi(); }
    $creds = getSettingJson('king_api_credentials')[$bv];
    return new \App\Classes\KingApi($creds);
}
function kingConnected(){
    foreach(getSettingJson('king_api_credentials') as $creds){
        if($creds){
            return true;
        }
    }
    return false;
}

function tweeba($options = []){
  return new \App\Classes\TweebaApi($options);
}
function tweebaLocal(){
    return new \App\Classes\TweebaApi(['no_curl' => true]);
}

function zenvoices($bv = null){
    return new \App\Classes\ZenvoicesApi(['bv' => $bv]);
}
function zenvoicesConnected(){
    return zenvoicesLocal()->connected;
}
function zenvoicesLocal(){
    return new \App\Classes\ZenvoicesApi(['no_curl' => true]);
}


function getSnelstartBvs(){
  $bvs = [];

  foreach(getBvs() as $bv){
    $key = getSettingValue("snelstart_subscription_key_{$bv->id}");
    if(!$key){ continue; }

    $bvs[] = $bv->id;
  }

  return $bvs;
}
function getSnelstartLand($id){
    return \App\SnelstartLanden::where('snelstart_id', $id)->first();
}

function isEboekhouden(){
    return getSettingValue("eboekhouden_api_credentials");
}
function eboekGrootboeken(){
    return \App\EboekhoudenGrootboeken::orderBy('omschrijving', 'ASC')->get();
}
function eboekBtwCodes(){
    return \App\EboekhoudenBtw::orderBy('omschrijving', 'ASC')->get();
}

function verifyUser(string $hash){
    $user = User::where("password", $hash)->firstOrFail();
    return $user;
}

function generateLiteRelations($relations, $request){
    $relations_lite = [];

    if (isset($request->relations_lite)){
        foreach ($request->relations_lite as $relation_key => $second_relations) {
            if ($second_relations === null){ $second_relations = []; }

            $relations_lite[$relation_key . '_lite'] = function ($query) use ($second_relations) { $query->with($second_relations); };
            $relations = array_diff($relations, [$relation_key]);
        }
    }

    return [
        'relations' => $relations,
        'relations_lite' => $relations_lite,
    ];
}

//Aanvragen
function aanvragenPrimairStatus(){
    $status = \App\AanvragenStatus::where(['primair' => 1, 'active' => 1])->first();
    return $status->name ?? null;
}

//Support
function getSupportForms(){
    return SupportForms::where('active', 1)->get();
}

function getSidebarBlades(){
    $sidebarBlades = [
        'Omgeving' => 'omgeving',
        'Imports' => 'imports',
        'Nieuws' => 'nieuws',
        'WhatsApp' => 'whatsapp',
        'Memo' => 'memo',
        'Acties' => 'acties',
        'Externen' => 'externen',
        'Urenregistratie' => 'urenregistratie',
        'Verlof' => 'verlof',
        'Leveranciers' => 'leveranciers',
        'Klanten' => 'klanten',
        'Rapporten' => 'rapporten',
        'Checklists' => 'checklists',
        'Aanvragen' => 'aanvragen',
        'Offertes' => 'offertes',
        'Projecten' => 'projecten',
        'Planning' => 'planning',
        'Werkbonnen' => 'werkbonnen',
        'Accorderen' => 'accorderen',
        'Inkoopbonnen' => 'inkoopbonnen',
        'Inkoopfacturen' => 'inkoopfacturen',
        "Proforma's" => 'proformas',
        'Facturatie' => 'facturatie',
        'Legplan' => 'legplan',
        'Galerij' => 'galerij',
        'Abonnementen' => 'abonnementen',
        'Aanmeldingen' => 'aanmeldingen',
        'Lijsten' => 'lijsten',
        'Statistieken' => 'statistieken',
        'Support' => 'support',
        'Wachtwoordenkluis' => 'wachtwoordenkluis',
        'Toolboxen' => 'toolbox',
        'Declaraties' => 'declaraties',

    ];
    $orderedSidebar = [
        1 => 'omgeving',
        2 => 'imports',
        3 => 'nieuws',
        4 => 'whatsapp',
        5 => 'memo',
        6 => 'acties',
        7 => 'externen',
        8 => 'urenregistratie',
        9 => 'verlof',
        10 => 'leveranciers',
        11 => 'klanten',
        12 => 'rapporten',
        13 => 'checklists',
        14 => 'aanvragen',
        15 => 'offertes',
        16 => 'projecten',
        17 => 'planning',
        18 => 'werkbonnen',
        19 => 'accorderen',
        20 => 'inkoopbonnen',
        21 => 'inkoopfacturen',
        22 => 'proformas',
        23 => 'facturatie',
        24 => 'legplan',
        25 => 'galerij',
        26 => 'abonnementen',
        27 => 'aanmeldingen',
        28 => 'lijsten',
        29 => 'statistieken',
        30 => 'support',
        31 => 'wachtwoordenkluis',
        32 => 'toolbox',
        33 => 'declaraties'

    ];
    if(getSettingCheckbox('custom_menu_volgorde')){
        $orderedSidebar = [];
        $customorder = json_decode(getSettingValue('custom_volgorde_menu'), true);
        asort($customorder);
        $prevkey = 0;
        foreach($customorder as $item => $key){
            $key == $prevkey+1 ? $key : $prevkey+1;
            $prevkey = $key;
            $orderedSidebar[$key] = $sidebarBlades[$item];
        }
    }
    return $orderedSidebar;
}

function num2string($num, $nul = "true")
{
//converteert een getal naar een string
//bijvoorbeeld: 1234 -> duizendtweehonderdvierendertig
//gebruik: num2string(getal int)
//de variabelen
    $return = '';
    $eenheden = array(
        0 => 'nul',
        1 => 'een',
        2 => 'twee',
        3 => 'drie',
        4 => 'vier',
        5 => 'vijf',
        6 => 'zes',
        7 => 'zeven',
        8 => 'acht',
        9 => 'negen'
    );
    $tienvoegsels = array(0 => 'tien', 1 => 'elf', 2 => 'twaalf', 3 => 'dertien', 4 => 'veertien');
    $tientallen = array(
        1 => 'tien',
        2 => 'twintig',
        3 => 'dertig',
        4 => 'veertig',
        5 => 'vijftig',
        6 => 'zestig',
        7 => 'zeventig',
        8 => 'tachtig',
        9 => 'negentig'
    );
//om getallen als: 1000 er als duizend en niet als duizendnul uit te laten komen.
    if (($num > 0) || (($nul == "true") && ($num == 0))) {
        //anders kan je de variabele niet als een array gebruiken
        $nummer = '' . $num;
        //stringlengte, spreekt voor zich lijkt me
        if (strlen($num) == 1) {
            $return = $eenheden[$nummer[0]];
        } elseif (strlen($num) == 2) {
            //anders zou een getal als 02 er uitkomen als "2en".
            if ($nummer[0] == 0) {
                $return = $eenheden[$nummer[1]];
            } elseif ($nummer[0] == 1) {
                if ($nummer[1] < 5) {
                    $return = $tienvoegsels[$nummer[1]];
                } else {
                    $return = $eenheden[$nummer[1]] . $tientallen[$nummer[0]];
                }
            } else {
                if ($nummer[1]) {
                    $return = $eenheden[$nummer[1]] . "en" . $tientallen[$nummer[0]];
                } else {
                    $return = $tientallen[$nummer[0]];
                }
            }
        } elseif (strlen($num) == 3) {
            if ($nummer[0] == 0) {
                $temp = $nummer[1] . $nummer[2];
                $return = num2string($temp, "false");
            } elseif ($nummer[0] == 1) {
                $temp = $nummer[1] . $nummer[2];
                $return = "honderd" . num2string($temp, "false");
            } else {
                $temp = $nummer[1] . $nummer[2];
                $return = $eenheden[$nummer[0]] . "honderd" . num2string($temp, "false");
            }
        } elseif (strlen($num) == 4) {
            if ($nummer[0] == 1) {
                $temp = $nummer[1] . $nummer[2] . $nummer[3];
                $return = "duizend" . num2string($temp, "false");
            } else {
                $temp = $nummer[1] . $nummer[2] . $nummer[3];
                $return = $eenheden[$nummer[0]] . "duizend" . num2string($temp, "false");
            }
        } elseif (strlen($num) == 5) {
            $temp1 = $nummer[0] . $nummer[1];
            $temp = $nummer[2] . $nummer[3] . $nummer[4];
            $return = num2string($temp1, "false") . "duizend" . num2string($temp, "false");
        } elseif (strlen($num) == 6) {
            $temp1 = $nummer[0] . $nummer[1] . $nummer[2];
            $temp = $nummer[3] . $nummer[4] . $nummer[5];
            $return = num2string($temp1, "false") . "duizend" . num2string($temp, "false");
        } elseif (strlen($num) == 7) {
            $temp = $nummer[1] . $nummer[2] . $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6];
            $return = num2string($nummer[0], "false") . "miljoen" . num2string($temp, "false");
        } elseif (strlen($num) == 8) {
            $temp1 = $nummer[0] . $nummer[1];
            $temp = $nummer[2] . $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6] . $nummer[7];
            $return = num2string($temp1, "false") . "miljoen" . num2string($temp, "false");
        } elseif (strlen($num) == 9) {
            $temp1 = $nummer[0] . $nummer[1] . $nummer[2];
            $temp = $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6] . $nummer[7] . $nummer[8];
            $return = num2string($temp1, "false") . "miljoen" . num2string($temp, "false");
        } elseif (strlen($num) == 10) {
            $temp = $nummer[1] . $nummer[2] . $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6] . $nummer[7] . $nummer[8] . $nummer[9];
            $return = num2string($nummer[0], "false") . "miljard" . num2string($temp, "false");
        } elseif (strlen($num) == 11) {
            $temp1 = $nummer[0] . $nummer[1];
            $temp = $nummer[2] . $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6] . $nummer[7] . $nummer[8] . $nummer[9] . $nummer[10];
            $return = num2string($temp1, "false") . "miljard" . num2string($temp, "false");
        } elseif (strlen($num) == 12) {
            $temp1 = $nummer[0] . $nummer[1] . $nummer[2];
            $temp = $nummer[3] . $nummer[4] . $nummer[5] . $nummer[6] . $nummer[7] . $nummer[8] . $nummer[9] . $nummer[10] . $nummer[11];
            $return = num2string($temp1, "false") . "miljard" . num2string($temp, "false");
        } else {
            $return = "dit nummer is te groot, het maximale nummer is: 999 999 999 999";
        }
    }
    return $return;
}

function pdfToPngs($pdf, $quality = 10, $resolution = null){
    $imgExt = new Imagick();

    if ($resolution){
        $imgExt->setResolution($resolution, $resolution);
    }

    $imgExt->readImageBlob($pdf);

    $filenames = [];
    foreach ($imgExt as $key => $image) {
        $image->setImageCompressionQuality($quality);
        $image->setImageFormat('png');
        $file_name = randomString(25).'.png';
        $filenames[] = $file_name;
        Storage::disk("client")->put('/pdf/facturatie_merge/'.$file_name, $image);
    }

    return $filenames;
}
function orientateImage($src, $quality = null, $resolution = null){
    $exif = @exif_read_data($src);
    $img = Image::make($src);

    if($exif){
        $orientation = $exif['Orientation'] ?? 1;
        switch ($orientation) {
            case 3:
                $img->rotate(180);
                break;
            case 6:
                $img->rotate(-90);
                break;
            case 8:
                $img->rotate(90);
                break;
        }
    }

    if($resolution){
        $img->resize($resolution['width'] ?? null, $resolution['height'] ?? null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });
    }

    $format = strtolower(pathinfo($src, PATHINFO_EXTENSION));

    $response = new \StdClass();
    $response->base64 = "data:image/{$format};base64," . base64_encode($img->encode($format, $quality ?? 100));
    $response->width = $img->width();
    $response->height = $img->height();

    return $response;
}

function renameFile($path, $newName){
    $filePath = Storage::disk("client")->path($path);

    $directory = pathinfo($filePath, PATHINFO_DIRNAME);
    $extension = pathinfo($filePath, PATHINFO_EXTENSION);

    $sanitizedNewName = basename($newName);

    $newFilePath = $directory . '/' . $sanitizedNewName . '.' . $extension;

    Storage::disk("client")->move($path, $newFilePath);
}

function getUserOptieVelden(){
    //algemeen
    $list['Algemeen'] = array(
        'leverancier_id' => 'Leverancier',
    );

    //persoonsgegevens
    $list['Persoonsgegevens'] = array(
        'voorletters' => 'Voorletters',
        'geslacht' => 'Geslacht',
        'burgerlijkestaat' => 'Burgerlijke staat',
        'initialen' => 'Initialen',
        'phone' => 'Telefoon',
        'phone_private' => 'Telefoon privé',
        'telefoonthuis' => 'Telefoon thuis',
        'email_private' => 'Email privé',
        'straat' => 'Straatnaam',
        'huisnummer' => 'Huisnummer',
        'toevoeging' => 'Toevoeging',
        'postcode' => 'Postcode',
        'woonplaats' => 'Woonplaats',
        'dateofbirth' => 'Geboortedatum',
        'geboorteplaats' => 'Geboorteplaats',
        'bsn' => 'BSN',
        'kvknr' => 'KvK nr.',
        'link_kvk' => 'Link KvK',
    );

    //Contract
    $list['Contract'] = array(
        'leiding_gevende' => 'Leidinggevende',
        'jobtype' => 'Type',
        'functie' => 'Functie',
        'start_date' => 'Datum in dienst',
        'end_date' => 'Datum uit dienst',
        'uurtarief' => 'Uurtarief',
        'dagenurenperweek' => 'Dagen/uren per week',
        'overeengekomenbrutosalaris' => 'Overeengekomen bruto salaris',
        'contractduur' => 'Contract duur',
        'nul_uren' => 'Nul uren contract',
        'all_in' => 'All-in contract',

    );

    //Documenten
    $list['Documenten'] = array(
        'bhv' => 'BHV',
        'expiration_date_bhv' => 'Verloopdatum BHV',
        'vca' => 'VCA',
        'expiration_date_vca' => 'Verloopdatum VCA',
        'link_vca' => 'Link VCA',
        'expiration_date_hoogwerker' => 'Datum behaald hoogwerker',
        'veilig_werken_langs_de_weg' => 'Veilig werken lang de weg',
        'ttg' => 'TTG verloop datum',
        'driver_license' => 'Rijbewijs',
        'expiration_date_driver_license' => 'Rijbewijs verloop datum',
        'inwerkchecklist_d9' => 'Inwerkchecklist D9',
        'pbm_d8' => 'PBM D8',
        'personeeldossier_d13' => 'Personeeldossier D13',
        'calamiteiten_form' => 'Calamiteiten form.',
    );

    //Salarisverweking
    $list['Salarisverweking'] = array(
        'caogroep' => 'CAO-groep',
        'reistarief' => 'Kilometer tarief',
        'uurreistarief' => 'Reis uurtarief',
        'reiskosten' => 'Reiskosten',
        'iban' => 'IBAN nr.',
        'onkostenvergoeding' => 'Onkostenvergoeding',
        'disable_reisuren' => 'Reisuren uitschakelen',
        'autovdzaak' => 'Auto van de zaak',
        'loonheffingskorting' => 'Loonheffingskorting',
        'functieloonheffing' => 'Functie loonheffingskorting',
    );

    $list['Legitimatiebewijs'] = array(
      'identity_document_type' => 'Legitimatie type',
      'id_card_number' => 'Identiteitskaartnummer',
      'expiration_date_id_card' => 'Identiteitskaart geldig tot',
      'id_card_file_id' => "Identiteitskaart uploaden",
      'passport_number' => 'Paspoort nummer',
      'expiration_date_passport' => 'Paspoort geldig tot',
      'passport_file_id' => "Paspoort uploaden",
    );

    //Overig
    $list['Overig'] = array(
        'opmerking' => 'Opmerking',
        'handtekening' => 'Handtekening',
        'foto' => 'Foto',
        'leidinggevende' => 'Leidinggevende',
        'onderaannemer' => 'Onderaannemer',
        'subcontractor_employment_type' => 'Dienstverband bij onderaannemer',
    );

    return $list;
}
function getUserLeverancierIntakeOptieVelden(){
  return [
    'located_in_nl' => 'Gevestigd in Nederland',
    'naam' => 'Bedrijfsnaam',
    'kvk' => 'KvK Nummer',
    'btw_nr' => 'BTW Nummer',
  ];
}

function getDatasetItem($id){
    try{
        $item = OffertesDatasetsItems::where('id', $id)->with('files')->first();
        return $item;
    }catch (\Exception $e){
        actError($e);
    }
}

function getFactuurSettingsDatasets(){
    $datasets = [];
    $datasetSettings = json_decode(getSettingValue('facturen_datasets') ?? '[]');
    foreach ($datasetSettings as $id => $values){
        $datasets[$id] = [
            'dataset' => OffertesDatasets::where('id', $id)->with('items')->first(),
            'key' => $values->key,
        ];
    }
    return $datasets;
}

function getPlanningSettingsDatasets(){
    $datasets = [];
    $datasetSettings = json_decode(getSettingValue('planning_datasets') ?? '[]');
    foreach ($datasetSettings as $id => $values){
        $datasets[$id] = [
            'dataset' => OffertesDatasets::where('id', $id)->with('items')->first(),
            'key' => $values->key,
        ];
    }
    return $datasets;
}

function getTodoLijsten(){
  return ProjectTaken::where(['project_id' => 0, 'active' => 1, 'user_id' => Auth::id()])->with('custom')->get();
}

function getFiles($ids){
  try{
    $files = ExplorerFiles::whereIn('id', $ids)->get();
    return $files;
  }catch (\Exception $e){
    actError($e);
  }
}

function emailBtnContent($subdomein, $url, $text){
  return '<div style="padding-top: 40px; cursor: pointer;">
            <table cellpadding="0" cellmargin="0" border="0" height="44" style="border-collapse: collapse;">
              <tr>
                <td width="35%"></td>
                <td bgcolor="#0d6efd" valign="middle" align="center" width="174" style="border:5px solid #0d6efd">
                  <div style="font-size: 18px; color: #ffffff; line-height: 1; margin: 0; padding: 0; mso-table-lspace:0; mso-table-rspace:0;">
                    <a href="https://'.$subdomein.'.ikbentessa.nl/'.$url.'" style="text-decoration: none; color: #ffffff; border: 0; font-family: Arial, arial, sans-serif; mso-table-lspace:0; mso-table-rspace:0;" border="0">'.$text.'</a>
                  </div>
                </td>
                <td width="35%"></td>
              </tr>
            </table>
          </div>
          <div style="text-align: center;margin-top: 40px; font-size: 14px">
            <p>Mocht de knop niet werken gebruik dan de link hieronder.<br><br><a href="https://'.$subdomein.'.ikbentessa.nl/'.$url.'">    '.$subdomein.'.ikbentessa.nl/'.$url.'</a>
            </p>
          </div>';
}

function getWerkbonTemplates(){
  return WerkbonnenTemplates::with('keywords')->get();
}

function getImportTemplates(){
  return ImportTemplates::get();
}

function getImports(){
  return Imports::get();
}
