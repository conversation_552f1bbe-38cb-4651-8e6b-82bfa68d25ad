<?php

namespace App;

use App\Mail\BlankMail;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class Checklists extends Model
{
    protected $table = 'checklists';

    function template(){
      return $this->hasOne(ChecklistsTemplates::class, 'id', 'template_id')->with('keywords');
    }
    function klant(){
      return $this->hasOne(Klanten::class, 'id', 'klant_id');
    }
    function klant_locatie(){
      return $this->hasOne(KlantenLocaties::class, 'id', 'klant_locatie_id');
    }
    function user(){
      return $this->hasOne(User::class, 'id', 'user_id')->with('role');
    }
    function project(){
      return $this->hasOne(Project::class, 'id', 'project_id');
    }
    function _bv(){
      return $this->hasOne(BV::class, 'id', 'bv');
    }
    function keywords(){
      return $this->hasMany(ChecklistsKeywords::class, 'checklist_id', 'id')->with('user', 'keyword_item', 'files');
    }
    function details(){
      return $this->hasMany(ChecklistsDetails::class, "checklist_id", "id")->with("values", "template");
    }
    function files(){
      return $this->hasMany(ChecklistsFiles::class, "checklist_id", "id");
    }


    public function setVervolgAfspraak(){
      try{
        $set = getSettingJson("checklist_verv_afspraken");

        if(!isset($set[$this->template_id]) || in_array(null, $set[$this->template_id])){return;}
        $set = $set[$this->template_id];

        $datum = $this->keywords->where('keyword', $set['date'])->first()->value;
        $begin = $this->keywords->where('keyword', $set['start'])->first()->value;
        $eind = $this->keywords->where('keyword', $set['end'])->first()->value;
        $bevestiging = $this->keywords->where('keyword', 'vervolgbevestiging')->first()->value;
        $reminder = $this->keywords->where('keyword', 'vervolgreminder')->first()->value;
        $email = $this->keywords->where('keyword', 'vervolgmail')->first()->value;

        $exsistingplan = Planning::where('checklist_id', $this->id)->first();
        if($exsistingplan){
          $exsistingplan->datum = $datum;
          $exsistingplan->begin = $begin;
          $exsistingplan->eind = $eind;
          $exsistingplan->remindermail = $email;
          $exsistingplan->reminder = $reminder == "ja" ? 1 : 0;
          $exsistingplan->save();
          return;
        }

        Planning::insert([
          "klant_id" => $this->klant_id,
          "user_id" => $this->user_id,
          "datum" => $datum,
          "begin" => $begin,
          "eind" => $eind,
          "type" => "checklist",
          "checklist_id" => $this->id,
          "reminder" => $reminder == "ja" ? 1 : 0,
          "remindermail" => $email,
        ]);

        if($bevestiging == "ja"){
          $content = "Beste " . $this->klant->contactpersoon_voornaam. " " . $this->klant->contactpersoon_achternaam . ",<br><br>";
          $content .= "Bij deze de afspraak bevestiging voor " . $this->vervolgAfspraakDescription() . ".<br><br>";
          $content .= "Datum: " . Carbon::parse($datum)->format('d-m-Y') . "<br>";
          $content .= "Tijd: " . $begin . " - " . $eind . "<br><br>";
          $content .= "Met vriendelijke groet,<br>";
          $content .= $this->user->name . " " . $this->user->lastname . "<br>";
          $content .= $this->_bv->name . "<br>";

          Mail::to($email)->send(new BlankMail($this->_bv, getSettingValue("planning_email", '<EMAIL>'), $this->_bv->name, "Afspraak bevestiging", $content));
        }
      }catch(\Exception $e){
        actError($e);
      }
    }

    public function vervolgAfspraakDescription(){
      $set = getSettingJson("checklist_verv_afspraken");
      if(isset($set[$this->template_id])){
        $set = $set[$this->template_id]['description'];
        return ($this->keywords->where('keyword', $set)->first()->value ?? "Vervolgafspraak");
      }
      return "Checklist: ".$this->checklistnummer;
    }

    public function _locatie(){
    $adres = new \stdClass();
    $adres->straat = null;
    $adres->huisnummer = null;
    $adres->toevoeging = null;
    $adres->postcode = null;
    $adres->plaats = null;
    $adres->land = null;
    $adres->type = null;
    $adres->empty = true;

    $klant = Klanten::where('id', $this->klant_id)->first();
    $klantLocatie = KlantenLocaties::where(['id' => $this->locatie_id])->first();
    if ($klantLocatie){
      $adres->type = 'Locatie';
      $adres->straat = $klantLocatie->straat;
      $adres->huisnummer = $klantLocatie->huisnummer;
      $adres->toevoeging = $klantLocatie->toevoeging;
      $adres->postcode = $klantLocatie->postcode;
      $adres->plaats = $klantLocatie->plaats;
      $adres->land = $klantLocatie->land;
    }
    elseif ($this->locatie_id == 'postadres'){
      $adres->type = 'Postadres';
      $adres->straat = $klant->postadres_straat ?? null;
      $adres->huisnummer = $klant->postadres_huisnummer ?? null;
      $adres->toevoeging = $klant->postadres_toevoeging ?? null;
      $adres->postcode = $klant->postadres_postcode ?? null;
      $adres->plaats = $klant->postadres_plaats ?? null;
      $adres->land = $klant->postadres_land ?? null;
    }
    else{
      $adres->type = 'Bezoekadres';
      $adres->straat = $klant->straat ?? null;
      $adres->huisnummer = $klant->huisnummer ?? null;
      $adres->toevoeging = $klant->toevoeging ?? null;
      $adres->postcode = $klant->postcode ?? null;
      $adres->plaats = $klant->plaats ?? null;
      $adres->land = $klant->land ?? null;
    }

    if($adres->straat || $adres->huisnummer || $adres->postcode || $adres->plaats){ $adres->empty = false; }

    return $adres;
  }

    public function finnishProjectFlow(){
      $project = Project::where('id', $this->project_id)->with('checklists')->first();
      if(!$project){ return; }

      foreach($project->checklists as $checklist){
        $checklist->active = 0;
        $checklist->save();
      }

      $project->status = 'Afgerond';
      $project->active = 0;
      $project->completion_date = Carbon::now();
      $project->save();

      if (!getSettingValue('checklist_custom_project_flow_mail')){return;}

      $username = Auth::user()->fullName();
      $adres = $project->projectAdres()->adres;

      $content = "Project $project->projectnr is afgerond door $username<br><br>";
      $content .= "<table style='border-collapse: collapse; width: 100%;'>
        <tr>
          <td style='border: 1px solid #dee2e6; padding: 5px;'>Projectnaam</td>
          <td style='border: 1px solid #dee2e6; padding: 5px;'>$project->projectnaam</td>
        </tr>
        <tr>
          <td style='border: 1px solid #dee2e6; padding: 5px;'>Projectadres</td>
          <td style='border: 1px solid #dee2e6; padding: 5px;'>". $adres ."</td>
        </tr>
        <tr>
          <td style='border: 1px solid #dee2e6; padding: 5px;'>Samenvatting</td>
          <td style='border: 1px solid #dee2e6; padding: 5px;'><a href=".url('checklists/pdf/project/'.$project->id).">Klik hier</a></td>
        </tr>
      ";

      $content .= "</table>";

        $emails = array_map('trim', explode(',', getSettingValue('checklist_custom_project_flow_mail')));

        Mail::to($emails)->send(new BlankMail(
            $this->_bv,
            '<EMAIL>',
            $this->_bv->name,
            "Project $project->projectnr is afgerond",
            $content,
            []
        ));
    }

    //Static functions
    public static function templates(){
        return ChecklistsTemplates::where('active',true)->orderBy('name', "ASC")->get();
    }

}
