<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel {
  /**
   * The Artisan commands provided by your application.
   *
   * @var array
   */
  protected $commands = [
    //
  ];

  /**
   * Define the application's command schedule.
   *
   * @param \Illuminate\Console\Scheduling\Schedule $schedule
   * @return void
   */
  protected function schedule(Schedule $schedule) {
      $schedule->command('WhatsAppSchedule:send')->everyFiveMinutes();

	  $schedule->command('business-central:sync')->cron('*/3 * * * *');
	  $schedule->command('business-central:syncbetaaldefacturen')->dailyAt('02:30');

	  $schedule->command('exact-sync:facturen')->cron('*/2 * * * *');
    $schedule->command('exact-sync:klanten')->cron('*/2 * * * *');
	  $schedule->command('exact-sync:projecten')->cron('*/2 * * * *');
//    $schedule->command('exact-sync:leveranciers')->dailyAt('01:05');
//    $schedule->command('exact-sync:inkoopfacturen')->dailyAt('01:20');

    $schedule->command('ClearPDFs:Offertes')->dailyAt('01:25');

    $schedule->command('urenregistratie:planningreminder')->everyFifteenMinutes();

    $schedule->command('abonnement:verlengen')->dailyAt('01:30');
    $schedule->command('abonnement:uitgave:reminder')->dailyAt('01:35');

    $schedule->command('push:send')->cron('55 18 * * 7');
    $schedule->command('autoinvul:send')->cron('1 5 * * 1');
    $schedule->command('birthday:send')->cron('00 11 * * *');
    $schedule->command('push:send')->cron('00 17 * * 5');
    $schedule->command('reminder:push')->cron('00 * * * *');
    $schedule->command('facturatie:reminder')->cron('00 10 * * *');
    $schedule->command('acties:push')->cron('0 * * * *');

    $schedule->command('users:deactivate')->dailyAt('00:01');

    // $schedule->command('wka:send')->hourly();
    $schedule->command('afrondentaken:update')->hourly();

    $schedule->command('data-check')->everyFiveMinutes();

		$schedule->command('execute:jobs')->everyFiveMinutes();

    $schedule->command('opvolgreminder:mail')->dailyAt('12:00');
    $schedule->command('opvolgstap:gereed')->dailyAt('00:01');

    $schedule->command('planning:reminder')->dailyAt('16:00');

    $schedule->command('zenvoice:commitmentSync')->hourly();


    $schedule->command('urenregistratie:standaardureninvullen')->dailyAt('00:01');

    $schedule->command('werkbon:reminder')->dailyAt('08:00');

    //Zenvoices
    $schedule->command('zenvoices:syncLeveranciers')->dailyAt('02:00');


      //Client specific cronjobs
    $schedule->command('ramudden:facturen')->dailyAt('05:00');

    $schedule->command('finishprofiles:inkoopbonAccorderenReminder')->dailyAt('12:00');



  }

  /**
   * Register the commands for the application.
   *
   * @return void
   */
  protected function commands() {
    $this->load(__DIR__ . '/Commands');

    require base_path('routes/console.php');
  }
}
