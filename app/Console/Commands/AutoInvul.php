<?php

namespace App\Console\Commands;

use App\ClientSettings;
use App\Feestdagen;
use DB;
use Config;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use App\User;
use App\Message;
use App\Project;
use App\News;
use App\UrenRegistratie;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewCorrection;
use App\Standaarduren;
use App\Classes\BaseCommand;

class AutoInvul extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autoinvul:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function executeCommand()
    {
        $exists = false;
        $clients = DB::connection('tessa')->table('clients')->where('push', 1)->get();
        $sql_settings = DB::connection('tessa')->table('databases')->get();

        Carbon::macro('range', function ($startDate, $endDate) {
          return new \DatePeriod($startDate, new \DateInterval('P1D'), $endDate);
        });

    foreach ($clients as $index => $client) {


      foreach($sql_settings as $db) {

        if($db->client_id == $client->id) {

          $settings = ClientSettings::where('client_id', $client->id)->get();
          foreach($settings as $index => $p) {
            $settings[$index]->permission = DB::connection('tessa')->table('settings')->where('id', $p->setting_id)->first();
          }

          Config::set('database.connections.mysql.host', $db->host);
          Config::set('database.connections.mysql.database', $db->database);
          Config::set('database.connections.mysql.username', $db->username);
          Config::set('database.connections.mysql.password', tes_decrypt($db->password));
          Config::set('database.connections.mysql.port', $db->port);
          Config::set('global.settings', $settings);

          DB::purge('mysql');
          $exists = true;

          $users = User::where('active', 1)->where("extern", 0)->get();
        }
      }

      if($exists) {
        foreach ($users as $user) {
          $dagen = '';

          $periode = CarbonPeriod::create(Carbon::now()->subDays(14), '1 day', Carbon::now()->subDays(7));
          foreach ($periode as $key => $date) {
            $row = UrenRegistratie::where('medewerker_id', $user->id)->where('datum', $date->format('Y-m-d'))->first();

            if(!$row) {
              $feestdag = Feestdagen::where('datum', $date->format('Y-m-d'))->first();
              $standaarduren = Standaarduren::where('medewerkers_id', $user->id)->where('dag', getWeekdagen($date->dayOfWeek))->first()->standaarduren ?? 0;

              $id = DB::table('urenregistratie')->insertGetId([
                'medewerker_id' => $user->id,
                'datum'         => $date,
                'projectnummer' => "-",
                'pauze'         => 0,
                'begintijd'     => null,
                'eindtijd'      => null,
                'gewerkte_uren' => $feestdag ? $standaarduren : 0,
                'reisuren'      => 0,
                'overnachting'  => null,

                'heenreis_huisproject'   => null,
                'terugreis_huisproject'  => null,
                'heenreis_woonwerk'      => null,
                'terugreis_woonwerk'     => null,

                'ladenlossen_begintijd'        => null,
                'ladenlossen_eindtijd'         => null,
                'ladenlossen_middag_begintijd' => null,
                'ladenlossen_middag_eindtijd'  => null,

                'bestuurder'      => null,
                'bijrijder'       => null,
                'kenteken_id'     => null,
                'verlof'          => $feestdag ? 1 : 0,
                'bijzonderverlof' => 0,
                'ziekteuren'      => 0,
                'feesturen'       => $feestdag ? $standaarduren : 0,
                'verlofreden_id'  => $feestdag ? 4 : null,
                'naca_id'         => null,
                'opmerkingen'     => null,

                'totaaluren100'      => 0,
                'totaal_overuren150' => 0,
                'totaal_overuren165' => 0,
                'totaal_overuren200' => 0,
                'tijdvoortijd' => 0,
                'totaaldaguren100' => 0
              ]);
            }
          }
        }
      }
    }
  }
}
