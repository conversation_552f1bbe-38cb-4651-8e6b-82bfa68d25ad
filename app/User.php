<?php

namespace App;

use App\Mail\BlankMail;
use Config;
use Auth;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Cache;

use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Throwable;

class User extends Authenticatable {
  use Notifiable;

  protected $fillable = ['name', 'active', 'email', 'password'];
  protected $hidden = ['password', 'remember_token'];

  function vestiging(){
    return $this->hasOne(Vestigingen::class, "id", "vestiging_id");
  }
  function devices(){
    return $this->hasMany(Device::class, "user_id", "id");
  }
  function role() {
    return $this->belongsTo('App\Role');
  }
  function leiding_gevende() {
    return $this->hasOne(User::class, 'id', 'leidinggevende');
  }
  function leverancier() {
    return $this->hasOne(Leveranciers::class, 'id', 'leverancier_id');
  }
  function bv() {
    return $this->belongsTo('App\BV');
  }
  function bvs() {
    return Cache::remember(Config::get('filesystems.disks.client.client_id') .'_user_'.$this->id.'_bvs', 360, function () {
      $bvs = \DB::connection('tessa')->table('bv')->where('client_id', Config::get('filesystems.disks.client.client_id'))->get();
      return $bvs;
    });
  }
  function planning(){
    return $this->hasMany("App\Planning");
  }
  function wachtwoorden(){
    return $this->hasManyThrough(Wachtwoordkluis::class, WachtwoordkluisUser::class, "password_id", "status", "id", "user_id");
  }
  public function machines(){
    return $this->belongsToMany(Machines::class, 'machines_user', 'user_id', 'machine_id');
  }
  public function id_card_file(){
    return $this->hasOne(ExplorerFiles::class, 'id', 'id_card_file_id');
  }
  public function passport_file(){
    return $this->hasOne(ExplorerFiles::class, 'id', 'passport_file_id');
  }

  function permissions() {
      $rp = \DB::table('roles_permissions')->where('role_id', $this->role_id)->get();
      $permissions = [];
      foreach($rp as $pid) {
        $p = \DB::connection('tessa')->table('permissions')->where('id', $pid->permission_id)->first();
        $permissions[] = $p;
      }
      return $permissions;
  }
  function hasPermissionTo($permission) {
    $perm = Cache::remember(Config::get('filesystems.disks.client.client_id') .'_permission_'.$permission, 360, function () use($permission) {
      return \DB::connection('tessa')->table('permissions')->where('permission', $permission)->first();
    });

    if ($perm) {
      //$roleHasPermission = Cache::remember(Config::get('filesystems.disks.client.client_id').'_role_has_permission_'. $this->role_id, 360, function () use($perm) { return \DB::table('roles_permissions')->where('role_id', $this->role_id)->where('permission_id', $perm->id)->first(); });
      $roleHasPermission = \DB::table('roles_permissions')->where('role_id', $this->role_id)->where('permission_id', $perm->id)->first();
      //var_dump($roleHasPermission);
      //echo 'Rol: '.$this->role_id.'  PermissieId: '.$perm->id.' Permissie: '.$roleHasPermission;
      if ($roleHasPermission) {
        return true;
      }
    }
    return false;
  }
  function hasModulePermission($module_name, $permission_name){
    $module = Module::where('name', $module_name)->first();
    if(!$module){ return false; }

    $permission = Permission::where(['module_id' => $module->id, 'permission' => $permission_name])->first();
    if(!$permission){ return false; }

    $rolePermission = RolePermission::where(['role_id' => Auth::user()->role_id, 'permission_id' => $permission->id])->first();
    return isset($rolePermission);
  }
  function standaarduren(){
    return $this->hasMany(Standaarduren::class, "medewerkers_id", "id");
  }
  function verlofsaldo($jaar = null, $total = false){
    $user = $this;

    $jaarsaldo = $user->standaard_verlofsaldo;
    if($jaar && $total){
      $jaarsaldo += UserVerlofsaldo::where(['user_id' => $user->id, 'jaar' => $jaar])->first()->aantal ?? $user->standaard_verlofsaldo;
    }elseif($jaar && !$total){
      $jaarsaldo = UserVerlofsaldo::where(['user_id' => $user->id, 'jaar' => $jaar])->first()->aantal ?? $user->standaard_verlofsaldo;
    }
    return $jaarsaldo;
  }
  function lastOnline(){
    $record = OnlineLog::where([
      'client_id' => getClientId(),
      'user_id' => $this->id
    ])->orderBy('created_at', 'DESC')->orderBy('updated_at', 'DESC')->first();

    return $record
      ? Carbon::parse($record->updated_at ?? $record->created_at)
      : null;
  }
  function hasDrivingLicense($license){
    try{
      $licenses = json_decode($this->driver_license ?? '[]');
      return in_array($license, $licenses);
    }
    catch(\Exception $e){ return false; }
  }
  function verlofsaldoExclusief($jaar = null){
    $totaalSaldo = $this->verlofsaldo($jaar) + $this->standaard_verlofsaldo;

    $jaarVerlofSum = UrenRegistratie::whereYear('datum', $jaar)
      ->where('medewerker_id', $this->id)
      ->sum('verlof');

    $jaarBijzonderverlofSum = UrenRegistratie::whereYear('datum', $jaar)
      ->where('medewerker_id', $this->id)
      ->sum('bijzonderverlof');

    $jaarVerlof = $jaarVerlofSum + $jaarBijzonderverlofSum;
    $saldo = $totaalSaldo - $jaarVerlof;

    return $saldo;
  }

  function gebruiktVerlofSaldo($start, $end){
    if (!isset($start) || !isset($end)) {return 0;}
    
    $verlofSum = UrenRegistratie::where('medewerker_id', $this->id)
      ->whereBetween('datum', [$start, $end])
      ->sum('verlof');

    $bijzonderverlofSum = UrenRegistratie::where('medewerker_id', $this->id)
      ->whereBetween('datum', [$start, $end])
      ->sum('bijzonderverlof');

    return $verlofSum + $bijzonderverlofSum;
  }

  function totalVerlofSaldoJaar($year){
    return $this->verlofsaldo($year) + $this->standaard_verlofsaldo;
  }

	function fullName(){
		return trim("{$this->name} {$this->lastname}");
	}

  //Files
  function storeFile($key, $file){
    $target = $this->fileTargetByKey($key);
    $src = randomString(10).".png";
    Storage::disk($target->disk)->put($target->path.$src, file_get_contents($file));

    return $src;
  }
  function fileTargetByKey($key, $src = ''){
    $response = new \stdClass();
    $response->disk = 'client';
    $response->path = 'overig';

    switch($key){
      case 'foto':
        $response->disk = 'client';
        $response->path = "/users/".$this->id."/headshot/{$src}";
        break;
      case 'handtekening':
        $response->disk = 'image';
        $response->path = "/handtekeningen/{$src}";
        break;
      case 'footer':
        $response->disk = 'client';
        $response->path = "/medewerkers/footers/{$src}";
        break;
    }

    return $response;
  }


  //Intake
  public function sendIntakeMail(){
    try{
      $html = $this->intakeMailContent('users_intake_mail') . emailBtnContent(getSubdomein(), "users/intake/fill/{$this->intake_token}", 'Formulier Invullen');

      Mail::to($this->intake_email)->send(
        new BlankMail(
          $this->bv,
          '<EMAIL>',
          $this->bv->name,
          "Intakeformulier {$this->bv->name}",
          $html
        )
      );

      $this->intake_status = 'Verzonden';
      $this->save();
    }
    catch(Throwable $e){
      actError($e);
    }
  }
  public function sendIntakeConfirmationMail(){
    try{
      $html = $this->intakeMailContent('users_intake_confirmation_mail') . emailBtnContent(getSubdomein(), "login", 'Inloggen');

      Mail::to($this->intake_email)->send(
        new BlankMail(
          $this->bv,
          '<EMAIL>',
          $this->bv->name,
          "Bevestiging intakeformulier {$this->bv->name}",
          $html
        )
      );
    }
    catch(Throwable $e){
      actError($e);
    }
  }
  public function sendIntakeFilledMail(){
    try{
      $mails = getSettingValue('intake_filled_confirmation_mail');
      $mails = str_replace(' ', '', $mails);
      $mails = explode(',', $mails);

      $mails = array_filter($mails, function($mail){
        return verifyEmail($mail);
      });
      $mails = resetIndex($mails);

      $html = "{$this->name} {$this->lastname} heeft zojuist het intakeformulier volledig ingevuld.";
      $html .= emailBtnContent( getSubdomein(), "users/intake/preview/$this->intake_token", 'Intakeformulier inzien' );

      Mail::to($mails)->send(
        new BlankMail(
          $this->bv,
          '<EMAIL>',
          $this->bv->name,
          "Intakeformulier ingevuld door {$this->name} {$this->lastname}",
          $html
        )
      );
    }
    catch(Throwable $e){
      actError($e);
    }
  }
  public function intakeMailContent($setting_key){
    $content = getSettingValue($setting_key);

    $content = str_replace('&lt;{voornaam}&gt;', $this->name, $content);
    $content = str_replace('&lt;{achternaam}&gt;', $this->lastname, $content);
    $content = str_replace('&lt;{email}&gt;', $this->email, $content);

    return $content;
  }
  public function intakeColor(){
    switch($this->intake_status){
      case 'Concept': $this->intake_color = 'secondary'; break;
      case 'Verzonden': $this->intake_color = 'primary'; break;
      case 'Ingevuld': $this->intake_color = 'info'; break;
      case 'Bevestigd': $this->intake_color = 'success'; break;
      case 'Verwijderd': $this->intake_color = 'dark'; break;
      default: $this->intake_color = null;
    }

    return $this->intake_color;
  }

  //Custom fields
  public function customFieldValue($keyword){
    $field = UsersCustomFields::where([
      'user_id' => $this->id,
      'keyword' => $keyword
    ])->first();

    return $field->value ?? null;
  }
  public function customFieldFile($keyword){
    $file_id = $this->customFieldValue($keyword);
    return ExplorerFiles::find($file_id);
  }

  public static function name(){
    if(!Auth::check()){
      return '';
    }

    return (Auth::user()->name ?? '')." ".(Auth::user()->lastname ?? '');
  }
  public static function id(){
    return Auth::user()->id ?? null;
  }



}
