<?php

namespace App\Classes;

use App\Settings;
use App\Zenvoices\Kostendragers;
use Illuminate\Support\Facades\Auth;

class ZenvoicesApi{

    public $connected = false;

    private $tenancyname;
    private $username;
    private $password;
    private $credentials;
    private $administration;

    private $curl;
    private $refreshToken;
    private $accessToken;

    private $url;
    private $method = 'GET';
    private $bv = null;

    public function __construct($options = []){
        if(!Auth::check() && !app()->runningInConsole()){return;}
        $this->defineCredentials();
        $this->defineRefreshToken();
        $this->defineConnected();

        if(isset($options['bv'])){
            $this->bv = $options['bv'];
        }else{
            $this->bv = Auth::user()->bv_id;
        }

        if(!$this->connected){
            return;
        }

        if(isset($options['no_curl'])){
            return;
        }

        $this->defineAccessToken();
    }

    //init
    private function defineAccessToken(){

        $setting = getSettingJson('zenvoices_access_token');
        if(!isset($setting['token']) || $setting['valid_till'] < time()){
            $this->defineCurl();
            $this->activate();
        }else{
            $this->accessToken = tes_decrypt($setting['token']);
        }
    }
    private function defineRefreshToken(){
        $setting = getSettingJson('zenvoices_refresh_token');
        if(!isset($setting['token']) || $setting['valid_till'] < time()){
            $this->defineCurl();
            $this->activate();
        }else{
            $this->refreshToken = tes_decrypt($setting['token']);
        }
    }
    private function defineCredentials(){
        $credsetting = getSettingJson('zenvoices_credentials');
        $creds = $credsetting[$this->bv] ?? null;

        if(!$creds){return;}

        $this->credentials = $creds;

        $this->tenancyname = $creds['tenancyname'];
        $this->username = $creds['username'];
        $this->password = $creds['password'];
        $this->administration = $creds['administration'];
    }
    public function defineConnected(){
        if($this->refreshToken || ($this->tenancyname && $this->username && $this->password && $this->administration)){
            $this->connected = true;
            return;
        }

        $this->connected = false;
    }

    private function defineCurl()
    {
        $this->curl = curl_init();
        $options = [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
        ];

        if (!$this->accessToken && !$this->refreshToken) {
            $options[CURLOPT_URL] = "https://app.zenvoices.com/api/TokenAuth/TokenLogin";
            $options[CURLOPT_HTTPHEADER] = [
                'Content-Type: application/json-patch+json',
                'accept: text/plain',
            ];
            $options[CURLOPT_POSTFIELDS] = json_encode([
                'userNameOrEmailAddress' => $this->username,
                'password' => $this->password,
                'tenancyName' => $this->tenancyname,
            ]);
        }
        elseif (!$this->accessToken) {
            $options[CURLOPT_URL] = "https://app.zenvoices.com/api/TokenAuth/RefreshToken";
            $options[CURLOPT_HTTPHEADER] = [
                'Content-Type: application/json-patch+json',
                'accept: text/plain',
            ];
            $options[CURLOPT_POSTFIELDS] = json_encode([
                'refreshToken' => $this->refreshToken,
            ]);
        }
        else {
            $options[CURLOPT_HTTPHEADER] = [
                "Authorization: Bearer $this->accessToken",
                "Content-Type: application/json",
            ];
        }

        curl_setopt_array($this->curl, $options);


    }

    //utility
    private function activate(){
        $start = microtime(true);

        $responseData = json_decode(curl_exec($this->curl),true);
        $error = curl_error($this->curl);
        $code = curl_getinfo($this->curl, CURLINFO_HTTP_CODE);
        curl_close($this->curl);

        if($code !== 200){
            $this->connected = false;
            return;
        }
        $this->connected = true;

        if (isset($responseData['result']['refreshToken'])) {
            $this->refreshToken = $responseData['result']['refreshToken'];
            $this->storeRefreshToken($responseData['result']['refreshToken'] , $responseData['result']['refreshTokenExpireInSeconds']);
        }

        if (isset($responseData['result']['accessToken'])) {
            $this->accessToken = $responseData['result']['accessToken'];
            $this->storeAccessToken($responseData['result']['accessToken'], $responseData['result']['expireInSeconds']);
        }

    }

    public function getStored($model){
        $model = 'App\Zenvoices\\' . $model;
        return $model::orderBy('naam', 'ASC')->get();
    }

    //Tokens
    public function storeAccessToken($token, $expires_in){
        Settings::updateOrInsert([
            'naam' => 'zenvoices_access_token',
        ], [
            'value' => json_encode([
                'token' => tes_encrypt($token),
                'valid_till' => time() + $expires_in,
            ]),
        ]);
    }
    public function storeRefreshToken($token, $expires_in){
        Settings::updateOrInsert([
            'naam' => 'zenvoices_refresh_token',
        ], [
            'value' => json_encode([
                'token' => tes_encrypt($token),
                'valid_till' => time() + $expires_in,
            ]),
        ]);
    }

    //Curl
    private function setCurlMethod($method){
        if(!$this->connected){
            return;
        }

        $this->method = $method;
        curl_setopt($this->curl, CURLOPT_CUSTOMREQUEST, $method);
    }
    private function setCurlUrl($path){
        if(!$this->connected){
            return;
        }

        $this->url = "https://app.zenvoices.com/$path";

        curl_setopt($this->curl, CURLOPT_URL, $this->url);
    }
    private function setCurlData($data){
        if(!$this->connected){
            return;
        }

        if($this->method == 'GET'){
            $url = $this->url . "?" . http_build_query($data);
            curl_setopt($this->curl, CURLOPT_URL, $url);
        }
        else{
            curl_setopt($this->curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

    }
    private function execCurl(){
        if(!$this->connected){
            abort(401, 'Zenvoices not connected');
        }

        $response = curl_exec($this->curl);
        $error = json_decode(curl_error($this->curl));

        $data = json_decode($response);
        $code = curl_getinfo($this->curl, CURLINFO_HTTP_CODE);

        $status = in_array($code, [200, 201, 204]) && !$error;

        curl_close($this->curl);
        $curl_response = new \stdClass();
        $curl_response->data = $data;
        $curl_response->error = $error;
        $curl_response->code = $code;
        $curl_response->status = $status;

        return $curl_response;
    }

    private function post($url, $data = []){
        $this->defineCurl();
        $this->setCurlMethod('POST');
        $this->setCurlUrl($url);
        $this->setCurlData($data);
        return $this->execCurl();
    }
    private function get($url, $data = []){
        $this->defineCurl();
        $this->setCurlMethod('GET');
        $this->setCurlUrl($url);
        $this->setCurlData($data);
        return $this->execCurl();
    }
    private function delete($url, $data = []){
        $this->defineCurl();
        $this->setCurlMethod('DELETE');
        $this->setCurlUrl($url);
        $this->setCurlData($data);
        return $this->execCurl();
    }

    //general
    public function administrations($data = []){
        return $this->post("public-api/v1/administration/list", $data);
    }

    //Suppliers
    public function accountsGet($data = []){
        return $this->post("public-api/v1/accounts/list", $data);
    }

    //Kostendrager
    public function costUnitsGet($data = []){
        return $this->post("public-api/v1/costUnits/list", $data);
    }
    public function getStoredCostUnits(){
        return Kostendragers::get();
    }

    //Inkoopbonnen
    //commitments
    public function inkoopbonnenGet($data = []){
        return $this->post("public-api/v1/commitment/list", $data);
    }
    public function uploadCommitment($data = []){
        return $this->post("public-api/v1/commitment", $data);
    }
    public function deleteCommitmentByCode($data = []){
        return $this->delete("public-api/v1/commitment", $data);
    }

    //Producten
    public function productsGet($data = []){
        return $this->post("public-api/v1/product/list", $data);
    }


    //Btw codes
    public function taxCodesGet($data = []){
        return $this->post("public-api/v1/taxCodes/list", $data);
    }

}
