<?php

namespace App\Classes\search;

use App\Classes\ProjectSearch;
use App\KlantenUsers;
use App\Models\Adressen;
use App\User;
use DB;
use Illuminate\Support\Facades\Auth;

class SearchManager extends SearchIndexes
{

    public $string;
    public $ids;
    public $string_exploded;
    public $active;

		public function __construct($string, $active = null, $ids = []){
			$this->string = str_replace("'", "%", $string);
			$this->string_exploded = explode(' ', $this->string);
      $this->active = $active;
			$this->ids = $ids;
		}


    public function user(){
      $where = $this->generateWhere($this->users_columns);
      $ids = $this->generateIDSIn('users');

      if(!count($where)){ return []; }

      return DB::select("
            SELECT users.name, users.lastname, users.email
            FROM users
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY users.name DESC
            LIMIT 100
        ");
    }
    public function project(){
        $where = $this->generateWhere($this->projecten_columns);

        if(!count($where)){ return []; }

        return DB::select("
            SELECT projecten.id, projecten.projectnr, projecten.projectnaam,
                   klanten.id AS klant_table_id,
                   klanten_contactpersonen.id AS contactpersonen_table_id
            FROM projecten
            LEFT JOIN klanten ON klant_id = klanten.id
            LEFT JOIN klanten_contactpersonen ON contactpersoon_id = klanten_contactpersonen.id
            WHERE ( ".implode(' AND ', $where).")
            ORDER BY projecten.created_at DESC
            LIMIT 100
        ");
    }
    public function offerte(){
      $where = $this->generateWhere($this->offertes_columns);

      if(!count($where)){ return []; }

      return DB::select("
              SELECT offertes.id, offertes.offertenummer, offertes.naam,
                     klanten.id AS klant_table_id,
                     klanten_contactpersonen.id AS contactpersonen_table_id,
                     klanten_locaties.id AS locaties_table_id
              FROM offertes
              LEFT JOIN klanten ON klant_id = klanten.id
              LEFT JOIN klanten_contactpersonen ON contactpersoon_id = klanten_contactpersonen.id
              LEFT JOIN klanten_locaties ON locatie_id = klanten_locaties.id
              WHERE type = 1 AND ( ".implode(' AND ', $where).")
              ORDER BY offertes.created_at DESC
              LIMIT 100
          ");
    }
    public function werkbon(){
        $where = $this->generateWhere($this->werkbonnen_columns);

        if(!count($where)){ return []; }

        return DB::select("
            SELECT werkbonnen.id, werkbonnen.werkbonnummer,
                   klanten.id AS klant_table_id,
                   projecten.id AS project_table_id,
                   klanten_contactpersonen.id AS contactpersonen_table_id
            FROM werkbonnen
            LEFT JOIN klanten ON klant_id = klanten.id
            LEFT JOIN projecten ON project_id = projecten.id
            LEFT JOIN klanten_contactpersonen ON werkbonnen.contactpersoon_id = klanten_contactpersonen.id
            LEFT JOIN users ON werkbonnen.user_id = users.id
            WHERE ( ".implode(' AND ', $where).")
            ORDER BY werkbonnen.created_at DESC
            LIMIT 100
        ");
    }
    public function klant(){
        $where = $this->generateWhere($this->klanten_columns);

        if(!count($where)){ return []; }

        if(!hasPermission('Alle klanten bekijken')){
            $klanten_ids = KlantenUsers::where('user_id', User::id())->pluck('klant_id')->toArray();
            $klanten_ids = implode(',', $klanten_ids);
        }

        return DB::select("
            SELECT klanten.id, klanten.naam, klanten.contactpersoon_voornaam, klanten.contactpersoon_achternaam, klanten.straat, klanten.huisnummer, klanten.toevoeging, klanten.status
            FROM klanten
            WHERE ( ".implode(' AND ', $where).")".(isset($klanten_ids) ? "AND id in ($klanten_ids)" : '')."
            ORDER BY klanten.status DESC
            LIMIT 100
        ");
    }
    public function leverancier(){
        $where = $this->generateWhere($this->leveranciers_columns);

        if(!count($where)){ return []; }
        if($this->active != null){
            $where[] = "leveranciers.active = $this->active";
        }
        return DB::select("
            SELECT leveranciers.id, leveranciers.naam, leveranciers.crediteurnummer, leveranciers.contactpersoon_achternaam
            FROM leveranciers
            WHERE ( ".implode(' AND ', $where).")
            ORDER BY leveranciers.crediteurnummer DESC
            LIMIT 100
        ");
    }
    public function factuur($options){
        $where = $this->generateWhere($this->facturen_columns);
        $is_proforma = $options['proforma'] ?? 0;

        if(!count($where)){ return []; }

        return DB::select("
            SELECT facturen.id, facturen.factuurnummer,
                   facturen_adres.id AS facturen_adres_table_id,
                   klanten.id AS klanten_table_id
            FROM facturen
            LEFT JOIN facturen_adres ON facturen.id = facturen_adres.factuur_id
            LEFT JOIN klanten ON facturen.klant_id = klanten.id
            WHERE ( ".implode(' AND ', $where).")
            AND is_proforma = $is_proforma
            ORDER BY facturen.factuurnummer DESC
            LIMIT 100
        ");
    }
    public function inkoopbon(){
      $where = $this->generateWhere($this->inkoopbonnen_columns);

      if(!count($where)){ return []; }

      return DB::select("
              SELECT inkoopbonnen.id, inkoopbonnen.bonnummer,
                     leveranciers.id AS leverancier_table_id,
                     projecten.id AS project_table_id,
                     projecten.projectnr AS project_projectnr
              FROM inkoopbonnen
              LEFT JOIN projecten ON project_id = projecten.id
              LEFT JOIN leveranciers ON leverancier_id = leveranciers.id
              WHERE ( ".implode(' AND ', $where).")
              ORDER BY inkoopbonnen.created_at DESC
              LIMIT 100
          ");
    }
    public function inkoopfactuur(){
      $where = $this->generateWhere($this->inkoopfacturen_columns);

      if(!count($where)){ return []; }

      return DB::select("
              SELECT inkoopfacturen.id, inkoopfacturen.factuurnummer, inkoopfacturen.local_factuurnummer,
                     leveranciers.id AS leverancier_table_id,
                     projecten.id AS project_table_id,
                     projecten.projectnr AS project_projectnr
              FROM inkoopfacturen
              LEFT JOIN projecten ON project_id = projecten.id
              LEFT JOIN leveranciers ON leverancier_id = leveranciers.id
              WHERE ( ".implode(' AND ', $where).")
              ORDER BY inkoopfacturen.local_factuurnummer DESC
              LIMIT 100
          ");
    }
    public function kluisregel(){
        $where = $this->generateWhere($this->wachtwoordkluis_columns);

        if (!count($where)) { return []; }

        return DB::select("
              SELECT id, naam, link FROM wachtwoordkluis WHERE ( " . implode(' AND ', $where) . ")
              LIMIT 100
          ");
    }
    public function declaraties(){
      $where = $this->generateWhere($this->declaraties_columns);
      $ids = $this->generateIDSIn('declaraties');

      if (!count($where)) { return []; }

      return DB::select("
            SELECT id, titel, bedrag, gebruiker, opmerkingen
            FROM declaraties
            WHERE $ids ( ".implode(' AND ', $where).")
            LIMIT 100
        ");
    }
    public function toolboxes(){
        $where = $this->generateWhere($this->toolbox_columns);

        if (!count($where)) { return []; }
        return DB::select("
              SELECT id, titel, video, bestand, beschrijving, created_at, updated_at, uitleg, active FROM toolbox WHERE ( " . implode(' AND ', $where) . ")
              LIMIT 100
          ");
    }
    public function memos(){
        $where = $this->generateWhere($this->memo_columns);

        if (!count($where)) { return []; }
        return DB::select("
              SELECT id, image, title, slug, content, active FROM memo WHERE ( " . implode(' AND ', $where) . ")
              LIMIT 100
          ");
    }
    public function explorer(){
        $where = $this->generateWhere($this->explorer_columns);
        $ids = $this->generateIDSIn('explorer_files');

        if (!count($where)) { return []; }

        $permission = '';
        if(!hasPermission('Alle bestanden bekijken') && !hasPermission('Eigen bestanden bekijken')) {
            $permission = 'path LIKE "/Gebruikers/('.User::id().')%" AND';
        }
        else if(!hasPermission('Alle bestanden bekijken')){
            $permission = 'user_id = '.User::id().' AND';
        }

        return DB::select("
            SELECT id, type, path, name, view_name
            FROM explorer_files
                WHERE active = 1
            AND $ids $permission ( ".implode(' AND ', $where).")
            LIMIT 100
        ");

    }
    public function taak(){
        $where = $this->generateWhere($this->projecten_taken_columns);
        $ids = $this->generateIDSIn('projecten_taken');

        if (!count($where)) { return []; }

        return DB::select("
            SELECT DISTINCT projecten_taken.id, projecten_taken.name, projecten_taken.planning_color, projecten_taken.completed
            FROM projecten_taken
            LEFT JOIN projecten_taken_custom_rows ON projecten_taken_custom_rows.taak_id = projecten_taken.id
            WHERE active = $this->active
            AND $ids ( ".implode(' AND ', $where).")
            LIMIT 100
        ");

    }

    public function checklists(){
    $where = $this->generateWhere($this->checklist_columns);

    if (!count($where)) { return []; }

    return DB::select("
              SELECT checklists.checklistnummer, checklists.datum, checklists.id
              FROM checklists
              LEFT JOIN projecten ON project_id = projecten.id
              LEFT JOIN klanten ON checklists.klant_id = klanten.id
              WHERE ( " . implode(' AND ', $where) . ")
              LIMIT 100
          ");
   }

    public function whatsappGroup(){
        $where = $this->generateWhere($this->whatsapp_groups_columns);
        $ids = $this->generateIDSIn('whatsapp_groups');

        if(!count($where)){ return []; }

        return DB::select("
            SELECT whatsapp_groups.id, whatsapp_groups.name
            FROM whatsapp_groups
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY name ASC
            LIMIT 100
        ");
    }
    public function whatsappContact(){
        $where = $this->generateWhere($this->whatsapp_contacts_columns);
        $ids = $this->generateIDSIn('whatsapp_contacts');

        if(!count($where)){ return []; }

        return DB::select("
            SELECT whatsapp_contacts.id, whatsapp_contacts.name
            FROM whatsapp_contacts
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY name ASC
            LIMIT 100
        ");
    }
    public function whatsappMessages(){
        $where = $this->generateWhere($this->whatsapp_messages_columns);
        $ids = $this->generateIDSIn('whatsapp_messages');

        if(!count($where)){ return []; }

        return DB::select("
            SELECT whatsapp_messages.id, whatsapp_messages.name
            FROM whatsapp_messages
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY name ASC
            LIMIT 100
        ");
    }
    public function whatsappSentMessages(){
        $where = $this->generateWhere($this->whatsapp_sent_messages_columns);
        $ids = $this->generateIDSIn('whatsapp_messages_sent');

        if(!count($where)){ return []; }

        return DB::select("
            SELECT whatsapp_messages_sent.id, whatsapp_messages.name AS message_name, whatsapp_contacts.name, whatsapp_contacts.tussenvoegsel, whatsapp_contacts.lastname
            FROM whatsapp_messages_sent
            LEFT JOIN whatsapp_messages ON message_id = whatsapp_messages.id
            LEFT JOIN whatsapp_contacts ON contact_id = whatsapp_contacts.id
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY name ASC
            LIMIT 100
        ");
    }
    public function whatsappScheduledMessages(){
        $where = $this->generateWhere($this->whatsapp_scheduled_messages_columns);
        $ids = $this->generateIDSIn('whatsapp_messages_schedule');

        if(!count($where)){ return []; }


        return DB::select("
            SELECT whatsapp_messages_schedule.id, whatsapp_messages.name AS message_name, whatsapp_contacts.name, whatsapp_contacts.tussenvoegsel, whatsapp_contacts.lastname
            FROM whatsapp_messages_schedule
            LEFT JOIN whatsapp_messages ON message_id = whatsapp_messages.id
            LEFT JOIN whatsapp_contacts ON contact_id = whatsapp_contacts.id
            WHERE $ids ( ".implode(' AND ', $where).")
            ORDER BY name ASC
            LIMIT 100
        ");
    }

    private function generateWhere($columns){
        $where = [];

        foreach($this->string_exploded as $word){
            if(!$word){ continue; }
            $where[] = "REPLACE(CONCAT_WS('', ".implode(', ', $columns)."), ' ', '') LIKE '%$word%'";
        }

        return $where;
    }

    private function generateIDSIn($prefix, $primary_key = 'id'){
      if(!$this->ids){
        return '';
      }

      $list = $this->arrayToList($this->ids);

      return "$prefix.$primary_key IN $list AND";
    }
    private function arrayToList($array){
      return "('" . implode("' ,'", $array) . "')";
    }
}
