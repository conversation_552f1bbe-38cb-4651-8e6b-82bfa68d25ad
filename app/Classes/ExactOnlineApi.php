<?php

namespace App\Classes;

use App\ExactOnline\ApiLog;
use App\ExactOnline\GeneralLedger;
use App\ExactOnline\Items;
use App\ExactOnline\Metadata;
use App\ExactOnline\VAT;
use App\Facturen;
use App\Klanten;
use App\KlantenBanknummers;
use App\KlantenContactpersonen;
use App\KlantenLocaties;
use App\Project;
use App\Settings;
use Carbon\Carbon;

class ExactOnlineApi{

	public $connected;

	private $options;

	private $refresh_token;
	private $access_token;
	private $settings;

	private $curl;
	private $url;
	private $division;
	private $instance;
	private $instance_id;
	private $method = 'GET';
	private $path;
	private $data;

	public function __construct($options){
		$this->options = $options;

		$this->defineConnected();

		if(isset($options['no_curl'])){
			return;
		}

		$this->division = $options['division'] ?? null;

		$this->defineDivision();
		$this->defineToken();
		$this->defineCurl();
	}

	//Init
	private function defineConnected(){
		$this->refresh_token = getSettingValue('exact_online_refresh_token');
		$this->access_token = json_decode(getSettingValue('exact_online_access_token', '{}'));
		$this->connected = !!$this->refresh_token;
	}
	private function defineDivision(){
		$this->division = $this->options['division'] ?? null;
	}
	private function defineToken(){
		if(!$this->refresh_token){
			return;
		}
		if(($this->access_token->expires ?? 0) > Carbon::now()->timestamp){
			return;
		}

		$data = [
			'grant_type' => 'refresh_token',
			'refresh_token' => tes_decrypt($this->refresh_token),
			'client_id' => env('EXACT_ONLINE_CLIENT_ID'),
			'client_secret' => env('EXACT_ONLINE_SECRET'),
		];

		$curl = curl_init('https://start.exactonline.nl/api/oauth2/token');
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

		$response = json_decode(curl_exec($curl));
		curl_close($curl);

		if(!isset($response->access_token)){
			dd($response);
			abort(401, 'No Access token');
		}

		Settings::updateOrInsert(['naam' => 'exact_online_refresh_token'],['value' => tes_encrypt($response->refresh_token)]);
		Settings::updateOrInsert(['naam' => 'exact_online_access_token'],['value' => json_encode([
			'expires' => Carbon::now()->addSeconds($response->expires_in)->timestamp,
			'token' => tes_encrypt($response->access_token),
		])]);
		$this->defineConnected();

	}
	private function defineCurl(){
		$this->curl = curl_init();
		curl_setopt_array($this->curl, [
			CURLOPT_HTTPHEADER => [
				"Authorization: Bearer ".tes_decrypt($this->access_token->token),
				"Accept: application/json",
				"Content-Type: application/json",
			],
			CURLOPT_RETURNTRANSFER => true,
		]);
	}

	//Logs
	private function setInstance($instance, $id){
		$this->instance = $instance;
		$this->instance_id = $id;
	}
	private function log($curl_response){
		try{
			$response = $curl_response->data;
			$error = $curl_response->error;

			ApiLog::insert([
				'url' => $this->url,
				'path' => $this->path,
				'instance' => $this->instance,
				'instance_id' => $this->instance_id,
				'method' => $this->method,
				'data' => is_array($this->data) || is_object($this->data) ? json_encode($this->data) : $this->data,
				'response' => is_array($response) || is_object($response) ? json_encode($response) : $response,
				'error' => is_array($error) || is_object($error) ? json_encode($error) : $error,
				'raw_response' => json_encode($curl_response->raw_response),
				'code' => $curl_response->code,
				'date' => Carbon::now(),
			]);
		}
		catch(\Exception $e){ actError($e); }
	}

	//Utility
	private function validate($data){
		foreach($data as $resource => $value){
			if($value){
				continue;
			}
			abort(403, "Resource $resource not found!");
		}
	}
	private function getService($instance){
		switch($instance){
			case 'Contacts':
			case 'Addresses':
			case 'Accounts':
			case 'BankAccounts': return 'crm'; break;
      case 'SalesInvoices': return 'salesinvoice'; break;
      case 'GLAccounts': return 'financial'; break;
      case 'Items': return 'logistics'; break;
      case 'VATCodes': return 'vat'; break;
      case 'Projects': return 'project'; break;
      case 'Warehouses': return 'inventory'; break;
		}

		return null;
	}
	public function search($division, $instance, $key, $value, $expand, $bulk = false, $select = null){
		$service = $this->getService($instance);
    if($bulk){
      $service = 'bulk/'.$service;
    }
		if(is_bool($value) || ($value === 'true' || $value === 'false')){
			$value = ($value === true || $value === 'true') ? 'true' : 'false';
		}
		elseif($this->isGuid($value)){
			$value = "guid'$value'";
		}
		elseif(!is_int($value) && !filter_var($value, FILTER_VALIDATE_INT)){
			$value = "'$value'";
		}

		$query = [];
        if(isset($key) && isset($value)){
         $query['$filter'] = "$key eq $value";
        }
        if($expand){
            $query['$expand'] = implode(', ', $expand);
        }
        if($select){
            $query['$select'] = $select;
        }

        $query = count($query) ? '?'.http_build_query($query) : '';

		$this->setCurlUrl("$division/$service/{$instance}{$query}");
		return $this->execCurl();
	}
	public function get($division, $instance, $bulk = false, $select = null){
		return $this->search($division, $instance, null, null, null, $bulk, $select);
	}
	public function formatCode($code, $length){
		return str_pad($code, $length, ' ', STR_PAD_LEFT);
	}
	public function isGuid($string){
		$guidRegex = '/^\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}?$/';
		return preg_match($guidRegex, $string) === 1;
	}

	//Curl
	private function setCurlMethod($method){
		if(!$this->connected){
			return;
		}

		$this->method = $method;
		curl_setopt($this->curl, CURLOPT_CUSTOMREQUEST, $method);
	}
	private function setCurlUrl($path){
		if(!$this->connected){
			return;
		}
		$this->path = $path;
		$this->url = "https://start.exactonline.nl/api/v1/$path";

		curl_setopt($this->curl, CURLOPT_URL, $this->url);
	}
	private function setCurlData($data){
		if(!$this->connected){
			return;
		}

		$this->data = $data;
		curl_setopt($this->curl, CURLOPT_POSTFIELDS, json_encode($data));
	}
	private function execCurl(){
		if(!$this->connected){
			abort(401, 'Exact Online not connected');
		}

		$curl_response = new \stdClass();

		$data = json_decode(curl_exec($this->curl));
		$error = json_decode(curl_error($this->curl));
		$code = curl_getinfo($this->curl, CURLINFO_HTTP_CODE);

		$curl_response->raw_response = [
			'data' => $data,
			'error' => $error,
		];

		if(isset($data->error)){
			$error = $data->error;
			if(isset($error->message->value)){
				$error = $error->message->value;
			}
		}
		if(isset($data->d)){
			$data = $data->d;
		}
		if(isset($data->results)){
			$data = $data->results;
		}

		$status = in_array($code, [200, 201, 204]) && !$error;

		curl_close($this->curl);
		$this->defineCurl();

		$curl_response->data = $data;
		$curl_response->error = $error;
		$curl_response->code = $code;
		$curl_response->status = $status;

		$this->log($curl_response);
		return $curl_response;
	}

	//Divisions
    public function getDivisions(){
        $division = getBv()->exact_online_division;
        $this->setCurlUrl("{$division}/system/Divisions");
        return $this->execCurl();
    }
	public function getMe(){
		$this->setCurlUrl('/current/Me');
		return $this->execCurl();
	}

	//Klanten
	public function postDebtor($id){
		$this->setInstance('klanten', $id);

		$klant = Klanten::find($id);
		$this->validate([
			'Division' => $this->division,
			'klant' => $klant,
			'Debiteurnummer' => $klant->debiteurnummer ?? null,
		]);

		$data = [
			'Code' => $this->formatCode($klant->debiteurnummer, 18),
			'Name' => $klant->title(),
			'Phone' => $klant->telefoonnummer,
			'Email' => $klant->email,
			'Website' => $klant->website,

			'VATNumber' => $klant->btw_nr,
			'ChamberOfCommerce' => $klant->kvk,

			'Status' => 'C',
			'Type' => 'A',
		];

		$this->setCurlData($data);

		$this->setCurlUrl("$this->division/crm/Accounts");
		$this->setCurlMethod('POST');
		if($klant->getExactOnlineMetadata('id')){
			$this->setCurlMethod('PUT');
			$this->setCurlUrl("$this->division/crm/Accounts(guid'".$klant->getExactOnlineMetadata('id')."')");
		}

		return $this->execCurl();
	}
	public function postDebtorContact($klant_id, $contact_id = null){
		$this->setInstance('klanten_contactpersonen', $contact_id);

		$klant = Klanten::find($klant_id);
		$contact = KlantenContactpersonen::find($contact_id);
		$guid = Metadata::get('klanten_contactpersonen', $contact_id, 'id');

		$this->validate([
			'Division' => $this->division,
			'Klant' => $klant,
			'Klant ID' => $klant ? $klant->getExactOnlineMetadata('id') : null,
		]);

		if($contact_id === "main-{$klant_id}"){
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'Title' => $klant->contactpersoon_title,
				'FirstName' => $klant->contactpersoon_voornaam,
				'LastName' => $klant->contactpersoon_achternaam,
				'Email' => $klant->contactpersoon_email,
				'Phone' => $klant->contactpersoon_telefoon,
				'Mobile' => $klant->contactpersoon_mobiel,
				'IsMainContact' => true,
			];
		}
		else{
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'Title' => $contact->title,
				'FirstName' => $contact->voornaam,
				'LastName' => $contact->achternaam,
				'Email' => $contact->email,
				'Phone' => $contact->telefoon,
				'Mobile' => $contact->mobiel,
			];
		}

		$this->setCurlData($data);

		$this->setCurlUrl("$this->division/crm/Contacts");
		$this->setCurlMethod('POST');
		if($guid){
			$this->setCurlMethod('PUT');
			$this->setCurlUrl("$this->division/crm/Contacts(guid'".$guid."')");
		}

		return $this->execCurl();
	}
	public function postDebtorAddress($klant_id, $adres_id = null){
		$this->setInstance('klanten_adressen', $adres_id);

		$klant = Klanten::find($klant_id);
		$adres = KlantenLocaties::find($adres_id);
		$guid = Metadata::get('klanten_adressen', $adres_id, 'id');

		$this->validate([
			'Division' => $this->division,
			'Klant' => $klant,
			'Klant ID' => $klant ? $klant->getExactOnlineMetadata('id') : null,
		]);

		//Types: Visit=1, Postal=2, Invoice=3, Delivery=4

		if($adres_id == "bezoekadres-{$klant_id}"){
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'AddressLine1' => $klant->addressLine(),
				'City' => $klant->plaats,
				'Country' => $klant->land,
				'Postcode' => $klant->postcode,
				'Type' => 1,
			];
		}
		elseif($adres_id == "postadres-${klant_id}"){
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'AddressLine1' => $klant->postalAddressLine(),
				'City' => $klant->postadres_plaats,
				'Country' => $klant->postadres_land,
				'Postcode' => $klant->postadres_postcode,
				'Type' => 2,
			];
		}
		else{
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'AddressLine1' => $adres->addressLine(),
				'City' => $adres->plaats,
				'Country' => $adres->land,
				'Postcode' => $adres->postcode,
			];
		}

		$this->setCurlData($data);

		$this->setCurlUrl("$this->division/crm/Addresses");
		$this->setCurlMethod('POST');
		if($guid){
			$this->setCurlMethod('PUT');
			$this->setCurlUrl("$this->division/crm/Addresses(guid'".$guid."')");
		}

		return $this->execCurl();
	}
	public function postDebtorBankAccount($klant_id, $bank_id){
		$this->setInstance('klanten_bankrekeningen', $bank_id);

		$klant = Klanten::find($klant_id);
		$bank = KlantenBanknummers::find($bank_id);
		$guid = Metadata::get('klanten_bankrekeningen', $bank_id, 'id');

		$this->validate([
			'Division' => $this->division,
			'Klant' => $klant,
			'Klant ID' => $klant ? $klant->getExactOnlineMetadata('id') : null,
		]);

		if($bank_id === "main-{$klant_id}"){
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'BankAccount' => $klant->iban,
				'IBAN' => $klant->iban,
				'BICCode' => $klant->bic,
				'Main' => true,
			];
		}
		else{
			$data = [
				'Account' => $klant->getExactOnlineMetadata('id'),
				'BankAccount' => $bank->iban,
				'IBAN' => $bank->iban,
				'BICCode' => $bank->bic,
			];
		}

		$this->setCurlData($data);

		$this->setCurlUrl("$this->division/crm/BankAccounts");
		$this->setCurlMethod('POST');
		if($guid){
			$this->setCurlMethod('PUT');
			$this->setCurlUrl("$this->division/crm/BankAccounts(guid'".$guid."')");
		}

		return $this->execCurl();
	}

  //Facturen
  public function postSalesInvoice($id){
      $this->setInstance('facturen', $id);

      $factuur = Facturen::find($id);
			$klant = Klanten::find($factuur->klant_id ?? null);
			$project = $factuur->firstProject();

      $this->validate([
	      'Division' => $this->division,
        'Factuur' => $factuur,
        'Factuurnummer' => $factuur->factuurnummer ?? null,
        'Klant' => $klant,
        'Klant ID' => $klant ? $klant->getExactOnlineMetadata('id') : null,
        'Project ID' => $project ? $project->getExactOnlineMetadata('id') : true,
      ]);

			$is_credit = $factuur->totaal()->excl < 0;
      $data = [
          "InvoiceNumber" => $factuur->factuurnummer,
          "OrderNumber" => $factuur->factuurnummer,
          "InvoiceTo" => $klant->getExactOnlineMetadata('id'),
          "OrderedBy" => $klant->getExactOnlineMetadata('id'),
          "YourRef" => $factuur->referentie,
          "InvoiceDate" => $factuur->datum,
          "DueDate" => $factuur->betalingstermijn,
          "Status" => $factuur->status == 'Betaald' ? 50 : 20,
		      "Type" => $is_credit ? 8021 : 8020,
          "SalesInvoiceLines" => [],
      ];

      foreach ($factuur->regels as $regel){
          if(intval($regel->tekstregel) || !$regel->excl() || !floatval($regel->aantal)){ continue; }

					$item = Items::where('code', $regel->getExactOnlineMetadata('item'))->first();
					$ledger = GeneralLedger::where('code', $regel->getExactOnlineMetadata('GL'))->first();

					$this->validate([
						'Item' => $item,
						'General Ledger' => $ledger,
						'VAT' => $regel->getExactOnlineMetadata('VAT'),
					]);

          $data['SalesInvoiceLines'][] = [
							"Project" => $project ? $project->getExactOnlineMetadata('id') : null,
              "Description" => $regel->naam,
              "Quantity" => $is_credit ? -floatval($regel->aantal) : floatval($regel->aantal),
              "UnitPrice" => $is_credit ? -$regel->excl() : $regel->excl(),
		          "GLAccount" => $ledger->eo_id,
		          "Item" => $item->eo_id,
	            "VATCode" => $regel->getExactOnlineMetadata('VAT'),
          ];
      }

      $this->setCurlData($data);

      $this->setCurlUrl("$this->division/salesinvoice/SalesInvoices");
      $this->setCurlMethod('POST');

      return $this->execCurl();
  }

	//Projecten
	public function postProject($id){
		$this->setInstance('projecten', $id);

		$project = Project::find($id);
		$klant = Klanten::find($project->klant_id ?? null);

		$this->validate([
			'Division' => $this->division,
			'Project' => $project,
			'Projectnummer' => $project->projectnr ?? null,
			'Klant' => $project->klant_id ? $klant : true,
			'Klant ID' => $klant ? $klant->getExactOnlineMetadata('id') : true,
		]);

		$data = [
			"Account" => $klant ? $klant->getExactOnlineMetadata('id') : null,
			"Code" => $project->projectnr,
			"Description" => $project->projectnaam,
			"Type" => 3 // 3 = Time and Material,
		];

		$this->setCurlData($data);

		$this->setCurlUrl("$this->division/project/Projects");
		$this->setCurlMethod('POST');
		if($project->getExactOnlineMetadata('id')){
			$this->setCurlMethod('PUT');
			$this->setCurlUrl("$this->division/project/Projects(guid'".$project->getExactOnlineMetadata('id')."')");
		}

		return $this->execCurl();
	}

	//Stored
	public function getStoredVat($options = []){
        $query = VAT::orderBy('percentage', 'ASC')->orderBy('incl', 'ASC');

        if(isset($options['division'])){
            $query = $query->where('division', $options['division']);
        }

		return $query->get();
	}
	public function getStoredGL($options = []){
        $query = GeneralLedger::orderBy('code', 'ASC');

        if(isset($options['division'])){
            $query = $query->where('division', $options['division']);
        }

		return $query->get();
	}
	public function getStoredItems($options = []){
        $query = Items::orderBy('code', 'ASC');

        if(isset($options['division'])){
            $query = $query->where('division', $options['division']);
        }

		return $query->get();
	}

}
