<?php

namespace App\Classes\imports\client83;

use App\ExplorerFiles;
use App\ImportData;
use App\Imports;
use App\Imports\ExcelImport;
use App\Klanten;
use App\Project;
use App\ProjectCustomRows;
use App\ProjectenCustomTablesRows;
use App\ProjectenCustomTablesRowsValues;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date;


class MiniContainment
{
  public function storeExcel($request){
    if(!$request->hasFile('file') || !$request->name){
      return $this->error('Vul alle verplichte velden in!');
    }

    $file = $request->file('file');
    $checkExists = ExplorerFiles::where('name', 'LIKE', '%_'.$file->getClientOriginalName())->first();
    if($checkExists){
      return $this->error('Bestand met deze naam is reeds geimporteerd!');
    }
    $file = ExplorerFiles::insertFromRequest([
      'file' => $file,
      'name' => randomString(5).'_'.$file->getClientOriginalName(),
      'view_name' => $request->name,
      'path' => "/imports",
      'description' => null,
    ]);

    $id = Imports::insertGetId([
      'name' => $request->name,
      'user_id' => User::id(),
      'template_id' => $request->template,
      'file_id' => $file->id,
    ]);

    return response()->json(
      [
        'success' => true,
        'message' => 'Bestand succesvol geimporteerd.',
        'src' => $file->src,
        'import_id' => $id,
      ], 200
    );
  }

  public function processExcel($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }
    $import = Imports::where('id', $request->import_id)->firstOrFail();
    $exfile = ExplorerFiles::where('id', $import->file_id)->firstOrFail();
    $filePath = 'explorer/files/' . $exfile->src;
    $fileContents = Storage::disk('client')->get($filePath);
    $tempPath = storage_path('app/temp.xlsx');

    file_put_contents($tempPath, $fileContents);
    $rows = Excel::toArray(new ExcelImport(), $tempPath);
    unlink($tempPath);
    $data = [];
    foreach ($rows[0] as $index => $row) {
      foreach ($row as $key => $value) {
        $rowdata = [
          'import_id' => $import->id,
          'index' => $index,
          'local_data' => 0,
        ];
        $rowdata['keyword'] = $key;
        $rowdata['value'] = $value;
        $data[] = $rowdata;
      }
    }
    $dupes = $this->checkDuplicates($data);
    if($dupes){
      return $dupes;
    }
    ImportData::insert($data);


    return response()->json(
      [
        'success' => true,
        'message' => 'Data succesvol verwerkt.',
      ], 200
    );
  }

  public function processProjecten(){
    $import_id = request('import_id');
    if(!$import_id){
      return $this->error('Import ID niet gevonden.');
    }
    $data = $this->getImportData($import_id);
    if(count($data) == 0){
      return $this->error('Geen data gevonden.');
    }
//    $dupes = $this->checkDuplicates($data);
//    if($dupes){
//      return $dupes;
//    }
    $custrowSet = getSettingJson('projecten_cusotm_rows');

    foreach($data as $row){

      $klantnaam = $row->where('keyword', 'opdrachtgever')->first();
      $trimmedklantnaam = strtolower(str_replace(' ', '', $klantnaam->value));
      $klant = Klanten::whereRaw("LOWER(REPLACE(naam, ' ', '')) = ?",[$trimmedklantnaam])->first();

      if(!$klant){
        $klantid = Klanten::insertGetId([
          'bv' => 69,
          'naam' => $klantnaam->value,
          'straat' => $row->where('keyword', 'straat')->first()->value ?? '',
          'huisnummer' => $row->where('keyword', 'huisnummer')->first()->value ?? '',
          'toevoeging' => $row->where('keyword', 'toevoeging')->first()->value ?? '',
          'postcode' => $row->where('keyword', 'postcode')->first()->value ?? '',
          'plaats' => $row->where('keyword', 'plaats')->first()->value ?? '',
        ]);

        $klant = Klanten::where('id', $klantid)->first();
      }

      $projectid = Project::insertGetId([
        'bv' => 69,
        'status' => 'Opdracht',
        'projectnr' => $row->where('keyword', 'projectnummer')->first()->value ?? '',
        'projectnaam' => $row->where('keyword', 'projectnaam')->first()->value ?? '',
        'adres' => $row->where('keyword', 'straat')->first()->value ?? '',
        'huisnummer' => $row->where('keyword', 'huisnummer')->first()->value ?? '',
        'toevoeging' => $row->where('keyword', 'toevoeging')->first()->value ?? '',
        'postcode' => $row->where('keyword', 'postcode')->first()->value ?? '',
        'woonplaats' => $row->where('keyword', 'plaats')->first()->value ?? '',
        'opdrachtgever' => $klant->naam ?? '',
        'klant_id' => $klant->id ?? '',
      ]);



      foreach($custrowSet as $set){
        $rowdata = $row->where('keyword', $set['keyword'])->first();

        if(substr($set['keyword'], 0,5) == 'naam_'){
          $trimmedusername = strtolower(str_replace(' ', '', $rowdata->value));
          $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();
          $rowdata->value = $user ? $user->id : '';
        }

        if(str_contains($set['keyword'], 'tijd')){
          $rowdata->value = Date::excelToDateTimeObject($rowdata->value)->format('H:i:s');
        }

        if(str_contains($set['keyword'], 'datum')){
          $rowdata->value = Date::excelToDateTimeObject($rowdata->value)->format('Y-m-d');
        }

        ProjectCustomRows::insert([
          'project_id' => $projectid,
          'keyword' => $set['keyword'],
          'name' => $set['name'],
          'value' => $rowdata ? $rowdata->value : '',
          'type' => $set['type'],
          'data' => json_encode($set['data']),
        ]);
      }

      $projecttableid = ProjectenCustomTablesRows::insertGetId([
        'project_id' => $projectid,
        'table_keyword' => 'asbest_bronnen'
      ]);

      ProjectenCustomTablesRowsValues::insert([
        'project_id' => $projectid,
        'table_row_id' => $projecttableid,
        'column_keyword' => 'te_saneren_bronnen_omschrijving',
        'value' => 'Asbestvilt'
      ]);
      ProjectenCustomTablesRowsValues::insert([
        'project_id' => $projectid,
        'table_row_id' => $projecttableid,
        'column_keyword' => 'te_saneren_bronnummers',
        'value' => $row->where('keyword', 'te_saneren_bronnummers')->first()->value ?? ''
      ]);
      ProjectenCustomTablesRowsValues::insert([
        'project_id' => $projectid,
        'table_row_id' => $projecttableid,
        'column_keyword' => 'vezelsoort_gehalte',
        'value' => $row->where('keyword', 'vezelsoort_en_gehalte')->first()->value ?? ''
      ]);
      ProjectenCustomTablesRowsValues::insert([
        'project_id' => $projectid,
        'table_row_id' => $projecttableid,
        'column_keyword' => 'omvang',
        'value' => '1 Stuks'
      ]);
      ProjectenCustomTablesRowsValues::insert([
        'project_id' => $projectid,
        'table_row_id' => $projecttableid,
        'column_keyword' => 'hechtgebondheid',
        'value' => 'Hechtgebonden'
      ]);
    }

    return response()->json(
      [
        'success' => true,
        'message' => 'Projecten succesvol verwerkt.',
      ], 200
    );

  }

  public function finishImport($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }

    $import = Imports::where('id', $request->import_id)->firstOrFail();
    $import->completed_at = Carbon::now();
    $import->save();

    return response()->json(
      [
        'success' => true,
        'message' => 'Import succesvol afgerond.',
      ], 200
    );
  }

  private function getImportData($import_id){
    return ImportData::where(['import_id' => $import_id, 'local_data' => 0])->get()->groupBy('index');
  }

  private function checkDuplicates($data){
    $import_id = $data[0]['import_id'];
    $data = groupBy($data, 'index');
    $serialized = [];
    $duplicates = [];

    foreach ($data as $index => $array) {
      foreach($array as $key => $value){
        unset($value['index']);
        unset($value['local_data']);
        unset($value['import_id']);
        $value['value'] = "$value[value]";
        $array[$key] = $value;
      }
      $data[$index] = $array;
      $key = serialize($array);

      if (isset($serialized[$key])) {
        $duplicates[] = ($serialized[$key] + 2) . " en " . ($index + 2);
      } else {
        $serialized[$key] = $index;
      }
    }
    if(count($duplicates) > 0){
      $duplicates = array_unique($duplicates);
      return $this->error('Dubbele rijen gevonden op rij(en): ' . implode(', ', array_reverse($duplicates)) . '.');
    }
    $project_id = ImportData::where(['import_id' => $import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();

    $prevImports = Imports::where('id', '!=', $import_id)->whereHas('data', function($q) use ($project_id){
      $q->where(['keyword' => 'project_id', 'value' => $project_id->value]);
    })->with('data')->get();

    foreach($prevImports as $prevImport){
      $prevdata = $prevImport->data->where('local_data', 0)->groupBy('index')->toArray();
      foreach ($prevdata as $index => $array) {
        foreach ($array as $key => $value) {
          unset($value['id']);
          unset($value['local_data']);
          unset($value['import_id']);
          unset($value['index']);
          unset($value['created_at']);
          unset($value['updated_at']);
          $array[$key] = $value;
        }
        $prevdata[$index] = $array;
      }
      $this->normalizeNumbers($prevdata);
      $this->normalizeNumbers($data);

      $duplicates = [];
      foreach ($prevdata as $index => $array) {
        if ($data[$index] == $array) {
          $dataindex = array_search($data[$index], $data);
          $duplicates[] = ($dataindex + 2) . ' en ' . ($index + 2);
        }
      }
      if (count($duplicates) > 0) {
        $duplicates = array_unique($duplicates);
        return $this->error('Dubbele rijen gevonden in import ' . $prevImport->name . ' op rij(en): ' . implode(', ', $duplicates) . '.');
      }
    }

    return false;
  }

  private function normalizeNumbers(&$array) {
    foreach ($array as &$item) {
      if (is_array($item)) {
        $this->normalizeNumbers($item);
      } elseif (is_numeric($item)) {
        $item = round((float)$item, 7);
      }
    }
  }

  private function error($message){
    return response()->json(
      [
        'success' => false,
        'message' => $message,
      ], 400
    );
  }
}
