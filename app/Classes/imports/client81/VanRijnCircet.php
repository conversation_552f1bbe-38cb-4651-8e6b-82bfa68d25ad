<?php

namespace App\Classes\imports\client81;

use App\ExplorerFiles;
use App\ImportData;
use App\Imports;
use App\Inkoopbonnen;
use App\InkoopbonnenRegels;
use App\Leveranciers;
use App\OffertesDatasets;
use App\Project;
use App\UrenRegistratie;
use App\User;


use Carbon\Carbon;
use Composer\DependencyResolver\Request;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use App\Imports\ExcelImport;
use Illuminate\Support\Facades\Storage;

class VanRijnCircet
{
  public function storeExcel($request){
    if(!$request->hasFile('file') || !$request->name || !$request->projectid){
      return $this->error('Vul alle verplichte velden in!');
    }

    $file = $request->file('file');
    $checkExists = ExplorerFiles::where('name', 'LIKE', '%_'.$file->getClientOriginalName())->first();
    if($checkExists){
      $checkExistImport = Imports::where('file_id', $checkExists->id)->where('template_id', $request->template)->first();
      if($checkExistImport){
        return $this->error('Bestand met deze naam is reeds geimporteerd!');
      }
    }
    $file = ExplorerFiles::insertFromRequest([
      'file' => $file,
      'name' => randomString(5).'_'.$file->getClientOriginalName(),
      'view_name' => $request->name,
      'path' => "/imports",
      'description' => null,
    ]);

    $id = Imports::insertGetId([
      'name' => $request->name,
      'user_id' => User::id(),
      'template_id' => $request->template,
      'file_id' => $file->id,
    ]);

    ImportData::insert([
      'import_id' => $id,
      'local_data' => 1,
      'keyword' => 'project_id',
      'value' => $request->projectid,
    ]);

    return response()->json(
      [
        'success' => true,
        'message' => 'Bestand succesvol geimporteerd.',
        'src' => $file->src,
        'import_id' => $id,
      ], 200
    );
  }

  public function storeCircetOrderPdf($request){
    if(!$request->hasFile('file')){
      return $this->error('Er is iets foutgegaan');
    }

    $file = $request->file('file');
    $file = ExplorerFiles::insertFromRequest([
      'file' => $file,
      'name' => randomString(5).'_'.$file->getClientOriginalName(),
      'path' => "/facturatie/custom",
      'description' => null,
    ]);

    $importdata = $this->getImportData($request->import_id);

    $projectid = ImportData::where(['import_id' => $request->import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first()->value;
    $project = Project::where('id', $projectid)->with('custom')->firstOrFail();
    $datasetName = $project->custom->where('keyword', 'dataset')->first()->value;
    $dataset = OffertesDatasets::where('naam', $datasetName)->with('items')->firstOrFail();

    return response()->json(
      [
        'success' => true,
        'message' => 'Bestand succesvol geupload.',
        'src' => $file->src,
        'import_data' => $importdata,
        'dataset' => $dataset,
      ], 200
    );
  }

  public function processExcel($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }
    $import = Imports::where('id', $request->import_id)->firstOrFail();
    $exfile = ExplorerFiles::where('id', $import->file_id)->firstOrFail();
    $filePath = 'explorer/files/' . $exfile->src;
    $fileContents = Storage::disk('client')->get($filePath);
    $tempPath = storage_path('app/temp.xlsx');

    file_put_contents($tempPath, $fileContents);
    $rows = Excel::toArray(new ExcelImport(), $tempPath);
    unlink($tempPath);
    $data = [];
    foreach ($rows[0] as $index => $row) {
      foreach ($row as $key => $value) {
        $rowdata = [
          'import_id' => $import->id,
          'index' => $index,
          'local_data' => 0,
        ];
        $rowdata['keyword'] = $key;
        $rowdata['value'] = $value;
        $data[] = $rowdata;
      }
    }
    $exceptions = $this->validateData($data);
    if($exceptions){
      $import->delete();
      return $exceptions;
    }
    ImportData::insert($data);

    $import->last_step = 'processExcel';
    $import->save();

    return response()->json(
      [
        'success' => true,
        'message' => 'Data succesvol verwerkt.',
      ], 200
    );
  }
  public function processUren($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }
    $data = $this->getImportData($request->import_id);
    $projectData = ImportData::where(['import_id' => $request->import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();
    $project = Project::where('id', $projectData->value)->with('custom')->firstOrFail();
    $datasetName = $project->custom->where('keyword', 'dataset')->first()->value;
    $dataset = OffertesDatasets::where('naam', $datasetName)->with('items')->firstOrFail();
    foreach($data as $row){
      $item = $row->where('keyword', 'item_number')->first();
      $datasetItem = $dataset->items->where('name', $item->value)->first();
      $itemVals = json_decode($datasetItem->value);

      $date = $row->where('keyword', 'actual_end')->first();
      $formattedDate = Date::excelToDateTimeObject($date->value)->format('Y-m-d');

      $username = $row->where('keyword', 'assigned_resource_resource_name')->first();
      $trimmedusername = strtolower(str_replace(' ', '', $username->value));
      $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();

      if (!$itemVals || !$user) {
        return $this->error('Geen item of gebruiker gevonden voor rij ' . ($row->index+2) . '.');
      }

      $uren = $itemVals->tijd / 60;
      $uren = $uren * $row->where('keyword', 'qty_actual')->first()->value;

      UrenRegistratie::insert([
        'projectnummer' => $project->projectnr,
        'medewerker_id' => $user->id,
        'datum' => $formattedDate,
        'gewerkte_uren' => $uren,
        'totaaluren100' => $uren,
        'begintijd_eindtijd' => 0,
        'opmerkingen' => $itemVals->naam,
      ]);


    }

    $import = Imports::where('id', $request->import_id)->update([
      'last_step' => 'processUren',
    ]);

    return response()->json(
      [
        'success' => true,
        'message' => 'Uren succesvol verwerkt.',
      ], 200
    );
  }
  public function processInkoop($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }
    $data = $this->getImportData($request->import_id);
    $projectData = ImportData::where(['import_id' => $request->import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();
    $project = Project::where('id', $projectData->value)->with('custom')->firstOrFail();
    $datasetName = $project->custom->where('keyword', 'dataset')->first()->value;
    $dataset = OffertesDatasets::where('naam', $datasetName)->with('items')->firstOrFail();
    $leveranciers = [];
    foreach($data as $row) {
      $item = $row->where('keyword', 'item_number')->first();
      $datasetItem = $dataset->items->where('name', $item->value)->first();
      $itemVals = json_decode($datasetItem->value);

      if (!$itemVals) {
        return $this->error('Geen item gevonden voor rij ' . ($row[0]->index + 2) . '.');
      }

      $username = $row->where('keyword', 'assigned_resource_resource_name')->first();
      $trimmedusername = strtolower(str_replace(' ', '', $username->value));
      $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();
      if(!$user->leverancier_id){continue;}
      if(!isset($leveranciers[$user->leverancier_id])){
        $leveranciers[$user->leverancier_id] = [];
      }

      if(isset($leveranciers[$user->leverancier_id][$datasetItem->id])){
        $leveranciers[$user->leverancier_id][$datasetItem->id]['item_uren'] += (($itemVals->tijd / 60) * $row->where('keyword', 'qty_actual')->first()->value);
      } else {
        $leveranciers[$user->leverancier_id][$datasetItem->id] = [];
        $leveranciers[$user->leverancier_id][$datasetItem->id]['item_uren'] = (($itemVals->tijd / 60) * $row->where('keyword', 'qty_actual')->first()->value);
        $leveranciers[$user->leverancier_id][$datasetItem->id]['item_name'] = $datasetItem->name;
        $leveranciers[$user->leverancier_id][$datasetItem->id]['item_name'] = $datasetItem->name;
        $leveranciers[$user->leverancier_id][$datasetItem->id]['item_price'] = $itemVals->inkoop;
        $leveranciers[$user->leverancier_id][$datasetItem->id]['row_index'] = $row[0]->index;
      }
    }

    $bonids = [];
    foreach($leveranciers as $key => $items){
      $leverancier = Leveranciers::where('id', $key)->first();
      $inkoopId = Inkoopbonnen::insertGetId([
        'bonnummer' => Inkoopbonnen::bonnummer(),
        'project_id' => $project->id,
        'leverancier_id' => $key,
        'user_id' => User::id(),
        'bv' => $leverancier->bv,
        'date' => Carbon::now(),
        'status' => 'Open',
        'token' => randomString(10),
        'custom_import_id' => $request->import_id,
      ]);

      foreach($items as $item){
        InkoopbonnenRegels::insert([
          'inkoopbon_id' => $inkoopId,
          'naam' => $item['item_name'],
          'prijs' => $item['item_price'],
          'aantal' => $item['item_uren'],
          'eenheid' => 'Uur',
          'btw' => 21,
          'incl' => 0,
          'custom_import_row_id' => $item['row_index'],
        ]);
      }
    }

    $import = Imports::where('id', $request->import_id)->update([
      'last_step' => 'processInkoop',
    ]);

    return response()->json(
      [
        'success' => true,
        'message' => 'Inkoopbonnen succesvol verwerkt.',
      ], 200
    );

  }

  public function finishImport($request){
    if(!$request->import_id){
      return $this->error('Import ID niet gevonden.');
    }

    $import = Imports::where('id', $request->import_id)->firstOrFail();
    $import->completed_at = Carbon::now();
    $import->last_step = 'finishImport';
    $import->save();

    return response()->json(
      [
        'success' => true,
        'message' => 'Import succesvol afgerond.',
      ], 200
    );
  }

  public static function inkoopMandagTableHtml($inkoopbon_id){
    $inkoopbon = Inkoopbonnen::where('id', $inkoopbon_id)->with('regels')->firstOrFail();

    $importData = ImportData::where('import_id', $inkoopbon->custom_import_id)->where('local_data', 0)->get()->groupBy('index');
    $projectData = ImportData::where(['import_id' => $inkoopbon->custom_import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();
    $project = Project::where('id', $projectData->value)->with('custom')->firstOrFail();
    $datasetName = $project->custom->where('keyword', 'dataset')->first()->value;
    $dataset = OffertesDatasets::where('naam', $datasetName)->with('items')->firstOrFail();

    $mandagregels = [];

    foreach($inkoopbon->regels as $regel) {
      $row = $importData[$regel->custom_import_row_id];

      $username = $row->where('keyword', 'assigned_resource_resource_name')->first();
      $itemname = $row->where('keyword', 'item_number')->first();
      $itemdate = $row->where('keyword', 'actual_end')->first();
      $itemdate = Date::excelToDateTimeObject($itemdate->value)->format('Y-m-d');
      if($regel->aantal > 1){
        foreach($importData as $subrow){
          $subusername = $subrow->where('keyword', 'assigned_resource_resource_name')->first();
          $trimmedusername = strtolower(str_replace(' ', '', $subusername->value));
          $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();

          $subitemname = $subrow->where('keyword', 'item_number')->first();
          $subitemdate = $subrow->where('keyword', 'actual_end')->first();
          $subitemdate = Date::excelToDateTimeObject($subitemdate->value)->format('Y-m-d');

          $item = $dataset->items->where('name', $subitemname->value)->first();
          $itemVals = json_decode($item->value);
          $tijd = (($itemVals->tijd / 60) * $subrow->where('keyword', 'qty_actual')->first()->value);

          if ($itemname->value != $subitemname->value || $subrow[0]->index == $row[0]->index){
            continue;
          }

          if (!isset($mandagregels[$subusername->value])) {
            $mandagregels[$subusername->value] = [];
            $mandagregels[$subusername->value]['bsn'] = $user->bsn;
          }
          if (!isset($mandagregels[$subusername->value][$subitemdate])) {
            $mandagregels[$subusername->value][$subitemdate] = 0;
          }
          $mandagregels[$subusername->value][$subitemdate] += $tijd;
        }
      }
      $trimmedusername = strtolower(str_replace(' ', '', $username->value));
      $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();

      $item = $row->where('keyword', 'item_number')->first();
      $datasetItem = $dataset->items->where('name', $item->value)->first();
      $itemVals = json_decode($datasetItem->value);
      $tijd = (($itemVals->tijd / 60) * $row->where('keyword', 'qty_actual')->first()->value);

      if (!isset($mandagregels[$username->value])) {
        $mandagregels[$username->value] = [];
        $mandagregels[$username->value]['bsn'] = $user->bsn;
      }
      if (!isset($mandagregels[$username->value][$itemdate])) {
        $mandagregels[$username->value][$itemdate] = 0;
      }
      $mandagregels[$username->value][$itemdate] += $tijd;
    }

    $alldates = [];
    foreach($mandagregels as $key => $row){
      foreach($row as $date => $value){
        if($date == 'bsn'){continue;}
        if(!in_array($date, $alldates)){
          $alldates[] = $date;
        }
      }
    }

    $html = '<table class="table table-striped table-bordered">';
    $html .= '<thead><tr><th>Naam</th><th>BSN</th>';
    foreach($alldates as $date){
      $html .= '<th>' . CarbonDmy($date) . '</th>';
    }
    $html .= '<th>Subtotaal</th></tr></thead><tbody>';
    $grandtotal = 0;
    foreach($mandagregels as $key => $row){
      $html .= '<tr>';
      $html .= '<td>' . $key . '</td>';
      $html .= '<td>' . $row['bsn'] . '</td>';
      $subtotal = 0;
      foreach($alldates as $date){
        if(isset($row[$date])){
          $html .= '<td>' . round($row[$date], 2) . '</td>';
          $subtotal += round($row[$date], 2);
          $grandtotal += round($row[$date], 2);
        } else {
          $html .= '<td></td>';
        }
      }
      $html .= '<td>' . round($subtotal, 2) . '</td>';
      $html .= '</tr>';
    }
    $html .= '</tbody>';
    $html .= '<tfoot><tr><th colspan="2">Totaal</th>';
    foreach($alldates as $date){
      $total = 0;
      foreach($mandagregels as $row){
        if(isset($row[$date])){
          $total += round($row[$date], 2);
        }
      }
      $html .= '<th>' . round($total, 2) . '</th>';
    }
    $html .= '<th>' . round($grandtotal, 2) . '</th>';
    $html .= '</tr></tfoot></table>';

    return $html;
  }

  private function getImportData($import_id, $local_data = false){
    $wheres = ['import_id' => $import_id];
    if(!$local_data){
      $wheres['local_data'] = 0;
    }
    return ImportData::where($wheres)->get()->groupBy('index');
  }

  private function validateData($data){
    $import_id = $data[0]['import_id'];
    $data = groupBy($data, 'index');

    $projectid = ImportData::where(['import_id' => $import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();
    $project = Project::where('id', $projectid->value)->with('custom')->firstOrFail();
    $datasetName = $project->custom->where('keyword', 'dataset')->first()->value;
    $dataset = OffertesDatasets::where('naam', $datasetName)->with('items')->firstOrFail();

    $serialized = [];
    $duplicates = [];
    $users = [];
    $items = [];
    $datums = [];

    foreach ($data as $index => $array) {
      foreach($array as $key => $value){

        if ($value['keyword'] == 'assigned_resource_resource_name'){
          $trimmedusername = strtolower(str_replace(' ', '', $value['value']));
          $user = User::whereRaw("LOWER(CONCAT(REPLACE(name, ' ', ''), REPLACE(lastname, ' ', ''))) = ?",[$trimmedusername])->first();
          if (!$user) {
            $users[] = $value['index'] + 2;
          }
        }

        if($value['keyword'] == 'item_number'){
          $item = $dataset->items->where('name', $value['value'])->first();
          if (!$item) {
            $items[] = $value['index'] + 2;
          }
        }

        if ($value['keyword'] == 'actual_end') {
          if (!$value['value']) {
            $datums[] = $value['index'] + 2;
          }
        }

        unset($value['index']);
        unset($value['local_data']);
        unset($value['import_id']);
        $value['value'] = "$value[value]";
        $array[$key] = $value;
      }
      $data[$index] = $array;
      $key = serialize($array);

      if (isset($serialized[$key])) {
        $duplicates[] = ($serialized[$key] + 2) . " en " . ($index + 2);
      } else {
        $serialized[$key] = $index;
      }
    }
    if(count($duplicates) > 0 || count($users) > 0 || count($items) > 0 || count($datums) > 0){
      $duplicates = array_unique($duplicates);
      $users = array_unique($users);
      $items = array_unique($items);
      $datums = array_unique($datums);

      $msg = '';
      if(count($duplicates) > 0){
        $msg .= 'regel ' . implode(', ', $duplicates) . (count($duplicates) > 1 ? ' zijn dubbele regels, ' : ' is een dubbele regel, ');
      }
      if(count($users) > 0){
        $msg .= 'regel ' . implode(', ', $users) . (count($users) > 1 ? ' hebben onbekende gebruikers, ' : ' heeft een onbekende gebruiker, ');
      }
      if(count($items) > 0){
        $msg .= 'regel ' . implode(', ', $items) . (count($items) > 1 ? ' hebben onbekende items, ' : ' heeft een onbekend item, ');
      }
      if(count($datums) > 0){
        $msg .= 'regel ' . implode(', ', $datums) . (count($datums) > 1 ? ' hebben geen datum, ' : ' heeft geen datum, ');
      }

      $msg = rtrim($msg, ', ');
      return $this->error('Fout in import: ' . $msg);
    }
    $project_id = ImportData::where(['import_id' => $import_id, 'local_data' => 1, 'keyword' => 'project_id'])->first();

    $prevImports = Imports::where('id', '!=', $import_id)->whereHas('data', function($q) use ($project_id){
      $q->where(['keyword' => 'project_id', 'value' => $project_id->value]);
    })->with('data')->get();

    if(!$prevImports){
      return false;
    }

    foreach($prevImports as $prevImport){
      $prevdata = $prevImport->data->where('local_data', 0)->groupBy('index')->toArray();
      foreach ($prevdata as $index => $array) {
        foreach ($array as $key => $value) {
          unset($value['id']);
          unset($value['local_data']);
          unset($value['import_id']);
          unset($value['index']);
          unset($value['created_at']);
          unset($value['updated_at']);
          $array[$key] = $value;
        }
        $prevdata[$index] = $array;
      }
      $this->normalizeNumbers($prevdata);
      $this->normalizeNumbers($data);

      $duplicates = [];
      foreach ($prevdata as $index => $array) {
        if ($data[$index] == $array) {
          $dataindex = array_search($data[$index], $data);
          $duplicates[] = ($dataindex + 2) . ' en ' . ($index + 2);
        }
      }
      if (count($duplicates) > 0) {
        $duplicates = array_unique($duplicates);
        return $this->error('Dubbele rijen gevonden in import ' . $prevImport->name . ' op rij(en): ' . implode(', ', $duplicates) . '.');
      }
    }

    return false;
  }

  public function status($import_id){
    $import = Imports::where('id', $import_id)->firstOrFail();
    switch($import->last_step){
      case 'processExcel':
        $step = 1;
        $tekst = 'Uren Verwerken';
        break;
      case 'processUren':
        $step = 2;
        $tekst = 'Inkoopbonnen Verwerken';
        break;
      case 'processInkoop':
        $step = 3;
        $tekst = 'Import Afronden';
        break;
      case 'finishImport':
        $step = 4;
        $tekst = 'Import Voltooid';
        break;
      default:
        $step = 0;
        $tekst = 'Bestand Verwerken';
    }

    return
      [
        'success' => true,
        'max_steps' => 4,
        'step' => $step,
        'text' => $tekst,
      ];


  }

  private function normalizeNumbers(&$array) {
    foreach ($array as &$item) {
      if (is_array($item)) {
        $this->normalizeNumbers($item);
      } elseif (is_numeric($item)) {
        $item = round((float)$item, 7);
      }
    }
  }

  private function error($message){
    return response()->json(
      [
        'success' => false,
        'message' => $message,
      ], 400
    );
  }
}
