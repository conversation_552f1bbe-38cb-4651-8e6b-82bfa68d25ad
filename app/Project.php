<?php

namespace App;

use App\ExactOnline\Metadata as EOMetadata;
use App\Http\Controllers\Api\ExplorerController AS APIExplorerController;
use Illuminate\Database\Eloquent\Model;

use Auth;
use Carbon\Carbon;

class Project extends Model {
  protected $table = 'projecten';
  protected $fillable = ['code', 'active', 'opdrachtgever', 'projectnr', 'projectnaam', 'adres', 'woonplaats', 'projectleider', 'reis'];

  function taken(){
    return $this->hasMany(ProjectTaken::class, 'project_id', 'id')->with("planning", "custom", "subtaken")->where('active', 1)->orderBy('created_at', "DESC")->orderBy("id", "DESC");
  }
  function completed_taken(){
    return $this->hasMany(ProjectTaken::class, 'project_id', 'id')->with("planning", "custom")->where(['active' => 1, 'completed' => 1])->orderBy('created_at', "DESC");
  }
  function uncompleted_taken(){
    return $this->hasMany(ProjectTaken::class, 'project_id', 'id')->with("planning", "custom")->where(['active' => 1, 'completed' => 0])->orderBy('created_at', "DESC");
  }

  function offerte() {
    return $this->hasOne(Offertes::class, "offertenummer", "projectnr")->with("template", "keywords", "regels", "details", "offerteplanning", "details");
  }
  function offertes(){
    return $this->hasMany(Offertes::class, 'project_id', 'id')->where(['type' => 1, 'meerwerk' => 0])->with('details')->orderBy('created_at', 'DESC');
  }
  function meerwerk_offertes(){
    return $this->hasMany(Offertes::class, 'project_id', 'id')->where(['type' => 1, 'meerwerk' => 1])->with('details')->orderBy('created_at', 'DESC');
  }
  function allOffertes(){
    return $this->hasMany(Offertes::class, 'project_id', 'id')->where(["active" => 1, "type" => 1])->with("template", "keywords", "regels", "details", "offerteplanning", "details");
  }

  function Uren() {
    return $this->hasMany(UrenRegistratie::class, 'projectnummer', "projectnr")->with('user.role.tarief', 'facturabelopties', 'machines', 'machineuren', 'uursoort');
  }
  function klant(){
    return $this->hasOne(Klanten::class, "id", "klant_id")->with('contactpersonen');
  }
  function werkbon() {
    return $this->hasOne(Werkbonnen::class, "project_id", "id")->with("values");
  }
  function werkbonnen(){
    return $this->hasMany(Werkbonnen::class, 'project_id', 'id')->orderBy('created_at', 'DESC')->with('template', 'keywords');
  }
  function _bv() {
    return $this->hasOne(BV::class, "id", "bv");
  }
  function contactpersoon(){
    return $this->hasOne(KlantenContactpersonen::class, "id", "contactpersoon_id");
  }
  function planning(){
    return $this->hasMany(Planning::class, 'project_id', 'id')->orderBy('datum', 'DESC')->orderBy('begin', 'DESC');
  }
  function custom(){
    return $this->hasMany(ProjectCustomRows::class, 'project_id', 'id');
  }
  function vestiging(){
    return $this->hasOne(Vestigingen::class, "id", "vestiging_id")->with('managers', 'rayon_managers');
  }
  function manager(){
    return $this->hasOne(User::class, 'id', 'manager_id');
  }
  function inkoopfacturen(){
    return $this->hasMany(Inkoopfacturen::class, 'project_id', 'id')->with('pdf', 'regels');
  }
  function opdrachtbon(){
    return $this->hasOne(Opdrachtbonnen::class, 'project_id', 'id')->with('termijnen');
  }
  function custom_tables_rows(){
    return $this->hasMany(ProjectenCustomTablesRows::class, 'project_id', 'id')->with('values');
  }


  public function locatie(){
    return $this->hasOneThrough(KlantenLocaties::class, ProjectenLocaties::class, 'project_id', 'id', 'id', 'locatie_id');
  }
  public function locatieContactpersoon(){
    return $this->hasOneThrough(
      KlantenLocatieContactpersoon::class,
      ProjectenLocaties::class,
      'project_id',
      'id',
      'id',
      'locatie_contactpersoon_id'
    );
  }
  public function checklists()
  {
    return $this->hasMany(Checklists::class, 'project_id', 'id');
  }
  public function projectAdres(){
    $fullAdres = new \stdClass();
    $fullAdres->adres = ($this->adres ?? '') . " " . ($this->huisnummer ?? '') . " " . ($this->toevoeging ?? '');
    $fullAdres->postcode = ($this->postcode ?? '') . " " . ($this->woonplaats ?? '');
    return $fullAdres;
  }

  //Lite relations
  function offerte_lite() {
    return $this->hasOne(Offertes::class, "offertenummer", "projectnr");
  }
  function offertes_lite(){
    return $this->hasMany(Offertes::class, 'project_id', 'id')->where(['type' => 1, 'meerwerk' => 0])->orderBy('created_at', 'DESC');
  }
  function meerwerk_offertes_lite(){
    return $this->hasMany(Offertes::class, 'project_id', 'id')->where(['type' => 1, 'meerwerk' => 1])->orderBy('created_at', 'DESC');
  }
  function werkbonnen_lite() {
    return $this->hasMany(Werkbonnen::class, 'project_id', 'id')->orderBy('created_at', 'DESC');
  }
  function vestiging_lite(){
    return $this->hasOne(Vestigingen::class, "id", "vestiging_id");
  }
  function checklists_lite(){
      return $this->hasMany(Checklists::class, 'project_id', 'id')->where(['active' => 1])->orderBy('created_at', 'DESC');
  }

  public function facturen($relations = []){
    $facturen = Facturen::where('projecten', 'LIKE', '%"'.$this->id.'"%')->with($relations)->where('is_proforma', 0)->orderBy('created_at', 'DESC')->get();
    foreach($facturen as $factuur){
      $factuur->status = $factuur->status();
    }
    return $facturen;
  }
  public function proformas($relations = []){
    return Facturen::where('projecten', 'LIKE', '%"'.$this->id.'"%')->with($relations)->where('is_proforma', 1)->orderBy('created_at', 'DESC')->get();
  }
  public function openProformas($relations = []){
    return $this->proformas($relations)->whereIn('status', Facturen::stadiumStatussen('open'));
  }
  public function background(){
    if($this->status == 'Acquisitie'){ return 'secondary'; }
    elseif($this->status == 'Opdracht'){ return 'primary'; }
    elseif($this->status == 'afgerond' || !$this->active){ return 'success'; }

    return 'secondary';
  }
  public function _contact(){
    $contact = new \stdClass();
    $contact->voornaam = null;
    $contact->achternaam = null;
    $contact->functie = null;
    $contact->email = null;
    $contact->telefoon = null;
    $contact->mobiel = null;

    $contactCheck = KlantenContactpersonen::where('id', $this->contactpersoon_id)->first();
    if($contactCheck){
      $contact->voornaam = $contactCheck->voornaam;
      $contact->achternaam = $contactCheck->achternaam;
      $contact->functie = $contactCheck->functie;
      $contact->email = $contactCheck->email;
      $contact->telefoon = $contactCheck->telefoon;
      $contact->mobiel = $contactCheck->mobiel;
    }
    else{
      $klant = Klanten::where('id', $this->klant_id)->first();
      $contact->voornaam = $klant->contactpersoon_voornaam ?? null;
      $contact->achternaam = $klant->contactpersoon_achternaam ?? null;
      $contact->functie = $klant->contactpersoon_functie ?? null;
      $contact->email = $klant->contactpersoon_email ?? null;
      $contact->telefoon = $klant->contactpersoon_telefoon ?? null;
      $contact->mobiel = $klant->contactpersoon_mobiel ?? null;
    }

    return $contact;

  }
  public function offertebedrag(){
    $total = new \stdClass();

    $total->texcl = isset($this->offerte) ? $this->offerte->totaal()->excl : 0;
    $total->tincl = isset($this->offerte) ? $this->offerte->totaal()->incl : 0;
    $total->tbtw = isset($this->offerte) ? $this->offerte->totaal()->btw : 0;
    $total->excl = isset($this->offerte) ? $this->offerte->totaal()->excl : 0;
    $total->mwoff = 0;

    foreach($this->meerwerk_offertes ?? [] as $offerte){
      if($offerte->status != 'Akkoord'){ continue; }
      $total->mwoff += $offerte->totaal()->excl ?? 0;
      $total->texcl += $offerte->totaal()->excl ?? 0;
      $total->tincl += $offerte->totaal()->incl ?? 0;
      $total->tbtw += $offerte->totaal()->btw ?? 0;
    }

		$this->offerte_bedrag = $total;

    return $total;
  }
  public function proformabedrag(){
    $total = new \stdClass();

    $total->excl = 0;
    $total->incl = 0;
    $total->btw = 0;

    foreach($this->openProformas() ?? [] as $proforma){
      $total->excl += $proforma->totaal()->excl ?? 0;
      $total->incl += $proforma->totaal()->incl ?? 0;
      $total->btw += $proforma->totaal()->btw ?? 0;
    }

    return $total;
  }
  public function factuurBedrag(){
    $total = new \stdClass();

    $total->excl = 0;
    $total->incl = 0;
    $total->btw = 0;

    foreach($this->facturen ?? [] as $factuur){
	    $total->excl += $factuur->totaal()->excl;
	    $total->incl += $factuur->totaal()->incl;
	    $total->btw += $factuur->totaal()->btw;
    }

    return $total;
  }
  public function factuurBedragBetaald(){
    $total = new \stdClass();

    $total->excl = 0;
    $total->incl = 0;
    $total->btw = 0;

    foreach($this->facturen ?? [] as $factuur){
			if($factuur->status !== 'Betaald'){ continue; }

	    $total->excl = $factuur->totaal()->incl;
	    $total->incl = $factuur->totaal()->excl;
	    $total->btw = $factuur->totaal()->btw;
    }

    return $total;
  }
	public function explorerFiles(){
		$files = ExplorerFiles::where([
			'path' => "/Projecten/({$this->id})",
			'type' => 'file',
			'active' => 1,
		])->get();
		$controller = new APIExplorerController();

		$this->explorer_files = $files->map([$controller, 'mapFiles']);
		return $this->explorer_files;
	}

  public static function nummer($bv = null){
    try{
      $pre = getSettingValue('projectnummer_prefix');


      $bvprefixes = json_decode((getSettingValue('bv_projectnummer_prefixes') ?? '{}'), true);
      if($bv && isset($bvprefixes[$bv])){
        $pre = $bvprefixes[$bv];
      }

      $date = date('Y');
      if(getSettingValue('projecten_projectnr_date_format') == 'short'){ $date = date('y'); }
      if(getSettingValue('projecten_projectnr_date_format') == 'none'){ $date = ''; }

      $pre .= $date;

      $afterfixLength = getSettingValue('projectnummer_afterfix_length') ?? 5;

      $count = Project::where('projectnr', 'LIKE', $pre.'%')->count();
      $increase = getSettingValue('projecten_projectnr_synth_increase') ?? 0;

      return $pre.str_pad(($count + $increase) + 1, $afterfixLength,0, STR_PAD_LEFT);
    }
    catch (\Exception $e){
      actError($e);
      return 'Error_0x'.str_replace('#', '', randomHex());
    }
  }
  public static function appendMainOfferte($projecten){
    // Temp & meerwerk offertes are correlated by project_id, default offertes are correlacted using project-offertenummmer.
    // To create good overview the default type offerte must be appended to the offertes relation

    foreach ($projecten ?? [] as $project){
      if(isset($project->offerte) && !find('id', $project->offerte->id, $project->offertes)){ $project->offertes[] = $project->offerte; }
    }
    return $projecten;
  }

  public static function setData($projecten, $data){
    foreach ($data as $key){
      if($key == 'contacts'){ $projecten = Project::setContacts($projecten); }
      if($key == 'proformas'){ $projecten = Project::setProformas($projecten); }
    }
    return $projecten;
  }
  public static function setContacts($projecten){
    foreach ($projecten as $project){
      $project->_contact = $project->_contact();
    }
    return $projecten;
  }
  public static function setProformas($projecten){
    foreach ($projecten as $project){
      $project->proformas = Facturen::where('projecten', 'LIKE', '%"'.$project->id.'"%')->where('is_proforma', 1)->with('regels')->orderBy('created_at', 'DESC')->get();
    }
    return $projecten;
  }

  public static function createDefaultFolders($id, $projectNr = null)
  {
    $data = json_decode(getSettingValue('standaard_project_mappen') ?? '[]');
    foreach($data as $folder){
			ExplorerFiles::createFoldersByPath("/Projecten/({$id})/$folder->name");
    }
  }

	public function businessCentralCompanyId(){
		return $this->_bv->business_central_company_id ?? null;
	}

	//Exact Online
	public function setExactOnlineMetadata($key, $value){
		EOMetadata::set('projecten', $this->id, $key, $value);
	}
	public function getExactOnlineMetadata($key = null){
		if($key){ return EOMetadata::get('projecten', $this->id, $key); }

		$this->exact_online_metadata = EOMetadata::get('projecten', $this->id);
		return $this->exact_online_metadata;
	}
}
