<?php

namespace App;

use App\Classes\AccorderenGenerator;
use App\Classes\Icons;
use App\Classes\SVG;
use App\ExactGlobe\GeneralLedger;
use App\ExactGlobe\Journals;
use App\ExactOnline\Metadata as EOMetadata;
use App\Http\Controllers\FacturatieController;
use App\Mail\BlankMail;
use App\Mail\SendStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Underscore\Types\Number;

class Facturen extends Model{
  protected $table = "facturen";

    function regels(){
      return $this->hasMany(Factuurregels::class, "factuur_id", "id")->with("dataset", "detail", 'exact_btw', 'exact_ledger', 'custom_fields')->orderBy('order_index', 'ASC');
    }

    function klant(){
        return $this->hasOne(Klanten::class, "id", "klant_id")->with('contactpersonen', 'vestiging');
    }

    function BV(){
        return $this->hasOne("App\BV", "id", "bv");
    }

    function _bv(){
        return $this->hasOne(BV::class, "id", "bv");
    }

    function user(){
        return $this->hasOne("App\User", "id", "user_id");
    }

    function adres(){
        return $this->hasOne(FacturenAdres::class, "factuur_id", "id");
    }

    function xml_export(){
        return $this->hasOne(FacturenXml::class, "factuur_id", "id");
    }

    function sign_files(){
        return $this->hasMany(FacturatieKlantAttachments::class, 'factuur_id', 'id')->with('file');
    }

    function emails(){
        return $this->hasMany(FacturatieEmails::class, 'factuur_id', 'id')->orderBy('created_at', 'DESC')->where('is_reminder', 0);
    }

    function reminders(){
        return $this->hasMany(FacturatieEmails::class, 'factuur_id', 'id')->orderBy('created_at', 'DESC')->where('is_reminder', 1);
    }

    function all_emails(){
        return $this->hasMany(FacturatieEmails::class, 'factuur_id', 'id')->orderBy('created_at', 'DESC');
    }

    function files(){
        return $this->hasManyThrough(ExplorerFiles::class, FacturenFiles::class, "factuur_id", "id", "id", "file_id")->where(['active' => 1, 'type' => 'file']);
    }

    function credit_factuur(){
        return $this->hasOne(Facturen::class, 'credit_parent', 'id');
    }

    function credit_parent_factuur(){
        return $this->hasOne(Facturen::class, 'id', 'credit_parent');
    }

    function slice_facturen(){
        return $this->hasMany(Facturen::class, 'slice_parent', 'id');
    }

    function slice_parent_factuur(){
        return $this->hasOne(Facturen::class, 'id', 'slice_parent');
    }
    function project_taken(){
        return $this->hasManyThrough(ProjectTaken::class, FacturenProjectTaken::class, 'factuur_id', 'id', 'id', 'taak_id');
    }

    function exact_journal(){
        return $this->hasOne(Journals::class, 'journal_number', 'exact_globe_journal');
    }

    function abonnement(){
        return $this->hasOneThrough(Abonnementen::class, AbonnementFacturen::class, "factuur_id", "id", "id", "abonnement_id")->orderBy("id", "DESC");
    }

    function mandagen(){
        return $this->hasMany(MandagenregistersRegels::class, 'factuur_id', 'id')->with('user');
    }

    function groupedMandagen(){
        return groupByGroupBy($this->mandagen, 'datum', 'user_id');
    }

    public function timeline(){
        if($this->is_proforma){
            return $this->proformaTimeline();
        }

        return $this->factuurTimeline();
    }

    private function factuurTimeline(){
        $timeline = new \stdClass();

        $timeline->periods = [];

        $timeline->periods['uitgebracht'] = [
            'status' => 'Uitgebracht',
            'start' => Carbon::parse($this->created_at)->format('d-m-Y'),
            'end' => isset($this->sent_at) ? Carbon::parse($this->sent_at)->format('d-m-Y') :
                (isset($this->betaald_op) ? Carbon::parse($this->betaald_op)->format('d-m-Y') : Carbon::now()->format('d-m-Y')),
            'color' => 'secondary',
        ];
        if(isset($this->sent_at)){
            $timeline->periods['verzonden'] = [
                'status' => 'Verzonden',
                'start' => Carbon::parse($this->sent_at)->format('d-m-Y'),
                'end' => isset($this->betaald_op) ? Carbon::parse($this->betaald_op)->format('d-m-Y') : Carbon::now()->format('d-m-Y'),
                'color' => 'primary',
            ];
        }
        if(isset($this->betaald_op)){
            $timeline->periods['betaald'] = [
                'status' => 'Betaald',
                'start' => Carbon::parse($this->betaald_op)->format('d-m-Y'),
                'end' => Carbon::parse($this->betaald_op)->addDay()->format('d-m-Y'),
                'color' => 'success',
            ];
        }

        $timeline->total = 0;
        foreach($timeline->periods as $index => $period){
            $diff = Carbon::parse($period['start'])->diffInDays($period['end']);
            $timeline->periods[$index]['days'] = $diff;
            $timeline->total += $diff;
        }

        return $timeline;
    }

    private function proformaTimeline(){
        $timeline = new \stdClass();

        $timeline->periods['uitgebracht'] = [
            'status' => 'Uitgebracht',
            'start' => Carbon::parse($this->created_at)->format('d-m-Y'),
            'end' => isset($this->sent_at) ? Carbon::parse($this->sent_at)->format('d-m-Y') :
                (isset($this->proforma_accepted_at) ? Carbon::parse($this->proforma_accepted_at)->format('d-m-Y') : Carbon::now()->format('d-m-Y')),
            'color' => 'secondary',
        ];
        if(isset($this->sent_at)){
            $timeline->periods['verzonden'] = [
                'status' => 'Verzonden',
                'start' => Carbon::parse($this->sent_at)->format('d-m-Y'),
                'end' => isset($this->proforma_accepted_at) ? Carbon::parse($this->proforma_accepted_at)->format('d-m-Y') : Carbon::now()->format('d-m-Y'),
                'color' => 'primary',
            ];
        }
        if(isset($this->proforma_accepted_at)){
            $timeline->periods['akkoord'] = [
                'status' => $this->proforma_accepted === '1' ? 'Akkoord' : 'Afgewezen',
                'start' => Carbon::parse($this->proforma_accepted_at)->format('d-m-Y'),
                'end' => isset($this->proforma_gefactureerd_op) ? Carbon::parse($this->proforma_gefactureerd_op)->format('d-m-Y') : Carbon::now()->format('d-m-Y'),
                'color' => $this->proforma_accepted === '1' ? 'success' : 'danger',
            ];
        }
        if(isset($this->proforma_gefactureerd_op)){
            $timeline->periods['gefactureerd'] = [
                'status' => $this->status,
                'start' => Carbon::parse($this->proforma_gefactureerd_op)->format('d-m-Y'),
                'end' => Carbon::parse($this->proforma_gefactureerd_op)->addDay()->format('d-m-Y'),
                'color' => 'aqua',
            ];
        }

        $timeline->total = 0;
        foreach($timeline->periods as $index => $period){
            $diff = Carbon::parse($period['start'])->diffInDays($period['end']);
            $timeline->periods[$index]['days'] = $diff;
            $timeline->total += $diff;
        }

        return $timeline;
    }

    public function hasStatusInRange($start, $end, $status){

        foreach($this->timeline()->periods as $period){
            if($period['status'] != $status){
                continue;
            }

            if(dateRangesOverlap($start, $end, $period['start'], $period['end'])){
                return true;
            }
        }

        return false;
    }

    public function statusByDateRange($start, $end){
        $response = [];

        foreach($this->timeline()->periods as $period){
            if(dateRangesOverlap($start, $end, $period['start'], $period['end'])){
                $response[] = $period;
            }
        }

        return $response;
    }

    public static function stadiumStatussen($stadium){
        switch(strtolower($stadium)){
            case 'afgerond':
                return ['Betaald', 'Akkoord', 'Afgewezen', 'Gefactureerd'];
            case 'verwijderd':
                return ['Verwijderd', 'Opgesplitst'];
            default:
                return ['Uitgebracht', 'Verzonden', 'Accorderen'];
        }
    }

    public function proformas(){
        return Facturen::whereIn('id', json_decode($this->proformas ?? '[]'))->get();
    }
    function factuur_explorer_files(){
        return ExplorerFiles::where('path', "/Facturen/({$this->id})")->get();
    }
    function factuur_files(){
        return $this->hasMany(FacturenFiles::class, 'factuur_id', 'id');
    }
    public function _proformas(){
      return $this->hasManyThrough(Facturen::class, FacturenProformas::class, 'factuur_id', 'id', 'id', 'proforma_id');
    }
    public function _offertes(){
      return $this->hasManyThrough(Offertes::class, FacturenOffertes::class, 'factuur_id', 'id', 'id', 'offerte_id');
    }
    public function offertesCouple(){
      return $this->hasMany(FacturenOffertes::class, 'factuur_id', 'id');
    }

    public function _projecten(){
      return $this->hasManyThrough(Project::class, FacturenProjecten::class, 'factuur_id', 'id', 'id', 'project_id')->with('manager', 'vestiging', 'klant', 'locatie', 'uren');
    }
    public function project(){
      return $this->hasOneThrough(Project::class, FacturenProjecten::class, 'factuur_id', 'id', 'id', 'project_id')->with('manager', 'vestiging', 'klant', 'locatie', 'uren');
    }
    public function ProjectenCouple(){
      return $this->hasMany(FacturenProjecten::class, 'factuur_id', 'id');
    }
    public function periodProjecten($start, $end)
    {
      return Project::whereIn('id', json_decode($this->_projecten->pluck('id') ?? '[]'))->with(['manager', 'vestiging', 'klant', 'locatie', 'uren' => function ($query) use ($start, $end) {$query->whereBetween('datum', [$start, $end]);}])->get();
    }

    public function _werkbonnen(){
      return $this->hasManyThrough(Werkbonnen::class, FacturenWerkbonnen::class, 'factuur_id', 'id', 'id', 'werkbon_id')->with('keywords', 'project.locatie');
    }
    public function werkbonnenCouple(){
      return $this->hasMany(FacturenWerkbonnen::class, 'factuur_id', 'id');
    }

    public function proformaParents(){
        return Facturen::where('proformas', 'LIKE', '%"' . $this->id . '"%')->get();
    }

    public function accorderen(){
        $status = AccorderenFacturen::where('factuur_id', $this->id)->with('user')->first();
        $historyFlow = AccorderenFacturen::where('factuur_id', "-$this->id")->orderBy('created_at', 'DESC')->get();

        return AccorderenGenerator::instance([
            'status' => $status,
            'historyFlow' => $historyFlow,
            'flowSetting' => intval($this->is_proforma) ? 'accorderen_flow_proformas' : 'accorderen_flow_facturen',
        ]);
    }

    public function accorderenStatus($decision){
        $status = AccorderenFacturen::where('factuur_id', $this->id)->first();
        $data = AccorderenGenerator::setProcesDecision($status->process, $decision);

        AccorderenFacturen::where('factuur_id', $this->id)->update($data);
    }

    public function status(){
        switch(strtolower($this->status)){
            case 'betaald':
                return getSettingValue('facturatie_alt_betaald_text', $this->status);
            default:
                return $this->status;
        }
    }

    public function stadium(){
        switch(strtolower($this->status)){
            case 'betaald':
            case 'akkoord':
            case 'afgewezen':
            case 'gefactureerd':
                return 'Afgerond';
            case 'verwijderd':
            case 'opgesplitst':
                return 'Verwijderd';
            default:
                return 'Open';
        }
    }

    public function color(){
        if($this->status == 'Betaald' || $this->status == 'Akkoord'){
            return 'success';
        }
        elseif($this->status == 'Verwijderd' || $this->status == 'Opgesplitst'){
            return 'dark';
        }
        elseif($this->status == 'Afgewezen'){
            return 'danger';
        }
        elseif(strContains('Verzonden', $this->status)){
            return 'primary';
        }
        elseif($this->status == 'Accorderen' && (!$this->accorderen()->failed && !$this->accorderen()->succeeded)){
            return 'inverse-info';
        }
        elseif($this->status == 'Accorderen'){
            return 'info';
        }
        elseif($this->status == 'Gefactureerd'){
            return 'aqua';
        }

        return 'secondary';
    }

    public function totaal(){
        $tot = new \stdClass();

        $tot->incl = 0;
        $tot->excl = 0;
        $tot->btw = 0;

        $regels = Factuurregels::where('factuur_id', $this->id)->get();
        foreach($regels as $row){
            $total = $row->prijs * $row->aantal;
            $perc = $row->btw;

            $tot->excl += number_format($row->incl === '1' ? ($total / (100 + $perc) * 100) : $total, 2, '.', '');
            $tot->btw  += number_format($row->incl === '1' ? ($total / (100 + $perc) * $perc) : ($total / 100 * $perc), 2, '.', '');
        }

        $tot->incl = number_format($tot->excl + $tot->btw, 2, '.', '');

        if($this->btw_verlegd){
            $tot->incl = $tot->excl;
            $tot->btw = 0;
        }

        return $tot;
    }

    public function totalGroupedByPerc(){
        $percs = [];
        $regels = Factuurregels::where('factuur_id', $this->id)->get();

        foreach($regels as $row){
            $btw_bedrag = 0;

            if(!isset($percs[$row->btw])){
                $percs[$row->btw] = [
                    'total_price_incl' => 0,
                    'total_price_excl' => 0,
                    'btw_bedrag' => 0,
                ];
            }

            $total_price_incl = $row->incl() * $row->aantal;
            $total_price_excl = $row->excl() * $row->aantal;

            $btw_bedrag = $total_price_incl - $total_price_excl;

            $percs[$row->btw]['total_price_incl'] += $total_price_incl;
            $percs[$row->btw]['total_price_excl'] += $total_price_excl;
            $percs[$row->btw]['btw_bedrag'] += $btw_bedrag;
        }

        foreach($percs as $perc => $data){
            if($this->btw_verlegd){
                $percs[$perc]['total_price_incl'] += $data['total_price_excl'];
                $percs[$perc]['btw_bedrag'] = 0;
            }
        }

        return $percs;
    }

    public function groupedBtws(){
        $btws = [];
        $regels = Factuurregels::where('factuur_id', $this->id)->with('eb_btw')->get();
        foreach($regels as $row){
            $perc = $row->btw;
            $desc = $perc;

            //exact_online btw
            if(exactOnlineLocal()->connected){
                $perc = $row->exact_online_btw()->percentage ?? $perc;
                $desc = $row->exact_online_btw()->description ?? $perc;
            }

            //eboekhouden
            if(isEboekhouden()){
                $perc = $row->eb_btw->percentage ?? $perc;
                $desc = $row->eb_btw->verlegd ? $row->eb_btw->omschrijving : $row->eb_btw->percentage;
            }

            if(!isset($btws[$desc])){
                $btws[$desc] = new \stdClass();
                $btws[$desc]->total_price_incl = 0;
                $btws[$desc]->total_price_excl = 0;
                $btws[$desc]->btw_bedrag = 0;
            }

            $btws[$desc]->total_price_incl += $row->totaal()->incl;
            $btws[$desc]->total_price_excl += $row->totaal()->excl;
            $btws[$desc]->btw_bedrag += $row->totaal()->btw;
        }
        return $btws;
    }

    public function totaalPerRekening(){
        $tot = new \stdClass();

        $tot->grekeningPercentage = $this->g_rekening_waarde ?? 0;
        $tot->bedrijfsrekeningPercentage = 100 - $tot->grekeningPercentage;

        $tot->grekeningBedrag = ($this->totaal()->incl / 100) * $tot->grekeningPercentage;
        $tot->bedrijfsrekeningBedrag = ($this->totaal()->incl / 100) * $tot->bedrijfsrekeningPercentage;

        return $tot;
    }

    public function firstProject($key = null){
      $project = $this->_projecten->first();
      if($key){
        return $project[$key];
      }
      return $project;
    }

    public function firstOfferte($key = null){
        $ids = json_decode($this->offertes ?? '[]');

        if(!count($ids)){
            return null;
        }

        $offerte = Offertes::find(firstValue($ids));

        if($key && $offerte){
            return $offerte[$key];
        }
        return $offerte;
    }

    public function isEditable(){
        if($this->stadium() == 'Afgerond'){
            return false;
        }
        else if($this->stadium() == 'Verwijderd'){
            return false;
        }
        else if($this->status == 'Verzonden' && !$this->is_proforma){
            return false;
        }

        return true;
    }

    public function hasProject($id){
        $ids = json_decode($this->projecten ?? '[]');
        return in_array($id, $ids);
    }

    public function notifProforma(){
        try{
            $setting = getSettingValue('facturatie_proforma_status_change_notifications');
            $options = json_decode(($setting ?? '[]'), true);


            $emails = [];
            $tokens = [];

            //Set array key instead of value to avoid double entries
            foreach($options['email'] ?? [] as $email){
                $emails[] = $email ?? null;
            }
            foreach($options['role'] ?? [] as $r){
                $role = Role::where('id', $r)->with('users')->first();
                foreach($role->users ?? [] as $user){
                    $emails[] = $user->email ?? null;
                    foreach($user->devices as $device){
                        $tokens[] = $device->device_id ?? 'no_token_given';
                    }
                }
            }
            foreach($options['user'] ?? [] as $i){
                $user = User::where('id', $i)->with('devices')->first();
                $emails[] = $user->email ?? null;
                foreach($user->devices ?? [] as $device){
                    $tokens[] = $device->device_id ?? 'no_token_given';
                }
            }
            foreach($options['anders'] ?? [] as $and){

            }

            $emails = array_unique($emails);
            $tokens = array_values(array_unique($tokens));


            $content = "<div style='margin: 20px 0;'>
                      <span>Status van de factuur: <b>" . $this->factuurnummer . "</b>&nbsp;is zojuist veranderd naar&nbsp;<b>" . ($this->proforma_accepted === '1' ? 'akkoord' : 'afgewezen') . "</b>.</span>
                    </div>
                    <div>
                      <table style='width: 100%; border-collapse: collapse;' >
                        <tbody>
                          <tr>
                            <th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Factuurnummer</th>
                            <td style='border: 1px solid #dee2e6; padding: 5px;' >" . $this->factuurnummer . "</td>
                          </tr>
                          <tr>
                            <th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Klant</th>
                            <td style='border: 1px solid #dee2e6; padding: 5px;' >" . ($this->klant->naam ?? '') . "</td>
                          </tr>
                          <tr>
                            <th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Contactpersoon</th>
                            <td style='border: 1px solid #dee2e6; padding: 5px;' >" . $this->klant->contactpersoon_voornaam . " " . $this->klant->contactpersoon_achternaam . "</td>
                          </tr>
                          <tr>
                            <th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>" . ($this->proforma_accepted === '1' ? 'Geaccordeerd' : 'Afgewezen') . "&nbsp;om</th>
                            <td style='border: 1px solid #dee2e6; padding: 5px;' >" . Carbon()->now()->format('d-m-Y H:i') . "</td>
                          </tr>
                          <tr>
                            <th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>PDF</th>
                            <td style='border: 1px solid #dee2e6; padding: 5px;' ><a href='" . url('/facturatie/facturen/open/' . $this->token) . "' >" . getSubdomein() . ".ikbentessa.nl</a></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>";

            foreach($emails as $email){
                if(!verifyEmail($email)){
                    continue;
                }
                Mail::to($email)->send(new BlankMail($this->_bv ?? firstBv(), '<EMAIL>', ($this->_bv->name ?? firstBv()->name), 'Proforma status verandering', $content));
            }
            pushToAll("Proforma factuur " . $this->factuurnummer . " status verandering", "Status van de factuur: $this->factuurnummer is zojuist veranderd naar " . ($this->proforma_accepted === '1' ? 'akkoord.' : 'afgewezen.'), $tokens);
        } catch(\Exception $e){
            actError($e);
        }
    }

    public function cloneFactuur($status = 'Uitgebracht'){
        try{
            $factuur = $this;
            $fclone = $factuur->replicate();
            $fclone->token = randomString(25);
            $fclone->is_proforma = 0;
            $fclone->factuurnummer = null;
            $fclone->sent_at = null;
            $fclone->sent_to = null;
            $fclone->mail_geopend = null;
            $fclone->exact_globe_journal = getSettingValue('facturatie_exact_globe_default_journal') ?? null;
            $fclone->datum = date('Y-m-d');
            $fclone->betalingstermijn = Carbon::parse(date('Y-m-d'))->addDays(getSettingValue("factuur_betalingstermijn") ?? 30)->format('Y-m-d');
            $fclone->reporting_date = Carbon::now()->addDays(getSettingValue('facturatie_factuur_reporting_date_default') ?? 0);
            $fclone->status = $status;

            if($factuur->is_proforma){
                $factuur->proforma_gefactureerd_op = Carbon::now();
                $factuur->proforma_gefactureerd = 1;
                $factuur->status = 'Gefactureerd';
                $factuur->save();

                $proformas = json_decode($fclone->proformas);
                foreach($proformas as $p){
                  FacturenProformas::insert([
                    'factuur_id' => $fclone->id,
                    'proforma_id' => $p,
                  ]);
                }
            }

            $fclone->setFactuurnummer();
            $fclone->save();

            if($status == 'Accorderen'){
                AccorderenFacturen::insert([
                    "factuur_id" => $fclone->id,
                    "process" => getSettingValue('accorderen_flow_facturen'),
                    "status" => null,
                    "date" => Carbon::now(),
                    "user_id" => $fclone->user_id,
                ]);
                Facturen::where('id', $fclone->id)->update([
                    'status' => 'Accorderen',
                ]);
            }

            $relations = [$factuur->regels, $factuur->adres, $factuur->projectenCouple, $factuur->offertesCouple, $factuur->werkbonnenCouple];
            foreach($relations as $relation){

                if($relation instanceof Collection){
                    foreach($relation as $row){
                        if(empty($row)){
                            continue;
                        }

                        $rclone = $row->replicate();
                        $rclone->factuur_id = $fclone->id;
                        $rclone->save();
                    }
                    continue;
                }

                if(!$relation){
                    continue;
                }

                $rclone = $relation->replicate();
                $rclone->factuur_id = $fclone->id;
                $rclone->save();
            }
            $fclone->refresh();

            JobQueue::addJob([
                'url' => 'api/facturatie/generatepdf',
                'instance_id' => $fclone->id,
            ]);

            return $fclone;
        } catch(\Exception $e){
            actError($e);
        }
    }

    public function setFactuurnummer($options = []){
        try{
            if($this->factuurnummer){
                return;
            }

            $prefix = $options['prefix'] ?? null;
            $force = $options['force'] ?? false;
            $definitief_status = getSettingValue('factuur_definitief', 'Uitgebracht');

            if(!$force && $definitief_status !== $this->status){
                return;
            }

            $domainFunction = 'factuurnummer' . getSubdomein();

            if(method_exists(__CLASS__, $domainFunction)){
                $instance = $this->{$domainFunction}();
            }
            else{
                $instance = Facturen::factuurnummer(['prefix' => $prefix, 'bv' => $this->bv]);
            }

            if(!isset($instance->factuurnummer)){
                abort(500, 'Factuurnummer kon niet worden gegenereerd.');
            }

            $this->factuurnummer = $instance->factuurnummer;
            $this->factuurnummer_prefix = $instance->prefix;
            $this->factuurnummer_afterfix = $instance->afterfix;
            $this->factuurnummer_jaar = $instance->date;
            $this->save();
            $this->setDates();
        }catch(\Exception $e){actError($e);}
    }

    public function setDates(){
        $termijn = Carbon::parse($this->datum)->diffInDays(Carbon::parse($this->betalingstermijn)) ?? 30;

        $this->datum = Carbon::now();
        $this->betalingstermijn = Carbon::now()->addDays($termijn);
        $this->reporting_date = Carbon::now()->addDays(getSettingValue('facturatie_factuur_reporting_date_default') ?? 0);
        $this->save();
    }

    public function businessCentralCompanyId(){
        return $this->BV->business_central_company_id ?? null;
    }

    public function shortenFactuurnummerDate(){
        if(strlen($this->factuurnummer_jaar) >= 4){
            return substr($this->factuurnummer, 2);
        }

        return $this->factuurnummer;
    }

    public static function proformanummer($options = []){

        $bv = $options['bv'] ?? firstBv();
        $project = $options['project'] ?? null;
        $offertes = $options['offertes'] ?? [];

        $length = getSettingValue("factuur_factuurnummer_afterfix_length", 4);

        $response = new \stdClass();
        $response->prefix = '';
        $response->afterfix = 0;
        $response->date = 0;
        $response->factuurnummer = '';

        try{
            if(isset($project)){
                $project = Project::where('id', $project)->first();
                $nummer = "PF-$project->projectnr";
                $count = Facturen::where('factuurnummer', 'LIKE', "$nummer%")->count();

                $response->factuurnummer = $nummer . "-" . str_pad(($count + 1), $length, '0', STR_PAD_LEFT);
                return $response;
            }
            elseif(isset($offertes[0])){
                $offerte = Offertes::where('id', $offertes[0])->first();
                $nummer = "PF-$offerte->offertenummer";
                $count = Facturen::where('factuurnummer', 'LIKE', "$nummer%")->count();

                $response->factuurnummer = $nummer . "-" . str_pad(($count + 1), $length, '0', STR_PAD_LEFT);
                return $response;
            }
            else{
                return Facturen::factuurnummer(['prefix' => 'PF', 'bv' => $bv]);
            }
        } catch(\Exception $e){
            actError($e);
            return Facturen::factuurnummer(['prefix' => 'PF', 'bv' => $bv]);
        }
    }

    public static function factuurnummer($options){
        $response = new \stdClass();

        $afterfix = 0;
        $bv = $options['bv'] ?? firstBv();
        $prefix = $options['prefix'] ?? getSettingValue('factuur_factuurnummer_prefix', '');
        $length = getSettingValue("factuur_factuurnummer_afterfix_length", 4);

        switch(getSettingValue('factuur_factuurnummer_datum_format')){
            case 'long':
                $date = date('Y');
                break;
            case 'long-':
                $date = date('Y') . '-';
                break;
            case 'short':
                $date = date('y');
                break;
            case 'short-':
                $date = date('y') . '-';
                break;
            default :
                $date = '';
                break;
        }

        $last_factuur = Facturen::where([
            'factuurnummer_prefix' => $prefix,
            'factuurnummer_jaar' => $date,
            'bv' => $bv
        ])->orderby("factuurnummer_afterfix", 'DESC')->first();

        if($last_factuur){
            $afterfix = intval($last_factuur->factuurnummer_afterfix);
        }

        $afterfix++;
        $factuurnummer = $prefix . $date . str_pad($afterfix, $length, '0', STR_PAD_LEFT);

        $response->prefix = $prefix;
        $response->afterfix = $afterfix;
        $response->date = $date;
        $response->factuurnummer = $factuurnummer;

        return $response;
    }

    public function factuurnummerhaltronic(){
        $klant = $this->klant;

        $klantData = [
            'debiteurnummer' => $klant->debiteurnummer ?? null,
            'naam' => $klant->naam ?? null,
            'straat' => $klant->straat ?? null,
            'huisnummer' => $klant->huisnummer ?? null,
            'postcode' => $klant->postcode ?? null,
            'plaats' => $klant->plaats ?? null,
            'land' => $klant->land ?? null,
            'factuuradres' => null,
            'uurtarief' => null,
            'btwtarief' => $klant->btw_perc ?? null,
            'betalingstermijn' => $klant->betalingstermijn ?? null,
        ];

        $teStortenPerc = floatval($this->overboeken_voorwaarden_waarde ?? 0) - floatval($this->g_rekening_waarde ?? 0);

        $data = [
            'klant' => $klantData,
            'factuuradres' => ($this->adres->straat . " " . $this->adres->huisnummer . $this->adres->toevoeging . " " . $this->adres->postcode . " " . $this->adres->plaats) ?? null,
            'factuurdatum' => $this->datum ?? null,
            'betalingstermijn' => $klant->betalingstermijn ?? null,
            'btwtarief' => $klant->btw_perc ?? null,
            'btwverlegd' => $klant->btw_verlegd ?? 0,
            'loonkostenbestanddeel' => $this->g_rekening_waarde ?? null,
            'testortenrekeningnummer' => $this->overboeken_voorwaarden_waarde ?? null,
            'testortenpercentage' => $teStortenPerc,
            'opmerkingen' => '',
            'verzondenop' => $this->sent_at ?? null,
            'emailadres' => $this->sent_to ?? null,
            'token' => $this->token ?? null,
            'bedragen' => $this->groupedBtws(),
        ];

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://www.mijnhaltronic.nl/api/insertTessaFactuur",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POSTFIELDS => ['data' => json_encode($data)],
        ));

        $curlResponse = json_decode(curl_exec($curl));
        curl_close($curl);

        $response = new \StdClass();
        $response->prefix = null;
        $response->afterfix = null;
        $response->date = null;
        $response->factuurnummer = $curlResponse->factuurnummer;

        return $response;
    }

    //Physical files by checkusm
    public function storeAsFile(){
        try{
            $checksum = $this->getTimeChecksum();
            if($checksum === null){
                return null;
            }

            if(!$this->hasChecksumFile()){
                $pdf = FacturatieController::generatePdf($this->id);
                Storage::disk('client')->put($this->getChecksumFileSrc(), $pdf->output());
            }

            if($this->hasChecksumFile()){
                return $this->getChecksumFileSrc();
            }
            return null;
        } catch(\Exception $e){
            actError($e);
            return null;
        }
    }

    public function getTimeChecksum(){
        try{
            if(isset($this->__checksum)){
                return $this->__checksum;
            }

            $checksum = Carbon::parse($this->updated_at ?? $this->created_at)->timestamp;
            $classes = [new Factuurregels(), new FacturenAdres(), new FacturenXml(), new FacturatieKlantAttachments(), new FacturatieEmails(), new FacturenFiles(), new AbonnementFacturen(), new AccorderenFacturen()];

            foreach($classes as $class){
                $records = $class->where('factuur_id', $this->id)->get();
                foreach($records as $record){
                    if(!$record->updated_at && !$record->created_at){
                        return "x";
                    }
                    $checksum += Carbon::parse($record->updated_at ?? $record->created_at)->timestamp;
                }
            }

            $this->__checksum = $checksum;
            return $checksum;
        } catch(\Exception $e){
            actError($e);
            return null;
        }
    }

    public function hasChecksumFile(){
        return Storage::disk('client')->exists($this->getChecksumFileSrc());
    }

    public function getChecksumFileSrc(){
        $checksum = $this->getTimeChecksum();
        return "/facturen/checksum/$this->id/$checksum.pdf";
    }

    public function slicePercs(){
        $percs = Facturen::where('slice_parent', $this->slice_parent)->orderBy('datum', 'ASC')->pluck('percentage')->toArray();
        return $percs;
    }

    public function termijnNr(){
        $factNrs = Facturen::where(['slice_parent' => $this->slice_parent])->orderBy('datum', 'ASC')->pluck('factuurnummer')->toArray();
        return array_search($this->factuurnummer, $factNrs) ?? "no_index";
    }

    //Exact Online
    public function setExactOnlineMetadata($key, $value){
        EOMetadata::set('facturen', $this->id, $key, $value);
    }

    public function getExactOnlineMetadata($key = null){
        if($key){
            return EOMetadata::get('facturen', $this->id, $key);
        }

        $this->exact_online_metadata = EOMetadata::get('facturen', $this->id);
        return $this->exact_online_metadata;
    }

    public function slice($data){
        foreach($data->percs ?? [] as $i => $perc){
            $betalingstermijn = $data->betalingstermijn ?? Carbon($perc['date'])->addDays($this->klant->betalingstermijn)->format("Y-m-d") ?? Carbon($perc['date'])->format("Y-m-d");

            $id = Facturen::insertGetId([
                "klant_id" => $this->klant_id,
                "user_id" => $this->user_id,
                "bv" => $this->bv,
                "status" => "Uitgebracht",
                "datum" => Carbon($perc['date'] ?? today())->format("Y-m-d"),
                "inleiding" => $this->inleiding,
                "slot" => $this->slot,
                "betalingstermijn" => $betalingstermijn,
                "referentie" => $this->referentie,
                "percentage" => $perc['perc'],
                "slice_parent" => $this->id,
                "token" => randomString(25),
                "created_at" => Carbon::now(),
                "factuurnummer" => null,
                "factuurnummer_prefix" => '',
                "factuurnummer_afterfix" => 0,
                "factuurnummer_jaar" => 0,
                "splitsing_naam" => $perc['name'] ?? '',
            ]);

            foreach($this->regels as $row){
                Factuurregels::insert([
                    "factuur_id" => $id,
                    "naam" => $row->naam,
                    "aantal" => $row->aantal,
                    "prijs" => $row->prijs * ($perc['perc'] / 100),
                    "btw" => $row->btw,
                    "incl" => $row->incl,
                    "created_at" => Carbon::now(),
                ]);
            }
            $this->copyRels($id);

        }
        $this->active = 0;
        $this->status = "Opgesplitst";
        $this->save();

        return $this;
    }

    public function copyRels($id){
        foreach($this->_offertes as $row){
            FacturenOffertes::insert([
                "factuur_id" => $id,
                "offerte_id" => $row->id,
                "created_at" => Carbon::now(),
            ]);
        }
        foreach($this->_projecten as $row){
            FacturenProjecten::insert([
                "factuur_id" => $id,
                "project_id" => $row->id,
                "created_at" => Carbon::now(),
            ]);
        }
        foreach($this->_werkbonnen as $row){
            FacturenWerkbonnen::insert([
                "factuur_id" => $id,
                "werkbon_id" => $row->id,
                "created_at" => Carbon::now(),
            ]);
        }
        foreach($this->_proformas as $row){
            FacturenProformas::insert([
                "factuur_id" => $id,
                "proforma_id" => $row->id,
                "created_at" => Carbon::now(),
            ]);
        }
    }

  public function refreshPdf()
  {
    $this->save();
    $this->storeAsFile();
  }

}
