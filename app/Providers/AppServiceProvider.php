<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider {

  public function boot() {
    Schema::defaultStringLength(191);
    \URL::forceScheme('https');
    Blade::directive('spinner', function() {return '<div class="spinner-border text-primary" role="status"><span class="sr-only"></span></div>';});
    Blade::directive('spinner_success', function() {return '<div class="spinner-border text-success" role="status"><span class="sr-only"></span></div>';});
    Blade::directive('spinner_large', function() {return '<div class="spinner-border text-primary" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div>';});
    Blade::directive('spinner_small', function() {return '<div class="spinner-border text-primary" role="status" style="width: 20px;height: 20px;font-size: .75rem;"><span class="sr-only"></span></div>';});

    Blade::directive('alert_post_error', function() {return '<div class="alert alert-danger my-0" >Er is iets foutgegaan</div>';});

    Blade::directive('url', function() {return url('/');});

    Blade::directive('icon_unlink', function() {return '<i class="fa-solid fa-link-slash m-0"></i>';});
    Blade::directive('icon_link', function() {return '<i class="fa-solid fa-link m-0"></i>';});
    Blade::directive('icon_plus', function() {return '<i class="fas fa-plus m-0"></i>';});
    Blade::directive('icon_minus', function() {return '<i class="fas fa-minus m-0"></i>';});
    Blade::directive('icon_trash', function() {return '<i class="far fa-trash-alt m-0"></i>';});
    Blade::directive('icon_edit', function() {return '<i class="far fa-edit m-0"></i>';});
    Blade::directive('icon_show', function() {return '<i class="fa-solid fa-eye m-0"></i>';});
    Blade::directive('icon_hide', function() {return '<i class="fa-solid fa-eye-slash m-0"></i>';});
    Blade::directive('icon_confirm', function() {return '<i class="fas fa-check m-0"></i>';});
    Blade::directive('icon_redo', function() {return '<i class="fas fa-redo-alt m-0"></i>';});
    Blade::directive('icon_toggle_vertical', function() {return '<i class="fas fa-arrows-alt-v m-0"></i>';});
    Blade::directive('icon_arrow_right', function() {return '<i class="fa-solid fa-arrow-right m-0"></i>';});
    Blade::directive('icon_arrow_left', function() {return '<i class="fa-solid fa-arrow-left m-0"></i>';});
    Blade::directive('icon_question', function() {return '<i class="far fa-question-circle m-0"></i>';});
    Blade::directive('icon_up', function() {return '<i class="fa fa-chevron-up transition-025 m-0"></i>';});
    Blade::directive('icon_down', function() {return '<i class="fa fa-chevron-down transition-025 m-0"></i>';});
    Blade::directive('icon_left', function() {return '<i class="fa fa-chevron-left transition-025 m-0"></i>';});
    Blade::directive('icon_right', function() {return '<i class="fa fa-chevron-right transition-025 m-0"></i>';});
    Blade::directive('icon_file', function() {return '<i class="fas fa-folder-open m-0"></i>';});
	  Blade::directive('icon_file_plus', function() {return '<i class="fas fa-folder-plus m-0"></i>';});
    Blade::directive('icon_download', function() {return '<i class="fas fa-download m-0"></i>';});
    Blade::directive('icon_cloud_download', function() {return '<i class="fa-solid fa-cloud-arrow-down m-0"></i>';});
    Blade::directive('icon_cloud', function() {return '<i class="fa fa-cloud m-0"></i>';});
    Blade::directive('icon_upload', function() {return '<i class="fa-solid fa-arrow-up-from-bracket m-0"></i>';});
    Blade::directive('icon_cloud_upload', function() {return '<i class="fa-solid fa-cloud-arrow-up m-0"></i>';});
    Blade::directive('icon_external_link', function() {return '<i class="fa-solid fa-arrow-up-right-from-square m-0"></i>';});
    Blade::directive('icon_close', function() {return '<i class="fa fa-xmark m-0"></i>';});
    Blade::directive('icon_zoom', function() {return '<i class="fa fa-magnifying-glass m-0"></i>';});
    Blade::directive('icon_list', function() {return '<i class="fa fa-list m-0"></i>';});
    Blade::directive('icon_bars', function() {return '<i class="fa fa-bars m-0"></i>';});
    Blade::directive('icon_check', function() {return '<i class="fa fa-check m-0"></i>';});
    Blade::directive('icon_expand', function() {return '<i class="bi bi-arrows-angle-expand m-0"></i>';});
    Blade::directive('icon_collapse', function() {return '<i class="bi bi-arrows-angle-contract m-0"></i>';});
    Blade::directive('icon_map_dot', function() {return '<i class="fa fa-map-location-dot m-0"></i>';});
    Blade::directive('icon_location_dot', function() {return '<i class="fa fa-location-dot m-0"></i>';});
    Blade::directive('icon_wrench', function() {return '<i class="fa fa-wrench m-0"></i>';});
    Blade::directive('icon_save', function() {return '<i class="fa fa-save m-0"></i>';});
    Blade::directive('icon_copy', function() {return '<i class="fa fa-copy m-0"></i>';});
    Blade::directive('icon_font', function() {return '<i class="fa-solid fa-font m-0"></i>';});
    Blade::directive('icon_graph', function() {return '<i class="fa fa-chart-column m-0"></i>';});
    Blade::directive('icon_img', function() {return '<i class="fa fa-image m-0"></i>';});
    Blade::directive('icon_color_fill', function() {return '<i class="fa fa-fill-drip m-0"></i>';});
    Blade::directive('icon_text', function() {return '<i class="fa fa-align-left m-0"></i>';});
    Blade::directive('icon_page_break', function() {return '<i class="fa fa-arrow-down-wide-short m-0"></i>';});
    Blade::directive('icon_shuffle', function() {return '<i class="fa fa-shuffle m-0"></i>';});
    Blade::directive('icon_random', function() {return '<i class="fa fa-dice m-0"></i>';});
    Blade::directive('icon_pencil', function() {return '<i class="fa fa-pen m-0"></i>';});
    Blade::directive('icon_comment', function() {return '<i class="fa fa-comment m-0"></i>';});
    Blade::directive('icon_comment_dots', function() {return '<i class="fa fa-comment-dots m-0"></i>';});
    Blade::directive('icon_signature', function() {return '<i class="fa fa-signature"></i>';});
    Blade::directive('icon_off', function() {return '<i class="fa-solid fa-power-off m-0"></i>';});
    Blade::directive('icon_calculator', function() {return '<i class="fa-solid fa-calculator m-0"></i>';});
    Blade::directive('icon_share', function() {return '<i class="fa-solid fa-share m-0"></i>';});
    Blade::directive('icon_grip_dots', function() {return '<i class="fa-solid fa-grip m-0"></i>';});
    Blade::directive('icon_exclamation', function() {return '<i class="fa-solid fa-exclamation m-0"></i>';});
    Blade::directive('icon_users', function() {return '<i class="fa-solid fa-users m-0"></i>';});
    Blade::directive('icon_house', function() {return '<i class="fa-solid fa-house-chimney m-0"></i>';});
    Blade::directive('icon_tasks', function() {return '<i class="fa-solid fa-list-check m-0"></i>';});
    Blade::directive('icon_warehouse', function() {return '<i class="fa-solid fa-warehouse m-0"></i>';});
    Blade::directive('icon_asc', function() {return '<i class="fa-solid fa-arrow-down-short-wide m-0"></i>';});
    Blade::directive('icon_desc', function() {return '<i class="fa-solid fa-arrow-down-wide-short m-0"></i>';});
    Blade::directive('icon_hourglass', function() {return '<i class="fa-regular fa-hourglass-half m-0"></i>';});
    Blade::directive('icon_clock', function() {return '<i class="fa-regular fa-clock m-0"></i>';});
    Blade::directive('icon_send', function() {return '<i class="fa-regular fa-paper-plane m-0"></i>';});
    Blade::directive('icon_envelope', function() {return '<i class="fa-solid fa-envelope m-0"></i>';});
    Blade::directive('icon_tasks', function() {return '<i class="fas fa-tasks m-0"></i>';});
    Blade::directive('icon_house', function() {return '<i class="fa-solid fa-house m-0"></i>';});
    Blade::directive('icon_equals', function() {return '<i class="fa-solid fa-equals m-0"></i>';});
    Blade::directive('icon_checklist', function() {return '<i class="menu-icon far fa-list-alt m-0 ml-1"></i>';});
    Blade::directive('icon_reply', function() {return '<i class="fa-solid fa-reply m-0"></i>';});
    Blade::directive('icon_code', function() {return '<i class="fa-solid fa-code m-0"></i>';});
    Blade::directive('icon_user_circle', function() {return '<i class="fa-solid fa-circle-user m-0"></i>';});
    Blade::directive('icon_excel', function() {return '<i class="fa-solid fa fa-file-excel m-0"></i>';});
    Blade::directive('icon_file_text', function() {return '<i class="fa-solid fa-file-text m-0"></i>';});




    Blade::directive('svg_snelstart',   function() {return '<svg xmlns="http://www.w3.org/2000/svg" class="tessa-svg"  version="1.0" width="99.000000pt" height="99.000000pt" viewBox="0 0 99.000000 99.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,99.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M0 560 l0 -430 495 0 495 0 0 430 0 430 -495 0 -495 0 0 -430z m546 223 c47 -123 37 -117 197 -128 48 -4 87 -10 87 -14 0 -3 -40 -40 -90 -81 -49 -41 -88 -79 -86 -85 12 -36 48 -216 44 -221 -3 -3 -51 23 -106 56 l-101 62 -101 -62 c-55 -35 -103 -60 -106 -58 -2 3 7 56 22 118 l26 113 -53 46 c-30 25 -72 62 -94 82 l-39 37 89 6 c174 13 155 2 210 127 27 60 51 109 55 109 3 0 24 -48 46 -107z"/><path d="M0 45 l0 -45 495 0 495 0 0 45 0 45 -495 0 -495 0 0 -45z"/></g></svg>';});
    Blade::directive('svg_exact_globe', function() {return '<svg xmlns="http://www.w3.org/2000/svg" class="tessa-svg"  version="1.0" width="347.000000pt" height="100.000000pt" viewBox="0 0 347.000000 100.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,100.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M3000 648 c0 -244 2 -277 19 -314 27 -59 65 -74 190 -74 l101 0 0 60 0 60 -73 0 c-105 0 -107 2 -107 173 l0 137 85 0 86 0 -3 63 -3 62 -82 3 -83 3 0 49 0 50 -65 0 -65 0 0 -272z"/><path d="M943 810 c-77 -16 -113 -43 -149 -110 -16 -29 -19 -57 -19 -160 0 -152 17 -201 86 -247 42 -28 45 -28 216 -31 l173 -4 0 60 0 61 -146 3 c-146 3 -146 3 -170 31 -45 53 -39 56 124 59 l147 3 0 60 0 60 -147 3 c-163 3 -169 6 -124 59 24 28 26 28 168 33 l143 5 0 60 0 60 -130 2 c-71 0 -149 -3 -172 -7z"/><path d="M1310 813 c0 -4 45 -67 101 -140 l100 -132 -93 -123 c-51 -67 -99 -130 -106 -140 -12 -17 -8 -18 65 -18 l79 0 63 85 c35 47 67 85 70 85 3 0 35 -39 70 -85 l64 -85 78 0 c44 0 79 1 79 3 0 2 -47 66 -104 143 -59 78 -101 143 -97 149 88 115 191 255 191 260 0 3 -34 5 -74 5 l-75 0 -63 -85 -63 -85 -20 23 c-11 13 -41 51 -67 85 l-48 62 -75 0 c-41 0 -75 -3 -75 -7z"/><path d="M1948 814 c-5 -4 -8 -34 -8 -66 l0 -58 145 0 c142 0 147 -1 170 -25 14 -13 25 -36 25 -50 l0 -25 -132 0 c-149 0 -180 -10 -218 -72 -29 -48 -28 -144 3 -189 41 -62 68 -69 285 -69 l192 0 0 209 c0 217 -5 248 -43 290 -42 45 -81 54 -252 59 -88 2 -163 1 -167 -4z m332 -389 l0 -45 -108 0 c-113 0 -132 6 -132 45 0 39 19 45 132 45 l108 0 0 -45z"/><path d="M2663 810 c-129 -26 -179 -112 -171 -299 5 -118 30 -179 91 -219 39 -26 47 -27 194 -30 l154 -4 -3 59 -3 58 -116 5 c-145 6 -163 16 -179 96 -16 77 -1 160 35 192 24 20 36 22 141 22 l114 0 0 65 0 65 -107 -1 c-60 -1 -127 -5 -150 -9z"/> <path d="M164 696 c-3 -7 -4 -35 -2 -62 l3 -49 238 -3 237 -2 0 65 0 65 -235 0 c-191 0 -237 -3 -241 -14z"/> <path d="M164 476 c-3 -7 -4 -35 -2 -62 l3 -49 238 -3 237 -2 0 65 0 65 -235 0 c-191 0 -237 -3 -241 -14z"/> </g></svg>';});
    Blade::directive('svg_eboekhouden', function() {return '<svg xmlns="http://www.w3.org/2000/svg" class="tessa-svg" width="669" height="517" viewBox="0 0 669 517" version="1.1"><path d="M 398 11.590 C 197.438 28.909, 95.687 251.265, 215.457 410.500 C 336.789 571.812, 593.965 525.020, 651.881 331.094 C 700.969 166.727, 566.158 -2.930, 398 11.590 M 399 146.527 C 351.775 153.932, 313.539 191.491, 302.568 241.250 C 301.962 244, 301.962 244, 267.617 244 C 228.681 244, 232 242.309, 232 262.140 C 232 271, 232 271, 266 271 C 300 271, 300 271, 300.006 275.250 C 300.174 396.108, 467.575 436.451, 529.347 330.521 C 535.904 319.277, 536.771 320, 516.716 320 C 499.431 320, 499.431 320, 497.670 322.982 C 459.723 387.221, 354.098 369.915, 335.451 296.404 C 328.424 268.704, 319.257 271, 436.856 271 C 555.659 271, 543.551 273.269, 540.897 251.500 C 532.250 180.575, 470.969 135.241, 399 146.527 M 404.238 176.110 C 373.990 181.149, 349.830 202.589, 338.418 234.519 C 335.029 244, 335.029 244, 421.461 244 C 518.636 244, 509.954 245.935, 501.750 226.102 C 486.951 190.323, 445.741 169.196, 404.238 176.110" stroke="none" fill="#7c7c7c" fill-rule="evenodd"/><path d="M 223.596 9.555 C 65.499 31.080, -31.739 188.783, 20.517 338.911 C 62.068 458.283, 188.774 527.460, 312.500 498.323 C 330.259 494.140, 330 494.571, 317.764 489.585 C 110.315 405.041, 117.279 101.354, 328.391 26.171 C 334.381 24.038, 338.865 22.034, 338.354 21.719 C 336.541 20.598, 317.247 15.162, 307.876 13.132 C 284.450 8.057, 246.446 6.443, 223.596 9.555 M 399 146.527 C 351.775 153.932, 313.539 191.491, 302.568 241.250 C 301.962 244, 301.962 244, 267.617 244 C 228.681 244, 232 242.309, 232 262.140 C 232 271, 232 271, 266 271 C 300 271, 300 271, 300.006 275.250 C 300.174 396.108, 467.575 436.451, 529.347 330.521 C 535.904 319.277, 536.771 320, 516.716 320 C 499.431 320, 499.431 320, 497.670 322.982 C 459.723 387.221, 354.098 369.915, 335.451 296.404 C 328.424 268.704, 319.257 271, 436.856 271 C 555.659 271, 543.551 273.269, 540.897 251.500 C 532.250 180.575, 470.969 135.241, 399 146.527 M 404.238 176.110 C 373.990 181.149, 349.830 202.589, 338.418 234.519 C 335.029 244, 335.029 244, 421.461 244 C 518.636 244, 509.954 245.935, 501.750 226.102 C 486.951 190.323, 445.741 169.196, 404.238 176.110" stroke="none" fill="#fcfcfc" fill-rule="evenodd"/></svg>';});

    Blade::directive('btn_img_business_central', function() {return '<img height="24" src="'.url('/client/public/img/businesscentral/btn-logo.png').'" >';});
    Blade::directive('btn_img_king', function() {return '<img height="24" src="'.url('/client/public/img/king/btn-logo.png').'" >';});
    Blade::directive('btn_img_2ba', function() {return '<img height="24" src="'.url('/client/public/img/2ba/btn-logo.png').'" >';});
    Blade::directive('btn_img_zenvoices', function() {return '<img height="24" src="'.url('/client/public/img/zenvoices/btn-logo.png').'" >';});
    Blade::directive('btn_img_zenvoices_vrij', function() {return '<img height="24" src="'.url('/client/public/img/zenvoices/btn-logo-vrij.png').'" >';});

  }

  public function register() {

  }
}
