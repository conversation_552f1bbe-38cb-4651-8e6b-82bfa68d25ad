<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Verlof extends Model {
  protected $table = 'verlof';

  public function user() {
    return $this->belongsTo('App\User', 'medewerker_id');
  }

  public function door() {
    return $this->belongsTo('App\User', 'akkoord_door');
  }

  public function verlofCard(){

    if($this->akkoord == 1){
     return [
     "background"=> "#19d895",
     "border"=> "#00683899",
     "icon"=> "<i class='fa fa-check m-0'></i>"
     ];

    }
    elseif($this->akkoord == 0 && $this->beoordeeld != 0){
    return [
      "background"=> "#ff6258",
      "border"=> "#d7544d",
      "icon"=> "<i class='fa fa-close m-0'></i>"
      ];
    }
    else
    {
      return [
      "background"=> "#ffaf00",
      "border"=> "#d69300",
      "icon"=> "<i class='bi bi-hourglass-split mx-0 d-flex'></i>"
      ];
    }
  }

  public function toPlanning($standaarduren, $verlofId){
    if (!isset($standaarduren) || $standaarduren->begintijd == null || $standaarduren->eindtijd == null) {return;}
    $begin = $standaarduren->begintijd;
    $eind = $standaarduren->eindtijd;

    if ($this->van != null && $this->tot != null) {
      $begin = $this->van;
      $eind = $this->tot;
    }

    Planning::insert([
      'user_id'       => $this->medewerker_id,
      'datum'         => $this->datum,
      'begin'         => $begin,
      'eind'          => $eind,
      'type'          => 'verlof',
      'verlof_id' => $verlofId,
    ]);
  }

  public function toUrenRegistratie($standaarduren){
    if (!isset($standaarduren) || $standaarduren->begintijd == null || $standaarduren->eindtijd == null) {return;}
    $begin = $standaarduren->begintijd;
    $eind = $standaarduren->eindtijd;

    if ($this->van != null && $this->tot != null) {
      $begin = $this->van;
      $eind = $this->tot;
    }
    $gewerkte_uren = $standaarduren->standaarduren ?? Carbon::parse($begin)->diffInHours($eind) - $standaarduren->pauze;
    UrenRegistratie::where('medewerker_id', $this->medewerker_id)->where('datum', $this->datum)->update(['tijdelijk' => 1]);
    UrenRegistratie::insert([
      'medewerker_id' => $this->medewerker_id,
      'datum'         => $this->datum,
      'projectnummer' => "-",
      'pauze'         => $standaarduren->pauze,
      'begintijd'     => $begin,
      'eindtijd'      => $eind,
      'gewerkte_uren' => $gewerkte_uren,
      'reisuren'      => 0,
      'overnachting'  => null,
      'heenreis_huisproject'   => null,
      'terugreis_huisproject'  => null,
      'heenreis_woonwerk'      => null,
      'terugreis_woonwerk'     => null,
      'ladenlossen_begintijd'        => null,
      'ladenlossen_eindtijd'         => null,
      'ladenlossen_middag_begintijd' => null,
      'ladenlossen_middag_eindtijd'  => null,
      'bestuurder'      => null,
      'bijrijder'       => null,
      'kenteken_id'     => null,
      'verlof'          => $gewerkte_uren,
      'bijzonderverlof' => 0,
      'ziekteuren'      => 0,
      'feesturen'       => 0,
      'verlofreden_id'  => $this->reden_id,
      'naca_id'         => null,
      'opmerkingen'     => $this->opmerkingen,
      'totaaluren100'      => 0,
      'totaal_overuren150' => 0,
      'totaal_overuren165' => 0,
      'totaal_overuren200' => 0
    ]);
  }
}
