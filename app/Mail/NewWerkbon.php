<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class NewWerkbon extends Mailable
{
  protected $werkbon;
  protected $subdomein;

  public function __construct($werkbon, $subdomein) {
    $this->werkbon = $werkbon;
    $this->subdomein = $subdomein;
  }

  public function build() {
    if (!isset($this->werkbon->template->mail_onderwerp)){
      $subject = "Werkbon ".$this->werkbon->werkbonnummer;
    }else{
      $subject = str_replace('{werkbon-werkbonnummer}', ($this->werkbon->werkbonnummer ?? ''), $this->werkbon->template->mail_onderwerp);
      $subject = str_replace('{werkbon-projectnaam}', ($this->werkbon->project->projectnaam ?? ''), $subject);
    }

    $email = $this->subject($subject)->from('<EMAIL>', 'IkBenTessa')->view('mails.newWerkbon')->with([
      'subdomein' => $this->subdomein,
      'werkbon' => $this->werkbon
    ]);

    $filePath = url('/werkbonnen/token/' . $this->werkbon->token);
    if ($filePath){
      $email->attach($filePath, [
        'as' => $this->werkbon->werkbonnummer . '.pdf',
      ]);
    }

    return $email;
  }


}
