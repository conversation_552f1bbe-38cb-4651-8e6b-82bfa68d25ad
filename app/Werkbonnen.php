<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\BlankMail;

class Werkbonnen extends Model
{
    protected $table = "werkbonnen";

    function template(){
      return $this->hasOne(WerkbonnenTemplates::class, 'id', 'template_id')->with('keywords');
    }
    function project(){
      return $this->hasOne(Project::class, "id", "project_id")->with('offerte', 'klant', 'taken', 'uncompleted_taken', 'contactpersoon');
    }
    function project_taken(){
      return $this->hasManyThrough(ProjectTaken::class, WerkbonnenProjectTaken::class, "werkbon_id", "id", "id", "taak_id")->with('custom');

    }
    function offerte(){
        return $this->hasOneThrough(Offertes::class, Project::class, "id", "offertenummer", "project_id", "projectnr");
    }
    function _bv(){
      return $this->hasOne(BV::class, 'id', 'bv');
    }
    function vestiging(){
      return $this->hasOne(Vestigingen::class, 'id', 'vestiging_id');
    }
    function klant(){
      return $this->hasOne(Klanten::class,"id", "klant_id")->with('contactpersonen');
    }
    function user(){
      return $this->hasOne("App\User", "id", "user_id");
    }
    function regels(){
      return $this->hasMany(WerkbonRegels::class,"werkbon_id", "id");
    }
    function values(){
      return $this->hasMany(WerkbonTeksten::class, "werkbon_id", "id")->with('input');
    }
    function opvolg_values(){
      return $this->hasMany(WerkbonOpvolgTeksten::class, "werkbon_id", "id");
    }
    function keywords(){
      return $this->hasMany(WerkbonTeksten::class, "werkbon_id", "id")->with('input');
    }
    public function planning(){
      return $this->hasOneThrough(Planning::class, PlanningReleaseWerkbon::class, "werkbon_id", "id", "id", "planning_id");
    }

    //Lite Relations
    function project_lite(){
        return $this->hasOne(Project::class, "id", "project_id");
    }
    function klant_lite(){
        return $this->hasOne(Klanten::class,"id", "klant_id");
    }
    function template_lite(){
        return $this->hasOne(WerkbonnenTemplates::class, 'id', 'template_id');
    }

    public function _contact(){
      if (isset($this->__contact)){ return $this->__contact; }

      $contact = new \stdClass();
      $contact->voornaam = null;
      $contact->achternaam = null;

      $klant = Klanten::where('id', $this->klant_id)->first();
      $contactpersoon = KlantenContactpersonen::where(['id' => $this->contactpersoon_id])->first();

      if ($klant){
        $contact->voornaam = $klant->contactpersoon_voornaam ?? null;
        $contact->achternaam = $klant->contactpersoon_achternaam ?? null;
        $contact->functie = $klant->contactpersoon_functie ?? null;
        $contact->email = $klant->contactpersoon_email ?? null;
        $contact->telefoon = $klant->contactpersoon_telefoon ?? null;
        $contact->mobiel = $klant->contactpersoon_mobiel ?? null;
      }
      if ($contactpersoon){
        $contact->voornaam = $contactpersoon->voornaam;
        $contact->achternaam = $contactpersoon->achternaam;
        $contact->functie = $contactpersoon->functie;
        $contact->email = $contactpersoon->email;
        $contact->telefoon = $contactpersoon->telefoon;
        $contact->mobiel = $contactpersoon->mobiel;
      }

      $this->__contact = $contact;
      return $contact;
    }
    public function color(){
      if($this->stadium() == 'Opvolgen'){
        return $this->opvolgColor();
      }
      if($this->status == 'Afgerond'){
        return 'success';
      }
      elseif($this->status == 'Verzonden'){
        return 'primary';
      }
      elseif($this->status == 'Verwijderd'){
        return 'dark';
      }

      return 'secondary';
    }

    public function opvolgColor(){
      try{
      $termijn = $this->opvolgTermijn();
      switch ($termijn) {
        case 'open': return 'primary';
        case 'gereed': return 'success';
        case 'reminder': return 'warning';
        case 'verlopen': return 'danger';
        default: return 'primary';
      }
      }catch (\Exception $e){ actError($e); }
    }

    public function stadium(){
        $opvset = getSettingJson('werkbon_opvolgstappen');
        $opvset = find(['status', 'statusgereed'], $this->status, $opvset);
        if($opvset){
          return $opvset['stadium'] ?? 'Opvolgen';
        }
        switch (strtolower($this->status)) {
            case 'afgerond': return 'Afgerond';
            case 'verwijderd': return 'Verwijderd';
            default: return 'Open';
        }
    }
    public function facturen(){
        return Facturen::where("werkbonnen", "like", "%\"".$this->id."\"%")->get();
    }
    public function opvolgen(){
      $planning = Planning::where(['werkbon_id' => $this->id])->get();
      if (!$planning->isEmpty() || $this->opvolgen == '0') {return false;}
      return true;
    }

    public function finishWerkbon($project, $selectFinish){
      if (!$selectFinish){return;}
      if($selectFinish == "finish"){
        Werkbonnen::where('project_id', $project->id ?? null)->update(['status' => 'Afgerond']);
      }

      if ($selectFinish == "opvolgen"){
        Werkbonnen::where('id', $this->id)->update(['status' => 'Afgerond', 'opvolgen' => 1]);
      }
    }

    public static function templates(){
      return WerkbonnenTemplates::get();
    }
    public static function templateKeywords(){
      $templates = WerkbonnenTemplates::with('keywords')->get()->mapWithKeys(function ($item) {
        $keywordsKeyedByKeyword = $item->keywords->mapWithKeys(function ($keyword) {
          return [$keyword->keyword => $keyword->naam];
        });
        return [$item->naam => $keywordsKeyedByKeyword];
      });

      return $templates;
    }
    public static function standaardVelden(){
      return array(
        'werkbonnummer' => 'Werkbonnummer',
        'template' => 'Template',
        'datum' => 'Datum',
        'gefactureerd' => 'Gefactureerd',
        'status' => 'Status',
        'medewerker' => 'Medewerker',
        'klant' => 'Klant',
        'contactpersoon' => 'Contactpersoon',
        'vestiging' => 'Vestiging',
        'opmerking' => 'Opmerking',
        'bv' => 'BV',
        'project_id' => 'Project',
        'locatie_id' => 'Locatie',
        'mail_data' => 'Mail informatie',
      );
    }

    public static function planningRelease($p){
      try{
        $template = WerkbonnenTemplates::where('id', getSettingValue('werkbonnen_planning_empty_on_release'))->first();
        if(!$template){ return; }

        $planning = Planning::where('id', $p)->with('user', 'project', 'taken')->firstOrFail();
        $user = getUser($planning->user_id);

        $contactpersoonId = $planning->project->contactpersoon_id ?? null;
        foreach($planning->project->custom ?? [] as $custom){
          if($custom->keyword == 'contactpersoon'){
            $contactpersoonId = $custom->value;
          }
        }
        $w = Werkbonnen::insertGetId([
          "werkbonnummer" => Werkbonnen::werkbonnummer(($user->bv ?? firstBvObject()), ($planning->project->id ?? null)),
          "template_id" => $template->id,
          "bv" => $request->bv ?? firstBv(),
          "user_id" => $user->id ?? null,
          "klant_id" => $planning->project->klant_id ?? null,
          "contactpersoon_id" => $contactpersoonId ?? null,
          "project_id" => $planning->project->id ?? null,
          "vestiging_id" => $planning->project->vestiging_id ?? null,
          "handtekeningen" => null,
          "files" => '[]',
          "betalingsoptie" => null,
          "datum" => Carbon::now()->format("d-m-Y"),
          "opmerking" => null,
          "gefactureerd" => 0,
          "status" => 'Uitgebracht',
          "klant_signature" => null,
          "signed_at" => null,
          "token" => randomString(25),
          "created_at" => Carbon::now(),
        ]);
        foreach ($planning->taken ?? [] as $taak){
          WerkbonnenProjectTaken::insert([
            'werkbon_id' => $w,
            'taak_id' => $taak->id,
          ]);
        }

        PlanningReleaseWerkbon::insert([
          'planning_id' => $p,
          'werkbon_id' => $w,
        ]);
        return Werkbonnen::where('id', $w)->first();
      }
      catch (\Exception $e){ actError($e); }
      return null;

    }
    public static function stadiumStatussen($stadium){
      $setting = getSettingJson('werkbon_opvolgstappen');
      $opvStatussen = [];
      $afgerondStatussen = ['Afgerond'];
      $verwijderdStatussen = ['Verwijderd'];
      $openStatussen = ['Uitgebracht', 'Verzonden',];
      foreach($setting as $set){
        switch ($set['stadium'] ?? 'Opvolgen') {
          case 'Afgerond':
            $set['status'] ? $afgerondStatussen[] = $set['status'] : null;
            $set['statusgereed'] ? $afgerondStatussen[] = $set['statusgereed'] : null;
            break;
          case 'Verwijderd':
            $set['status'] ? $verwijderdStatussen[] = $set['status'] : null;
            $set['statusgereed'] ? $verwijderdStatussen[] = $set['statusgereed'] : null;
            break;
          case 'Opvolgen':
            $set['status'] ? $opvStatussen[] = $set['status'] : null;
            $set['statusgereed'] ? $opvStatussen[] = $set['statusgereed'] : null;
            break;
          case 'Open':
            $set['status'] ? $openStatussen[] = $set['status'] : null;
            $set['statusgereed'] ? $openStatussen[] = $set['statusgereed'] : null;
            break;
          default: break;
        }
      }
      switch (strtolower($stadium)) {
        case 'afgerond': return $afgerondStatussen;
        case 'verwijderd': return $verwijderdStatussen;
        case 'opvolgen': return $opvStatussen;
        default: return $openStatussen;
      }
    }
    public static function werkbonnummer($bv, $p = null){

      $prefix = getSettingValue('werkbonnummer_prefix');
      $afterfixLengthProject = getSettingValue('werkbonnummer_afterfix_length_project') ?? 3;
      $afterfixLengthNonProject = getSettingValue('werkbonnummer_afterfix_length_non_project') ?? 5;

      if(isset($p)){
        $project = Project::where('id', $p)->first();
        $count = Werkbonnen::where('project_id', $p)->count();
        return $project->projectnr.'/'.str_pad($count+1, $afterfixLengthProject, "0", STR_PAD_LEFT);
      }

      $count = Werkbonnen::where("bv", $bv->id)->count();


      return $prefix.date("Y").str_pad($count+1, $afterfixLengthNonProject, "0", STR_PAD_LEFT);
    }
    public static function verifyWerkbonnummer($nr){
      $check = Werkbonnen::where('werkbonnummer', $nr)->first();

      if(!isset($check)){return $nr;}

      $count = Werkbonnen::where('werkbonnummer', 'like', $nr.'-%')->count();
      return $nr.'-'.($count + 1);
    }

    public function opvolgTermijn(){
      $doneDate = $this->opvolg_values->where('date_done', 1)->first();
      $setting = find('statusgereed', $this->status, getSettingJson('werkbon_opvolgstappen'));

      if(!isset($doneDate) || !$setting || Carbon($doneDate->value)->isFuture()){ return 'open'; }

      $reminderdate = Carbon($doneDate->value)->addDays($setting['reminder']);
      $enddate = Carbon($doneDate->value)->addDays($setting['doorlooptijd']);

      if(Carbon($doneDate->value)->isPast() && $reminderdate->isFuture()){ return 'gereed'; }
      if($reminderdate->isPast() && $enddate->isFuture()){ return 'reminder'; }
      if($enddate->isPast()){ return 'verlopen'; }

      return 'open';
    }

    public function opvolgMail($edit = false){
      $termijn = $this->opvolgTermijn();
      if($termijn == 'gereed'){ return; }

      $setting = getSettingJson('werkbon_opvolgstappen');
      if($termijn == 'reminder'){
        $onderwerp = find('statusgereed', $this->status, $setting)['reminderonderwerp'] ?? $this->werkbonnummer.' '.$this->status.' Reminder!';
      }elseif($termijn == 'verlopen'){
        $onderwerp = find('statusgereed', $this->status, $setting)['verlopenonderwerp'] ?? $this->werkbonnummer.' '.$this->status.' Verlopen!';
      }else{
        $onderwerp = find('statusgereed', $this->status, $setting)['mailonderwerp'] ?? $this->werkbonnummer.' '.$this->status.'!';
      }
      $html = $edit ? '<p><b>Let op, wijziging!</b></p>' : '';
      $html .= "<table style='border-collapse: collapse; width: 100%;'><tbody>";
      foreach($this->opvolg_values as $value){
        if(isDate($value->value)){
          $value->value = Carbon($value->value)->format('d-m-Y');
        }
        $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>$value->titel:</th><td style='border: 1px solid #dee2e6; padding: 5px;' >$value->value</td></tr>";
      }

      $klant = '';
      if(isset($this->klant)){
        $klant = $this->klant->title();
      }

      $project = $this->project;
      $werkbonTekstExtraVelden = getSettingJson('opvolgmail_extra_velden');

      $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Werkbon:</th><td style='border: 1px solid #dee2e6; padding: 5px;'>$this->werkbonnummer</td></tr>";
      $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Klant:</th><td style='border: 1px solid #dee2e6; padding: 5px;'>$klant</td></tr>";
      $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Project:</th><td style='border: 1px solid #dee2e6; padding: 5px;'>$project->projectnaam</td></tr>";
      $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>Adres:</th><td style='border: 1px solid #dee2e6; padding: 5px;'>$project->adres $project->huisnummer$project->toevoeging $project->woonplaats</td></tr>";

      //Toon in setting geselecteerde velden van de werkbon in de mail
      if(isset($werkbonTekstExtraVelden[$this->template->id])){
        foreach ($werkbonTekstExtraVelden[$this->template->id] as $werkbonExtraVeld){
          $werkbonTekst = $this->regelByKeyword($werkbonExtraVeld);

          if(!$werkbonTekst){continue;}

          $html .= "<tr><th style='border: 1px solid #dee2e6; padding: 5px; text-align: left;'>$werkbonTekst->titel</th><td style='border: 1px solid #dee2e6; padding: 5px;'>$werkbonTekst->value</td></tr>";
        }
      }

      $html .= "</tbody></table>";

      //send mail
      $bv = BV::find($this->bv);
      $toMail = $this->user->email;
      $cc = '';
      $mailbuilder = Mail::to($toMail);
      $cc = getSettingValue('werkbon_email_sender', '');
      if($termijn == 'verlopen' && verifyEmail($cc)){
        $mailbuilder->cc($cc);
      }
      $mailbuilder->send( new BlankMail(
        $bv,
        getSettingValue('werkbon_email_sender', '<EMAIL>'),
        $bv->name,
        $onderwerp,
        $html,
      ));

    }

    function regelByKeyword($keyword){
      if(!$keyword){return;}

      $input = WerkbonnenTemplateKeywords::where(['template_id' => $this->template->id, 'keyword' => $keyword])->first();
      return $this->hasOne(WerkbonTeksten::class, "werkbon_id", "id")->where(['keyword' => $keyword, 'input_id' => $input->id, 'werkbon_id' => $this->id])->first();
    }
}
