# In production:
# APP_NAME="Ikbentessa.nl"
# APP_ENV=production
# APP_KEY= (php artisan key:generate in console)
# APP_DEBUG=true
# APP_URL=ikbentessa.nl (geen protocol meegeven!)
APP_NAME=Laravel
APP_ENV=local
APP_KEY= # php artisan key:generate
APP_DEBUG=true
APP_URL=localhost
APP_LOCALE=nl
APP_PROTOCOL=https:// # HTTPS:// of HTTP://

DB_TESSA_CONNECTION=mysql
DB_TESSA_HOST=127.0.0.1
DB_TESSA_PORT=3306
DB_TESSA_DATABASE=homestead
DB_TESSA_USERNAME=homestead
DB_TESSA_PASSWORD=secret

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

FCM_API_KEY=
